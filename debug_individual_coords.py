#!/usr/bin/env python3
"""
Debug individual coordinates for M5 and M6
"""

import cv2
import numpy as np
import pyautogui
import pytesseract

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def test_coordinate(name, x, y, size=35):
    """Test reading at a specific coordinate"""
    print(f"\nTesting {name} at ({x}, {y}) with {size}x{size} region:")
    
    try:
        # Capture region
        region_x = x - size // 2
        region_y = y - size // 2
        
        screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Save debug image
        cv2.imwrite(f"debug_{name}.png", gray)
        
        # Try OCR
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        if text.isdigit():
            print(f"  ✅ OCR Success: {text}")
            return int(text)
        else:
            print(f"  ❌ OCR Failed: '{text}'")
            return None
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def debug_m5_m6():
    """Debug M5 and M6 specifically"""
    print("M5 and M6 Debug")
    print("=" * 30)
    
    # Current coordinates
    m5_coord = (1144, 966)
    m6_coord = (1298, 965)
    
    print("Make sure you're on the initiate exchange screen")
    input("Press Enter to test current M5 and M6 coordinates...")
    
    # Test current coordinates
    m5_result = test_coordinate("M5_current", m5_coord[0], m5_coord[1])
    m6_result = test_coordinate("M6_current", m6_coord[0], m6_coord[1])
    
    print(f"\nCurrent Results:")
    print(f"M5: {m5_result if m5_result else 'FAILED'}")
    print(f"M6: {m6_result if m6_result else 'FAILED'}")
    
    # Test with different sizes
    print(f"\nTesting different region sizes:")
    for size in [25, 30, 35, 40, 45]:
        print(f"\n--- {size}x{size} region ---")
        m5_test = test_coordinate(f"M5_{size}x{size}", m5_coord[0], m5_coord[1], size)
        m6_test = test_coordinate(f"M6_{size}x{size}", m6_coord[0], m6_coord[1], size)

def recalibrate_m5_m6():
    """Recalibrate M5 and M6 coordinates"""
    print("M5 and M6 Recalibration")
    print("=" * 30)
    
    new_coords = {}
    
    for map_name in ['M5', 'M6']:
        print(f"\nRecalibrating {map_name}:")
        print(f"Position your mouse EXACTLY over the {map_name} quantity number")
        input("Press Enter to capture...")
        
        x, y = pyautogui.position()
        new_coords[map_name] = (x, y)
        print(f"  New {map_name} coordinate: ({x}, {y})")
        
        # Test immediately
        result = test_coordinate(f"{map_name}_new", x, y)
        if result:
            print(f"  ✅ New coordinate works: {result}")
        else:
            print(f"  ❌ New coordinate failed")
    
    print(f"\n{'='*40}")
    print("NEW COORDINATES:")
    print(f"{'='*40}")
    for map_name, coord in new_coords.items():
        print(f"{map_name}: {coord}")
    
    return new_coords

def test_offset_coordinates():
    """Test coordinates with small offsets"""
    print("Offset Coordinate Test")
    print("=" * 30)
    
    # Current coordinates
    m5_base = (1144, 966)
    m6_base = (1298, 965)
    
    print("Testing M5 and M6 with small offsets...")
    input("Press Enter to start...")
    
    offsets = [
        (0, 0, "original"),
        (-5, 0, "left_5"),
        (5, 0, "right_5"),
        (0, -5, "up_5"),
        (0, 5, "down_5"),
        (-3, -3, "up_left_3"),
        (3, 3, "down_right_3"),
    ]
    
    for dx, dy, name in offsets:
        print(f"\n--- Offset {name} ({dx:+d}, {dy:+d}) ---")
        
        m5_x, m5_y = m5_base[0] + dx, m5_base[1] + dy
        m6_x, m6_y = m6_base[0] + dx, m6_base[1] + dy
        
        m5_result = test_coordinate(f"M5_{name}", m5_x, m5_y)
        m6_result = test_coordinate(f"M6_{name}", m6_x, m6_y)
        
        print(f"M5 at ({m5_x}, {m5_y}): {m5_result if m5_result else 'FAILED'}")
        print(f"M6 at ({m6_x}, {m6_y}): {m6_result if m6_result else 'FAILED'}")

def main():
    """Main menu"""
    while True:
        print("\nM5/M6 Debug Tool")
        print("=" * 20)
        print("1. Debug M5 and M6")
        print("2. Recalibrate M5 and M6")
        print("3. Test Offset Coordinates")
        print("0. Exit")
        
        choice = input("\nChoice (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            debug_m5_m6()
        elif choice == '2':
            recalibrate_m5_m6()
        elif choice == '3':
            test_offset_coordinates()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
