#!/usr/bin/env python3
"""
Backup Script for Map Trade System
Creates a complete backup of the working map trade system
"""

import os
import shutil
import datetime
import json

def create_backup():
 """Create a complete backup of the map trade system"""
 
 # Create backup directory with timestamp
 timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
 backup_dir = f"backup_map_trade_{timestamp}"
 
 print(f"Creating backup: {backup_dir}")
 print("=" * 50)
 
 # Create backup directory
 os.makedirs(backup_dir, exist_ok=True)
 
 # Files to backup
 files_to_backup = [
 "modules/map_trade.py",
 "test_scenario_detection.py",
 "capture_new_exchange_after_red_thumb.py",
 "debug_screen_state.py",
 "capture_new_exchange_coord.py"
 ]
 
 # Backup main files
 print("Backing up main files:")
 for file_path in files_to_backup:
 if os.path.exists(file_path):
 dest_path = os.path.join(backup_dir, os.path.basename(file_path))
 shutil.copy2(file_path, dest_path)
 print(f" {file_path} → {dest_path}")
 else:
 print(f" {file_path} not found")
 
 # Backup templates directory if it exists
 if os.path.exists("templates"):
 templates_backup = os.path.join(backup_dir, "templates")
 shutil.copytree("templates", templates_backup, dirs_exist_ok=True)
 print(f" templates/ → {templates_backup}")
 
 # Backup module configuration
 if os.path.exists("module_configs.json"):
 shutil.copy2("module_configs.json", os.path.join(backup_dir, "module_configs.json"))
 print(f" module_configs.json → {backup_dir}/module_configs.json")
 
 # Create system documentation
 create_system_documentation(backup_dir)
 
 print(f"\n Backup completed: {backup_dir}")
 print(f" Total files backed up: {len(os.listdir(backup_dir))}")
 
 return backup_dir

def create_system_documentation(backup_dir):
 """Create comprehensive documentation of the working system"""
 
 doc_content = """# Map Trade System - Working Backup
 
## System Status: FULLY FUNCTIONAL 

### Key Features:
- Intelligent trade analysis (lowest → highest quantity)
- Red thumb detection with 0.9 confidence (no false positives)
- Cancel exchange detection and proper handling
- New exchange button detection and clicking
- Complete trade sequence execution
- Template-based screen state detection
- Manual and automatic execution modes

### Scenarios Handled:
1. **Cancel Exchange visible** → Go to Step 4 sequence
2. **Red thumb visible (confidence > 0.9)** → Click thumb → Click new exchange (1255, 1142)
3. **New exchange visible (no red thumb)** → Click new exchange directly (1255, 1142)
4. **Already on initiate exchange** → Execute intelligent trade
5. **Normal state** → Continue with normal sequence

### Key Coordinates:
- **New Exchange Button**: (1255, 1142) - Exact coordinate after red thumb click
- **Red Thumb**: Auto-detected via color detection
- **Step 4**: (1424, 280) - 4_confirm_1

### Template Confidence Levels:
- **Red Thumb Templates**: 0.9 (very strict to avoid false positives)
- **Cancel Exchange**: 0.7 (high priority detection)
- **Initiate Exchange**: 0.6-0.7 (template matching)
- **Trade Button**: 0.8 (trade detection)

### Flow Summary:
```
Steps 1-3 (from config)
↓
Post-Step-3 Analysis:
├── Cancel Exchange? → Step 4
├── Red Thumb? → Click thumb → Click (1255, 1142) → Intelligent trade
├── New Exchange? → Click (1255, 1142) → Intelligent trade
├── Initiate Exchange? → Intelligent trade
└── Normal → Continue sequence
```

### Files in This Backup:
- **map_trade.py**: Main module with all functionality
- **test_scenario_detection.py**: Testing script for scenarios
- **capture_new_exchange_after_red_thumb.py**: Coordinate capture tool
- **debug_screen_state.py**: Debug detection states
- **module_configs.json**: Module configuration
- **templates/**: All template images

### Usage:
1. **Manual Trigger**: Use GUI button or call `manual_trigger_map_trade()`
2. **Automatic**: Scheduled execution via main controller
3. **Testing**: Use test_scenario_detection.py to verify scenarios

### Last Working Date: """ + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """

### Notes:
- Red thumb confidence raised to 0.9 to eliminate false positives
- New exchange coordinate (1255, 1142) precisely calibrated
- Simple logic flow for reliable operation
- All scenarios tested and working
"""
 
 # Save documentation
 doc_path = os.path.join(backup_dir, "SYSTEM_DOCUMENTATION.md")
 with open(doc_path, 'w', encoding='utf-8') as f:
 f.write(doc_content)
 
 print(f" Documentation → {doc_path}")

def create_restore_script(backup_dir):
 """Create a script to restore the backup"""
 
 restore_script = f"""#!/usr/bin/env python3
'''
Restore Script for Map Trade System Backup
Restores the backed up map trade system
'''

import os
import shutil

def restore_backup():
 backup_dir = "{backup_dir}"
 
 print(f"Restoring from backup: {{backup_dir}}")
 print("=" * 50)
 
 # Restore main files
 files_to_restore = [
 "map_trade.py",
 "test_scenario_detection.py", 
 "capture_new_exchange_after_red_thumb.py",
 "debug_screen_state.py",
 "capture_new_exchange_coord.py"
 ]
 
 for filename in files_to_restore:
 src = os.path.join(backup_dir, filename)
 if filename == "map_trade.py":
 dest = os.path.join("modules", filename)
 else:
 dest = filename
 
 if os.path.exists(src):
 os.makedirs(os.path.dirname(dest), exist_ok=True)
 shutil.copy2(src, dest)
 print(f" {{src}} → {{dest}}")
 
 # Restore templates
 templates_src = os.path.join(backup_dir, "templates")
 if os.path.exists(templates_src):
 if os.path.exists("templates"):
 shutil.rmtree("templates")
 shutil.copytree(templates_src, "templates")
 print(f" {{templates_src}} → templates/")
 
 # Restore config
 config_src = os.path.join(backup_dir, "module_configs.json")
 if os.path.exists(config_src):
 shutil.copy2(config_src, "module_configs.json")
 print(f" {{config_src}} → module_configs.json")
 
 print("\\n Restore completed!")

if __name__ == "__main__":
 restore_backup()
"""
 
 restore_path = os.path.join(backup_dir, "restore_backup.py")
 with open(restore_path, 'w', encoding='utf-8') as f:
 f.write(restore_script)
 
 print(f" Restore script → {restore_path}")

def main():
 """Main backup function"""
 print("Map Trade System Backup")
 print("=" * 30)
 print("Creating complete backup of the working system...")
 print()
 
 try:
 backup_dir = create_backup()
 create_restore_script(backup_dir)
 
 print(f"\n SUCCESS! Complete backup created:")
 print(f" Directory: {backup_dir}")
 print(f" Documentation: {backup_dir}/SYSTEM_DOCUMENTATION.md")
 print(f" Restore script: {backup_dir}/restore_backup.py")
 print()
 print("To restore later, run:")
 print(f" python {backup_dir}/restore_backup.py")
 
 except Exception as e:
 print(f" Backup failed: {e}")
 import traceback
 traceback.print_exc()

if __name__ == "__main__":
 main()
