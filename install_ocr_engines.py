#!/usr/bin/env python3
"""
Install OCR engines for the diagnostic tool
"""
import subprocess
import sys

def install_package(package_name, import_name=None):
    """Install a package and test if it can be imported"""
    if import_name is None:
        import_name = package_name
    
    print(f"\n{'='*50}")
    print(f"Installing {package_name}...")
    print(f"{'='*50}")
    
    try:
        # Try to import first
        __import__(import_name)
        print(f"✅ {package_name} is already installed")
        return True
    except ImportError:
        pass
    
    try:
        # Install the package
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        
        # Test import
        __import__(import_name)
        print(f"✅ {package_name} installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False
    except ImportError as e:
        print(f"❌ {package_name} installed but cannot import: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error with {package_name}: {e}")
        return False

def main():
    print("OCR Engines Installation Script")
    print("=" * 50)
    print("This will install advanced OCR engines for better digit recognition")
    print()
    
    # List of packages to install
    packages = [
        ("easyocr", "easyocr"),
        ("paddlepaddle", "paddle"),
        ("paddleocr", "paddleocr"),
        ("transformers", "transformers"),
        ("torch", "torch"),  # Required for transformers
    ]
    
    results = {}
    
    for package_name, import_name in packages:
        results[package_name] = install_package(package_name, import_name)
    
    print(f"\n{'='*50}")
    print("INSTALLATION SUMMARY")
    print(f"{'='*50}")
    
    for package_name, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{package_name:15} : {status}")
    
    print(f"\n{'='*50}")
    print("NEXT STEPS")
    print(f"{'='*50}")
    
    successful_count = sum(results.values())
    total_count = len(results)
    
    if successful_count == total_count:
        print("🎉 All OCR engines installed successfully!")
        print("You can now run: python live_ocr_diagnostic.py")
    elif successful_count > 2:  # At least Tesseract + one other
        print(f"✅ {successful_count}/{total_count} OCR engines installed")
        print("You can run the diagnostic tool with partial functionality")
        print("Run: python live_ocr_diagnostic.py")
    else:
        print("⚠️  Only basic Tesseract OCR will be available")
        print("You can still run: python live_ocr_diagnostic.py")
        print("But consider installing at least EasyOCR for better results")
    
    print("\nNote: Some packages are large and may take time to download")
    print("EasyOCR: ~500MB, PaddleOCR: ~200MB, Transformers: ~1GB")

if __name__ == "__main__":
    main()
