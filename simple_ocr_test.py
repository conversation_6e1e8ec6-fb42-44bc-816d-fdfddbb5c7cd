#!/usr/bin/env python3
"""
Simple OCR test to verify map trade OCR improvements
"""
import sys
import os
import cv2
import numpy as np

def test_ocr_basic():
    """Basic OCR test"""
    print("🧪 Basic OCR Test")
    print("=" * 30)
    
    try:
        # Add modules to path
        sys.path.append('modules')
        
        # Import map trade module
        from map_trade import MapTradeModule
        print("✅ MapTradeModule imported successfully")
        
        # Initialize module
        module = MapTradeModule()
        print("✅ MapTradeModule initialized")
        
        # Create a simple test image with number "5"
        image = np.ones((35, 35, 3), dtype=np.uint8) * 255  # White background
        cv2.putText(image, "5", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
        print("✅ Test image created")
        
        # Test traditional OCR
        print("\n🔍 Testing Traditional OCR...")
        try:
            result = module._extract_number_traditional_ocr(image)
            if result == 5:
                print(f"✅ Traditional OCR: SUCCESS (result: {result})")
            else:
                print(f"⚠️ Traditional OCR: Got {result}, expected 5")
        except Exception as e:
            print(f"❌ Traditional OCR: ERROR - {e}")
        
        # Test AI OCR
        print("\n🤖 Testing AI OCR...")
        try:
            result = module._extract_number_ai_ocr(image)
            if result == 5:
                print(f"✅ AI OCR: SUCCESS (result: {result})")
            elif result is None:
                if hasattr(module, '_ai_ocr_available') and not module._ai_ocr_available:
                    print("⚠️ AI OCR: Not available (libraries not installed)")
                else:
                    print("⚠️ AI OCR: Failed to recognize number")
            else:
                print(f"⚠️ AI OCR: Got {result}, expected 5")
        except Exception as e:
            print(f"❌ AI OCR: ERROR - {e}")
        
        # Test combined OCR
        print("\n🔄 Testing Combined OCR...")
        try:
            result = module._extract_number_from_image(image)
            if result == 5:
                print(f"✅ Combined OCR: SUCCESS (result: {result})")
            else:
                print(f"⚠️ Combined OCR: Got {result}, expected 5")
        except Exception as e:
            print(f"❌ Combined OCR: ERROR - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tesseract():
    """Test if Tesseract is working"""
    print("\n🔍 Testing Tesseract OCR")
    print("=" * 30)
    
    try:
        import pytesseract
        
        # Test basic Tesseract functionality
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        # Create simple test image
        image = np.ones((50, 100, 3), dtype=np.uint8) * 255
        cv2.putText(image, "123", (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
        
        # Test OCR
        text = pytesseract.image_to_string(image, config='--psm 8 -c tessedit_char_whitelist=0123456789').strip()
        
        if text == "123":
            print(f"✅ Tesseract OCR: SUCCESS (result: '{text}')")
            return True
        else:
            print(f"⚠️ Tesseract OCR: Got '{text}', expected '123'")
            return False
            
    except Exception as e:
        print(f"❌ Tesseract test failed: {e}")
        return False

def test_ai_libraries():
    """Test AI OCR libraries"""
    print("\n🤖 Testing AI OCR Libraries")
    print("=" * 30)
    
    # Test EasyOCR
    try:
        import easyocr
        print("✅ EasyOCR library available")
        
        # Quick test
        reader = easyocr.Reader(['en'], gpu=False)
        
        # Create test image
        image = np.ones((50, 100), dtype=np.uint8) * 255
        cv2.putText(image, "42", (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, 0, 2)
        
        results = reader.readtext(image, allowlist='0123456789')
        if results and results[0][1] == "42":
            print("✅ EasyOCR working correctly")
        else:
            print(f"⚠️ EasyOCR result: {results}")
            
    except ImportError:
        print("❌ EasyOCR not installed")
    except Exception as e:
        print(f"⚠️ EasyOCR error: {e}")
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR library available")
        
        # Quick test
        ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        
        # Create test image
        image = np.ones((50, 100), dtype=np.uint8) * 255
        cv2.putText(image, "7", (35, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, 0, 2)
        
        results = ocr.ocr(image, cls=True)
        if results and results[0] and results[0][0][1][0] == "7":
            print("✅ PaddleOCR working correctly")
        else:
            print(f"⚠️ PaddleOCR result: {results}")
            
    except ImportError:
        print("❌ PaddleOCR not installed")
    except Exception as e:
        print(f"⚠️ PaddleOCR error: {e}")

if __name__ == "__main__":
    print("🚀 Simple OCR Test for Map Trade Module")
    print("Testing OCR reliability improvements")
    print("=" * 50)
    
    # Test Tesseract
    tesseract_ok = test_tesseract()
    
    # Test AI libraries
    test_ai_libraries()
    
    # Test map trade OCR
    ocr_ok = test_ocr_basic()
    
    print("\n📊 SUMMARY")
    print("=" * 50)
    print(f"Tesseract OCR: {'✅ Working' if tesseract_ok else '❌ Issues'}")
    print(f"Map Trade OCR: {'✅ Working' if ocr_ok else '❌ Issues'}")
    
    if tesseract_ok and ocr_ok:
        print("\n🎉 OCR system is working correctly!")
        print("✅ Traditional OCR should handle map piece detection")
        print("✅ AI OCR provides additional fallback capability")
    else:
        print("\n⚠️ OCR system needs attention")
        if not tesseract_ok:
            print("• Check Tesseract installation")
        if not ocr_ok:
            print("• Check map trade module OCR methods")
    
    print("\n🔧 If issues persist:")
    print("• Ensure Tesseract is properly installed")
    print("• Check image preprocessing methods")
    print("• Verify OCR configuration parameters")
