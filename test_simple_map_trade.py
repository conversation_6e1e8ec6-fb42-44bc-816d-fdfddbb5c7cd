#!/usr/bin/env python3
"""
Simple test for map trade - no complications
"""

from modules.map_trade import MapTradeModule
import logging

def test_simple_map_trade():
    """Simple test of map trade functionality"""
    print("Simple Map Trade Test")
    print("=" * 30)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Create module
    module = MapTradeModule()
    
    print("Current screen state detection:")
    screen_state = module._detect_screen_state()
    print(f"  Screen state: {screen_state}")
    
    print("\nRed thumb detection:")
    has_red_thumb = module._has_red_thumb()
    print(f"  Has red thumb: {has_red_thumb}")
    
    print("\nNew exchange button detection:")
    has_new_exchange = module._has_new_exchange_button()
    print(f"  Has new exchange button: {has_new_exchange}")
    
    print(f"\n{'='*40}")
    print("DECISION LOGIC:")
    print(f"{'='*40}")
    
    if screen_state == "trade_in_progress":
        print("✅ Cancel exchange visible → Go to step 4")
        action = "Continue with step 4 sequence"
    elif has_red_thumb:
        print("✅ Red thumb visible → Click thumb → Click new exchange")
        action = "Handle red thumb sequence"
    elif has_new_exchange:
        print("✅ New exchange button visible → Click it")
        action = "Click new exchange button"
    elif screen_state == "initiate_exchange":
        print("✅ Already on initiate exchange → Do intelligent trade")
        action = "Execute intelligent exchange"
    else:
        print("⚠️ Unknown state → Try step 4")
        action = "Try step 4 sequence"
    
    print(f"Action: {action}")
    
    execute = input(f"\nExecute this action? (y/n): ").lower().strip()
    
    if execute == 'y':
        print("\n🚀 Executing manual trigger...")
        try:
            result = module.manual_trigger_map_trade()
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("Test cancelled")

def main():
    """Main function"""
    try:
        test_simple_map_trade()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
