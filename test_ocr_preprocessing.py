#!/usr/bin/env python3
"""
Test different OCR preprocessing techniques on the debug images
"""
import cv2
import numpy as np
import pytesseract
import os

def test_ocr_preprocessing():
    """Test different preprocessing techniques on existing debug images"""
    
    # Configure Tesseract path
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    # Test on the debug images we just created
    debug_images = [
        "debug_M1_single_test.png",
        "debug_M4_capture.png",  # This one worked
        "debug_gray_last.png",
        "debug_thresh_last.png"
    ]
    
    for image_path in debug_images:
        if not os.path.exists(image_path):
            print(f"Image {image_path} not found, skipping...")
            continue
            
        print(f"\n=== TESTING {image_path} ===")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Could not load {image_path}")
            continue
            
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        print(f"Image size: {gray.shape}")
        
        # Test different preprocessing techniques
        preprocessing_methods = [
            ("Original", gray),
            ("Inverted", 255 - gray),
            ("Binary", cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)[1]),
            ("Binary Inverted", cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)[1]),
            ("OTSU", cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]),
            ("OTSU Inverted", cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]),
            ("Scaled 2x", cv2.resize(gray, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)),
            ("Scaled 4x", cv2.resize(gray, None, fx=4, fy=4, interpolation=cv2.INTER_CUBIC)),
        ]
        
        # Test different OCR configurations
        ocr_configs = [
            ('PSM 8', '--psm 8 -c tessedit_char_whitelist=0123456789'),
            ('PSM 7', '--psm 7 -c tessedit_char_whitelist=0123456789'),
            ('PSM 6', '--psm 6 -c tessedit_char_whitelist=0123456789'),
            ('PSM 13', '--psm 13 -c tessedit_char_whitelist=0123456789'),
            ('PSM 10', '--psm 10 -c tessedit_char_whitelist=0123456789'),
            ('Default', ''),
        ]
        
        best_results = []
        
        for prep_name, prep_image in preprocessing_methods:
            print(f"\n  --- {prep_name} ---")
            
            # Save preprocessed image for inspection
            debug_prep_path = f"debug_prep_{prep_name.replace(' ', '_')}_{os.path.basename(image_path)}"
            cv2.imwrite(debug_prep_path, prep_image)
            
            for config_name, config in ocr_configs:
                try:
                    if config:
                        text = pytesseract.image_to_string(prep_image, config=config).strip()
                    else:
                        text = pytesseract.image_to_string(prep_image).strip()
                    
                    if text and text.isdigit():
                        result = f"  {config_name}: SUCCESS -> '{text}'"
                        print(result)
                        best_results.append((prep_name, config_name, text))
                    elif text:
                        print(f"  {config_name}: '{text}' (not digit)")
                    else:
                        print(f"  {config_name}: (empty)")
                        
                except Exception as e:
                    print(f"  {config_name}: ERROR - {e}")
        
        if best_results:
            print(f"\n*** SUCCESSFUL RESULTS FOR {image_path}: ***")
            for prep, config, result in best_results:
                print(f"  {prep} + {config} = {result}")
        else:
            print(f"\n*** NO SUCCESSFUL RESULTS FOR {image_path} ***")

if __name__ == "__main__":
    test_ocr_preprocessing()
