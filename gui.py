"""
GUI interface for Last War automation
Provides user-friendly controls for all modules and system status
Enhanced with Unified Configuration System
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import threading
import time
from typing import Dict, Any
import configparser
import logging

from main_controller import MainController
from centralized_scanner_gui import CentralizedScannerGUI
from unified_config_manager import UnifiedConfigManager


class LastWarGUI:
    """Main GUI application for Last War automation"""
    
    def __init__(self, controller=None):
        self.root = tk.Tk()
        self.root.title("Last War Automation Helper")
        # Set initial size and position at top-right corner
        screen_width = self.root.winfo_screenwidth()
        window_width = 1260
        window_height = 1300
        x_position = screen_width - window_width - 10  # 10px from right edge
        y_position = 0  # Top of screen

        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        self.root.resizable(True, True)  # Allow resizing

        # Configure modern styling first
        self._setup_styles()

        # Apply Chrome dark background
        self.root.configure(bg=self.colors['bg_primary'])

        # Set minimum window size to prevent too small
        self.root.minsize(1200, 700)

        # Try to make title bar dark (Windows 10/11)
        try:
            import ctypes
            from ctypes import wintypes
            hwnd = self.root.winfo_id()
            # Enable dark mode for title bar
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd, 20, ctypes.byref(ctypes.c_int(1)), ctypes.sizeof(ctypes.c_int)
            )
        except:
            pass  # Fallback if dark title bar not supported

        # Initialize logger
        self.logger = logging.getLogger("LastWar.GUI")

        # Initialize unified configuration system
        self.unified_config = UnifiedConfigManager()
        self.config_data = self.unified_config.load_config()

        # Load legacy configuration for backward compatibility
        self.config = configparser.ConfigParser()
        self.config.read("config.ini")

        # Initialize controller (use provided one or create new)
        self.controller = controller if controller else MainController()

        self.logger.info("GUI initialized with unified configuration system")

        # If no controller was provided, auto-register modules
        if controller is None:
            self._auto_register_modules()

        # Set GUI reference in controller for statistics updates
        if self.controller:
            self.controller.gui = self

        # Initialize pause state
        self.is_paused = False
        
        # GUI state
        self.module_vars = {}
        self.module_frames = {}  # Track module frames
        self.status_update_thread = None
        self.running_status_updates = False
        self.status_panel = None
        self.status_tree = None
        self.manual_wait_vars = {}  # Store manual wait time variables
        self.status_panel_visible = False
        self.gem_buttons = {}  # Track gem toggle buttons

        # Initialize centralized scanner GUI
        self.centralized_scanner_gui = CentralizedScannerGUI(self.root, self.controller)

        # Define module definitions early (needed for header)
        self.module_definitions = [
            ("dig", "🏗️ Dig", "High-priority dig opportunities (URGENT)"),
            ("daily_tasks", "🎯 Daily Tasks", "Collect daily rewards automatically"),
            ("alliance_donation", "🤝 Alliance Donation", "Help alliance members hourly"),
            ("zombie_invasion", "🧟 Zombie Invasion", "Deploy squads to hunt zombies"),
            ("map_trade", "🗺️ Map Trade", "Trade resources every 5 minutes"),
            ("help_click", "🆘 Help Click", "Click alliance help requests")
        ]

        # Create GUI
        self._create_widgets()
        self._setup_layout()
        self._load_module_states()
        
        # Start status updates
        self._start_status_updates()

        # Update mouse override button state on startup
        self._update_mouse_override_button()

        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _auto_register_modules(self):
        """Auto-register modules when no controller is provided"""
        try:
            self.logger.info("[AUTO-REG] Auto-registering modules for centralized scanner...")

            # Import and register all modules
            from modules.enhanced_help_click import EnhancedHelpClickModule
            from modules.alliance_donation import AllianceDonationModule
            from modules.zombie_invasion import ZombieInvasionModule
            from modules.map_trade import MapTradeModule
            from modules.find_truck import FindTruckModule
            from modules.daily_tasks import DailyTasksModule

            # Create module instances
            modules = [
                DailyTasksModule(),
                EnhancedHelpClickModule(),
                AllianceDonationModule(),
                ZombieInvasionModule(),
                MapTradeModule(),
                FindTruckModule(self.controller.screen_scanner),
            ]

            # Register each module
            for module in modules:
                self.controller.register_module(module)
                self.logger.info(f"[AUTO-REG] Auto-registered: {module.name}")

            self.logger.info(f"[AUTO-REG] Auto-registered {len(modules)} modules successfully")

        except Exception as e:
            self.logger.error(f"❌ Error auto-registering modules: {e}")
            import traceback
            traceback.print_exc()

    def _setup_styles(self):
        """Setup modern styling for the application"""
        style = ttk.Style()

        # Configure modern theme
        style.theme_use('clam')

        # Define proper Chrome-style dark mode color scheme
        self.colors = {
            'bg_primary': '#202124',     # Chrome dark background
            'bg_secondary': '#303134',   # Chrome dark secondary
            'bg_tertiary': '#3c4043',    # Chrome dark tertiary
            'text_primary': '#e8eaed',   # Chrome light text
            'text_secondary': '#9aa0a6', # Chrome secondary text
            'text_disabled': '#5f6368',  # Chrome disabled text
            'accent_blue': '#8ab4f8',    # Chrome blue accent
            'accent_green': '#81c995',   # Chrome green
            'accent_red': '#f28b82',     # Chrome red
            'accent_yellow': '#fdd663',  # Chrome yellow
            'accent_orange': '#ffb74d',  # Chrome orange
            'accent_purple': '#c58af9',  # Chrome purple
            'border': '#5f6368',         # Chrome border
            'hover': '#48494a',          # Chrome hover state
            'active': '#5f6368',         # Chrome active state
            'surface': '#292a2d',        # Chrome surface
            'divider': '#3c4043'         # Chrome divider
        }

        # Configure Chrome-style dark mode
        style.theme_use('clam')  # Use clam theme as base for better dark mode support

        # Configure main styles
        style.configure('.',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_secondary'],
                       bordercolor=self.colors['border'],
                       focuscolor=self.colors['accent_blue'])

        style.configure('Title.TLabel',
                       font=('Segoe UI', 18, 'bold'),
                       foreground=self.colors['text_primary'],
                       background=self.colors['bg_primary'])

        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 11),
                       foreground=self.colors['text_secondary'],
                       background=self.colors['bg_primary'])

        # Frame styles
        style.configure('TFrame',
                       background=self.colors['bg_primary'],
                       bordercolor=self.colors['border'])

        style.configure('TLabelFrame',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       bordercolor=self.colors['border'])

        style.configure('TLabelFrame.Label',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'])

        # Button styles
        style.configure('TButton',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       bordercolor=self.colors['border'],
                       focuscolor=self.colors['accent_blue'])

        style.map('TButton',
                 background=[('active', self.colors['hover']),
                           ('pressed', self.colors['active'])])

        # Checkbutton styles
        style.configure('TCheckbutton',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'],
                       focuscolor=self.colors['accent_blue'])

        style.map('TCheckbutton',
                 background=[('active', self.colors['bg_primary'])])

        # Label styles
        style.configure('TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'])

        style.configure('Module.TFrame',
                       relief='raised',
                       borderwidth=1,
                       background='white')

        style.configure('Control.TFrame',
                       relief='flat',
                       borderwidth=0,
                       background=self.colors['bg_primary'])

    def _create_widgets(self):
        """Create all GUI widgets with better space usage"""
        # Main frame - no padding for maximum space usage
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights for compact horizontal layout
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)  # Single column layout
        main_frame.rowconfigure(2, weight=1)     # Module area gets most space
        main_frame.rowconfigure(3, weight=0)     # Status area fixed height
        
        # Compact header section
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'], pady=10)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        header_frame.columnconfigure(1, weight=1)

        # Clean, compact title
        title_label = tk.Label(header_frame, text="Last War Automation Helper",
                              font=('Segoe UI', 16, 'bold'),
                              fg=self.colors['text_primary'],
                              bg=self.colors['bg_primary'])
        title_label.grid(row=0, column=0, sticky=tk.W, padx=(20, 0))
        
        # Compact control frame
        control_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=20, pady=12)
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        control_frame.columnconfigure(9, weight=1)  # Allow expansion after all buttons

        # Compact uniform control buttons
        button_style = {
            'font': ("Segoe UI", 9, "bold"),
            'relief': "flat",
            'padx': 18,
            'pady': 8,
            'cursor': "hand2",
            'bd': 0,
            'highlightthickness': 0,
            'activebackground': self.colors['hover']
        }

        self.start_button = tk.Button(control_frame, text="▶ Start",
                                     command=self._start_automation,
                                     bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                     **button_style)
        self.start_button.grid(row=0, column=0, padx=(0, 8))

        self.pause_button = tk.Button(control_frame, text="⏸ Pause",
                                     command=self._pause_automation, state="disabled",
                                     bg=self.colors['accent_yellow'], fg=self.colors['bg_primary'],
                                     **button_style)
        self.pause_button.grid(row=0, column=1, padx=(0, 8))

        self.stop_button = tk.Button(control_frame, text="⏹ Stop",
                                    command=self._stop_automation, state="disabled",
                                    bg=self.colors['accent_red'], fg=self.colors['bg_primary'],
                                    **button_style)
        self.stop_button.grid(row=0, column=2, padx=(0, 8))

        config_button = tk.Button(control_frame, text="⚙️ Config",
                                 command=self._open_config,
                                 bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                                 **button_style)
        config_button.grid(row=0, column=3, padx=(0, 8))

        # Mouse Override Toggle Button
        self.mouse_override_button = tk.Button(control_frame, text="🖱️ Mouse Override: ON",
                                              command=self._toggle_mouse_override,
                                              bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                              **button_style)
        self.mouse_override_button.grid(row=0, column=4, padx=(0, 8))

        self.status_button = tk.Button(control_frame, text="📊 Status",
                                      command=self._toggle_status_panel,
                                      bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                                      **button_style)
        self.status_button.grid(row=0, column=5, padx=(0, 8))

        diagnostic_button = tk.Button(control_frame, text="🔍 Diagnostic",
                                     command=self._open_diagnostic,
                                     bg=self.colors['accent_blue'], fg=self.colors['bg_primary'],
                                     **button_style)
        diagnostic_button.grid(row=0, column=6, padx=(0, 8))

        # Centralized Scanner Configuration Button
        scanner_button = tk.Button(control_frame, text="🎯 Scanner",
                                  command=self._open_centralized_scanner,
                                  bg=self.colors['accent_purple'], fg=self.colors['bg_primary'],
                                  **button_style)
        scanner_button.grid(row=0, column=7, padx=(0, 8))

        emergency_button = tk.Button(control_frame, text="🚨 Emergency",
                                   command=self._emergency_stop,
                                   bg=self.colors['accent_red'], fg=self.colors['bg_primary'],
                                   **button_style)
        emergency_button.grid(row=0, column=8, padx=(0, 8))

        # Window management test button
        window_button = tk.Button(control_frame, text="Focus Game",
                                 command=self._test_window_management,
                                 bg=self.colors['accent_blue'], fg=self.colors['bg_primary'],
                                 **button_style)
        window_button.grid(row=0, column=9)

        # Status label with Chrome styling
        self.status_label = ttk.Label(control_frame, text="🔴 Status: Stopped",
                                     font=('Segoe UI', 11, 'bold'),
                                     foreground=self.colors['accent_red'])
        self.status_label.grid(row=1, column=0, columnspan=5, pady=(10, 0))

        # Scanner mode indicator
        scanner_mode = "Centralized" if self.controller.use_centralized_scanner else "Legacy"
        self.scanner_mode_label = ttk.Label(control_frame, text=f"🎯 Scanner: {scanner_mode}",
                                           font=('Segoe UI', 10),
                                           foreground=self.colors['accent_purple'])
        self.scanner_mode_label.grid(row=1, column=5, columnspan=4, pady=(10, 0), sticky='e')
        
        # Main content frame - single column layout
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)  # Module panel gets most space
        content_frame.rowconfigure(1, weight=0)  # Status panel fixed height

        # Module controls panel (top)
        self._create_module_panel(content_frame)

        # Status and logs panel (bottom)
        self._create_status_panel(content_frame)
    
    def _create_module_panel(self, parent):
        """Create industrial clean module control panel"""
        # Compact module frame - reduced padding
        module_frame = tk.Frame(parent, bg=self.colors['bg_primary'], padx=10, pady=5)
        module_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        module_frame.columnconfigure(0, weight=1)
        module_frame.rowconfigure(2, weight=1)  # List gets most space

        # Compact module header
        header_label = tk.Label(module_frame, text="Automation Modules",
                               font=('Segoe UI', 13, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_primary'])
        header_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        # Module summary header
        summary_frame = tk.Frame(module_frame, bg=self.colors['bg_primary'])
        summary_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        summary_frame.columnconfigure(1, weight=1)

        # Active modules counter with Chrome styling
        active_label = tk.Label(summary_frame, text="📊 Active Modules:",
                               font=('Segoe UI', 11, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_primary'])
        active_label.grid(row=0, column=0, sticky=tk.W)

        # Dynamic active/total counter with Chrome styling
        self.module_count_label = tk.Label(summary_frame, text="0/5",
                                          fg=self.colors['accent_green'],
                                          bg=self.colors['bg_primary'],
                                          font=('Segoe UI', 11, 'bold'))
        self.module_count_label.grid(row=0, column=1, sticky=tk.E)
        
        # Industrial clean module list
        list_frame = tk.Frame(module_frame, bg=self.colors['bg_primary'])
        list_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Industrial dark themed canvas and scrollbar
        canvas = tk.Canvas(list_frame, bg=self.colors['bg_primary'],
                          highlightthickness=0, bd=0)
        scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=canvas.yview,
                                bg=self.colors['bg_tertiary'],
                                troughcolor=self.colors['bg_secondary'],
                                activebackground=self.colors['accent_blue'],
                                highlightbackground=self.colors['bg_tertiary'],
                                highlightcolor=self.colors['bg_tertiary'],
                                width=14)
        self.scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_primary'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Module definitions already defined in __init__ - create module controls
        
        # Create module controls
        for i, (module_id, name, description) in enumerate(self.module_definitions):
            self._create_module_control(self.scrollable_frame, i, module_id, name, description)
        
        # Industrial clean bulk controls
        bulk_frame = tk.Frame(module_frame, bg=self.colors['bg_primary'])
        bulk_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(8, 0))

        # Uniform bulk control buttons
        bulk_button_style = {
            'font': ("Segoe UI", 9, "bold"),
            'relief': "flat",
            'padx': 15,
            'pady': 8,
            'cursor': "hand2",
            'bd': 0,
            'highlightthickness': 0,
            'activebackground': self.colors['hover']
        }

        tk.Button(bulk_frame, text="Enable All",
                 command=self._enable_all_modules,
                 bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                 **bulk_button_style).grid(row=0, column=0, padx=(0, 10))

        tk.Button(bulk_frame, text="Disable All",
                 command=self._disable_all_modules,
                 bg=self.colors['accent_red'], fg=self.colors['bg_primary'],
                 **bulk_button_style).grid(row=0, column=1, padx=(0, 10))

        tk.Button(bulk_frame, text="Save Config",
                 command=self._save_config,
                 bg=self.colors['accent_blue'], fg=self.colors['bg_primary'],
                 **bulk_button_style).grid(row=0, column=2)
    
    def _create_module_control(self, parent, row, module_id, name, description):
        """Create beautiful, modern control widgets for a single module"""
        # Compact module card - reduced padding for thinner rows
        frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=0, padx=10, pady=5)
        frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 1), padx=0)
        frame.columnconfigure(1, weight=1)

        # Module state variable
        var = tk.BooleanVar()
        self.module_vars[module_id] = var

        # Checkbox with better styling
        checkbox = ttk.Checkbutton(frame, variable=var,
                                  command=lambda: self._toggle_module(module_id))
        checkbox.grid(row=0, column=0, padx=(0, 10), sticky='w')

        # Module info section with Chrome styling
        info_frame = tk.Frame(frame, bg=self.colors['bg_secondary'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        info_frame.columnconfigure(0, weight=1)

        # Module name with Chrome styling
        name_label = tk.Label(info_frame, text=name,
                             font=('Segoe UI', 13, 'bold'),
                             fg=self.colors['text_primary'],
                             bg=self.colors['bg_secondary'])
        name_label.grid(row=0, column=0, sticky='w', pady=(0, 2))

        # Description with Chrome styling
        desc_label = tk.Label(info_frame, text=description,
                             font=('Segoe UI', 10),
                             fg=self.colors['text_secondary'],
                             bg=self.colors['bg_secondary'])
        desc_label.grid(row=1, column=0, sticky='w')

        # Status indicator with Chrome styling
        status_label = tk.Label(frame, text="🔴",
                               font=('Segoe UI', 16),
                               bg=self.colors['bg_secondary'])
        status_label.grid(row=0, column=2, padx=(10, 15), sticky='e')

        # Control buttons frame with Chrome styling
        controls_frame = tk.Frame(frame, bg=self.colors['bg_secondary'])
        controls_frame.grid(row=0, column=3, sticky='e', padx=(0, 5))

        # Modern button layout
        col = 0

        # Industrial uniform module buttons
        module_button_style = {
            'font': ("Segoe UI", 8, "bold"),
            'relief': "flat",
            'padx': 12,
            'pady': 6,
            'cursor': "hand2",
            'bd': 0,
            'highlightthickness': 0,
            'activebackground': self.colors['hover']
        }

        if module_id == "daily_tasks":
            daily_button = tk.Button(controls_frame, text="▶",
                                   command=lambda: self._trigger_daily_tasks_no_popup(),
                                   bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                   **module_button_style)
            daily_button.grid(row=0, column=col, padx=(0, 8))
            col += 1

        elif module_id == "alliance_donation":
            alliance_button = tk.Button(controls_frame, text="▶",
                                      command=lambda: self._trigger_alliance_donation_no_popup(),
                                      bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                      **module_button_style)
            alliance_button.grid(row=0, column=col, padx=(0, 8))
            col += 1

            # Gem toggle button - uniform styling
            gem_button = tk.Button(controls_frame, text="💎",
                                 command=lambda: self._toggle_gem_step(),
                                 bg=self.colors['accent_yellow'], fg=self.colors['bg_primary'],
                                 **module_button_style)
            gem_button.grid(row=0, column=col, padx=(0, 8))
            self.gem_buttons[module_id] = gem_button
            self._update_gem_button_status()
            col += 1

        elif module_id == "map_trade":
            map_trade_button = tk.Button(controls_frame, text="▶",
                                        command=lambda: self._trigger_map_trade_no_popup(),
                                        bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                        **module_button_style)
            map_trade_button.grid(row=0, column=col, padx=(0, 8))
            col += 1

        # Configuration button - uniform styling
        config_btn = tk.Button(controls_frame, text="⚙️",
                              command=lambda m=module_id: self._open_module_config_direct(m),
                              bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                              **module_button_style)
        config_btn.grid(row=0, column=col, padx=(0, 8))

        # Calibration button - uniform styling
        calib_btn = tk.Button(controls_frame, text="📍",
                             command=lambda m=module_id: self._open_calibration_tool(m),
                             bg=self.colors['accent_blue'], fg=self.colors['bg_primary'],
                             **module_button_style)
        calib_btn.grid(row=0, column=col+1, padx=(8, 0))

        # Store references for status updates
        setattr(self, f"{module_id}_status", status_label)
        self.module_frames[module_id] = frame

    def _update_active_module_count(self):
        """Update the active/total module counter"""
        try:
            total_modules = len(self.module_definitions)
            active_modules = sum(1 for var in self.module_vars.values() if var.get())

            # Update the counter display
            self.module_count_label.config(text=f"{active_modules}/{total_modules}")

            # Change color based on status (Chrome colors)
            if active_modules == 0:
                color = self.colors['accent_red']  # Chrome red if none active
            elif active_modules == total_modules:
                color = self.colors['accent_green']  # Chrome green if all active
            else:
                color = self.colors['accent_yellow']  # Chrome yellow if some active

            self.module_count_label.config(fg=color)

        except Exception as e:
            print(f"Error updating active module count: {e}")

    def _create_status_panel(self, parent):
        """Create compact status and log panel below modules"""
        # Compact status frame - horizontal layout
        status_frame = tk.Frame(parent, bg=self.colors['bg_primary'], padx=15, pady=5)
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        status_frame.columnconfigure(0, weight=1)  # Statistics area
        status_frame.columnconfigure(1, weight=1)  # Module status area

        # Compact status header
        header_label = tk.Label(status_frame, text="Status & Logs",
                               font=('Segoe UI', 13, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_primary'])
        header_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        # Compact status info
        info_frame = tk.Frame(status_frame, bg=self.colors['bg_secondary'], padx=12, pady=8)
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        info_frame.columnconfigure(1, weight=1)

        # Status labels with Chrome styling
        tk.Label(info_frame, text="Uptime:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_secondary']).grid(row=0, column=0, sticky=tk.W)
        self.uptime_label = tk.Label(info_frame, text="00:00:00", font=('Segoe UI', 10, 'bold'),
                                    fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        self.uptime_label.grid(row=0, column=1, sticky=tk.W, padx=(15, 0))

        tk.Label(info_frame, text="Total Scans:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_secondary']).grid(row=1, column=0, sticky=tk.W)
        self.scans_label = tk.Label(info_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                   fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        self.scans_label.grid(row=1, column=1, sticky=tk.W, padx=(15, 0))

        tk.Label(info_frame, text="Total Executions:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_secondary']).grid(row=2, column=0, sticky=tk.W)
        self.executions_label = tk.Label(info_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                        fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        self.executions_label.grid(row=2, column=1, sticky=tk.W, padx=(15, 0))

        # Log area - left side with responsive dimensions
        log_frame = tk.Frame(status_frame, bg=self.colors['bg_primary'])
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)

        stats_header = tk.Label(log_frame, text="Module Statistics",
                             font=('Segoe UI', 11, 'bold'),
                             fg=self.colors['text_primary'],
                             bg=self.colors['bg_primary'])
        stats_header.grid(row=0, column=0, sticky='w', pady=(0, 5))

        # Statistics display frame
        stats_display_frame = tk.Frame(log_frame, bg=self.colors['bg_tertiary'], relief='flat', bd=0)
        stats_display_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=2)
        stats_display_frame.columnconfigure(0, weight=1)
        stats_display_frame.columnconfigure(1, weight=1)

        # Initialize statistics counters
        self.stats_counters = {
            'help_click': 0,
            'map_trade': 0,
            'zombie_invasion': 0,
            'alliance_donation': 0,
            'daily_tasks': 0,
            'dig': 0
        }

        # Create statistics labels
        self.stats_labels = {}
        row = 0

        # Help Click Counter
        tk.Label(stats_display_frame, text="🆘 Help Click:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['help_click'] = tk.Label(stats_display_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                                  fg=self.colors['accent_green'], bg=self.colors['bg_tertiary'])
        self.stats_labels['help_click'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Map Trade Counter
        tk.Label(stats_display_frame, text="🗺️ Map Trade:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['map_trade'] = tk.Label(stats_display_frame, text="0 | 👍0", font=('Segoe UI', 10, 'bold'),
                                                 fg=self.colors['accent_blue'], bg=self.colors['bg_tertiary'])
        self.stats_labels['map_trade'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Find Truck Manual Trigger
        tk.Label(stats_display_frame, text="Find Truck:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)

        # Button frame for Find and Stop buttons
        truck_button_frame = tk.Frame(stats_display_frame, bg=self.colors['bg_tertiary'])
        truck_button_frame.grid(row=row, column=1, sticky='e', padx=10, pady=5)

        # Manual trigger button
        self.find_truck_button = tk.Button(truck_button_frame, text="Find",
                                          command=self._trigger_find_truck,
                                          font=('Segoe UI', 9, 'bold'),
                                          bg=self.colors['accent_orange'], fg='white',
                                          padx=8, pady=2)
        self.find_truck_button.pack(side=tk.LEFT, padx=(0, 5))

        # Stop button
        self.stop_truck_button = tk.Button(truck_button_frame, text="Stop",
                                          command=self._stop_find_truck,
                                          font=('Segoe UI', 9, 'bold'),
                                          bg=self.colors['accent_red'], fg='white',
                                          padx=8, pady=2, state="disabled")
        self.stop_truck_button.pack(side=tk.LEFT)
        row += 1

        # Power Threshold Setting
        power_frame = tk.Frame(stats_display_frame, bg=self.colors['bg_tertiary'])
        power_frame.grid(row=row, column=0, columnspan=2, sticky='ew', padx=10, pady=5)

        tk.Label(power_frame, text="Max Power:", font=('Segoe UI', 9),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).pack(side=tk.LEFT)

        self.power_threshold_var = tk.StringVar(value="1.8")
        self.power_threshold_entry = tk.Entry(power_frame, textvariable=self.power_threshold_var,
                                             font=('Segoe UI', 9), width=6,
                                             bg=self.colors['bg_primary'], fg=self.colors['text_primary'])
        self.power_threshold_entry.pack(side=tk.LEFT, padx=5)

        tk.Label(power_frame, text="M", font=('Segoe UI', 9),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).pack(side=tk.LEFT)

        # Update button for power threshold
        power_update_btn = tk.Button(power_frame, text="Set",
                                    command=self._update_power_threshold,
                                    font=('Segoe UI', 8),
                                    bg=self.colors['accent_blue'], fg='white',
                                    padx=8, pady=1)
        power_update_btn.pack(side=tk.RIGHT, padx=5)
        row += 1

        # Zombie Invasion Counter
        tk.Label(stats_display_frame, text="🧟 Zombie Invasion:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['zombie_invasion'] = tk.Label(stats_display_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                                       fg=self.colors['accent_orange'], bg=self.colors['bg_tertiary'])
        self.stats_labels['zombie_invasion'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Alliance Donation Counter
        tk.Label(stats_display_frame, text="🤝 Alliance Donation:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['alliance_donation'] = tk.Label(stats_display_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                                         fg=self.colors['accent_purple'], bg=self.colors['bg_tertiary'])
        self.stats_labels['alliance_donation'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Daily Tasks Counter
        tk.Label(stats_display_frame, text="📋 Daily Tasks:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['daily_tasks'] = tk.Label(stats_display_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                                   fg=self.colors['accent_yellow'], bg=self.colors['bg_tertiary'])
        self.stats_labels['daily_tasks'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Dig Counter
        tk.Label(stats_display_frame, text="🏗️ Dig:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.stats_labels['dig'] = tk.Label(stats_display_frame, text="0", font=('Segoe UI', 10, 'bold'),
                                           fg=self.colors['accent_red'], bg=self.colors['bg_tertiary'])
        self.stats_labels['dig'].grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Add separator
        separator = tk.Frame(stats_display_frame, height=2, bg=self.colors['bg_secondary'])
        separator.grid(row=row, column=0, columnspan=2, sticky='ew', padx=10, pady=10)
        row += 1

        # Session info
        tk.Label(stats_display_frame, text="📊 Session Started:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        import time
        start_time = time.strftime('%H:%M:%S')
        self.session_start_label = tk.Label(stats_display_frame, text=start_time, font=('Segoe UI', 10, 'bold'),
                                           fg=self.colors['text_primary'], bg=self.colors['bg_tertiary'])
        self.session_start_label.grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Add another separator
        separator2 = tk.Frame(stats_display_frame, height=2, bg=self.colors['bg_secondary'])
        separator2.grid(row=row, column=0, columnspan=2, sticky='ew', padx=10, pady=10)
        row += 1

        # System monitoring
        tk.Label(stats_display_frame, text="💻 CPU Usage:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.cpu_label = tk.Label(stats_display_frame, text="0%", font=('Segoe UI', 10, 'bold'),
                                 fg=self.colors['accent_blue'], bg=self.colors['bg_tertiary'])
        self.cpu_label.grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # RAM usage
        tk.Label(stats_display_frame, text="🧠 RAM Usage:", font=('Segoe UI', 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_tertiary']).grid(row=row, column=0, sticky='w', padx=10, pady=5)
        self.ram_label = tk.Label(stats_display_frame, text="0 MB", font=('Segoe UI', 10, 'bold'),
                                 fg=self.colors['accent_green'], bg=self.colors['bg_tertiary'])
        self.ram_label.grid(row=row, column=1, sticky='e', padx=10, pady=5)
        row += 1

        # Clear cache button
        self.clear_cache_button = tk.Button(stats_display_frame, text="🗑️ Clear Cache",
                                           font=('Segoe UI', 9, 'bold'),
                                           fg=self.colors['text_primary'],
                                           bg=self.colors['bg_secondary'],
                                           activebackground=self.colors['accent_red'],
                                           activeforeground=self.colors['bg_primary'],
                                           relief='flat', bd=0, padx=15, pady=5,
                                           command=self._clear_cache)
        self.clear_cache_button.grid(row=row, column=0, columnspan=2, pady=10, padx=10, sticky='ew')

        # Module status panel - right side, compact
        status_panel_frame = tk.Frame(status_frame, bg=self.colors['bg_primary'])
        status_panel_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        status_panel_frame.columnconfigure(0, weight=1)
        status_panel_frame.rowconfigure(0, weight=1)

        # Module status panel (always visible)
        self._create_embedded_status_panel(status_panel_frame)

    def _create_embedded_status_panel(self, parent):
        """Create embedded module status panel with dark theme"""
        # Dark themed status panel - always visible with fixed layout
        self.status_panel = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=12, pady=10)
        self.status_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.status_panel.columnconfigure(0, weight=1)
        self.status_panel.rowconfigure(1, weight=1)  # TreeView gets most space
        self.status_panel_visible = True  # Always visible now

        # Status panel header
        header_label = tk.Label(self.status_panel, text="Module Status Monitor",
                               font=('Segoe UI', 12, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_secondary'])
        header_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Status tree with dark theme and fixed layout
        tree_frame = tk.Frame(self.status_panel, bg=self.colors['bg_secondary'])
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Create dark themed treeview with proper size
        columns = ("Status", "Current Activity", "Progress", "Waiting For", "Actions")
        self.status_tree = ttk.Treeview(tree_frame, columns=columns, show="tree headings", height=12)

        # Configure dark theme for treeview
        style = ttk.Style()
        style.configure("Treeview",
                       background=self.colors['bg_tertiary'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_tertiary'],
                       borderwidth=0)
        style.configure("Treeview.Heading",
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1)
        style.map("Treeview",
                 background=[('selected', self.colors['accent_blue'])],
                 foreground=[('selected', self.colors['bg_primary'])])

        # Configure columns
        self.status_tree.heading("#0", text="Module")
        self.status_tree.heading("Status", text="Status")
        self.status_tree.heading("Current Activity", text="Current Activity")
        self.status_tree.heading("Progress", text="Progress")
        self.status_tree.heading("Waiting For", text="Waiting For")
        self.status_tree.heading("Actions", text="Manual Actions")

        # Column widths optimized for responsive layout
        self.status_tree.column("#0", width=100, minwidth=80)
        self.status_tree.column("Status", width=80, minwidth=60)
        self.status_tree.column("Current Activity", width=150, minwidth=100)
        self.status_tree.column("Progress", width=100, minwidth=70)
        self.status_tree.column("Waiting For", width=120, minwidth=80)
        self.status_tree.column("Actions", width=120, minwidth=80)

        # Dark themed scrollbar
        scrollbar = tk.Scrollbar(tree_frame, orient="vertical", command=self.status_tree.yview,
                                bg=self.colors['bg_tertiary'],
                                troughcolor=self.colors['bg_secondary'],
                                activebackground=self.colors['accent_blue'],
                                width=14)
        self.status_tree.configure(yscrollcommand=scrollbar.set)

        # Grid treeview and scrollbar for fixed layout
        self.status_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Dark themed manual action controls frame (toggleable)
        self.action_controls_frame = tk.Frame(self.status_panel, bg=self.colors['bg_tertiary'], padx=12, pady=10)
        # Initially hidden
        self.action_frame_visible = False
        self.action_controls_frame.columnconfigure(1, weight=1)

        # Action frame header
        action_header = tk.Label(self.action_controls_frame, text="Manual Module Actions",
                                font=('Segoe UI', 11, 'bold'),
                                fg=self.colors['text_primary'],
                                bg=self.colors['bg_tertiary'])
        action_header.grid(row=0, column=0, columnspan=6, sticky=tk.W, pady=(0, 8))

        # Instructions
        instruction_label = tk.Label(self.action_controls_frame, text="Select module and action for manual control:",
                                    font=('Segoe UI', 9),
                                    fg=self.colors['text_secondary'],
                                    bg=self.colors['bg_tertiary'])
        instruction_label.grid(row=1, column=0, columnspan=6, sticky=tk.W, pady=(0, 10))

        # Module selection with checkboxes for sticky selection
        module_label = tk.Label(self.action_controls_frame, text="Module:",
                               font=('Segoe UI', 9, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_tertiary'])
        module_label.grid(row=2, column=0, sticky=tk.W, padx=(0, 5))

        # Create checkboxes for each module (sticky selection)
        self.selected_modules = {}
        module_frame = tk.Frame(self.action_controls_frame, bg=self.colors['bg_tertiary'])
        module_frame.grid(row=2, column=1, sticky=tk.W, padx=(0, 10))

        for i, (module_id, name, _) in enumerate(self.module_definitions):
            var = tk.BooleanVar()
            self.selected_modules[module_id] = var

            checkbox = tk.Checkbutton(module_frame, text=name.replace('🏗️ ', '').replace('🎯 ', '').replace('🤝 ', '').replace('🧟 ', '').replace('🗺️ ', '').replace('🆘 ', ''),
                                     variable=var,
                                     font=('Segoe UI', 8),
                                     fg=self.colors['text_primary'],
                                     bg=self.colors['bg_tertiary'],
                                     selectcolor=self.colors['bg_secondary'],
                                     activebackground=self.colors['bg_tertiary'],
                                     activeforeground=self.colors['text_primary'])
            checkbox.grid(row=i//3, column=i%3, sticky=tk.W, padx=(0, 15), pady=2)

        # Action selection
        action_label = tk.Label(self.action_controls_frame, text="Action:",
                               font=('Segoe UI', 9, 'bold'),
                               fg=self.colors['text_primary'],
                               bg=self.colors['bg_tertiary'])
        action_label.grid(row=2, column=2, sticky=tk.W, padx=(0, 5))

        # Action controls row
        controls_frame = tk.Frame(self.action_controls_frame, bg=self.colors['bg_tertiary'])
        controls_frame.grid(row=3, column=0, columnspan=6, sticky=(tk.W, tk.E), pady=(10, 0))

        actions = ["Execute Now", "Add Wait", "Clear Wait", "Reset Cooldown", "Force Stop", "Enable", "Disable"]
        self.action_type_var = tk.StringVar(value="Execute Now")

        # Configure combobox style for dark theme
        style = ttk.Style()
        style.configure("Dark.TCombobox",
                       fieldbackground=self.colors['bg_secondary'],
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'])

        action_combo = ttk.Combobox(controls_frame, textvariable=self.action_type_var,
                                   values=actions, state="readonly", width=12,
                                   style="Dark.TCombobox")
        action_combo.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        # Value input (for actions that need values)
        value_label = tk.Label(controls_frame, text="Value:",
                              font=('Segoe UI', 9, 'bold'),
                              fg=self.colors['text_primary'],
                              bg=self.colors['bg_tertiary'])
        value_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 5))

        self.action_value_var = tk.StringVar(value="")
        value_entry = tk.Entry(controls_frame, textvariable=self.action_value_var, width=10,
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['text_primary'],
                              insertbackground=self.colors['text_primary'],
                              relief='flat', bd=1)
        value_entry.grid(row=0, column=2, sticky=tk.W, padx=(0, 15))

        # Execute button with dark theme
        execute_button = tk.Button(controls_frame, text="Execute Action",
                                  command=self._execute_manual_action,
                                  bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                                  font=("Segoe UI", 9, "bold"), relief="flat",
                                  padx=15, pady=6, cursor="hand2",
                                  bd=0, highlightthickness=0,
                                  activebackground=self.colors['hover'])
        execute_button.grid(row=0, column=3, padx=(15, 0))

    def _toggle_status_panel(self):
        """Toggle the visibility of the manual action panel"""
        # Status panel is always visible now, just toggle action controls
        if hasattr(self, 'action_frame_visible') and self.action_frame_visible:
            # Hide the action controls
            if hasattr(self, 'action_controls_frame'):
                self.action_controls_frame.grid_remove()
            self.action_frame_visible = False
            self.status_button.config(text="📊 Show Actions")
        else:
            # Show the action controls
            if hasattr(self, 'action_controls_frame'):
                self.action_controls_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
            self.action_frame_visible = True
            self.status_button.config(text="📊 Hide Actions")
            # Start status updates if not already running
            self._start_embedded_status_updates()

    def _start_embedded_status_updates(self):
        """Start periodic updates for the embedded status panel"""
        def update_loop():
            while self.status_panel_visible:
                try:
                    self.root.after(0, self._update_status_tree)
                    time.sleep(1)  # Update every second
                except:
                    break

        import threading
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()

    def _setup_layout(self):
        """Setup the overall layout"""
        # Configure styles
        style = ttk.Style()
        style.configure("Accent.TButton", foreground="white")
    
    def _load_module_states(self):
        """Load module states from unified configuration with legacy fallback"""
        for module_id, var in self.module_vars.items():
            # Try unified config first
            module_config = self.unified_config.get_module_config(module_id)
            if module_config:
                enabled = module_config.get('enabled', True)
                self.logger.info(f"[UNIFIED] Loading {module_id}: enabled={enabled}")
            else:
                # Fallback to legacy config
                enabled = self.config.getboolean('MODULES', module_id, fallback=True)
                self.logger.info(f"[LEGACY] Loading {module_id}: enabled={enabled}")

            var.set(enabled)

        # Sync GUI states with centralized scanner
        self._sync_with_centralized_scanner()

        # Update active module counter after loading states
        self._update_active_module_count()

    def _sync_with_centralized_scanner(self):
        """Sync GUI module states with centralized scanner template mappings"""
        try:
            if not hasattr(self.controller, 'centralized_scanner') or not self.controller.centralized_scanner:
                self.logger.debug("Centralized scanner not available for sync")
                return

            scanner = self.controller.centralized_scanner

            # For each module in GUI, check its actual state in centralized scanner
            for module_id, var in self.module_vars.items():
                scanner_enabled = scanner.get_module_enabled_state(module_id)
                gui_enabled = var.get()

                if scanner_enabled != gui_enabled:
                    self.logger.info(f"[SYNC] Syncing {module_id}: GUI={gui_enabled} -> Scanner={scanner_enabled}")
                    var.set(scanner_enabled)

                    # Update config to match scanner state
                    self.config.set('MODULES', module_id, str(scanner_enabled))

                    # Also update the legacy module if it exists
                    module = self.controller.get_module_by_name(module_id)
                    if module:
                        module.enabled = scanner_enabled

        except Exception as e:
            self.logger.error(f"Failed to sync with centralized scanner: {e}")

    def _toggle_module(self, module_id):
        """Toggle a module on/off - this will enable/disable ALL templates for this module"""
        enabled = self.module_vars[module_id].get()

        # Find the module and toggle it
        module = self.controller.get_module_by_name(module_id)
        if module:
            module.enabled = enabled
            self.logger.info(f"[GUI] Module {module_id} {'enabled' if enabled else 'disabled'}")
        else:
            self.logger.warning(f"Module {module_id} not found in legacy modules")

        # Sync with centralized scanner - this will enable/disable ALL templates for this module
        if hasattr(self.controller, 'centralized_scanner') and self.controller.centralized_scanner:
            try:
                self.controller.centralized_scanner.sync_module_state(module_id, enabled)

                # Count how many templates were affected
                template_count = sum(1 for mapping in self.controller.centralized_scanner.template_mappings
                                   if mapping.module == module_id)

                self.logger.info(f"[SYNC] Successfully synced {module_id} with centralized scanner ({template_count} templates {'enabled' if enabled else 'disabled'})")
            except Exception as e:
                self.logger.error(f"[SYNC-ERROR] Failed to sync {module_id} with centralized scanner: {e}")
        else:
            self.logger.warning("Centralized scanner not available for sync")

        # Update unified configuration
        try:
            self.unified_config.update_module_config(module_id, {'enabled': enabled})
            self.logger.info(f"[UNIFIED] Updated {module_id}: enabled={enabled}")
        except Exception as e:
            self.logger.error(f"Failed to update unified config for {module_id}: {str(e)}")

        # Update legacy config for backward compatibility
        self.config.set('MODULES', module_id, str(enabled))

        # Update active module counter
        self._update_active_module_count()

    def _enable_all_modules(self):
        """Enable all modules"""
        for module_id, var in self.module_vars.items():
            var.set(True)
            module = self.controller.get_module_by_name(module_id)
            if module:
                module.enabled = True

            # Sync with centralized scanner
            if hasattr(self.controller, 'centralized_scanner') and self.controller.centralized_scanner:
                self.controller.centralized_scanner.sync_module_state(module_id, True)

            # Update unified configuration
            try:
                self.unified_config.update_module_config(module_id, {'enabled': True})
                self.logger.info(f"[UNIFIED] Enabled {module_id}")
            except Exception as e:
                self.logger.error(f"Failed to update unified config for {module_id}: {str(e)}")

            # Update legacy config
            self.config.set('MODULES', module_id, 'True')

        # Update active module counter
        self._update_active_module_count()

    def _disable_all_modules(self):
        """Disable all modules"""
        for module_id, var in self.module_vars.items():
            var.set(False)
            module = self.controller.get_module_by_name(module_id)
            if module:
                module.enabled = False

            # Sync with centralized scanner
            if hasattr(self.controller, 'centralized_scanner') and self.controller.centralized_scanner:
                self.controller.centralized_scanner.sync_module_state(module_id, False)

            # Update unified configuration
            try:
                self.unified_config.update_module_config(module_id, {'enabled': False})
                self.logger.info(f"[UNIFIED] Disabled {module_id}")
            except Exception as e:
                self.logger.error(f"Failed to update unified config for {module_id}: {str(e)}")

            # Update legacy config
            self.config.set('MODULES', module_id, 'False')

        # Update active module counter
        self._update_active_module_count()

    def _save_config(self):
        """Save current configuration to unified config and legacy files"""
        try:
            # Save unified configuration
            self.unified_config.save_config()

            # Save controller configuration (includes legacy files)
            self.controller.save_config()

            self.logger.info("Unified and legacy configurations saved successfully")
            messagebox.showinfo("Success", "Unified configuration saved successfully!\n\nBoth unified and legacy configurations have been updated.")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {str(e)}")
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def _trigger_daily_tasks(self):
        """Trigger manual execution of daily tasks"""
        try:
            # Find the daily_tasks module
            daily_module = self.controller.get_module_by_name("daily_tasks")
            if not daily_module:
                messagebox.showerror("Error", "Daily tasks module not found!")
                return

            # Check if daily tasks are already running
            if hasattr(daily_module, 'is_daily_tasks_running') and daily_module.is_daily_tasks_running():
                messagebox.showwarning("Warning", "Daily tasks are already running!")
                return

            # Confirm with user
            result = messagebox.askyesno("Confirm Daily Tasks",
                                       "This will:\n"
                                       "• Pause all other modules\n"
                                       "• Execute daily tasks sequence\n"
                                       "• Resume other modules when complete\n\n"
                                       "Continue?")
            if not result:
                return

            # Trigger daily tasks in a separate thread to avoid blocking GUI
            def run_daily_tasks():
                try:
                    self.logger.info("[MANUAL] Manual trigger: Starting daily tasks")
                    success = daily_module.manual_trigger_daily_tasks()

                    # Show result to user
                    if success:
                        messagebox.showinfo("Success", "Daily tasks completed successfully!")
                    else:
                        messagebox.showerror("Error", "Daily tasks failed to complete!")

                except Exception as e:
                    self.logger.error(f"Error in daily tasks: {str(e)}")
                    messagebox.showerror("Error", f"Daily tasks error: {str(e)}")

            # Run in background thread
            threading.Thread(target=run_daily_tasks, daemon=True).start()

            # Show immediate feedback
            messagebox.showinfo("Daily Tasks Started", "Daily tasks sequence started!\nCheck logs for progress.")

        except Exception as e:
            self.logger.error(f"Error triggering daily tasks: {str(e)}")
            messagebox.showerror("Error", f"Failed to trigger daily tasks: {str(e)}")

    def _trigger_daily_tasks_no_popup(self):
        """Trigger daily tasks without confirmation popup"""
        try:
            daily_module = self.controller.get_module_by_name("daily_tasks")
            if not daily_module:
                return

            if hasattr(daily_module, 'is_daily_tasks_running') and daily_module.is_daily_tasks_running():
                return

            # Run in background thread
            def run_daily_tasks():
                try:
                    self.logger.info("[QUICK] Quick trigger: Starting daily tasks")
                    daily_module.manual_trigger_daily_tasks()
                except Exception as e:
                    self.logger.error(f"Error in daily tasks: {str(e)}")

            threading.Thread(target=run_daily_tasks, daemon=True).start()

        except Exception as e:
            self.logger.error(f"Error triggering daily tasks: {str(e)}")

    def _trigger_alliance_donation(self):
        """Trigger manual execution of alliance donations"""
        try:
            # Find the alliance_donation module
            alliance_module = self.controller.get_module_by_name("alliance_donation")
            if not alliance_module:
                messagebox.showerror("Error", "Alliance donation module not found!")
                return

            # Check if alliance donations are already running
            if hasattr(alliance_module, 'is_alliance_donation_running') and alliance_module.is_alliance_donation_running():
                messagebox.showwarning("Warning", "Alliance donations are already running!")
                return

            # Confirm with user
            result = messagebox.askyesno("Confirm Alliance Donations",
                                       "This will:\n"
                                       "• Pause all other modules\n"
                                       "• Execute alliance donation sequence\n"
                                       "• Resume other modules when complete\n\n"
                                       "Continue?")
            if not result:
                return

            # Trigger alliance donations in a separate thread to avoid blocking GUI
            def run_alliance_donations():
                try:
                    self.logger.info("🤝 Manual trigger: Starting alliance donations")
                    success = alliance_module.manual_trigger_alliance_donation()

                    # Show result to user
                    if success:
                        messagebox.showinfo("Success", "Alliance donations completed successfully!")
                    else:
                        messagebox.showerror("Error", "Alliance donations failed to complete!")

                except Exception as e:
                    self.logger.error(f"Error in alliance donations: {str(e)}")
                    messagebox.showerror("Error", f"Alliance donations error: {str(e)}")

            # Run in background thread
            threading.Thread(target=run_alliance_donations, daemon=True).start()

            # Show immediate feedback
            messagebox.showinfo("Alliance Donations Started", "Alliance donation sequence started!\nCheck logs for progress.")

        except Exception as e:
            self.logger.error(f"Error triggering alliance donations: {str(e)}")
            messagebox.showerror("Error", f"Failed to trigger alliance donations: {str(e)}")

    def _trigger_alliance_donation_no_popup(self):
        """Trigger alliance donations without confirmation popup"""
        try:
            alliance_module = self.controller.get_module_by_name("alliance_donation")
            if not alliance_module:
                return

            if hasattr(alliance_module, 'is_alliance_donation_running') and alliance_module.is_alliance_donation_running():
                return

            # Run in background thread
            def run_alliance_donations():
                try:
                    self.logger.info("🤝 Quick trigger: Starting alliance donations")
                    alliance_module.manual_trigger_alliance_donation()
                except Exception as e:
                    self.logger.error(f"Error in alliance donations: {str(e)}")

            threading.Thread(target=run_alliance_donations, daemon=True).start()

        except Exception as e:
            self.logger.error(f"Error triggering alliance donations: {str(e)}")

    def _trigger_map_trade_no_popup(self):
        """Trigger map trade without confirmation popup"""
        try:
            map_trade_module = self.controller.get_module_by_name("map_trade")
            if not map_trade_module:
                self.logger.error("Map trade module not found!")
                return

            # Check if map trade is already running
            if hasattr(map_trade_module, 'is_executing') and map_trade_module.is_executing:
                self.logger.warning("Map trade is already running!")
                return

            # Run in background thread
            def run_map_trade():
                try:
                    self.logger.info("[QUICK] Quick trigger: Starting map trade")
                    map_trade_module.manual_trigger_map_trade()
                except Exception as e:
                    self.logger.error(f"Error in map trade: {str(e)}")

            threading.Thread(target=run_map_trade, daemon=True).start()

        except Exception as e:
            self.logger.error(f"Error triggering map trade: {str(e)}")

    def _toggle_gem_step(self):
        """Toggle the 11_gem step in alliance donation on/off"""
        try:
            # Get the alliance donation module config from unified system
            config = self.unified_config.get_module_config("alliance_donation")
            if not config:
                # Fallback to legacy config
                from module_config_manager import ModuleConfigManager
                config_manager = ModuleConfigManager()
                config = config_manager.get_module_config("alliance_donation")

            if not config or 'click_coordinates' not in config:
                messagebox.showerror("Error", "Alliance donation configuration not found!")
                return

            # Find the 11_gem step
            gem_step = None
            for coord in config['click_coordinates']:
                if coord.get('name') == '11_gem':
                    gem_step = coord
                    break

            if not gem_step:
                messagebox.showerror("Error", "11_gem step not found in alliance donation configuration!")
                return

            # Toggle the enabled status
            current_enabled = gem_step.get('enabled', True)
            new_enabled = not current_enabled
            gem_step['enabled'] = new_enabled

            # Save the unified configuration
            self.unified_config.update_module_config("alliance_donation", config)
            self.unified_config.save_config()

            # Also save legacy config if it was used
            if 'config_manager' in locals():
                config_manager.update_module_config("alliance_donation", config)
                config_manager.save_config()

            # Update button text
            self._update_gem_button_status()

            # Show feedback
            status = "ENABLED" if new_enabled else "DISABLED"
            messagebox.showinfo("Gem Step Toggled", f"11_gem step is now {status}")
            self.logger.info(f"[GEM] Gem step toggled: {status}")

        except Exception as e:
            self.logger.error(f"Error toggling gem step: {str(e)}")
            messagebox.showerror("Error", f"Failed to toggle gem step: {str(e)}")

    def _update_gem_button_status(self):
        """Update the gem button text based on current 11_gem step status"""
        try:
            if "alliance_donation" not in self.gem_buttons:
                return

            # Get the alliance donation module config from unified system
            config = self.unified_config.get_module_config("alliance_donation")
            if not config:
                # Fallback to legacy config
                from module_config_manager import ModuleConfigManager
                config_manager = ModuleConfigManager()
                config = config_manager.get_module_config("alliance_donation")

            if not config or 'click_coordinates' not in config:
                return

            # Find the 11_gem step
            gem_enabled = True  # Default to enabled if not found
            for coord in config['click_coordinates']:
                if coord.get('name') == '11_gem':
                    gem_enabled = coord.get('enabled', True)
                    break

            # Update button text and color with Chrome styling
            button = self.gem_buttons["alliance_donation"]
            if gem_enabled:
                button.config(text="💎", bg=self.colors['accent_green'], fg=self.colors['bg_primary'])
            else:
                button.config(text="💎", bg=self.colors['accent_red'], fg=self.colors['bg_primary'])

        except Exception as e:
            self.logger.error(f"Error updating gem button status: {str(e)}")

    def _start_automation(self):
        """Start the automation system"""
        try:
            # First, focus and resize the game window, then handle full screen
            if self._focus_and_resize_game_window():
                self._log_message("Game window focused and resized to 1200x1200")

                # Check if game is already full screen and handle accordingly
                if self._set_game_fullscreen():
                    self._log_message("Game window set to full screen mode")
                else:
                    self._log_message("Game window full screen status handled")

                # Move GUI to second screen (if available)
                self._move_to_second_screen()

            else:
                self._log_message("Warning: Could not find or resize game window")

            self.controller.start()
            self.is_paused = False
            self.start_button.config(state="disabled")
            self.pause_button.config(state="normal", text="⏸ Pause")
            self.stop_button.config(state="normal")
            self.status_label.config(text="Status: Running", foreground="green")
            self._log_message("Automation started")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start automation: {str(e)}")

    def _move_to_second_screen(self):
        """Move GUI to second screen if available"""
        try:
            import tkinter as tk

            # Get all available screens
            screens = []
            try:
                # Try to get screen info using tkinter
                root_x = self.root.winfo_rootx()
                root_y = self.root.winfo_rooty()
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                # Check if we have multiple monitors by trying to move beyond primary screen
                # This is a simple approach - move to right side of screen
                second_screen_x = screen_width + 100  # Move to second screen
                second_screen_y = 100

                # Try to position on second screen
                self.root.geometry(f"+{second_screen_x}+{second_screen_y}")
                self._log_message("GUI moved to second screen")

            except Exception as e:
                # Fallback: move to top-right corner of primary screen
                primary_x = screen_width - 600  # GUI width is about 500px
                primary_y = 50
                self.root.geometry(f"+{primary_x}+{primary_y}")
                self._log_message("GUI moved to top-right corner (second screen not available)")

        except Exception as e:
            self._log_message(f"Could not move GUI to second screen: {str(e)}")

    def _set_game_fullscreen(self):
        """Resize game window to full screen dimensions only if needed"""
        try:
            import pygetwindow as gw
            import time
            import tkinter as tk

            # Get actual screen dimensions
            temp_root = tk.Tk()
            screen_width = temp_root.winfo_screenwidth()
            screen_height = temp_root.winfo_screenheight()
            temp_root.destroy()

            self._log_message(f"Screen dimensions: {screen_width}x{screen_height}")

            # Search for Last War window
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game",
                "Last War - Survival Game"
            ]

            game_window = None
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        break
                except:
                    continue

            if not game_window:
                self._log_message("Could not find game window for full screen check")
                return False

            # Check current window properties
            current_width = game_window.width
            current_height = game_window.height
            current_x = game_window.left
            current_y = game_window.top

            self._log_message(f"Current fullscreen check: {current_width}x{current_height} at ({current_x}, {current_y})")

            # Check if window is already fullscreen (with tolerance)
            size_tolerance = 20  # Allow some difference for borders/taskbar
            position_tolerance = 10

            is_fullscreen_size = (abs(current_width - screen_width) <= size_tolerance and
                                abs(current_height - screen_height) <= size_tolerance)
            is_fullscreen_position = (abs(current_x) <= position_tolerance and
                                    abs(current_y) <= position_tolerance)

            if is_fullscreen_size and is_fullscreen_position:
                self._log_message("Window is already fullscreen - no changes needed")
                return True

            # Focus the game window first
            game_window.activate()
            time.sleep(0.5)

            # Make window borderless and full screen (covers taskbar)
            self._log_message(f"Setting game window to borderless full screen: {screen_width}x{screen_height}")

            # First maximize the window to get rid of borders
            game_window.maximize()
            time.sleep(0.5)

            # Then resize to exact screen dimensions to cover taskbar
            game_window.resizeTo(screen_width, screen_height)
            game_window.moveTo(0, 0)

            time.sleep(1.0)  # Wait for resize to complete

            self._log_message(f"Game window set to borderless full screen: {screen_width}x{screen_height}")
            return True

        except Exception as e:
            self._log_message(f"Error checking/setting full screen: {str(e)}")
            return False

    def _focus_and_resize_game_window(self):
        """Focus and resize the Last War game window only if needed"""
        try:
            import pygetwindow as gw
            import time

            # Search for Last War window with various possible titles
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game",
                "Last War - Survival Game",
                "LastWar",
                "last war"
            ]

            game_window = None

            # Try to find the window by exact title match first
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        self._log_message(f"Found game window: '{title}'")
                        break
                except:
                    continue

            # If exact match failed, try partial match
            if not game_window:
                all_windows = gw.getAllWindows()
                for window in all_windows:
                    window_title = window.title.lower()
                    if any(title.lower() in window_title for title in ["last war", "survival game"]):
                        game_window = window
                        self._log_message(f"Found game window by partial match: '{window.title}'")
                        break

            if not game_window:
                self._log_message("Could not find Last War game window")
                return False

            # Check current window properties
            current_width = game_window.width
            current_height = game_window.height
            current_x = game_window.left
            current_y = game_window.top

            self._log_message(f"Current window: {current_width}x{current_height} at ({current_x}, {current_y})")

            # Focus the window
            try:
                game_window.activate()
                time.sleep(0.5)  # Give time for window to focus
                self._log_message("Game window focused")
            except Exception as e:
                self._log_message(f"Could not focus window: {str(e)}")

            # Set target dimensions and position
            target_width = 1260
            target_height = 740
            target_x = 0
            target_y = 0

            # Tolerance for size/position checking (allow small differences)
            size_tolerance = 10
            position_tolerance = 20

            # Check if resize is needed
            size_correct = (abs(current_width - target_width) <= size_tolerance and
                          abs(current_height - target_height) <= size_tolerance)

            # Check if repositioning is needed
            position_correct = (abs(current_x - target_x) <= position_tolerance and
                              abs(current_y - target_y) <= position_tolerance)

            # Only resize if needed
            if not size_correct:
                try:
                    game_window.resizeTo(target_width, target_height)
                    time.sleep(0.3)  # Give time for resize
                    self._log_message(f"Window resized to {target_width}x{target_height}")
                except Exception as e:
                    self._log_message(f"Could not resize window: {str(e)}")
            else:
                self._log_message("Window size is already correct - no resize needed")

            # Only reposition if needed
            if not position_correct:
                try:
                    game_window.moveTo(target_x, target_y)
                    time.sleep(0.2)  # Give time for move
                    self._log_message(f"Window moved to ({target_x}, {target_y})")
                except Exception as e:
                    self._log_message(f"Could not move window: {str(e)}")
            else:
                self._log_message("Window position is already correct - no move needed")

            # Position automation helper at top-right
            self._position_automation_helper(target_width)

            return True

        except ImportError:
            self._log_message("❌ pygetwindow not available - cannot manage game window")
            return False
        except Exception as e:
            self._log_message(f"❌ Error managing game window: {str(e)}")
            return False

    def _position_automation_helper(self, game_window_width):
        """Position the automation helper window at top-right corner"""
        try:
            # Get screen width
            screen_width = self.root.winfo_screenwidth()

            # Force window to update and get actual current size
            self.root.update_idletasks()
            current_width = self.root.winfo_width()
            current_height = self.root.winfo_height()

            # Log current size for debugging
            self._log_message(f"[SIZE] Current automation helper size: {current_width}x{current_height}")

            # Position at top-right corner with small gap from edge
            x_position = screen_width - current_width - 10  # 10px from right edge
            y_position = 0  # Top of screen

            # Move to top-right corner WITHOUT changing size
            self.root.geometry(f"+{x_position}+{y_position}")

            self._log_message(f"[OK] Automation helper positioned at top-right ({x_position}, {y_position})")
            self._log_message(f"[OK] Keeping current size: {current_width}x{current_height}")

        except Exception as e:
            self._log_message(f"⚠️ Could not position automation helper: {str(e)}")

    def _test_window_management(self):
        """Smart game focus - only resize/move if needed"""
        self._log_message("Setting up smart game focus...")

        # Log current automation helper size first
        self.root.update_idletasks()
        current_width = self.root.winfo_width()
        current_height = self.root.winfo_height()
        self._log_message(f"Current automation helper size: {current_width}x{current_height}")

        changes_made = []

        # First, focus and resize the game window only if needed
        if self._focus_and_resize_game_window():
            # Check if game is already full screen and handle accordingly
            if self._set_game_fullscreen():
                changes_made.append("fullscreen adjusted")

            # Move GUI to second screen (if available)
            self._move_to_second_screen()
            changes_made.append("helper positioned")

            if changes_made:
                changes_text = ", ".join(changes_made)
                self._log_message(f"Game focus completed - changes made: {changes_text}")
                messagebox.showinfo("Game Focus Complete", f"Game window managed successfully!\nChanges: {changes_text}")
            else:
                self._log_message("Game focus completed - no changes needed (already optimal)")
                messagebox.showinfo("Game Focus Complete", "Game window is already optimally configured!\nNo changes were needed.")
        else:
            messagebox.showwarning("Warning", "Could not find or manage game window. Check the log for details.")

    def _pause_automation(self):
        """Pause or resume the automation system"""
        try:
            if self.is_paused:
                # Resume
                self.controller.resume()
                self.is_paused = False
                self.pause_button.config(text="⏸ Pause")
                self.status_label.config(text="Status: Running", foreground="green")
                self._log_message("Automation resumed")
            else:
                # Pause
                self.controller.pause()
                self.is_paused = True
                self.pause_button.config(text="▶ Resume")
                self.status_label.config(text="Status: Paused", foreground="orange")
                self._log_message("Automation paused")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to pause/resume automation: {str(e)}")

    def _stop_automation(self):
        """Stop the automation system"""
        try:
            self.controller.stop()
            self.is_paused = False
            self.start_button.config(state="normal")
            self.pause_button.config(state="disabled", text="⏸ Pause")
            self.stop_button.config(state="disabled")
            self.status_label.config(text="Status: Stopped", foreground="red")
            self._log_message("Automation stopped")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop automation: {str(e)}")

    def _emergency_stop(self):
        """Emergency stop the system"""
        try:
            self.controller.emergency_stop()
            self.is_paused = False
            self.start_button.config(state="normal")
            self.pause_button.config(state="disabled", text="⏸ Pause")
            self.stop_button.config(state="disabled")
            self.status_label.config(text="Status: Emergency Stopped", foreground="red")
            self._log_message("EMERGENCY STOP ACTIVATED!")
            messagebox.showwarning("Emergency Stop", "System has been emergency stopped!")
        except Exception as e:
            messagebox.showerror("Error", f"Emergency stop failed: {str(e)}")

    def _toggle_mouse_override(self):
        """Toggle mouse movement detection on/off"""
        try:
            if self.controller.mouse_detection_enabled:
                # Disable mouse override
                self.controller.disable_mouse_detection()
                self.mouse_override_button.config(
                    text="🖱️ Mouse Override: OFF",
                    bg=self.colors['accent_red']
                )
                self._log_message("Mouse override disabled")
            else:
                # Enable mouse override
                self.controller.enable_mouse_detection()
                self.mouse_override_button.config(
                    text="🖱️ Mouse Override: ON",
                    bg=self.colors['accent_green']
                )
                self._log_message("Mouse override enabled")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to toggle mouse override: {str(e)}")

    def _update_mouse_override_button(self):
        """Update mouse override button state based on controller setting"""
        try:
            if hasattr(self.controller, 'mouse_detection_enabled'):
                if self.controller.mouse_detection_enabled:
                    self.mouse_override_button.config(
                        text="🖱️ Mouse Override: ON",
                        bg=self.colors['accent_green']
                    )
                else:
                    self.mouse_override_button.config(
                        text="🖱️ Mouse Override: OFF",
                        bg=self.colors['accent_red']
                    )
        except Exception as e:
            self.logger.error(f"Error updating mouse override button: {str(e)}")

    def _trigger_find_truck(self):
        """Manually trigger the find truck module"""
        try:
            find_truck_module = self.controller.get_module_by_name("find_truck")
            if find_truck_module:
                self.find_truck_button.config(text="Searching...", state="disabled")
                self.stop_truck_button.config(state="normal")
                self._log_message("Find Truck: Starting search...")
                self._log_message("Find Truck: Move mouse to screen edge to pause, or click Stop to cancel")

                # Execute in separate thread to avoid blocking GUI
                import threading
                def run_find_truck():
                    try:
                        success = find_truck_module.execute()
                        if success:
                            self._log_message("Find Truck: Suitable truck found!")
                        else:
                            self._log_message("Find Truck: Search stopped")
                    except Exception as e:
                        self._log_message(f"Find Truck error: {str(e)}")
                    finally:
                        # Re-enable buttons
                        self.root.after(0, lambda: (
                            self.find_truck_button.config(text="Find", state="normal"),
                            self.stop_truck_button.config(state="disabled")
                        ))

                thread = threading.Thread(target=run_find_truck, daemon=True)
                thread.start()
            else:
                messagebox.showerror("Error", "Find Truck module not found")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to trigger find truck: {str(e)}")
            self.find_truck_button.config(text="Find", state="normal")
            self.stop_truck_button.config(state="disabled")

    def _stop_find_truck(self):
        """Stop the find truck search"""
        try:
            find_truck_module = self.controller.get_module_by_name("find_truck")
            if find_truck_module:
                find_truck_module.stop_search()
                self._log_message("Find Truck: Stop requested")
            else:
                self._log_message("Find Truck: Module not found")
        except Exception as e:
            self._log_message(f"Find Truck stop error: {str(e)}")

    def _update_power_threshold(self):
        """Update the power threshold for find truck module"""
        try:
            threshold_text = self.power_threshold_var.get().strip()
            threshold_value = float(threshold_text)

            find_truck_module = self.controller.get_module_by_name("find_truck")
            if find_truck_module:
                find_truck_module.set_power_threshold(threshold_value)
                self._log_message(f"Power threshold updated to {threshold_value}M")
            else:
                messagebox.showerror("Error", "Find Truck module not found")

        except ValueError:
            messagebox.showerror("Error", "Invalid power threshold. Please enter a number (e.g., 1.8)")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update power threshold: {str(e)}")

    def _open_config(self):
        """Open the main configuration editor"""
        self._open_configuration()

    def _open_diagnostic(self):
        """Open diagnostic window"""
        try:
            import subprocess
            subprocess.Popen(["python", "main_module_diagnostic.py"])
            self.logger.info("[DIAGNOSTIC] Diagnostic window opened")
        except Exception as e:
            self.logger.error(f"Failed to open diagnostic window: {str(e)}")
            messagebox.showerror("Error", f"Failed to open diagnostic window: {str(e)}")

    def _open_centralized_scanner(self):
        """Open centralized template scanner configuration"""
        try:
            self.centralized_scanner_gui.open_configuration_window()
            self.logger.info("[SCANNER] Centralized scanner configuration opened")
            # Update scanner mode indicator
            self._update_scanner_mode_indicator()
        except Exception as e:
            self.logger.error(f"Failed to open centralized scanner: {str(e)}")
            messagebox.showerror("Error", f"Failed to open centralized scanner: {str(e)}")

    def _update_scanner_mode_indicator(self):
        """Update the scanner mode indicator"""
        try:
            scanner_mode = "Centralized" if self.controller.use_centralized_scanner else "Legacy"
            self.scanner_mode_label.config(text=f"🎯 Scanner: {scanner_mode}")
        except Exception as e:
            self.logger.error(f"Failed to update scanner mode indicator: {str(e)}")

    def _open_module_config(self, module_id):
        """Open configuration for a specific module"""
        try:
            import subprocess
            import sys
            import os

            # Check if config editor exists
            config_editor_path = "config_editor_gui.py"
            if not os.path.exists(config_editor_path):
                messagebox.showerror("Error",
                                   f"Configuration editor not found at {config_editor_path}")
                return

            # Launch config editor with specific module
            subprocess.Popen([sys.executable, config_editor_path, "--module", module_id])

        except Exception as e:
            self.logger.error(f"Error opening module config for {module_id}: {str(e)}")
            messagebox.showerror("Error", f"Failed to open module configuration: {str(e)}")

    def _open_module_config_direct(self, module_id):
        """Open configuration directly to specific module without popup"""
        try:
            # Special handling for dig module
            if module_id == "dig":
                self._open_dig_config_dialog()
                return

            import subprocess
            import sys
            import os

            config_editor_path = "config_editor_gui.py"
            if not os.path.exists(config_editor_path):
                self.logger.error(f"Config editor not found: {config_editor_path}")
                return

            # Launch config editor directly to module
            subprocess.Popen([sys.executable, config_editor_path, module_id])

        except Exception as e:
            self.logger.error(f"Error opening config for {module_id}: {str(e)}")

    def _open_dig_config_dialog(self):
        """Open special configuration dialog for dig module"""
        try:
            # Create dialog window
            dialog = tk.Toplevel(self.root)
            dialog.title("🏗️ Dig Module Configuration")
            dialog.geometry("500x400")
            dialog.configure(bg=self.colors['bg_primary'])
            dialog.transient(self.root)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
            y = (dialog.winfo_screenheight() // 2) - (400 // 2)
            dialog.geometry(f"500x400+{x}+{y}")

            # Main frame
            main_frame = tk.Frame(dialog, bg=self.colors['bg_primary'], padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title
            title_label = tk.Label(main_frame, text="Dig Module Settings",
                                 font=('Segoe UI', 16, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
            title_label.pack(pady=(0, 20))

            # Get current dig module
            dig_module = None
            if self.controller and hasattr(self.controller, 'modules'):
                for module in self.controller.modules:
                    if module.name == "dig":
                        dig_module = module
                        break

            # Rapid Click Speed Setting
            speed_frame = tk.LabelFrame(main_frame, text="Rapid Click Speed",
                                      font=('Segoe UI', 12, 'bold'),
                                      fg=self.colors['text_primary'], bg=self.colors['bg_secondary'],
                                      padx=15, pady=15)
            speed_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(speed_frame, text="Click interval (seconds between clicks):",
                    font=('Segoe UI', 10), fg=self.colors['text_primary'],
                    bg=self.colors['bg_secondary']).pack(anchor=tk.W)

            speed_var = tk.DoubleVar(value=dig_module.rapid_click_speed if dig_module else 0.1)
            speed_frame_inner = tk.Frame(speed_frame, bg=self.colors['bg_secondary'])
            speed_frame_inner.pack(fill=tk.X, pady=(5, 0))

            speed_spinbox = tk.Spinbox(speed_frame_inner, from_=0.001, to=1.0, increment=0.01,
                                     textvariable=speed_var, width=10, format="%.3f")
            speed_spinbox.pack(side=tk.LEFT)

            tk.Label(speed_frame_inner, text="seconds (0.001 = 1000 clicks/sec, 0.1 = 10 clicks/sec)",
                    font=('Segoe UI', 9), fg=self.colors['text_secondary'],
                    bg=self.colors['bg_secondary']).pack(side=tk.LEFT, padx=(10, 0))

            # Rapid Click Duration Setting
            duration_frame = tk.LabelFrame(main_frame, text="Duration Settings",
                                         font=('Segoe UI', 12, 'bold'),
                                         fg=self.colors['text_primary'], bg=self.colors['bg_secondary'],
                                         padx=15, pady=15)
            duration_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(duration_frame, text="Total clicking duration:",
                    font=('Segoe UI', 10), fg=self.colors['text_primary'],
                    bg=self.colors['bg_secondary']).pack(anchor=tk.W)

            duration_var = tk.DoubleVar(value=dig_module.rapid_click_duration if dig_module else 20.0)
            duration_frame_inner = tk.Frame(duration_frame, bg=self.colors['bg_secondary'])
            duration_frame_inner.pack(fill=tk.X, pady=(5, 0))

            duration_spinbox = tk.Spinbox(duration_frame_inner, from_=1.0, to=60.0, increment=1.0,
                                        textvariable=duration_var, width=10, format="%.1f")
            duration_spinbox.pack(side=tk.LEFT)

            tk.Label(duration_frame_inner, text="seconds (how long to keep clicking)",
                    font=('Segoe UI', 9), fg=self.colors['text_secondary'],
                    bg=self.colors['bg_secondary']).pack(side=tk.LEFT, padx=(10, 0))

            # Timer Threshold Setting
            tk.Label(duration_frame, text="Timer threshold (start clicking when timer ≤):",
                    font=('Segoe UI', 10), fg=self.colors['text_primary'],
                    bg=self.colors['bg_secondary']).pack(anchor=tk.W, pady=(10, 0))

            threshold_var = tk.IntVar(value=dig_module.timer_threshold if dig_module else 10)
            threshold_frame_inner = tk.Frame(duration_frame, bg=self.colors['bg_secondary'])
            threshold_frame_inner.pack(fill=tk.X, pady=(5, 0))

            threshold_spinbox = tk.Spinbox(threshold_frame_inner, from_=1, to=30, increment=1,
                                         textvariable=threshold_var, width=10)
            threshold_spinbox.pack(side=tk.LEFT)

            tk.Label(threshold_frame_inner, text="seconds (when to start rapid clicking)",
                    font=('Segoe UI', 9), fg=self.colors['text_secondary'],
                    bg=self.colors['bg_secondary']).pack(side=tk.LEFT, padx=(10, 0))

            # Thank You Message Setting
            message_frame = tk.LabelFrame(main_frame, text="💬 Thank You Message",
                                        font=('Segoe UI', 12, 'bold'),
                                        fg=self.colors['text_primary'], bg=self.colors['bg_secondary'],
                                        padx=15, pady=15)
            message_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(message_frame, text="Message to send in alliance chat after dig completion:",
                    font=('Segoe UI', 10), fg=self.colors['text_primary'],
                    bg=self.colors['bg_secondary']).pack(anchor=tk.W)

            message_var = tk.StringVar(value=dig_module.thank_you_message if dig_module else "Thank you for the dig!")
            message_entry = tk.Entry(message_frame, textvariable=message_var, width=50,
                                   font=('Segoe UI', 10))
            message_entry.pack(fill=tk.X, pady=(5, 0))

            # Status Display
            status_frame = tk.LabelFrame(main_frame, text="📊 Current Status",
                                       font=('Segoe UI', 12, 'bold'),
                                       fg=self.colors['text_primary'], bg=self.colors['bg_secondary'],
                                       padx=15, pady=15)
            status_frame.pack(fill=tk.X, pady=(0, 15))

            status_text = "Ready" if not dig_module or not dig_module.dig_in_progress else dig_module.get_dig_status()
            status_label = tk.Label(status_frame, text=f"Status: {status_text}",
                                  font=('Segoe UI', 10), fg=self.colors['text_primary'],
                                  bg=self.colors['bg_secondary'])
            status_label.pack(anchor=tk.W)

            # Buttons
            button_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
            button_frame.pack(fill=tk.X, pady=(10, 0))

            def save_and_close():
                try:
                    if dig_module:
                        dig_module.set_rapid_click_speed(speed_var.get())
                        dig_module.set_rapid_click_duration(duration_var.get())
                        dig_module.set_timer_threshold(threshold_var.get())
                        dig_module.set_thank_you_message(message_var.get())

                        # Update the unified configuration
                        dig_config_updates = {
                            "rapid_click_speed": speed_var.get(),
                            "rapid_click_duration": duration_var.get(),
                            "timer_threshold": threshold_var.get(),
                            "thank_you_message": message_var.get()
                        }

                        self.unified_config.update_module_config("dig", dig_config_updates)
                        self.unified_config.save_config()

                        # Also update legacy config for backward compatibility
                        from module_config_manager import ModuleConfigManager
                        config_manager = ModuleConfigManager()
                        config = config_manager.get_module_config("dig")
                        if config:
                            config.update(dig_config_updates)
                            config_manager.update_module_config("dig", config)

                        expected_clicks = int(duration_var.get() / speed_var.get())
                        self.logger.info(f"Dig module settings updated:")
                        self.logger.info(f"  Speed: {speed_var.get()}s, Duration: {duration_var.get()}s, Threshold: {threshold_var.get()}s")
                        self.logger.info(f"  Expected clicks: {expected_clicks}, Message: '{message_var.get()}'")

                    dialog.destroy()
                except Exception as e:
                    self.logger.error(f"Error saving dig config: {str(e)}")
                    messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

            save_btn = tk.Button(button_frame, text="💾 Save & Close", command=save_and_close,
                               bg=self.colors['accent_green'], fg=self.colors['bg_primary'],
                               font=('Segoe UI', 10, 'bold'), padx=20, pady=5)
            save_btn.pack(side=tk.RIGHT, padx=(10, 0))

            cancel_btn = tk.Button(button_frame, text="❌ Cancel", command=dialog.destroy,
                                 bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                                 font=('Segoe UI', 10), padx=20, pady=5)
            cancel_btn.pack(side=tk.RIGHT)

        except Exception as e:
            self.logger.error(f"Error opening dig config dialog: {str(e)}")
            messagebox.showerror("Error", f"Failed to open dig configuration: {str(e)}")

    def _open_calibration_tool(self, module_id):
        """Open calibration tool for easy click recording"""
        try:
            import subprocess
            import sys
            import os

            # Check if calibration tool exists, create if not
            calib_tool_path = "calibration_tool.py"
            if not os.path.exists(calib_tool_path):
                self._create_calibration_tool()

            # Launch calibration tool for specific module
            subprocess.Popen([sys.executable, calib_tool_path, module_id])

        except Exception as e:
            self.logger.error(f"Error opening calibration tool for {module_id}: {str(e)}")

    def _create_calibration_tool(self):
        """Create the calibration tool if it doesn't exist"""
        # This will be implemented next
        pass

    def _open_configuration(self):
        """Open the unified configuration helper"""
        try:
            import subprocess
            import sys
            import os

            # Check if Config Helper exists
            config_helper_path = "config_helper.py"
            if not os.path.exists(config_helper_path):
                messagebox.showerror("Error",
                                   f"Config Helper not found: {config_helper_path}\n\n"
                                   f"Make sure config_helper.py exists in the same directory.")
                return

            # Launch the Config Helper as a separate process
            subprocess.Popen([sys.executable, config_helper_path],
                           cwd=os.getcwd())

            self._log_message("Config Helper opened - Unified configuration management available")
            self.logger.info("Config Helper launched for unified configuration management")

        except Exception as e:
            self.logger.error(f"Failed to open Config Helper: {str(e)}")
            messagebox.showerror("Error",
                               f"Failed to open Config Helper:\n{str(e)}")

    def _log_message(self, message):
        """Log a message (now uses logger instead of GUI log display)"""
        # Since we replaced the log display with statistics panel,
        # just log the message using the standard logger
        self.logger.info(f"GUI: {message}")

    def _start_status_updates(self):
        """Start the status update thread"""
        self.running_status_updates = True
        self.status_update_thread = threading.Thread(target=self._status_update_loop, daemon=True)
        self.status_update_thread.start()

    def _status_update_loop(self):
        """Status update loop running in separate thread"""
        error_count = 0
        last_error_time = 0

        while self.running_status_updates:
            try:
                self._update_status()
                error_count = 0  # Reset error count on success
                time.sleep(1.0)  # Update every second
            except Exception as e:
                current_time = time.time()
                error_count += 1

                # Only log errors occasionally to prevent spam
                if current_time - last_error_time > 10:  # Log every 10 seconds max
                    self.logger.error(f"Status update error (count: {error_count}): {str(e)}")
                    last_error_time = current_time

                # If too many consecutive errors, increase sleep time
                if error_count > 10:
                    time.sleep(5.0)
                else:
                    time.sleep(1.0)

    def _update_status(self):
        """Update the status display"""
        if not self.controller:
            return

        status = self.controller.get_status()

        # Update uptime
        if status['running'] and status['uptime']:
            hours = int(status['uptime'] // 3600)
            minutes = int((status['uptime'] % 3600) // 60)
            seconds = int(status['uptime'] % 60)
            uptime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.uptime_label.config(text=uptime_str)
        else:
            self.uptime_label.config(text="00:00:00")

        # Update counters
        self.scans_label.config(text=str(status['total_scans']))
        self.executions_label.config(text=str(status['total_executions']))

        # Update mouse override button if status changed
        if hasattr(status, 'mouse_detection_enabled') or 'mouse_detection_enabled' in status:
            self._update_mouse_override_button()

        # Show mouse pause status if active
        if 'mouse_pause_remaining' in status and status['mouse_pause_remaining'] > 0:
            remaining = int(status['mouse_pause_remaining'])
            self.status_label.config(text=f"Status: Mouse Override ({remaining}s)", foreground="orange")
        elif hasattr(self, 'is_paused') and self.is_paused:
            self.status_label.config(text="Status: Paused", foreground="orange")
        elif status['running']:
            self.status_label.config(text="Status: Running", foreground="green")

        # Update system statistics
        self._update_system_stats()

        # Update module status indicators
        try:
            for module_status in status['modules']:
                try:
                    # Ensure module_status is a dictionary
                    if not isinstance(module_status, dict):
                        self.logger.warning(f"Invalid module status format: {type(module_status)} - {module_status}")
                        continue

                    module_name = module_status.get('name', 'unknown')
                    if hasattr(self, f"{module_name}_status"):
                        status_label = getattr(self, f"{module_name}_status")

                        if not module_status.get('enabled', False):
                            color = "gray"
                        elif module_status.get('on_cooldown', False):
                            color = "orange"
                        else:
                            color = "green"

                        status_label.config(foreground=color)
                except Exception as e:
                    self.logger.error(f"Error updating status for module {module_status}: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error in module status update loop: {str(e)}")

    def _on_closing(self):
        """Handle window closing"""
        if self.controller.running:
            if messagebox.askokcancel("Quit", "Automation is still running. Stop and quit?"):
                self.controller.stop()
                self.running_status_updates = False
                self.root.destroy()
        else:
            self.running_status_updates = False
            self.root.destroy()



    def _execute_manual_action(self):
        """Execute manual action on selected modules (checkbox-based)"""
        action_type = self.action_type_var.get()
        action_value = self.action_value_var.get()

        # Get selected modules from checkboxes
        selected_modules = [module_id for module_id, var in self.selected_modules.items() if var.get()]

        if not selected_modules:
            messagebox.showwarning("Warning", "Please select at least one module using the checkboxes")
            return

        if not action_type:
            messagebox.showwarning("Warning", "Please select an action")
            return

        # Execute action on all selected modules
        results = []
        for module_id in selected_modules:
            # Find module
            module = None
            for mod in self.controller.modules:
                if mod.name == module_id:
                    module = mod
                    break

            if not module:
                results.append(f"❌ Module {module_id} not found")
                continue

            try:
                if action_type == "Execute Now":
                    self._execute_module_now(module)
                    results.append(f"✅ {module_id}: Executed")

                elif action_type == "Add Wait":
                    wait_minutes = self._get_wait_time_input(action_value)
                    if wait_minutes is not None:
                        self._apply_wait_to_module(module, wait_minutes)
                        results.append(f"✅ {module_id}: Wait added ({wait_minutes}min)")

                elif action_type == "Clear Wait":
                    self._clear_wait_from_module(module)
                    results.append(f"✅ {module_id}: Wait cleared")

                elif action_type == "Reset Cooldown":
                    self._reset_module_cooldown(module)
                    results.append(f"✅ {module_id}: Cooldown reset")

                elif action_type == "Force Stop":
                    self._force_stop_module(module)
                    results.append(f"✅ {module_id}: Force stopped")

                elif action_type == "Enable":
                    self._enable_module(module)
                    results.append(f"✅ {module_id}: Enabled")

                elif action_type == "Disable":
                    self._disable_module(module)
                    results.append(f"✅ {module_id}: Disabled")

                else:
                    results.append(f"❌ {module_id}: Unknown action: {action_type}")

            except Exception as e:
                results.append(f"❌ {module_id}: Failed - {str(e)}")

        # Show results
        if results:
            result_text = "\n".join(results)
            messagebox.showinfo("Action Results", f"Executed on {len(selected_modules)} modules:\n\n{result_text}")

    def _get_wait_time_input(self, value):
        """Get wait time from input or prompt user"""
        if value:
            try:
                return float(value)
            except ValueError:
                messagebox.showerror("Error", "Invalid wait time value")
                return None
        else:
            # Prompt user for wait time
            result = tk.simpledialog.askfloat(
                "Wait Time",
                "Enter wait time in minutes:",
                minvalue=0,
                maxvalue=1440  # 24 hours max
            )
            return result

    def _execute_module_now(self, module):
        """Execute module immediately"""
        if not self.controller.running:
            messagebox.showwarning("Warning", "Automation is not running. Start automation first.")
            return

        # Clear any wait times and cooldowns
        module.manual_wait_until = 0
        module.last_execution = 0
        module.last_execution_time = 0

        self.logger.info(f"Manual execution triggered for {module.name}")
        messagebox.showinfo("Success", f"Module {module.name} will execute on next cycle")

    def _apply_wait_to_module(self, module, wait_minutes):
        """Apply wait time to module"""
        import time
        module.manual_wait_until = time.time() + (wait_minutes * 60)
        self.manual_wait_vars[module.name] = wait_minutes
        self.logger.info(f"Applied {wait_minutes} minute wait to {module.name}")
        messagebox.showinfo("Success", f"Applied {wait_minutes} minute wait to {module.name}")

    def _clear_wait_from_module(self, module):
        """Clear wait time from module"""
        module.manual_wait_until = 0
        if module.name in self.manual_wait_vars:
            del self.manual_wait_vars[module.name]
        self.logger.info(f"Cleared manual wait for {module.name}")
        messagebox.showinfo("Success", f"Cleared manual wait for {module.name}")

    def _reset_module_cooldown(self, module):
        """Reset module cooldown"""
        module.last_execution = 0
        module.last_execution_time = 0
        self.logger.info(f"Reset cooldown for {module.name}")
        messagebox.showinfo("Success", f"Reset cooldown for {module.name}")

    def _force_stop_module(self, module):
        """Force stop module execution"""
        module.is_executing = False
        module.current_activity = "Force stopped"
        module.waiting_for = "Manual restart"
        # Add a long wait to effectively stop it
        import time
        module.manual_wait_until = time.time() + (60 * 60)  # 1 hour
        self.logger.info(f"Force stopped {module.name}")
        messagebox.showinfo("Success", f"Force stopped {module.name}")

    def _enable_module(self, module):
        """Enable module"""
        module.enabled = True
        # Update checkbox in main GUI if it exists
        if module.name in self.module_vars:
            self.module_vars[module.name].set(True)
        self.logger.info(f"Enabled {module.name}")
        messagebox.showinfo("Success", f"Enabled {module.name}")

    def _disable_module(self, module):
        """Disable module"""
        module.enabled = False
        # Update checkbox in main GUI if it exists
        if module.name in self.module_vars:
            self.module_vars[module.name].set(False)
        self.logger.info(f"Disabled {module.name}")
        messagebox.showinfo("Success", f"Disabled {module.name}")

    def update_module_stats(self, module_name):
        """Update statistics counter for a module"""
        if module_name == "map_trade":
            # For map_trade, show actual trade counts instead of module executions
            try:
                map_trade_module = self.controller.get_module_by_name("map_trade")
                if map_trade_module and hasattr(map_trade_module, 'total_trades_completed'):
                    trade_count = map_trade_module.total_trades_completed
                    red_thumb_count = getattr(map_trade_module, 'total_red_thumb_clicks', 0)

                    if module_name in self.stats_labels:
                        # Show format: "Trades: X | Thumbs: Y"
                        display_text = f"{trade_count} | 👍{red_thumb_count}"
                        self.stats_labels[module_name].config(text=display_text)
                    return
            except Exception as e:
                self.logger.error(f"Error updating map trade stats: {str(e)}")

        # Default behavior for other modules
        if module_name in self.stats_counters:
            self.stats_counters[module_name] += 1
            new_count = self.stats_counters[module_name]

            if module_name in self.stats_labels:
                self.stats_labels[module_name].config(text=str(new_count))

    def _update_system_stats(self):
        """Update CPU and RAM usage statistics"""
        try:
            import psutil

            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_label.config(text=f"{cpu_percent:.1f}%")

            # Color code CPU usage
            if cpu_percent < 50:
                self.cpu_label.config(fg=self.colors['accent_green'])
            elif cpu_percent < 80:
                self.cpu_label.config(fg=self.colors['accent_yellow'])
            else:
                self.cpu_label.config(fg=self.colors['accent_red'])

            # Get RAM usage for current process
            process = psutil.Process()
            memory_info = process.memory_info()
            ram_mb = memory_info.rss / 1024 / 1024  # Convert to MB
            self.ram_label.config(text=f"{ram_mb:.1f} MB")

            # Color code RAM usage
            if ram_mb < 100:
                self.ram_label.config(fg=self.colors['accent_green'])
            elif ram_mb < 200:
                self.ram_label.config(fg=self.colors['accent_yellow'])
            else:
                self.ram_label.config(fg=self.colors['accent_red'])

        except ImportError:
            # psutil not available
            self.cpu_label.config(text="N/A")
            self.ram_label.config(text="N/A")
        except Exception as e:
            self.logger.error(f"Error updating system stats: {str(e)}")

    def _clear_cache(self):
        """Clear various caches to free up memory"""
        try:
            import gc
            import os

            # Clear Python garbage collection
            collected = gc.collect()

            # Clear template cache if screen scanner has one
            if hasattr(self.controller, 'screen_scanner') and self.controller.screen_scanner:
                if hasattr(self.controller.screen_scanner, 'templates'):
                    template_count = len(self.controller.screen_scanner.templates)
                    self.controller.screen_scanner.templates.clear()
                    self.logger.info(f"[CACHE] Cleared {template_count} cached templates")

            # Clear any module caches
            for module in self.controller.modules:
                if hasattr(module, 'clear_cache'):
                    module.clear_cache()

            self.logger.info(f"[CACHE] Cache cleared - {collected} objects collected by garbage collector")

            # Show success message
            import tkinter.messagebox as messagebox
            messagebox.showinfo("Cache Cleared",
                              f"Cache cleared successfully!\n"
                              f"• {collected} objects freed by garbage collector\n"
                              f"• Template cache cleared\n"
                              f"• Module caches cleared")

        except Exception as e:
            self.logger.error(f"Error clearing cache: {str(e)}")
            import tkinter.messagebox as messagebox
            messagebox.showerror("Error", f"Failed to clear cache:\n{str(e)}")

    def _update_status_tree(self):
        """Update the status tree with enhanced live activity information"""
        if not self.status_tree:
            return

        # Clear existing items
        for item in self.status_tree.get_children():
            self.status_tree.delete(item)

        # Add module status with enhanced live activity
        for module in self.controller.modules:
            # Basic status
            if not module.enabled:
                status = "Disabled"
                current_activity = "Module disabled"
                progress = "Ready to enable"
                waiting_for = "User activation"
                last_action = "Disabled"
            elif not self.controller.running:
                status = "Stopped"
                current_activity = "Automation stopped"
                progress = "Ready to start"
                waiting_for = "Start button"
                last_action = getattr(module, 'last_action', 'Ready')
            else:
                status = "Active"

                # Get enhanced activity information
                current_activity = getattr(module, 'current_activity', 'Monitoring')
                progress = getattr(module, 'current_progress', 'Scanning')
                waiting_for = getattr(module, 'waiting_for', 'Conditions')
                last_action = getattr(module, 'last_action', 'Monitoring')

                # Check if module is on cooldown or waiting
                import time
                current_time = time.time()

                # Check manual wait
                manual_wait_until = getattr(module, 'manual_wait_until', 0)
                if manual_wait_until > current_time:
                    wait_remaining = manual_wait_until - current_time
                    status = "Manual Wait"
                    current_activity = f"Manual wait active"
                    progress = f"{int(wait_remaining//60)}m {int(wait_remaining%60)}s remaining"
                    waiting_for = "Wait timer"

                # Check normal cooldown
                elif hasattr(module, 'last_execution_time') and module.last_execution_time:
                    cooldown = getattr(module, 'cooldown', 300)
                    next_execution = module.last_execution_time + cooldown

                    if next_execution > current_time:
                        remaining = next_execution - current_time
                        status = "Cooldown"
                        current_activity = "Waiting for cooldown"
                        progress = f"{int(remaining//60)}m {int(remaining%60)}s remaining"
                        waiting_for = "Cooldown timer"

                # Check for module-specific cooldowns and status
                self._update_module_specific_status(module, current_time)

                # Get updated values after module-specific updates
                if hasattr(module, '_status_override'):
                    status = module._status_override
                if hasattr(module, '_activity_override'):
                    current_activity = module._activity_override
                if hasattr(module, '_progress_override'):
                    progress = module._progress_override
                if hasattr(module, '_waiting_override'):
                    waiting_for = module._waiting_override

                # Check if module is currently executing
                if getattr(module, 'is_executing', False):
                    status = "Executing"
                    current_activity = getattr(module, 'current_step', 'Running...')
                    progress = getattr(module, 'execution_progress', 'In progress')
                    waiting_for = getattr(module, 'waiting_for_template', 'Screen scan')

            # Format last action with timestamp
            if hasattr(module, 'last_execution_time') and module.last_execution_time:
                import time
                last_action_time = time.strftime('%H:%M:%S', time.localtime(module.last_execution_time))
                last_action = f"{getattr(module, 'last_action', 'Executed')} at {last_action_time}"

            # Action buttons text
            action_text = "Execute | Wait | Reset"

            # Insert into tree
            self.status_tree.insert("", "end", text=module.name,
                                   values=(status, current_activity, progress, waiting_for, action_text))

    def _update_module_specific_status(self, module, current_time):
        """Update module-specific status information"""
        # Clear any previous overrides
        for attr in ['_status_override', '_activity_override', '_progress_override', '_waiting_override']:
            if hasattr(module, attr):
                delattr(module, attr)

        module_name = module.name

        # Zombie Invasion - No Stamina and No Monster Cooldowns
        if module_name == "zombie_invasion":
            # Check for no stamina pause (higher priority)
            no_stamina_pause_until = getattr(module, 'no_stamina_pause_until', 0)
            if no_stamina_pause_until > current_time:
                remaining = no_stamina_pause_until - current_time
                minutes = int(remaining // 60)
                seconds = int(remaining % 60)
                module._status_override = "No Stamina"
                module._activity_override = "Waiting for stamina recovery"
                module._progress_override = f"{minutes}m {seconds}s remaining"
                module._waiting_override = "Stamina regeneration"
            else:
                # Check for no monster pause
                no_monster_pause_until = getattr(module, 'no_monster_pause_until', 0)
                if no_monster_pause_until > current_time:
                    remaining = no_monster_pause_until - current_time
                    minutes = int(remaining // 60)
                    seconds = int(remaining % 60)
                    module._status_override = "No Monsters"
                    module._activity_override = "Waiting for monsters to spawn"
                    module._progress_override = f"{minutes}m {seconds}s remaining"
                    module._waiting_override = "Monster spawn timer"
                else:
                    # Check squad status
                    squad_status = getattr(module, 'current_squad_status', 'Unknown')
                    if squad_status != 'Unknown':
                        if '4/4' in squad_status:
                            module._activity_override = "All squads deployed"
                            module._progress_override = "4/4 squads active"
                            module._waiting_override = "Squad return"
                        else:
                            module._activity_override = f"Deploying squads ({squad_status})"
                            module._progress_override = f"Current: {squad_status}"
                            module._waiting_override = "Monster search"

        # Alliance Donation - Hourly Cooldown
        elif module_name == "alliance_donation":
            # Alliance donation uses last_auto_execution instead of last_execution_time
            last_execution = getattr(module, 'last_auto_execution', 0)
            if last_execution > 0:
                # Alliance donation runs every hour (3600 seconds)
                next_execution = last_execution + 3600
                if next_execution > current_time:
                    remaining = next_execution - current_time
                    hours = int(remaining // 3600)
                    minutes = int((remaining % 3600) // 60)
                    seconds = int(remaining % 60)

                    if hours > 0:
                        time_str = f"{hours}h {minutes}m"
                    else:
                        time_str = f"{minutes}m {seconds}s"

                    module._status_override = "Cooldown"
                    module._activity_override = "Waiting for next donation cycle"
                    module._progress_override = f"{time_str} remaining"
                    module._waiting_override = "Hourly timer"
                else:
                    module._activity_override = "Ready for donation"
                    module._progress_override = "Hourly cycle ready"
                    module._waiting_override = "Alliance detection"
            else:
                module._activity_override = "Ready for first donation"
                module._progress_override = "No previous execution"
                module._waiting_override = "Alliance detection"

        # Map Trade - 5 Minute Cooldown
        elif module_name == "map_trade":
            last_execution = getattr(module, 'last_execution_time', 0)
            if last_execution > 0:
                # Map trade runs every 5 minutes (300 seconds)
                next_execution = last_execution + 300
                if next_execution > current_time:
                    remaining = next_execution - current_time
                    minutes = int(remaining // 60)
                    seconds = int(remaining % 60)
                    module._status_override = "Cooldown"
                    module._activity_override = "Waiting for next trade cycle"
                    module._progress_override = f"{minutes}m {seconds}s remaining"
                    module._waiting_override = "5-minute timer"
                else:
                    module._activity_override = "Ready for trade"
                    module._progress_override = "5-minute cycle ready"
                    module._waiting_override = "Map interface"

        # Daily Tasks
        elif module_name == "daily_tasks":
            if not getattr(module, 'enabled', True):
                module._activity_override = "Manual execution only"
                module._progress_override = "Awaiting manual trigger"
                module._waiting_override = "User action"
            else:
                module._activity_override = "Ready for daily tasks"
                module._progress_override = "Manual trigger ready"
                module._waiting_override = "User activation"

        # Help Click
        elif module_name == "help_click":
            help_detected = getattr(module, 'help_available', False)
            if help_detected:
                module._activity_override = "Help request detected"
                module._progress_override = "Ready to assist"
                module._waiting_override = "Click execution"
            else:
                module._activity_override = "Monitoring for help requests"
                module._progress_override = "Scanning screen"
                module._waiting_override = "Help template"



    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main():
    """Main entry point"""
    app = LastWarGUI()
    app.run()


if __name__ == "__main__":
    main()
