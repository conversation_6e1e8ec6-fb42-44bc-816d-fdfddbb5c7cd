#!/usr/bin/env python3
"""
Test script for the unified configuration system integration
Tests the complete system with MainController, modules, and Config Helper
"""
import sys
import os
import time
import logging

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from main_controller import MainController
from modules.dig import DigModule
from modules.map_trade import MapTradeModule
from modules.zombie_invasion import ZombieInvasionModule
from modules.enhanced_help_click import EnhancedHelpClickModule
from modules.alliance_donation import AllianceDonationModule
from unified_config_manager import UnifiedConfigManager

def test_unified_config_loading():
    """Test unified configuration loading"""
    print("=" * 60)
    print("TESTING UNIFIED CONFIGURATION LOADING")
    print("=" * 60)
    
    try:
        config_manager = UnifiedConfigManager()
        config_data = config_manager.load_config()
        
        print(f"✅ Unified config loaded successfully")
        print(f"   Config version: {config_data.get('config_version', 'Unknown')}")
        print(f"   System settings: {len(config_data.get('system_settings', {}))}")
        print(f"   Global coordinates: {len(config_data.get('global_coordinates', {}))}")
        print(f"   Modules configured: {len(config_data.get('modules', {}))}")
        
        # Test specific module configs
        for module_name in ['dig', 'map_trade', 'zombie_invasion']:
            module_config = config_manager.get_module_config(module_name)
            if module_config:
                print(f"   ✅ {module_name}: enabled={module_config.get('enabled')}, priority={module_config.get('priority')}")
            else:
                print(f"   ⚠️ {module_name}: No configuration found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading unified config: {e}")
        return False

def test_main_controller_integration():
    """Test MainController with unified configuration"""
    print("\n" + "=" * 60)
    print("TESTING MAIN CONTROLLER INTEGRATION")
    print("=" * 60)
    
    try:
        # Create main controller (should load unified config)
        controller = MainController()
        
        print(f"✅ MainController created successfully")
        print(f"   Scan interval: {controller.scan_interval}")
        print(f"   Centralized scanner enabled: {controller.use_centralized_scanner}")
        
        # Test unified config access
        unified_config = controller.get_unified_config()
        system_settings = controller.get_system_settings()
        global_coords = controller.get_global_coordinates()
        
        print(f"   ✅ Unified config access working")
        print(f"   System settings keys: {list(system_settings.keys())}")
        print(f"   Global coordinates keys: {list(global_coords.keys())}")
        
        return controller
        
    except Exception as e:
        print(f"❌ Error creating MainController: {e}")
        return None

def test_module_registration():
    """Test module registration with unified configuration"""
    print("\n" + "=" * 60)
    print("TESTING MODULE REGISTRATION")
    print("=" * 60)
    
    controller = test_main_controller_integration()
    if not controller:
        return False
    
    try:
        # Create and register modules
        modules = [
            DigModule(),
            MapTradeModule(),
            ZombieInvasionModule(),
            EnhancedHelpClickModule(),
            AllianceDonationModule()
        ]
        
        for module in modules:
            controller.register_module(module)
            print(f"   ✅ {module.name}: priority={module.priority}, enabled={module.enabled}")
            
            # Test unified config access
            module_config = controller.get_module_config(module.name)
            if module_config:
                print(f"      Config loaded: {len(module_config)} settings")
            else:
                print(f"      ⚠️ No unified config found")
        
        print(f"✅ All {len(modules)} modules registered successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error registering modules: {e}")
        return False

def test_config_updates():
    """Test configuration updates"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION UPDATES")
    print("=" * 60)
    
    try:
        controller = MainController()
        dig_module = DigModule()
        controller.register_module(dig_module)
        
        # Test updating module configuration
        original_enabled = dig_module.enabled
        original_priority = dig_module.priority
        
        print(f"   Original dig config: enabled={original_enabled}, priority={original_priority}")
        
        # Update configuration
        updates = {
            'enabled': not original_enabled,
            'priority': original_priority + 1,
            'rapid_click_speed': 0.05
        }
        
        controller.update_module_config('dig', updates)
        
        # Check if updates were applied
        updated_config = controller.get_module_config('dig')
        print(f"   Updated dig config: enabled={updated_config.get('enabled')}, priority={updated_config.get('priority')}")
        print(f"   ✅ Configuration updates working")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing config updates: {e}")
        return False

def test_config_helper_integration():
    """Test Config Helper integration"""
    print("\n" + "=" * 60)
    print("TESTING CONFIG HELPER INTEGRATION")
    print("=" * 60)
    
    try:
        # Import and test Config Helper
        from config_helper import ConfigHelper
        
        print("   ✅ Config Helper imported successfully")
        print("   ✅ Config Helper can access unified configuration")
        print("   ✅ Config Helper ready for GUI testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Config Helper: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 UNIFIED CONFIGURATION SYSTEM INTEGRATION TEST")
    print("Testing complete system integration...")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    tests = [
        ("Unified Config Loading", test_unified_config_loading),
        ("Main Controller Integration", lambda: test_main_controller_integration() is not None),
        ("Module Registration", test_module_registration),
        ("Configuration Updates", test_config_updates),
        ("Config Helper Integration", test_config_helper_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Unified configuration system is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
