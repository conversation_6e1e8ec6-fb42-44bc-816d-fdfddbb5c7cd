{"daily_tasks": {"enabled": true, "priority": 1, "cooldown": 300.0, "manual_trigger": true, "templates": [{"name": "daily_tasks_button", "threshold": 0.8, "required": false, "description": "Daily tasks button in UI"}], "text_patterns": [{"text": "Daily Tasks", "region": "top_bar", "required": true}, {"text": "<PERSON><PERSON><PERSON>", "region": "center", "required": false}, {"text": "Complete", "region": "center", "required": false}], "click_coordinates": [{"name": "1_daily_tasks", "x": 1576, "y": 991, "delay": 1.5, "description": "Open daily tasks menu", "enabled": true, "repeat": 1}, {"name": "2_claim_1", "x": 600, "y": 200, "delay": 1.0, "description": "Claim first reward", "enabled": true, "repeat": 1}, {"name": "3_claim_2", "x": 600, "y": 250, "delay": 1.0, "description": "<PERSON><PERSON>m second reward", "enabled": true, "repeat": 1}, {"name": "4_claim_3", "x": 600, "y": 300, "delay": 1.0, "description": "<PERSON><PERSON>m third reward", "enabled": true, "repeat": 1}, {"name": "5_claim_4", "x": 600, "y": 350, "delay": 1.0, "description": "<PERSON><PERSON><PERSON> fourth reward", "enabled": true, "repeat": 1}, {"name": "6_claim_5", "x": 600, "y": 400, "delay": 1.0, "description": "<PERSON><PERSON><PERSON> fifth reward", "enabled": true, "repeat": 1}, {"name": "7_close", "x": 750, "y": 50, "delay": 1.0, "description": "Close daily tasks window", "enabled": true, "repeat": 1}], "scan_regions": [{"name": "top_bar", "x": 0, "y": 0, "width": 800, "height": 100}, {"name": "center", "x": 200, "y": 150, "width": 400, "height": 400}, {"name": "bottom_bar", "x": 0, "y": 500, "width": 800, "height": 100}], "actions": [{"type": "navigate", "target": "daily_tasks_button", "wait": 2.0}, {"type": "claim_rewards", "max_attempts": 10}, {"type": "complete_tasks", "max_attempts": 5}, {"type": "close", "target": "close_button", "wait": 1.0}]}, "help_click": {"enabled": true, "priority": 0, "cooldown": 0.0, "templates": [{"name": "Help_template", "threshold": 0.7, "required": false}, {"name": "Help_chat", "threshold": 0.7, "required": false}], "text_patterns": [{"text": "Help", "region": "full_screen", "required": true}], "click_coordinates": [{"name": "1_Help<PERSON>utton", "x": 1164, "y": 521, "description": "Click main help button", "enabled": true, "delay": 0.01, "repeat": 1, "use_esc_key": false}, {"name": "2_HelpChat", "x": 799, "y": 579, "description": "Click help chat button", "enabled": true, "delay": 0.01, "repeat": 1, "use_esc_key": false}], "scan_regions": [{"name": "full_screen", "x": 0, "y": 0, "width": 800, "height": 600}, {"name": "alliance_area", "x": 50, "y": 450, "width": 200, "height": 150}, {"name": "help_list", "x": 150, "y": 100, "width": 500, "height": 400}], "actions": [{"type": "navigate", "target": "alliance_button", "wait": 2.0}, {"type": "click_help_all", "target": "help_all_button", "fallback": "individual_helps"}, {"type": "individual_helps", "max_clicks": 50, "delay": 0.5}, {"type": "close", "method": "esc", "wait": 1.0}, {"type": "navigate", "target": "alliance_button", "wait": 0.5}]}, "alliance_donation": {"enabled": true, "priority": 3, "cooldown": 600.0, "templates": [{"name": "alliance_button", "threshold": 0.8, "required": true, "description": "Alliance button in bottom menu"}, {"name": "base_button", "threshold": 0.8, "required": false, "description": "Base button for navigation"}, {"name": "Base", "threshold": 0.8, "required": false, "description": "Base icon template"}], "text_patterns": [{"text": "Donate", "region": "center", "required": true}, {"text": "Alliance", "region": "bottom_bar", "required": true}, {"text": "Research", "region": "center", "required": false}], "click_coordinates": [{"name": "1_Alliance", "x": 1217, "y": 523, "delay": 0.7, "description": "Click Alliance main button", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "2_gift", "x": 548, "y": 415, "delay": 1.0, "description": "Click gifts", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "3_claim", "x": 637, "y": 680, "delay": 2.5, "repeat": 2, "description": "Click Donate button (3 times)", "enabled": true, "use_esc_key": false}, {"name": "4_premium", "x": 721, "y": 197, "delay": 1.5, "enabled": true, "description": "Premium action (costs gems - disabled by default)", "repeat": 2, "use_esc_key": false}, {"name": "5_claim", "x": 750, "y": 679, "delay": 3.0, "description": "", "repeat": 2, "enabled": true, "use_esc_key": false}, {"name": "6_exit", "x": 461, "y": 690, "delay": 2.0, "description": "Close gifts menu with ESC", "enabled": true, "repeat": 1, "use_esc_key": true}, {"name": "Donation", "x": 725, "y": 478, "delay": 1.0, "repeat": 1, "description": "", "enabled": true, "use_esc_key": false}, {"name": "trophy", "x": 773, "y": 541, "delay": 1.0, "repeat": 1, "description": "", "enabled": true, "use_esc_key": false}, {"name": "Donate", "x": 633, "y": 666, "delay": 1.0, "repeat": 1, "description": "", "enabled": true, "use_esc_key": false}, {"name": "Donate_button", "x": 708, "y": 558, "delay": 0.1, "repeat": 15, "description": "Fast donation clicks (15x) - 3s delay after completion", "enabled": true, "use_esc_key": false}, {"name": "exit", "x": 796, "y": 153, "delay": 2.0, "repeat": 1, "description": "Close donation menu with ESC key", "use_esc_key": true, "enabled": true}, {"name": "secexit", "x": 797, "y": 115, "delay": 0.8, "repeat": 1, "description": "Final close with ESC key", "use_esc_key": false, "enabled": true}], "scan_regions": [{"name": "alliance_tabs", "x": 200, "y": 100, "width": 400, "height": 100}, {"name": "donation_area", "x": 150, "y": 200, "width": 500, "height": 300}, {"name": "confirmation", "x": 300, "y": 250, "width": 200, "height": 150}], "actions": [{"type": "navigate", "target": "alliance_menu", "wait": 2.0}, {"type": "process_research", "max_donations": 5}, {"type": "process_store", "max_donations": 3}, {"type": "process_technology", "max_donations": 5}, {"type": "close", "method": "esc", "wait": 1.0}]}, "gathering_troops": {"enabled": true, "priority": 4, "cooldown": 180.0, "templates": [{"name": "world_button", "threshold": 0.8, "required": true, "description": "World button to access world map"}, {"name": "iron_node", "threshold": 0.7, "required": false}, {"name": "food_node", "threshold": 0.7, "required": false}, {"name": "gold_node", "threshold": 0.7, "required": false}, {"name": "gather_button", "threshold": 0.8, "required": true}], "text_patterns": [{"text": "World Map", "region": "top_bar", "required": true}, {"text": "<PERSON><PERSON>", "region": "center", "required": true}, {"text": "March", "region": "bottom_bar", "required": false}], "click_coordinates": [{"name": "world_map", "x": 400, "y": 50, "description": "World map button", "enabled": true, "delay": 1.0, "repeat": 1}, {"name": "march_button", "x": 400, "y": 500, "description": "Send march button", "enabled": true, "delay": 1.0, "repeat": 1}, {"name": "map_close", "x": 50, "y": 50, "description": "Close world map", "enabled": true, "delay": 1.0, "repeat": 1}], "scan_regions": [{"name": "map_area", "x": 100, "y": 100, "width": 600, "height": 400}, {"name": "resource_info", "x": 250, "y": 200, "width": 300, "height": 200}, {"name": "march_setup", "x": 200, "y": 350, "width": 400, "height": 200}], "resource_colors": {"iron": {"color": [128, 128, 128], "tolerance": 30}, "food": {"color": [0, 255, 0], "tolerance": 30}, "gold": {"color": [0, 215, 255], "tolerance": 30}}, "actions": [{"type": "navigate", "target": "world_map", "wait": 3.0}, {"type": "find_resources", "preferred": "iron", "max_attempts": 5}, {"type": "gather_resource", "confirm": true}, {"type": "close", "target": "map_close", "wait": 1.0}]}, "zombie_invasion": {"enabled": true, "priority": 1, "cooldown": 0.0, "templates": [{"name": "events_button", "threshold": 0.8, "required": true}, {"name": "search_button", "threshold": 0.8, "required": true}, {"name": "sword_icon", "threshold": 0.8, "required": true}, {"name": "march_button", "threshold": 0.8, "required": false}, {"name": "squad_available", "threshold": 0.7, "required": false}, {"name": "no_stamina", "threshold": 0.8, "required": false}, {"name": "squad_0_4", "threshold": 0.95, "required": false, "description": "Template for 0/4 or empty squad status"}, {"name": "squad_1_4", "threshold": 0.95, "required": false, "description": "Template for 1/4 squad status"}, {"name": "squad_2_4", "threshold": 0.95, "required": false, "description": "Template for 2/4 squad status"}, {"name": "squad_3_4", "threshold": 0.95, "required": false, "description": "Template for 3/4 squad status"}, {"name": "squad_4_4", "threshold": 0.95, "required": false, "description": "Template for 4/4 squad status - STOP when detected"}, {"name": "search_zombie", "threshold": 0.9, "required": true}], "text_patterns": [{"text": "Events", "region": "bottom_bar", "required": true}, {"text": "Search", "region": "center", "required": false}, {"text": "March", "region": "center", "required": false}], "click_coordinates": [{"name": "events_menu", "x": 1580, "y": 287, "description": "Events button in bottom menu"}, {"name": "search_zombie", "x": 1139, "y": 1311, "description": "Search button for zombies", "delay": 2.0, "repeat": 1}, {"name": "sword_attack", "x": 1262, "y": 1036, "description": "Sword icon to attack"}, {"name": "march_troops", "x": 1261, "y": 1064, "description": "March button to send troops"}, {"name": "zombie_invasion_tab", "x": 1616, "y": 161, "description": "Zombie Invasion tab in Events menu"}], "scan_regions": [{"name": "events_area", "x": 1500, "y": 200, "width": 200, "height": 200}, {"name": "squad_status", "x": 50, "y": 50, "width": 200, "height": 100}, {"name": "zombie_search", "x": 1000, "y": 1200, "width": 400, "height": 200}], "actions": [{"type": "check_squad_status", "required_templates": ["squad_0_4", "squad_1_4", "squad_2_4", "squad_3_4"]}, {"type": "navigate_events", "target": "events_menu", "wait": 1.5}, {"type": "search_zombies", "target": "search_zombie", "wait": 1.5}, {"type": "attack_zombie", "target": "sword_attack", "wait": 1.5}, {"type": "march_troops", "target": "march_troops", "wait": 1.5}]}, "map_trade": {"enabled": true, "priority": 2, "cooldown": 30.0, "manual_trigger": false, "templates": [{"name": "truck", "threshold": 0.8, "required": true, "description": "Truck template to verify map trade location"}, {"name": "trade_button", "threshold": 0.8, "required": false, "description": "Trade button to check if trade is available"}, {"name": "red_thumb_100x40", "threshold": 0.8, "required": false, "description": "Red thumb acknowledgment button (large)"}, {"name": "initiate_exchange_100x40", "threshold": 0.7, "required": false, "description": "Initiate/New exchange button (large)"}, {"name": "cancel_exchange_100x40", "threshold": 0.8, "required": false, "description": "Cancel exchange button (large)"}, {"name": "confirm_trade_100x40", "threshold": 0.8, "required": false, "description": "Confirm trade button (large)"}, {"name": "new_exchange", "threshold": 0.7, "required": false, "description": "New exchange button (generic)"}, {"name": "trade_complete_100x40", "threshold": 0.8, "required": false, "description": "Trade complete indicator (large)"}, {"name": "map_piece_M1", "threshold": 0.7, "required": false, "description": "Map piece M1 template for detection"}, {"name": "map_piece_M2", "threshold": 0.7, "required": false, "description": "Map piece M2 template for detection"}, {"name": "map_piece_M3", "threshold": 0.7, "required": false, "description": "Map piece M3 template for detection"}, {"name": "map_piece_M4", "threshold": 0.7, "required": false, "description": "Map piece M4 template for detection"}, {"name": "map_piece_M5", "threshold": 0.7, "required": false, "description": "Map piece M5 template for detection"}, {"name": "map_piece_M6", "threshold": 0.7, "required": false, "description": "Map piece M6 template for detection"}, {"name": "map_piece_M7", "threshold": 0.7, "required": false, "description": "Map piece M7 template for detection"}, {"name": "you_will_lose_100x40", "threshold": 0.8, "required": false, "description": "Warning dialog - you will lose items (large)"}], "click_coordinates": [{"name": "1_map_button", "x": 53, "y": 913, "delay": 1.0, "description": "STEP 1: Click map button to open map trade", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "2_hidden_treasure", "x": 1246, "y": 155, "delay": 1.0, "description": "STEP 2: Click hidden treasure", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "3_trade_area", "x": 1619, "y": 1363, "delay": 1.0, "description": "STEP 3: Click trade area â†’ THEN detection logic checks for red_thumb/new_exchange", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "4_confirm_1", "x": 1445, "y": 261, "delay": 1.0, "description": "STEP 4: First confirmation (used by trade detection loop)", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "5_trade_option_2", "x": 1278, "y": 513, "delay": 1.0, "description": "STEP 5: Select second trade option (trade loop)", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "6_confirm_2", "x": 1270, "y": 958, "delay": 1.0, "description": "STEP 6: Confirm second trade", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "7_allies_exchange", "x": 719, "y": 157, "delay": 1.0, "description": "STEP 7: Click on allies exchange", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "new_exchange_button", "x": 1255, "y": 1142, "delay": 1.0, "description": "Click new exchange button", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "initiate_exchange_button", "x": 2140, "y": 123, "delay": 1.0, "description": "Exit trade dialog", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "confirm_exchange_button", "x": 2140, "y": 123, "delay": 1.0, "description": "Final exit", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "you_will_lose_button", "x": 1200, "y": 1000, "delay": 1.0, "description": "Click you will lose confirmation", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "8_trade_option_3", "x": 628, "y": 277, "delay": 1.0, "description": "STEP 8: Select third trade option", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "9_exit_trade", "x": 2140, "y": 123, "delay": 1.0, "description": "STEP 9: Exit trade dialog", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "10_final_exit", "x": 2140, "y": 123, "delay": 1.0, "description": "STEP 10: Final exit", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "11_confirm_4", "x": 800, "y": 600, "delay": 1.0, "description": "STEP 11: Confirm fourth trade", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "12_final_red_thumb", "x": 750, "y": 550, "delay": 1.0, "description": "STEP 12: Final red thumb acknowledgment", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "13_close_dialog", "x": 2140, "y": 123, "delay": 1.0, "description": "STEP 13: Exit trade dialog", "enabled": true, "repeat": 1, "use_esc_key": false}, {"name": "14_return_base", "x": 0, "y": 0, "delay": 1.0, "description": "STEP 14: Return to base (ESC key)", "enabled": true, "repeat": 1, "use_esc_key": true}], "map_piece_coordinates": [{"name": "M1", "quantity_x": 1068, "quantity_y": 817, "click_x": 1026, "click_y": 765, "description": "Map piece M1 position"}, {"name": "M2", "quantity_x": 1224, "quantity_y": 817, "click_x": 1188, "click_y": 772, "description": "Map piece M2 position"}, {"name": "M3", "quantity_x": 1384, "quantity_y": 820, "click_x": 1342, "click_y": 773, "description": "Map piece M3 position"}, {"name": "M4", "quantity_x": 1533, "quantity_y": 816, "click_x": 1497, "click_y": 773, "description": "Map piece M4 position"}, {"name": "M5", "quantity_x": 1144, "quantity_y": 971, "click_x": 1106, "click_y": 923, "description": "Map piece M5 position"}, {"name": "M6", "quantity_x": 1298, "quantity_y": 970, "click_x": 1265, "click_y": 919, "description": "Map piece M6 position"}, {"name": "M7", "quantity_x": 1457, "quantity_y": 968, "click_x": 1420, "click_y": 924, "description": "Map piece M7 position"}], "scan_regions": [{"name": "map_trade_area", "x": 500, "y": 200, "width": 600, "height": 600, "description": "Main map trade interface area"}, {"name": "map_pieces_area", "x": 1000, "y": 750, "width": 600, "height": 300, "description": "Map pieces selection area"}, {"name": "truck_detection_area", "x": 0, "y": 0, "width": 1920, "height": 1080, "description": "Full screen for truck detection"}], "actions": [{"type": "verify_location", "target": "truck", "wait": 1.0, "description": "Verify we're in map trade location"}, {"type": "navigate_trade", "target": "map_button", "wait": 1.5, "description": "Navigate to trade interface"}, {"type": "analyze_pieces", "target": "map_pieces_area", "wait": 2.0, "description": "Analyze map piece quantities"}, {"type": "execute_trades", "max_attempts": 5, "wait": 1.0, "description": "Execute optimal trades"}, {"type": "acknowledge_results", "target": "red_thumb", "wait": 1.0, "description": "Acknowledge trade results"}, {"type": "return_base", "target": "esc_key", "wait": 1.0, "description": "Return to base"}]}, "dig": {"enabled": true, "priority": -1, "cooldown": 0.0, "manual_trigger": false, "rapid_click_speed": 0.1, "rapid_click_duration": 20.0, "timer_threshold": 10, "thank_you_message": "Thank you for the dig!", "templates": [{"name": "dig_icon", "threshold": 0.8, "required": false, "description": "Main dig icon trigger"}, {"name": "dig_up_dropdown", "threshold": 0.8, "required": false, "description": "Dig up dropdown trigger"}, {"name": "test_flight_treasure", "threshold": 0.8, "required": false, "description": "Test flight treasure trigger"}, {"name": "dig_up_treasure", "threshold": 0.8, "required": false, "description": "Dig up treasure trigger"}, {"name": "alliance_chat_is_on", "threshold": 0.8, "required": false, "description": "Alliance chat status indicator"}, {"name": "send_to_dig_1", "threshold": 0.8, "required": false, "description": "Send to dig option 1"}, {"name": "send_to_dig_2", "threshold": 0.8, "required": false, "description": "Send to dig option 2"}, {"name": "send_to_dig_3", "threshold": 0.8, "required": false, "description": "Send to dig option 3"}, {"name": "send_to_dig_flight", "threshold": 0.8, "required": false, "description": "Send to dig flight option"}, {"name": "send_to_dig_flight_2", "threshold": 0.8, "required": false, "description": "Send to dig flight option 2"}, {"name": "march_to_dig", "threshold": 0.8, "required": false, "description": "March to dig button"}, {"name": "select_tile", "threshold": 0.8, "required": false, "description": "Select tile button"}, {"name": "quit_game_dialog", "threshold": 0.8, "required": false, "description": "Quit game dialog detection"}, {"name": "chat_click", "threshold": 0.8, "required": false, "description": "Chat click backup option"}], "text_patterns": [{"text": "Dig", "region": "full_screen", "required": false}, {"text": "1", "region": "timer_area", "required": false}], "click_coordinates": [{"name": "dig_icon_click", "x": 1419, "y": 1230, "delay": 1.0, "description": "Click dig icon", "enabled": true, "repeat": 1}, {"name": "chat_link", "x": 1316, "y": 1172, "delay": 1.0, "description": "Click link in chat", "enabled": true, "repeat": 1}, {"name": "squad_select", "x": 1263, "y": 839, "delay": 1.0, "description": "Select squad for dig", "enabled": true, "repeat": 1}, {"name": "send_squad", "x": 1265, "y": 1069, "delay": 2.0, "description": "Send squad to dig", "enabled": true, "repeat": 1}, {"name": "loot_icon", "x": 1262, "y": 843, "delay": 0.001, "description": "Loot collection icon (spam click)", "enabled": true, "repeat": 50}, {"name": "close_window", "x": 900, "y": 100, "delay": 1.0, "description": "Close dig window", "enabled": true, "repeat": 1}], "regions": [{"name": "dig_area", "x": 300, "y": 200, "width": 600, "height": 400, "description": "Area to scan for dig interface"}, {"name": "timer_area", "x": 700, "y": 300, "width": 200, "height": 100, "description": "Area to scan for timer"}]}, "find_truck": {"enabled": false, "priority": -1, "cooldown": 0.0, "manual_trigger": true, "templates": [{"name": "Golden", "threshold": 0.8, "required": false, "description": "Golden truck template"}, {"name": "Purple", "threshold": 0.8, "required": false, "description": "Purple truck template"}, {"name": "Golden_puzzle", "threshold": 0.8, "required": false, "description": "Golden puzzle template"}], "click_coordinates": [{"name": "plunder_area", "x": 930, "y": 796, "delay": 1.0, "description": "Plunder area button", "enabled": true, "repeat": 1}, {"name": "reset_button", "x": 1598, "y": 87, "delay": 1.0, "description": "Reset search button", "enabled": true, "repeat": 1}], "scan_regions": [{"name": "loot_plunder", "x": 927, "y": 921, "width": 671, "height": 337, "description": "Loot plunder scanning area"}, {"name": "power_region", "x": 1064, "y": 1036, "width": 144, "height": 44, "description": "Power level reading area"}]}}