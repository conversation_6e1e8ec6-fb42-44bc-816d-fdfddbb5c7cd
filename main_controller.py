"""
Main controller for Last War automation
Manages all modules, priorities, and the main execution loop
Enhanced with centralized template scanner
"""
import time
import threading
import logging
import configparser
from typing import List, Dict, Any, Optional
from datetime import datetime
import os

from base_module import BaseModule
from screen_scanner import ScreenScanner
from centralized_template_scanner import CentralizedTemplateScanner


class MainController:
    """Main controller that orchestrates all automation modules"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_file)
        
        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger("LastWar.MainController")
        
        # Initialize centralized template scanner with controller reference for mouse override control
        self.centralized_scanner = CentralizedTemplateScanner(controller=self)

        # Keep legacy screen scanner for backward compatibility
        self.screen_scanner = ScreenScanner(
            templates_path=self.config.get('GENERAL', 'templates_path', fallback='templates/'),
            screenshots_path=self.config.get('GENERAL', 'screenshots_path', fallback='screenshots/')
        )

        # Clean up any old screenshot files (cache system doesn't save to disk)
        self.screen_scanner.cleanup_old_screenshots()

        # Module management
        self.modules: List[BaseModule] = []
        self.running = False
        self.paused = False
        self.main_thread = None

        # Centralized scanner mode (new architecture)
        self.use_centralized_scanner = True
        
        # Timing configuration
        self.scan_interval = self.config.getfloat('GENERAL', 'scan_interval', fallback=1.0)  # 1 second for responsive control
        self.priority_check_interval = self.config.getfloat('GENERAL', 'priority_check_interval', fallback=1.0)
        
        # Statistics
        self.start_time = None
        self.total_scans = 0
        self.total_executions = 0

        # Memory management for overnight stability
        self.memory_cleanup_interval = 300  # Clean memory every 5 minutes
        self.last_memory_cleanup = time.time()
        self.max_memory_mb = 500  # Restart if memory exceeds 500MB

        # Mouse movement detection for auto-pause override system
        self.last_mouse_pos = None
        self.mouse_pause_duration = 10.0  # Pause for 10 seconds when mouse movement detected
        self.mouse_pause_end_time = 0
        self.mouse_detection_enabled = True
        self.module_executing = False  # Track when modules are executing to avoid false positives
        self.module_end_time = 0  # Track when module execution ended for 1-second cooldown

        # UI recovery skip tracking - avoid ESC spam after module execution
        self.last_module_execution_time = 0
        self.ui_recovery_skip_duration = 8.0  # Skip UI recovery for 8 seconds after module execution

        # Debug ESC timer - only send ESC after events button missing for X seconds
        self.events_button_missing_since = 0
        self.debug_esc_delay = 5.0  # Wait 5 seconds before first ESC attempt
        self.debug_esc_extended_delay = 10.0  # Wait 10 seconds if 5 seconds wasn't enough

        self.logger.info("MainController initialized")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # Create log filename with timestamp
        log_filename = os.path.join(log_dir, f"lastwar_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()  # Also log to console
            ]
        )
    
    def register_module(self, module: BaseModule):
        """Register a new automation module"""
        # Give module a reference to the controller so it can check running status
        module.controller = self
        self.modules.append(module)
        self.modules.sort(key=lambda x: x.priority)  # Sort by priority

        # Register with centralized scanner
        self.centralized_scanner.register_module(module.name, module)

        self.logger.info(f"Registered module: {module.name} (priority: {module.priority})")

    def get_module_by_name(self, module_name: str):
        """Get a module by name"""
        for module in self.modules:
            if module.name == module_name:
                return module
        self.logger.warning(f"Module not found: {module_name}")
        return None
    
    def unregister_module(self, module_name: str):
        """Unregister a module by name"""
        self.modules = [m for m in self.modules if m.name != module_name]
        self.logger.info(f"Unregistered module: {module_name}")
    
    def get_module(self, module_name: str) -> Optional[BaseModule]:
        """Get a module by name"""
        for module in self.modules:
            if module.name == module_name:
                return module
        return None
    
    def enable_module(self, module_name: str):
        """Enable a specific module"""
        module = self.get_module(module_name)
        if module:
            module.enabled = True
            self.logger.info(f"Enabled module: {module_name}")
        else:
            self.logger.warning(f"Module not found: {module_name}")
    
    def disable_module(self, module_name: str):
        """Disable a specific module"""
        module = self.get_module(module_name)
        if module:
            module.enabled = False
            self.logger.info(f"Disabled module: {module_name}")
        else:
            self.logger.warning(f"Module not found: {module_name}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the controller and all modules"""
        mouse_pause_remaining = max(0, self.mouse_pause_end_time - time.time()) if self.mouse_pause_end_time > 0 else 0

        return {
            'running': self.running,
            'start_time': self.start_time,
            'uptime': time.time() - self.start_time if self.start_time else 0,
            'total_scans': self.total_scans,
            'total_executions': self.total_executions,
            'modules': [module.get_status() for module in self.modules],
            'scan_interval': self.scan_interval,
            'mouse_detection_enabled': self.mouse_detection_enabled,
            'mouse_pause_remaining': mouse_pause_remaining
        }

    def _check_ui_state_during_scan(self, screen_data):
        """
        Efficient UI state check during main scan
        Checks for events button presence and quit game dialog, recovers with ESC if needed
        """
        try:
            # Check templates detected in current scan
            templates_detected = screen_data.get('templates_detected', {})
            events_detected = templates_detected.get('events_button', False)
            quit_detected = templates_detected.get('quit_game_dialog', False)

            # Priority 1: Check for quit game dialog
            if quit_detected:
                self.logger.warning("[QUIT_RECOVERY] Quit game dialog detected - pressing ESC to return to game")
                import pyautogui
                pyautogui.press('escape')
                time.sleep(1.5)  # Longer wait for quit dialog to close

                # Verify recovery worked
                events_detected = self._quick_events_button_check()
                if events_detected:
                    self.logger.info("[QUIT_RECOVERY] ESC successful - returned to game from quit dialog")
                    return True
                else:
                    self.logger.warning("[QUIT_RECOVERY] ESC may not have worked - will try standard recovery")
                    # Continue to standard recovery below

            # Priority 2: Check for events button (normal UI state)
            if events_detected:
                self.logger.debug("[UI_CHECK] Events button detected - UI state OK")
                self.events_button_missing_since = 0  # Reset timer
                return True

            # Priority 3: Check if we should skip UI recovery (recent module execution)
            current_time = time.time()
            time_since_module = current_time - self.last_module_execution_time
            if time_since_module < self.ui_recovery_skip_duration:
                self.logger.debug(f"[UI_RECOVERY] Skipping ESC recovery - module executed {time_since_module:.1f}s ago (< {self.ui_recovery_skip_duration}s)")
                return True

            # Priority 4: Timer-based ESC recovery - only after events button missing for X seconds
            if self.events_button_missing_since == 0:
                self.events_button_missing_since = current_time
                self.logger.debug("[UI_RECOVERY] Events button missing - starting timer")
                return True

            time_missing = current_time - self.events_button_missing_since
            if time_missing < self.debug_esc_delay:
                self.logger.debug(f"[UI_RECOVERY] Events button missing for {time_missing:.1f}s (< {self.debug_esc_delay}s) - waiting")
                return True

            # Events button missing for enough time - attempt recovery
            self.logger.warning(f"[UI_RECOVERY] Events button missing for {time_missing:.1f}s - attempting ESC recovery")

            # First ESC attempt
            import pyautogui
            pyautogui.press('escape')
            time.sleep(1.0)

            # Quick check if recovery worked
            events_detected = self._quick_events_button_check()
            if events_detected:
                self.logger.info("[UI_RECOVERY] First ESC successful - events button restored")
                return True

            # Second ESC attempt
            self.logger.warning("[UI_RECOVERY] First ESC failed - trying second ESC")
            pyautogui.press('escape')
            time.sleep(1.0)

            # Final check
            events_detected = self._quick_events_button_check()
            if events_detected:
                self.logger.info("[UI_RECOVERY] Second ESC successful - events button restored")
                return True
            else:
                self.logger.error("[UI_RECOVERY] Both ESC attempts failed - UI may be stuck")
                return False

        except Exception as e:
            self.logger.error(f"[UI_RECOVERY] Error during UI state check: {str(e)}")
            return False

    def _quick_events_button_check(self) -> bool:
        """
        Quick check for events button presence
        Returns True if events button is detected
        """
        try:
            # Use the screen scanner to quickly check for events button
            if hasattr(self, 'screen_scanner') and self.screen_scanner:
                # Quick template match for events button only
                templates_to_check = ['events_button']
                screen_data = self.screen_scanner.scan_screen_cache_optimized(templates_to_check)

                if 'templates_detected' in screen_data:
                    return screen_data['templates_detected'].get('events_button', False)

            return False

        except Exception as e:
            self.logger.error(f"[UI_CHECK] Error during quick events button check: {str(e)}")
            return False

    def _check_mouse_movement(self) -> bool:
        """
        Check for physical mouse movement and trigger auto-pause if detected
        Returns True if mouse movement detected and pause triggered
        """
        try:
            if not self.mouse_detection_enabled:
                return False

            # Skip mouse detection during module execution to avoid false positives
            if self.module_executing:
                self.logger.debug("[MOUSE_OVERRIDE] Skipping detection - module executing")
                return False

            # Skip mouse detection for 1 second after module execution ends
            current_time = time.time()
            if self.module_end_time > 0 and current_time < self.module_end_time + 1.0:
                self.logger.debug("[MOUSE_OVERRIDE] Skipping detection - in post-execution cooldown")
                return False

            import pyautogui
            current_mouse_pos = pyautogui.position()

            # Initialize last position if first check
            if self.last_mouse_pos is None:
                self.last_mouse_pos = current_mouse_pos
                return False

            # Check if mouse has moved significantly (more than 10 pixels to reduce false positives)
            distance = ((current_mouse_pos.x - self.last_mouse_pos.x) ** 2 +
                       (current_mouse_pos.y - self.last_mouse_pos.y) ** 2) ** 0.5

            if distance > 10:  # Increased threshold from 5 to 10 pixels
                self.logger.info(f"[MOUSE_OVERRIDE] Human mouse movement detected - pausing automation for {self.mouse_pause_duration}s")
                self.mouse_pause_end_time = time.time() + self.mouse_pause_duration
                self.last_mouse_pos = current_mouse_pos
                return True

            # Update last position
            self.last_mouse_pos = current_mouse_pos
            return False

        except Exception as e:
            self.logger.error(f"[MOUSE_OVERRIDE] Error checking mouse movement: {str(e)}")
            return False

    def _is_mouse_paused(self) -> bool:
        """
        Check if automation should be paused due to mouse movement
        Returns True if currently in mouse pause period
        """
        if self.mouse_pause_end_time > 0 and time.time() < self.mouse_pause_end_time:
            return True
        elif self.mouse_pause_end_time > 0 and time.time() >= self.mouse_pause_end_time:
            # Pause period ended
            self.mouse_pause_end_time = 0
            self.logger.info("[MOUSE_OVERRIDE] Mouse pause period ended - resuming automation")
            return False
        return False
    
    def _main_loop(self):
        """Main execution loop"""
        self.logger.info("Starting main execution loop")
        
        while self.running:
            try:
                # Check if paused (manual or mouse override)
                if self.paused:
                    time.sleep(0.5)  # Sleep while paused
                    continue

                # Check for mouse movement override
                if self._check_mouse_movement() or self._is_mouse_paused():
                    time.sleep(0.5)  # Sleep while mouse override is active
                    continue

                # Memory management and crash prevention
                if not self._check_memory_usage():
                    self.logger.error("[MEMORY] Stopping due to memory issues")
                    break

                # CENTRALIZED SCANNER: New architecture with template-to-module mapping
                if self.use_centralized_scanner:
                    # Log first scan to confirm centralized scanner is active
                    if self.total_scans == 0:
                        self.logger.info("[SCANNER] Centralized scanner active - starting template detection")
                    scan_start = time.time()

                    # Use centralized template scanner
                    result = self.centralized_scanner.scan_and_execute()
                    scan_time = time.time() - scan_start

                    if result:
                        template_name, module_name, success = result
                        self.total_executions += 1
                        status = "SUCCESS" if success else "FAILED"
                        self.logger.info(f"[{status}] Centralized: {template_name} -> {module_name} ({scan_time:.2f}s)")
                    else:
                        # Show scanning activity every 10 scans to confirm it's working
                        self.total_scans += 1
                        if self.total_scans % 10 == 0:
                            self.logger.info(f"[SCAN] Centralized scan: {self.total_scans} scans completed, no actions needed ({scan_time:.2f}s)")

                    # Continue to next iteration
                    time.sleep(self.scan_interval)
                    continue

                # LEGACY SCANNING: Original architecture (fallback)
                scan_start = time.time()

                # Get list of enabled modules sorted by priority
                enabled_modules = [m for m in self.modules if m.enabled]
                enabled_modules.sort(key=lambda x: x.priority)

                if not enabled_modules:
                    # No modules enabled - minimal scan
                    screen_data = self.screen_scanner.scan_screen_fast()
                    scan_time = time.time() - scan_start
                    self.logger.debug(f"Minimal scan completed in {scan_time:.4f}s")
                else:
                    # Collect required templates from all enabled modules
                    required_templates = set()

                    # Always include events_button for UI state checking
                    required_templates.add('events_button')

                    # Always include quit_game_dialog for quit detection
                    required_templates.add('quit_game_dialog')

                    for module in enabled_modules:
                        # Get templates needed by this module
                        if hasattr(module, 'get_required_templates'):
                            module_templates = module.get_required_templates()
                            required_templates.update(module_templates)
                        else:
                            # Default templates for modules that don't specify
                            if module.name == "zombie_invasion":
                                required_templates.update(['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4', 'squad_4_4', 'events_button'])
                            elif module.name == "alliance_donation":
                                required_templates.update(['events_button'])
                            elif module.name == "daily_tasks":
                                required_templates.update(['events_button'])
                            elif module.name == "help_click":
                                # help_click needs templates to detect help requests!
                                required_templates.update(['Help_template', 'Help_chat'])

                    # Special case: help_click gets ultra-fast path
                    help_click_enabled = any(m.name == "help_click" and m.enabled and m.priority == 0 for m in enabled_modules)

                    if help_click_enabled and len(required_templates) == 0:
                        # ULTRA-FAST PATH: help_click only, no templates needed
                        screen_data = {
                            'timestamp': time.time(),
                            'screenshot': None,
                            'templates_detected': {},  # No templates to detect
                            'templates_found': {'events_button_detected': True},
                            'text_regions': {},
                            'screen_size': (1920, 1080)
                        }
                        scan_time = time.time() - scan_start
                        self.logger.debug(f"[CACHE] Ultra-fast cache: {scan_time:.4f}s (help_click only, no templates)")
                    else:
                        # CACHE-OPTIMIZED PATH: One screenshot to memory, analyze all templates
                        screen_data = self.screen_scanner.scan_screen_cache_optimized(list(required_templates))
                        scan_time = time.time() - scan_start
                        self.logger.debug(f"[CACHE] Cache-optimized scan: {scan_time:.4f}s ({len(required_templates)} templates)")
                
                self.total_scans += 1
                
                if 'error' in screen_data:
                    self.logger.error(f"Screen scan failed: {screen_data['error']}")
                    time.sleep(self.scan_interval)
                    continue
                
                self.logger.debug(f"Screen scan completed in {scan_time:.2f}s")

                # EFFICIENT DEBUG: Check UI state during main scan (not after module execution)
                self._check_ui_state_during_scan(screen_data)

                # REVOLUTIONARY PRIORITY EXECUTION: Your brilliant cache-based approach!
                executed_module = False
                templates_detected = screen_data.get('templates_detected', {})

                # Sort enabled modules by priority (0 = highest priority)
                enabled_modules = [m for m in self.modules if m.enabled]
                enabled_modules.sort(key=lambda x: x.priority)

                self.logger.debug(f"[PRIORITY] Priority execution: {len(enabled_modules)} modules, templates: {templates_detected}")

                # Execute highest priority module that can run based on cache results
                for module in enabled_modules:
                    # Check if module can run based on cached template detection
                    if module.can_run(screen_data):
                        # Set module execution flag to prevent false mouse detection
                        self.module_executing = True

                        execution_start = time.time()

                        # Log which templates this module detected
                        if hasattr(module, 'get_required_templates'):
                            required = module.get_required_templates()
                            detected_templates = [t for t in required if templates_detected.get(t, False)]
                            self.logger.debug(f"[MODULE] {module.name}: Required {required}, Detected {detected_templates}")

                        success = module.run(screen_data)
                        execution_time = time.time() - execution_start

                        # Clear module execution flag and record end time for 1-second cooldown
                        self.module_executing = False
                        self.module_end_time = time.time()  # Record when module ended for 1-second cooldown

                        if success:
                            self.total_executions += 1
                            executed_module = True
                            self.last_module_execution_time = time.time()  # Track for UI recovery skip
                            self.logger.info(f"[SUCCESS] Module {module.name} (priority {module.priority}) executed in {execution_time:.4f}s")

                            # Update GUI statistics counter
                            if hasattr(self, 'gui') and self.gui and hasattr(self.gui, 'update_module_stats'):
                                self.gui.update_module_stats(module.name)

                            break  # Execute highest priority module only
                        else:
                            self.logger.warning(f"[FAILED] Module {module.name} execution failed")
                
                if not executed_module:
                    self.logger.debug("No modules executed this cycle")
                
                # Wait before next scan
                time.sleep(self.scan_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt, stopping...")
                self.stop()
                break
            except Exception as e:
                self.logger.error(f"[ERROR] Error in main loop: {str(e)}")
                self.logger.error(f"[ERROR] Error type: {type(e).__name__}")

                # Enhanced error recovery for overnight stability
                try:
                    import traceback
                    self.logger.error(f"[ERROR] Full traceback: {traceback.format_exc()}")
                except:
                    pass

                # Force garbage collection after error
                try:
                    import gc
                    collected = gc.collect()
                    self.logger.info(f"[CLEANUP] Emergency cleanup: {collected} objects collected")
                except:
                    pass

                # Longer wait for recovery
                self.logger.info("[RECOVERY] Waiting 10 seconds for recovery...")
                time.sleep(10)
    
    def start(self):
        """Start the main controller"""
        if self.running:
            self.logger.warning("Controller is already running")
            return
        
        self.running = True
        self.start_time = time.time()
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
        self.logger.info("MainController started")

    def pause(self):
        """Pause the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return

        self.paused = True
        self.logger.info("MainController paused")

    def resume(self):
        """Resume the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return

        self.paused = False
        self.logger.info("MainController resumed")

    def _check_memory_usage(self) -> bool:
        """Check memory usage and perform cleanup if needed"""
        try:
            import psutil
            import gc

            # Get current memory usage
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # Check if cleanup is needed
            current_time = time.time()
            if current_time - self.last_memory_cleanup > self.memory_cleanup_interval:
                self.logger.info(f"[CLEANUP] Performing memory cleanup - Current usage: {memory_mb:.1f}MB")

                # Force garbage collection
                collected = gc.collect()

                # Clear screen scanner template cache periodically
                if hasattr(self.screen_scanner, 'templates'):
                    template_count = len(self.screen_scanner.templates)
                    # Don't clear all templates, just reload them to free fragmented memory
                    self.screen_scanner.templates = self.screen_scanner._load_templates()
                    self.logger.info(f"[MEMORY] Reloaded {template_count} templates to defragment memory")

                # Clean up any old screenshot files
                if hasattr(self.screen_scanner, 'cleanup_old_screenshots'):
                    self.screen_scanner.cleanup_old_screenshots()

                # Update cleanup time
                self.last_memory_cleanup = current_time

                # Check memory after cleanup
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                self.logger.info(f"[SUCCESS] Memory cleanup complete - {collected} objects collected, "
                               f"Memory: {memory_mb:.1f}MB -> {new_memory_mb:.1f}MB")

                memory_mb = new_memory_mb

            # Check if memory is too high (crash prevention)
            if memory_mb > self.max_memory_mb:
                self.logger.error(f"[MEMORY] MEMORY CRITICAL: {memory_mb:.1f}MB > {self.max_memory_mb}MB limit!")
                self.logger.error("[MEMORY] Stopping automation to prevent crash - please restart application")
                return False

            return True

        except ImportError:
            # psutil not available, skip memory checks
            return True
        except Exception as e:
            self.logger.error(f"Error checking memory usage: {str(e)}")
            return True

    def stop(self):
        """Stop the main controller"""
        if not self.running:
            self.logger.warning("Controller is not running")
            return
        
        self.running = False
        self.paused = False

        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=5.0)

        self.logger.info("MainController stopped")

    def enable_mouse_detection(self):
        """Enable mouse movement detection for auto-pause"""
        self.mouse_detection_enabled = True
        self.logger.info("[MOUSE_OVERRIDE] Mouse movement detection enabled")

    def disable_mouse_detection(self):
        """Disable mouse movement detection"""
        self.mouse_detection_enabled = False
        self.mouse_pause_end_time = 0  # Clear any active pause
        self.logger.info("[MOUSE_OVERRIDE] Mouse movement detection disabled")

    def set_mouse_pause_duration(self, duration: float):
        """Set the duration for mouse movement pause"""
        self.mouse_pause_duration = max(1.0, duration)  # Minimum 1 second
        self.logger.info(f"[MOUSE_OVERRIDE] Mouse pause duration set to {self.mouse_pause_duration}s")

    def reload_config(self):
        """Reload configuration from file"""
        self.config.read("config.ini")
        self.scan_interval = self.config.getfloat('GENERAL', 'scan_interval', fallback=2.0)
        self.priority_check_interval = self.config.getfloat('GENERAL', 'priority_check_interval', fallback=1.0)
        self.logger.info("Configuration reloaded")

    def get_module_by_name(self, module_name):
        """Get a module by its name"""
        for module in self.modules:
            if hasattr(module, 'name') and module.name == module_name:
                return module
        return None
    
    def save_config(self):
        """Save current configuration to file"""
        with open("config.ini", 'w') as configfile:
            self.config.write(configfile)
        self.logger.info("Configuration saved")

    def get_status_info(self) -> Dict[str, Any]:
        """Get current status information for GUI display"""
        status = {
            'running': self.running,
            'paused': self.paused,
            'total_scans': self.total_scans,
            'total_executions': self.total_executions,
            'uptime': time.time() - self.start_time if self.start_time else 0,
            'scanner_mode': 'Centralized' if self.use_centralized_scanner else 'Legacy',
            'modules': [
                {
                    'name': module.name,
                    'enabled': module.enabled,
                    'priority': module.priority,
                    'last_execution': getattr(module, 'last_execution_time', 0),
                    'total_executions': getattr(module, 'total_executions', 0),
                    'success_rate': getattr(module, 'success_rate', 0),
                    'current_activity': getattr(module, 'current_activity', 'Idle'),
                    'waiting_for': getattr(module, 'waiting_for', 'N/A')
                }
                for module in self.modules
            ]
        }

        # Add centralized scanner status if enabled
        if self.use_centralized_scanner:
            status['centralized_scanner'] = self.centralized_scanner.get_status_info()

        return status

    def toggle_scanner_mode(self):
        """Toggle between centralized and legacy scanner modes"""
        self.use_centralized_scanner = not self.use_centralized_scanner
        mode = "Centralized" if self.use_centralized_scanner else "Legacy"
        self.logger.info(f"[SCANNER] Switched to {mode} scanner mode")
        return self.use_centralized_scanner

    def get_centralized_scanner(self):
        """Get the centralized scanner instance for GUI configuration"""
        return self.centralized_scanner

    def get_module_by_name(self, name: str) -> Optional[BaseModule]:
        """Get module by name"""
        for module in self.modules:
            if module.name == name:
                return module
        return None

    def emergency_stop(self):
        """Emergency stop - immediately halt all operations"""
        self.logger.warning("Emergency stop activated!")
        self.running = False
        # Force stop all modules if they have a stop method
        for module in self.modules:
            if hasattr(module, 'emergency_stop'):
                try:
                    module.emergency_stop()
                except Exception as e:
                    self.logger.error(f"Error during emergency stop of {module.name}: {str(e)}")
