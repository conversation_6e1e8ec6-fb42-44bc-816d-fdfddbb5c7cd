#!/usr/bin/env python3
"""
Test script to actually execute ESC key presses and verify they work
"""

import pyautogui
import time
import sys

def test_esc_key_execution():
 """Test actual ESC key execution"""
 print(" Testing ESC key execution...")
 print(" This will actually press the ESC key in 3 seconds!")
 print("Make sure you have a window open that responds to ESC key.")
 
 # Countdown
 for i in range(3, 0, -1):
 print(f" {i}...")
 time.sleep(1)
 
 print(" Pressing ESC key now!")
 
 try:
 # Press ESC key
 pyautogui.press('esc')
 print(" ESC key pressed successfully!")
 
 # Wait a moment
 time.sleep(1)
 
 # Press ESC again to test multiple presses
 print(" Pressing ESC key again...")
 pyautogui.press('esc')
 print(" Second ESC key pressed successfully!")
 
 except Exception as e:
 print(f" Error pressing ESC key: {str(e)}")
 return False
 
 return True

def test_click_vs_esc():
 """Test the difference between clicking and pressing ESC"""
 print("\n Testing click vs ESC execution...")
 
 # Test coordinates (safe area)
 test_x, test_y = 100, 100
 
 print(" This will perform a click and ESC key press in 3 seconds!")
 
 # Countdown
 for i in range(3, 0, -1):
 print(f" {i}...")
 time.sleep(1)
 
 try:
 # Test click
 print(f" Clicking at ({test_x}, {test_y})")
 pyautogui.click(test_x, test_y)
 time.sleep(0.5)
 
 # Test ESC
 print(" Pressing ESC key")
 pyautogui.press('esc')
 time.sleep(0.5)
 
 print(" Both click and ESC executed successfully!")
 return True
 
 except Exception as e:
 print(f" Error during execution: {str(e)}")
 return False

def simulate_alliance_donation_esc_steps():
 """Simulate the alliance donation ESC steps"""
 print("\n Simulating alliance donation ESC steps...")
 print(" This will simulate steps 6, 11, and 12 of alliance donation!")
 print("These steps use ESC key instead of clicking.")
 
 # ESC steps from alliance donation
 esc_steps = [
 (6, "6_exit", True),
 (11, "exit: Close menu with ESC key", True),
 (12, "secexit: Final close with ESC key", True)
 ]
 
 print(" Starting simulation in 3 seconds...")
 
 # Countdown
 for i in range(3, 0, -1):
 print(f" {i}...")
 time.sleep(1)
 
 try:
 for step_num, description, use_esc_key in esc_steps:
 print(f"📍 Step {step_num}: {description}")
 
 if use_esc_key:
 print(f" Pressing ESC key")
 pyautogui.press('esc')
 else:
 print(f" Would click (but this step uses ESC)")
 
 print(f" Waiting 1 second...")
 time.sleep(1)
 
 print(" All ESC steps executed successfully!")
 return True
 
 except Exception as e:
 print(f" Error during simulation: {str(e)}")
 return False

if __name__ == "__main__":
 print(" ESC Key Execution Test")
 print("=" * 50)
 
 # Ask user if they want to proceed
 response = input("Do you want to test actual ESC key presses? (y/n): ").lower().strip()
 
 if response == 'y' or response == 'yes':
 print("\n Starting ESC key tests...")
 
 # Test 1: Basic ESC key execution
 test1_result = test_esc_key_execution()
 
 # Test 2: Click vs ESC comparison
 test2_result = test_click_vs_esc()
 
 # Test 3: Alliance donation simulation
 test3_result = simulate_alliance_donation_esc_steps()
 
 print("\n" + "=" * 50)
 print(" Test Results:")
 print(f" Basic ESC test: {'PASSED' if test1_result else 'FAILED'}")
 print(f" Click vs ESC test: {'PASSED' if test2_result else 'FAILED'}")
 print(f" Alliance donation simulation: {'PASSED' if test3_result else 'FAILED'}")
 
 if all([test1_result, test2_result, test3_result]):
 print(" All ESC key tests PASSED!")
 else:
 print(" Some ESC key tests FAILED!")
 else:
 print(" Skipping actual ESC key execution tests.")
 print(" Use this script with 'y' to test actual ESC key presses.")
