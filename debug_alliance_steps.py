#!/usr/bin/env python3
"""
Debug script to check which alliance donation steps are enabled/disabled
"""

import sys
import os
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.alliance_donation import AllianceDonationModule

def debug_alliance_steps():
    """Debug which steps are enabled and which use ESC key"""
    print("🔍 Debugging Alliance Donation Steps")
    print("=" * 60)
    
    # Load raw config
    with open('module_configs.json', 'r') as f:
        config = json.load(f)
    
    alliance_config = config.get('alliance_donation', {})
    click_coordinates = alliance_config.get('click_coordinates', [])
    
    print(f"📋 Raw configuration has {len(click_coordinates)} coordinates:")
    print()
    
    for i, coord in enumerate(click_coordinates):
        name = coord.get('name', 'Unknown')
        enabled = coord.get('enabled', True)  # Default to True if not specified
        use_esc_key = coord.get('use_esc_key', False)
        description = coord.get('description', '')
        
        status = "✅ ENABLED" if enabled else "❌ DISABLED"
        action = "🔑 ESC KEY" if use_esc_key else "🖱️ CLICK"
        
        print(f"Step {i+1:2d}: {name:15s} | {status} | {action} | {description}")
    
    print("\n" + "=" * 60)
    
    # Create module and check loaded sequence
    print("🔧 Creating alliance donation module...")
    module = AllianceDonationModule()
    
    sequence = module.donation_click_sequence
    print(f"📋 Module loaded {len(sequence)} steps:")
    print()
    
    for i, step_data in enumerate(sequence):
        if len(step_data) == 6:
            x, y, delay, repeat_count, description, use_esc_key = step_data
            action = "🔑 ESC KEY" if use_esc_key else f"🖱️ CLICK ({x}, {y})"
            print(f"Step {i+1:2d}: {description[:50]:50s} | {action}")
        else:
            print(f"Step {i+1:2d}: {step_data} | ⚠️ UNEXPECTED FORMAT")
    
    print("\n" + "=" * 60)
    
    # Count ESC steps
    esc_steps = []
    click_steps = []
    
    for i, step_data in enumerate(sequence):
        if len(step_data) >= 6 and step_data[5]:  # use_esc_key is True
            esc_steps.append(i+1)
        else:
            click_steps.append(i+1)
    
    print("📊 Summary:")
    print(f"🔑 ESC key steps: {esc_steps} (Total: {len(esc_steps)})")
    print(f"🖱️ Click steps: {click_steps} (Total: {len(click_steps)})")
    
    if len(esc_steps) == 0:
        print("❌ NO ESC KEY STEPS FOUND! This is the problem!")
    elif len(esc_steps) < 3:
        print("⚠️ Expected 3 ESC key steps, but only found", len(esc_steps))
    else:
        print("✅ ESC key steps found correctly")

def debug_step_execution_order():
    """Debug the step execution order"""
    print("\n" + "=" * 60)
    print("🔍 Debugging Step Execution Order")
    print("=" * 60)
    
    # Load raw config to see the order
    with open('module_configs.json', 'r') as f:
        config = json.load(f)
    
    alliance_config = config.get('alliance_donation', {})
    click_coordinates = alliance_config.get('click_coordinates', [])
    
    print("📋 Steps in configuration file order:")
    
    enabled_steps = []
    disabled_steps = []
    
    for i, coord in enumerate(click_coordinates):
        name = coord.get('name', 'Unknown')
        enabled = coord.get('enabled', True)  # Default to True if not specified
        use_esc_key = coord.get('use_esc_key', False)
        
        if enabled:
            enabled_steps.append((i+1, name, use_esc_key))
        else:
            disabled_steps.append((i+1, name, use_esc_key))
    
    print(f"\n✅ ENABLED steps ({len(enabled_steps)}):")
    for step_num, name, use_esc in enabled_steps:
        action = "🔑 ESC" if use_esc else "🖱️ CLICK"
        print(f"  {step_num:2d}. {name:15s} | {action}")
    
    if disabled_steps:
        print(f"\n❌ DISABLED steps ({len(disabled_steps)}):")
        for step_num, name, use_esc in disabled_steps:
            action = "🔑 ESC" if use_esc else "🖱️ CLICK"
            print(f"  {step_num:2d}. {name:15s} | {action}")
    
    print(f"\n📊 Expected execution order:")
    execution_order = [step for step in enabled_steps]
    for i, (step_num, name, use_esc) in enumerate(execution_order):
        action = "🔑 ESC" if use_esc else "🖱️ CLICK"
        print(f"  Execution {i+1:2d}: {name:15s} | {action}")

if __name__ == "__main__":
    try:
        debug_alliance_steps()
        debug_step_execution_order()
        
        print("\n" + "=" * 60)
        print("🎯 Debug Complete!")
        print("If ESC key steps are missing or in wrong order, that's the issue.")
        
    except Exception as e:
        print(f"❌ Debug failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
