#!/usr/bin/env python3
"""
Test script to manually trigger alliance donation ESC steps
"""

import sys
import os
import time
import pya<PERSON><PERSON><PERSON>

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.alliance_donation import AllianceDonationModule

def test_alliance_donation_esc_execution():
 """Test actual execution of alliance donation ESC steps"""
 print(" Testing Alliance Donation ESC Key Execution")
 print("=" * 50)
 
 # Create alliance donation module
 print(" Creating alliance donation module...")
 module = AllianceDonationModule()
 
 # Get the sequence
 sequence = module.donation_click_sequence
 print(f" Loaded sequence with {len(sequence)} steps")
 
 # Find ESC steps
 esc_steps = []
 for i, step_data in enumerate(sequence):
 if len(step_data) >= 6 and step_data[5]: # use_esc_key is True
 esc_steps.append((i, step_data))
 
 print(f" Found {len(esc_steps)} ESC steps:")
 for i, (step_idx, step_data) in enumerate(esc_steps):
 x, y, delay, repeat_count, description, use_esc_key = step_data
 print(f" Step {step_idx + 1}: {description}")
 
 # Ask user if they want to test
 print("\n This will actually press ESC keys!")
 print("Make sure you have a window open that responds to ESC.")
 response = input("Do you want to test ESC key execution? (y/n): ").lower().strip()
 
 if response != 'y' and response != 'yes':
 print(" Skipping ESC key execution test.")
 return
 
 print("\n Starting ESC key execution test in 3 seconds...")
 
 # Countdown
 for i in range(3, 0, -1):
 print(f" {i}...")
 time.sleep(1)
 
 print("\n Executing ESC steps...")
 
 try:
 for i, (step_idx, step_data) in enumerate(esc_steps):
 x, y, delay, repeat_count, description, use_esc_key = step_data
 
 print(f"\n📍 Step {step_idx + 1}: {description}")
 print(f" use_esc_key = {use_esc_key}")
 print(f" repeat_count = {repeat_count}")
 print(f" delay = {delay}")
 
 # Execute the step using the same logic as the module
 for repeat_num in range(repeat_count):
 if use_esc_key:
 if repeat_count > 1:
 print(f" ESC key press {repeat_num+1}/{repeat_count}")
 else:
 print(f" Pressing ESC key")
 
 # Actually press ESC key
 pyautogui.press('esc')
 print(" ESC key pressed!")
 else:
 print(f" ERROR: Step marked as ESC but use_esc_key is False!")
 
 # Inter-repeat delay
 if repeat_count > 1 and repeat_num < repeat_count - 1:
 repeat_delay = max(0.01, delay / 2)
 print(f"⚡ Inter-ESC delay: {repeat_delay}s")
 time.sleep(repeat_delay)
 
 # Step delay
 if delay > 0:
 print(f" Waiting {delay} seconds...")
 time.sleep(delay)
 
 print("\n All ESC steps executed successfully!")
 
 except Exception as e:
 print(f"\n Error during execution: {str(e)}")
 import traceback
 traceback.print_exc()

def test_manual_esc_vs_module_esc():
 """Compare manual ESC press vs module ESC press"""
 print("\n Testing Manual ESC vs Module ESC")
 print("=" * 50)
 
 response = input("Do you want to compare manual vs module ESC? (y/n): ").lower().strip()
 
 if response != 'y' and response != 'yes':
 print(" Skipping comparison test.")
 return
 
 print("\n This will press ESC key twice with 2 second delay!")
 print("Starting in 3 seconds...")
 
 # Countdown
 for i in range(3, 0, -1):
 print(f" {i}...")
 time.sleep(1)
 
 try:
 # Manual ESC press
 print("\n1️⃣ Manual ESC press:")
 print(" Pressing ESC manually...")
 pyautogui.press('esc')
 print(" Manual ESC pressed!")
 
 time.sleep(2)
 
 # Module-style ESC press (same code as in alliance donation)
 print("\n2️⃣ Module-style ESC press:")
 print(" Pressing ESC using module logic...")
 pyautogui.press('esc')
 print(" Module-style ESC pressed!")
 
 print("\n Both ESC methods executed successfully!")
 print("If you didn't see ESC key effects, there might be an issue with:")
 print(" - No active window that responds to ESC")
 print(" - PyAutoGUI ESC key not working")
 print(" - System blocking automated key presses")
 
 except Exception as e:
 print(f"\n Error during comparison: {str(e)}")
 import traceback
 traceback.print_exc()

if __name__ == "__main__":
 print(" Alliance Donation ESC Key Test")
 print("=" * 50)
 
 try:
 # Test 1: ESC step execution
 test_alliance_donation_esc_execution()
 
 # Test 2: Manual vs module comparison
 test_manual_esc_vs_module_esc()
 
 print("\n" + "=" * 50)
 print(" Test Complete!")
 print("If ESC keys were pressed but you didn't see effects:")
 print(" 1. Make sure a window is open that responds to ESC")
 print(" 2. Check if PyAutoGUI is working correctly")
 print(" 3. Verify system allows automated key presses")
 
 except Exception as e:
 print(f" Test failed with error: {str(e)}")
 import traceback
 traceback.print_exc()
