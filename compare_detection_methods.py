#!/usr/bin/env python3
"""
Compare detection methods between debug tool and module
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def debug_tool_method(x, y, size=20):
    """Exact method from debug tool"""
    print(f"\n=== DEBUG TOOL METHOD ===")
    print(f"Coordinate: ({x}, {y}), Size: {size}x{size}")
    
    # Capture region (debug tool method)
    capture_x = x - size // 2
    capture_y = y - size // 2
    screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
    image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Save debug image
    debug_filename = f"debug_tool_method_{x}_{y}_{int(time.time())}.png"
    cv2.imwrite(debug_filename, gray)
    print(f"Saved: {debug_filename}")
    
    # OCR detection
    config = '--psm 8 -c tessedit_char_whitelist=0123456789'
    text = pytesseract.image_to_string(gray, config=config).strip()
    
    print(f"OCR raw result: '{text}' (length: {len(text)})")
    
    if text.isdigit():
        result = int(text)
        print(f"SUCCESS: {result}")
        return result
    else:
        print(f"FAILED: '{text}' (not digit)")
        return None

def module_method(x, y, size=20):
    """Exact method from module"""
    print(f"\n=== MODULE METHOD ===")
    print(f"Coordinate: ({x}, {y}), Size: {size}x{size}")
    
    # Capture region (module method)
    capture_x = x - size // 2
    capture_y = y - size // 2
    screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
    image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Save debug image
    debug_filename = f"module_method_{x}_{y}_{int(time.time())}.png"
    cv2.imwrite(debug_filename, gray)
    print(f"Saved: {debug_filename}")
    
    # Use direct OCR on the entire region
    config = '--psm 8 -c tessedit_char_whitelist=0123456789'
    text = pytesseract.image_to_string(gray, config=config).strip()
    
    print(f"OCR raw result: '{text}' (length: {len(text)})")
    
    if text.isdigit():
        result = int(text)
        print(f"SUCCESS: {result}")
        return result
    else:
        print(f"FAILED: '{text}' (not digit)")
        return None

def compare_images(x, y, size=20):
    """Compare the actual captured images"""
    print(f"\n=== IMAGE COMPARISON ===")
    
    # Debug tool capture
    capture_x = x - size // 2
    capture_y = y - size // 2
    screenshot1 = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
    image1 = cv2.cvtColor(np.array(screenshot1), cv2.COLOR_RGB2BGR)
    gray1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
    
    time.sleep(0.1)  # Small delay
    
    # Module capture
    screenshot2 = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
    image2 = cv2.cvtColor(np.array(screenshot2), cv2.COLOR_RGB2BGR)
    gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)
    
    # Save both
    cv2.imwrite(f"compare_debug_{x}_{y}.png", gray1)
    cv2.imwrite(f"compare_module_{x}_{y}.png", gray2)
    
    # Check if identical
    diff = cv2.absdiff(gray1, gray2)
    total_diff = np.sum(diff)
    
    print(f"Image difference: {total_diff}")
    if total_diff == 0:
        print("Images are IDENTICAL")
    else:
        print("Images are DIFFERENT")
        cv2.imwrite(f"compare_diff_{x}_{y}.png", diff)

def test_m1_coordinate():
    """Test M1 coordinate specifically"""
    x, y = 1083, 843
    size = 20
    
    print("TESTING M1 COORDINATE")
    print("=" * 50)
    
    # Compare images first
    compare_images(x, y, size)
    
    # Test debug tool method
    debug_result = debug_tool_method(x, y, size)
    
    time.sleep(0.5)  # Small delay
    
    # Test module method
    module_result = module_method(x, y, size)
    
    print(f"\n=== RESULTS COMPARISON ===")
    print(f"Debug tool result: {debug_result}")
    print(f"Module result: {module_result}")
    
    if debug_result == module_result:
        print("RESULTS MATCH!")
    else:
        print("RESULTS DIFFER!")
        print("This indicates a difference in the detection methods")

if __name__ == "__main__":
    print("Map Detection Method Comparison")
    print("Make sure the game is visible and ready")
    input("Press ENTER to start test...")
    
    test_m1_coordinate()
