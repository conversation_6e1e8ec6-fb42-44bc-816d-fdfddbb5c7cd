#!/usr/bin/env python3
"""
Test script to execute only the ESC key steps from alliance donation
"""

import sys
import os
import time
import pya<PERSON>gu<PERSON>

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.alliance_donation import AllianceDonationModule

def test_esc_steps_only():
    """Test executing only the ESC key steps"""
    print("🔑 Testing ESC Key Steps Only")
    print("=" * 50)
    
    # Create alliance donation module
    module = AllianceDonationModule()
    sequence = module.donation_click_sequence
    
    # Find ESC steps
    esc_steps = []
    for i, step_data in enumerate(sequence):
        if len(step_data) >= 6 and step_data[5]:  # use_esc_key is True
            esc_steps.append((i, step_data))
    
    print(f"🔍 Found {len(esc_steps)} ESC steps:")
    for i, (step_idx, step_data) in enumerate(esc_steps):
        x, y, delay, repeat_count, description, use_esc_key = step_data
        print(f"  ESC Step {i+1}: Step {step_idx+1} - {description}")
    
    # Ask user if they want to test
    print(f"\n⚠️ This will press ESC key {len(esc_steps)} times with delays!")
    print("Make sure you have a window open that responds to ESC.")
    response = input("Do you want to test ESC key steps? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("⏭️ Skipping ESC key test.")
        return
    
    print(f"\n🎯 Starting ESC key test in 3 seconds...")
    print("You should see 3 separate ESC key presses...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    print("\n🚀 Executing ESC steps...")
    
    try:
        for i, (step_idx, step_data) in enumerate(esc_steps):
            x, y, delay, repeat_count, description, use_esc_key = step_data
            
            print(f"\n📍 ESC Step {i+1}/{len(esc_steps)}: {description}")
            print(f"   Original step number: {step_idx+1}")
            print(f"   use_esc_key: {use_esc_key}")
            print(f"   repeat_count: {repeat_count}")
            print(f"   delay: {delay}s")
            
            # Execute the step using the same logic as the module
            for repeat_num in range(repeat_count):
                if use_esc_key:
                    if repeat_count > 1:
                        print(f"⌨️ ESC key press {repeat_num+1}/{repeat_count}")
                    else:
                        print(f"⌨️ Pressing ESC key")
                    
                    # Actually press ESC key
                    print(f"🔑 DEBUG: About to press ESC key using pyautogui.press('esc')")
                    pyautogui.press('esc')
                    print(f"✅ DEBUG: ESC key press completed successfully")
                else:
                    print(f"❌ ERROR: Step marked as ESC but use_esc_key is False!")
                
                # Inter-repeat delay
                if repeat_count > 1 and repeat_num < repeat_count - 1:
                    repeat_delay = max(0.01, delay / 2)
                    print(f"⚡ Inter-ESC delay: {repeat_delay}s")
                    time.sleep(repeat_delay)
            
            # Step delay
            if delay > 0:
                print(f"⏳ Waiting {delay} seconds before next ESC step...")
                time.sleep(delay)
        
        print(f"\n✅ All {len(esc_steps)} ESC steps executed successfully!")
        print("If you only saw 1 ESC effect, the issue might be:")
        print("  1. No menu/dialog open to close")
        print("  2. Game window not in focus")
        print("  3. ESC key not affecting the game")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {str(e)}")
        import traceback
        traceback.print_exc()

def test_single_esc_press():
    """Test a single ESC key press for comparison"""
    print("\n" + "=" * 50)
    print("🔑 Testing Single ESC Key Press")
    print("=" * 50)
    
    response = input("Do you want to test a single ESC press? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("⏭️ Skipping single ESC test.")
        return
    
    print("\n⚠️ This will press ESC key once in 3 seconds!")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    try:
        print("⌨️ Pressing ESC key once...")
        print("🔑 DEBUG: About to press ESC key using pyautogui.press('esc')")
        pyautogui.press('esc')
        print("✅ DEBUG: Single ESC key press completed successfully")
        
        print("\nIf this single ESC press worked but the 3-step test didn't,")
        print("then the issue is with the step execution flow, not ESC key itself.")
        
    except Exception as e:
        print(f"\n❌ Error during single ESC press: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 ESC Key Steps Test")
    print("=" * 50)
    
    try:
        # Test 1: Execute all ESC steps
        test_esc_steps_only()
        
        # Test 2: Single ESC press for comparison
        test_single_esc_press()
        
        print("\n" + "=" * 50)
        print("📊 Test Complete!")
        print("Compare the results to understand the issue.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
