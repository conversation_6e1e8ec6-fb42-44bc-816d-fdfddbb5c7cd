#!/usr/bin/env python3
"""
Test Simple Map Trade Logic - Your exact approach
"""

import cv2
import numpy as np
import pyautogui
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def read_quantity_at_coordinate(x, y, size=35):
    """Read quantity number at coordinate with larger region for multi-digit numbers"""
    try:
        # Capture region around coordinate (35x35 works better for multi-digit numbers)
        region_x = x - size // 2
        region_y = y - size // 2

        screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Direct OCR (the working method)
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()

        if text.isdigit():
            return int(text)
        else:
            return None

    except Exception as e:
        print(f"Error reading coordinate ({x}, {y}): {e}")
        return None

def test_simple_logic():
    """Your exact logic: coordinate → read → assign → find min/max → click"""
    print("Simple Map Trade Logic Test")
    print("=" * 40)
    
    # YOUR CALIBRATED COORDINATES (M5 and M6 corrected)
    map_pieces = {
        'M1': {'quantity_coord': (1068, 817), 'click_coord': (1026, 765)},
        'M2': {'quantity_coord': (1224, 817), 'click_coord': (1188, 772)},
        'M3': {'quantity_coord': (1384, 820), 'click_coord': (1342, 773)},
        'M4': {'quantity_coord': (1533, 816), 'click_coord': (1497, 773)},
        'M5': {'quantity_coord': (1144, 971), 'click_coord': (1106, 923)},  # Corrected for 39
        'M6': {'quantity_coord': (1298, 970), 'click_coord': (1265, 919)},  # Corrected for 39
        'M7': {'quantity_coord': (1457, 968), 'click_coord': (1420, 924)},
    }
    
    print("Make sure you're on the initiate exchange screen")
    input("Press Enter to read all quantities...")
    
    # Step 1: Read each coordinate → assign to piece
    quantities = {}
    
    for map_name, coords in map_pieces.items():
        qty_x, qty_y = coords['quantity_coord']
        print(f"Reading {map_name} at ({qty_x}, {qty_y})...")
        
        quantity = read_quantity_at_coordinate(qty_x, qty_y)
        
        if quantity is not None:
            quantities[map_name] = quantity
            print(f"  ✅ {map_name}: {quantity} pieces")
        else:
            print(f"  ❌ {map_name}: Failed")
    
    if not quantities:
        print("❌ No quantities read - update coordinates")
        return
    
    # Step 2: Find lowest and highest
    lowest_map = min(quantities.keys(), key=lambda k: quantities[k])
    highest_map = max(quantities.keys(), key=lambda k: quantities[k])
    
    print(f"\n🎯 TRADE DECISION:")
    print(f"  Give:    {lowest_map} ({quantities[lowest_map]} pieces) ← LOWEST")
    print(f"  Receive: {highest_map} ({quantities[highest_map]} pieces) ← HIGHEST")
    
    # Step 3: Execute clicks (optional)
    execute = input("\nExecute trade clicks? (y/n): ").lower().strip()
    
    if execute == 'y':
        print("\n🚀 Executing clicks...")
        
        # Click lowest map piece
        give_x, give_y = map_pieces[lowest_map]['click_coord']
        print(f"1. Click {lowest_map} at ({give_x}, {give_y})")
        pyautogui.click(give_x, give_y)
        time.sleep(0.5)
        
        # Click "you will lose" area (YOUR CALIBRATED COORDINATE)
        lose_x, lose_y = 1448, 468
        print(f"2. Click 'you will lose' at ({lose_x}, {lose_y})")
        pyautogui.click(lose_x, lose_y)
        time.sleep(0.5)

        # Click highest map piece
        receive_x, receive_y = map_pieces[highest_map]['click_coord']
        print(f"3. Click {highest_map} at ({receive_x}, {receive_y})")
        pyautogui.click(receive_x, receive_y)
        time.sleep(0.5)

        # Click initiate exchange (YOUR CALIBRATED COORDINATE)
        initiate_x, initiate_y = 1258, 1145
        print(f"4. Click 'Initiate Exchange' at ({initiate_x}, {initiate_y})")
        pyautogui.click(initiate_x, initiate_y)
        time.sleep(1.0)  # Wait for confirmation box to appear

        # Click confirmation box (EXACT CAPTURED COORDINATE)
        confirm_x, confirm_y = 1258, 933  # Captured exact coordinate
        print(f"5. Click 'Confirm' at ({confirm_x}, {confirm_y})")
        pyautogui.click(confirm_x, confirm_y)

        print("✅ Trade executed with confirmation!")

def calibrate_all_coordinates():
    """Calibrate all needed coordinates"""
    print("Complete Coordinate Calibration")
    print("=" * 40)
    
    coords = {}
    
    # Map piece quantity coordinates
    print("1. QUANTITY READING COORDINATES")
    for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
        print(f"\nPosition mouse over {map_name} QUANTITY NUMBER")
        input("Press Enter...")
        x, y = pyautogui.position()
        coords[f'{map_name}_qty'] = (x, y)
        print(f"  {map_name} quantity: ({x}, {y})")
    
    # Map piece click coordinates
    print("\n2. MAP PIECE CLICK COORDINATES")
    for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
        print(f"\nPosition mouse over {map_name} MAP PIECE (clickable)")
        input("Press Enter...")
        x, y = pyautogui.position()
        coords[f'{map_name}_click'] = (x, y)
        print(f"  {map_name} click: ({x}, {y})")
    
    # UI elements
    print("\n3. UI ELEMENT COORDINATES")
    
    print("\nPosition mouse over 'YOU WILL LOSE' area")
    input("Press Enter...")
    x, y = pyautogui.position()
    coords['lose_area'] = (x, y)
    print(f"  Lose area: ({x}, {y})")
    
    print("\nPosition mouse over 'INITIATE EXCHANGE' button")
    input("Press Enter...")
    x, y = pyautogui.position()
    coords['initiate'] = (x, y)
    print(f"  Initiate: ({x}, {y})")

    print("\nPosition mouse over 'CONFIRM' button (appears after initiate)")
    print("Note: This appears AFTER clicking initiate exchange")
    input("Press Enter...")
    x, y = pyautogui.position()
    coords['confirm'] = (x, y)
    print(f"  Confirm: ({x}, {y})")

    # Generate code
    print(f"\n{'='*50}")
    print("COPY THIS CODE:")
    print(f"{'='*50}")
    print("map_pieces = {")
    for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
        qty = coords.get(f'{map_name}_qty', (0, 0))
        click = coords.get(f'{map_name}_click', (0, 0))
        print(f"    '{map_name}': {{'quantity_coord': {qty}, 'click_coord': {click}}},")
    print("}")
    print(f"\nlose_area_coord = {coords.get('lose_area', (0, 0))}")
    print(f"initiate_coord = {coords.get('initiate', (0, 0))}")
    print(f"confirm_coord = {coords.get('confirm', (0, 0))}")

def main():
    """Main menu"""
    while True:
        print("\nSimple Map Trade Logic Test")
        print("=" * 40)
        print("1. Test Simple Logic")
        print("2. Calibrate All Coordinates")
        print("0. Exit")
        
        choice = input("\nChoice (0-2): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            test_simple_logic()
        elif choice == '2':
            calibrate_all_coordinates()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
