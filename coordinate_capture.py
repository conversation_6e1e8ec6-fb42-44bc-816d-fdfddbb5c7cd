"""
Coordinate Capture Tool for Map Trade Module
Run this script to capture new coordinates for map pieces M1-M7
"""
import pyautogui
import time

def capture_coordinates():
    """Capture coordinates for map pieces"""
    print("=== MAP PIECE COORDINATE CAPTURE TOOL ===")
    print("This tool will help you capture new coordinates for M1-M7 map pieces")
    print("Make sure the map trade screen is visible with all M1-M7 quantities shown")
    print()
    
    coordinates = {}
    map_pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
    
    for map_piece in map_pieces:
        print(f"\n--- Capturing {map_piece} ---")
        print(f"1. Position your mouse over the CENTER of the {map_piece} QUANTITY NUMBER")
        print("2. Press SPACE when ready to capture")
        print("3. Press ESC to skip this piece")
        
        while True:
            # Check for key presses
            try:
                # Wait for key press
                key = input("Press ENTER when mouse is positioned (or type 'skip' to skip): ").strip().lower()
                
                if key == 'skip':
                    print(f"Skipped {map_piece}")
                    break
                
                # Capture current mouse position
                x, y = pyautogui.position()
                coordinates[map_piece] = {
                    'quantity_pos': (x, y),
                    'click_pos': (x - 20, y - 30)  # Approximate click position (adjust as needed)
                }
                
                print(f"✅ {map_piece} quantity position captured: ({x}, {y})")
                break
                
            except KeyboardInterrupt:
                print("\nCapture cancelled by user")
                return None
            except Exception as e:
                print(f"Error: {e}")
                continue
    
    # Display results
    print("\n" + "="*50)
    print("CAPTURED COORDINATES:")
    print("="*50)
    
    for map_piece, coords in coordinates.items():
        qty_x, qty_y = coords['quantity_pos']
        click_x, click_y = coords['click_pos']
        print(f"'{map_piece}': {{")
        print(f"    'quantity_pos': ({qty_x}, {qty_y}),")
        print(f"    'click_pos': ({click_x}, {click_y}),")
        print(f"    'quantity': None")
        print(f"}},")
    
    print("\n" + "="*50)
    print("COPY THE ABOVE COORDINATES TO UPDATE THE _define_map_piece_regions METHOD")
    print("="*50)
    
    return coordinates

if __name__ == "__main__":
    print("=== COORDINATE CAPTURE TOOL ===")
    print("This is a standalone tool for capturing map coordinates.")
    print("Make sure you meant to run this script and not the main program.")
    print()
    response = input("Do you want to continue with coordinate capture? (y/N): ").strip().lower()

    if response in ['y', 'yes']:
        print("Starting coordinate capture in 3 seconds...")
        time.sleep(3)
        capture_coordinates()
    else:
        print("Coordinate capture cancelled.")
        print("To run the main program, use: python Lastwar_helper_v1.py")
