"""
Test script for smart window focus functionality
Tests that game focus only resizes/moves when necessary
"""

import sys
import os
import time

def test_window_detection():
    """Test that we can detect the game window"""
    print("=== Testing Game Window Detection ===")
    
    try:
        import pygetwindow as gw
        
        # Search for Last War window with various possible titles
        possible_titles = [
            "Last War-Survival Game",
            "Last War",
            "Survival Game",
            "Last War - Survival Game",
            "LastWar",
            "last war"
        ]

        game_window = None

        # Try to find the window by exact title match first
        for title in possible_titles:
            try:
                windows = gw.getWindowsWithTitle(title)
                if windows:
                    game_window = windows[0]
                    print(f"✓ Found game window: '{title}'")
                    break
            except:
                continue

        # If exact match failed, try partial match
        if not game_window:
            all_windows = gw.getAllWindows()
            for window in all_windows:
                window_title = window.title.lower()
                if any(title.lower() in window_title for title in ["last war", "survival game"]):
                    game_window = window
                    print(f"✓ Found game window by partial match: '{window.title}'")
                    break

        if not game_window:
            print("✗ Could not find Last War game window")
            print("Available windows:")
            all_windows = gw.getAllWindows()
            for window in all_windows[:10]:  # Show first 10 windows
                if window.title.strip():  # Only show windows with titles
                    print(f"  - '{window.title}'")
            return False

        # Display current window properties
        print(f"✓ Current window properties:")
        print(f"  Title: {game_window.title}")
        print(f"  Size: {game_window.width}x{game_window.height}")
        print(f"  Position: ({game_window.left}, {game_window.top})")
        
        return True
        
    except Exception as e:
        print(f"✗ Window detection test failed: {e}")
        return False

def test_size_position_logic():
    """Test the size and position checking logic"""
    print("\n=== Testing Size/Position Logic ===")
    
    try:
        # Test tolerance logic
        target_width = 1260
        target_height = 740
        target_x = 0
        target_y = 0
        
        size_tolerance = 10
        position_tolerance = 20
        
        # Test cases
        test_cases = [
            # (current_width, current_height, current_x, current_y, expected_size_correct, expected_pos_correct)
            (1260, 740, 0, 0, True, True),      # Perfect match
            (1265, 745, 5, 5, True, True),      # Within tolerance
            (1280, 760, 25, 25, False, False),  # Outside tolerance
            (1260, 740, 25, 25, True, False),   # Size correct, position wrong
            (1280, 760, 0, 0, False, True),     # Position correct, size wrong
        ]
        
        for i, (cw, ch, cx, cy, exp_size, exp_pos) in enumerate(test_cases):
            size_correct = (abs(cw - target_width) <= size_tolerance and 
                          abs(ch - target_height) <= size_tolerance)
            
            position_correct = (abs(cx - target_x) <= position_tolerance and 
                              abs(cy - target_y) <= position_tolerance)
            
            size_match = size_correct == exp_size
            pos_match = position_correct == exp_pos
            
            status = "✓" if (size_match and pos_match) else "✗"
            print(f"{status} Test case {i+1}: {cw}x{ch} at ({cx},{cy})")
            print(f"    Size correct: {size_correct} (expected: {exp_size}) {'✓' if size_match else '✗'}")
            print(f"    Position correct: {position_correct} (expected: {exp_pos}) {'✓' if pos_match else '✗'}")
        
        print("✓ Size/position logic test completed")
        return True
        
    except Exception as e:
        print(f"✗ Size/position logic test failed: {e}")
        return False

def test_smart_focus_simulation():
    """Simulate the smart focus logic without actually moving windows"""
    print("\n=== Testing Smart Focus Logic Simulation ===")
    
    try:
        # Simulate different window states
        scenarios = [
            {
                "name": "Already Perfect",
                "current": {"width": 1260, "height": 740, "x": 0, "y": 0},
                "expected_resize": False,
                "expected_move": False
            },
            {
                "name": "Needs Resize Only",
                "current": {"width": 1200, "height": 700, "x": 0, "y": 0},
                "expected_resize": True,
                "expected_move": False
            },
            {
                "name": "Needs Move Only",
                "current": {"width": 1260, "height": 740, "x": 100, "y": 100},
                "expected_resize": False,
                "expected_move": True
            },
            {
                "name": "Needs Both",
                "current": {"width": 1200, "height": 700, "x": 100, "y": 100},
                "expected_resize": True,
                "expected_move": True
            }
        ]
        
        target_width = 1260
        target_height = 740
        target_x = 0
        target_y = 0
        size_tolerance = 10
        position_tolerance = 20
        
        for scenario in scenarios:
            current = scenario["current"]
            
            # Check if resize is needed
            size_correct = (abs(current["width"] - target_width) <= size_tolerance and 
                          abs(current["height"] - target_height) <= size_tolerance)
            
            # Check if repositioning is needed
            position_correct = (abs(current["x"] - target_x) <= position_tolerance and 
                              abs(current["y"] - target_y) <= position_tolerance)
            
            needs_resize = not size_correct
            needs_move = not position_correct
            
            resize_match = needs_resize == scenario["expected_resize"]
            move_match = needs_move == scenario["expected_move"]
            
            status = "✓" if (resize_match and move_match) else "✗"
            print(f"{status} Scenario: {scenario['name']}")
            print(f"    Current: {current['width']}x{current['height']} at ({current['x']}, {current['y']})")
            print(f"    Needs resize: {needs_resize} (expected: {scenario['expected_resize']}) {'✓' if resize_match else '✗'}")
            print(f"    Needs move: {needs_move} (expected: {scenario['expected_move']}) {'✓' if move_match else '✗'}")
        
        print("✓ Smart focus logic simulation completed")
        return True
        
    except Exception as e:
        print(f"✗ Smart focus simulation test failed: {e}")
        return False

def main():
    """Run all smart window focus tests"""
    print("Testing Smart Window Focus Functionality...\n")
    
    tests = [
        test_window_detection,
        test_size_position_logic,
        test_smart_focus_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Smart window focus is working correctly.")
        print("\nThe game focus button will now:")
        print("✓ Only resize if window size is wrong")
        print("✓ Only move if window position is wrong") 
        print("✓ Skip changes if window is already correct")
        print("✓ Provide clear feedback about what was changed")
    else:
        print("⚠ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
