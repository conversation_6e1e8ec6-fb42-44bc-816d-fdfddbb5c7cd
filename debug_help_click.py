"""
Debug help click execution issues
"""
import sys
import os
sys.path.append('modules')

def debug_help_click():
    """Debug help click step by step"""
    print("🔍 DEBUGGING HELP CLICK MODULE")
    print("=" * 60)
    
    try:
        # Step 1: Import and initialize
        print("Step 1: Importing modules...")
        from enhanced_help_click import EnhancedHelpClickModule
        from screen_scanner import ScreenScanner
        from module_config_manager import ModuleConfigManager
        
        help_module = EnhancedHelpClickModule()
        scanner = ScreenScanner()
        config_manager = ModuleConfigManager()
        
        print(f"✅ Help module: {help_module.name}, priority: {help_module.priority}")
        print(f"✅ Scanner loaded: {len(scanner.templates)} templates")
        print()
        
        # Step 2: Check configuration
        print("Step 2: Checking configuration...")
        config = config_manager.get_module_config('help_click')
        
        print(f"✅ Config loaded:")
        print(f"   - Enabled: {config.get('enabled', 'Unknown')}")
        print(f"   - Priority: {config.get('priority', 'Unknown')}")
        print(f"   - Cooldown: {config.get('cooldown', 'Unknown')}")
        
        templates_config = config.get('templates', [])
        print(f"   - Templates: {len(templates_config)}")
        for template in templates_config:
            name = template.get('name', 'Unknown')
            threshold = template.get('threshold', 'Unknown')
            required = template.get('required', 'Unknown')
            print(f"     * {name}: threshold={threshold}, required={required}")
        
        coords_config = config.get('click_coordinates', [])
        print(f"   - Click coordinates: {len(coords_config)}")
        for coord in coords_config:
            name = coord.get('name', 'Unknown')
            x = coord.get('x', '?')
            y = coord.get('y', '?')
            enabled = coord.get('enabled', True)
            repeat = coord.get('repeat', 1)
            print(f"     * {name}: ({x}, {y}) enabled={enabled}, repeat={repeat}")
        print()
        
        # Step 3: Check template files
        print("Step 3: Checking template files...")
        help_templates = ['Help_template', 'Help_chat']
        
        for template_name in help_templates:
            # Check if loaded in scanner
            loaded = template_name in scanner.templates
            print(f"   - {template_name}: Loaded in scanner = {loaded}")
            
            # Check if file exists
            template_path = os.path.join('templates', f"{template_name}.png")
            file_exists = os.path.exists(template_path)
            print(f"     File exists: {file_exists}")
            
            if file_exists:
                size = os.path.getsize(template_path)
                print(f"     File size: {size} bytes")
            print()
        
        # Step 4: Perform screen scan
        print("Step 4: Performing screen scan...")
        screen_data = scanner.scan_screen_cache_optimized(help_templates)
        
        if 'error' in screen_data:
            print(f"❌ Scan failed: {screen_data['error']}")
            return
        
        print(f"✅ Scan completed successfully")
        print(f"   - Screen size: {screen_data.get('screen_size', 'Unknown')}")
        
        templates_detected = screen_data.get('templates_detected', {})
        templates_found = screen_data.get('templates_found', {})
        
        print(f"   - Templates detected: {templates_detected}")
        print(f"   - Templates found: {len(templates_found)} total")
        
        for template_name in help_templates:
            detected = templates_detected.get(template_name, False)
            found_matches = templates_found.get(template_name, [])
            print(f"     * {template_name}: detected={detected}, matches={len(found_matches)}")
            
            if found_matches:
                for i, match in enumerate(found_matches[:2]):  # Show first 2 matches
                    x, y, w, h = match
                    print(f"       Match {i+1}: position=({x}, {y}), size={w}x{h}")
        print()
        
        # Step 5: Test can_execute
        print("Step 5: Testing can_execute...")
        try:
            can_execute = help_module.can_execute(screen_data)
            print(f"✅ can_execute result: {can_execute}")
            
            if can_execute:
                print("   🎯 Module CAN execute - help detected!")
            else:
                print("   ⏸️ Module CANNOT execute - no help detected")
                print("   🔍 Debugging why...")
                
                # Check what the module is actually looking for
                for template in help_templates:
                    in_detected = templates_detected.get(template, False)
                    in_found = template in templates_found and len(templates_found[template]) > 0
                    print(f"     - {template}: in_detected={in_detected}, in_found={in_found}")
                    
        except Exception as e:
            print(f"❌ can_execute failed: {str(e)}")
            import traceback
            traceback.print_exc()
        print()
        
        # Step 6: Test execute (only if can_execute is True)
        if 'can_execute' in locals() and can_execute:
            print("Step 6: Testing execute...")
            try:
                print("   🚀 Attempting to execute help click...")
                result = help_module.execute(screen_data)
                print(f"   ✅ Execute result: {result}")
                
                if result:
                    print("   🎉 Help click executed successfully!")
                else:
                    print("   ❌ Help click execution failed")
                    
            except Exception as e:
                print(f"   ❌ Execute failed: {str(e)}")
                import traceback
                traceback.print_exc()
        else:
            print("Step 6: Skipping execute (can_execute is False or failed)")
        
        print("\n" + "=" * 60)
        print("🏁 DEBUG COMPLETE")
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_help_click()
