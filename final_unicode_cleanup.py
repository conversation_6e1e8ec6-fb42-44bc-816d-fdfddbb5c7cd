#!/usr/bin/env python3
"""
Final targeted Unicode cleanup - fix specific problematic lines
"""

import re

def fix_specific_unicode_issues():
    """Fix the specific Unicode issues we saw in the terminal output"""
    
    fixes_applied = 0
    
    # Fix daily_tasks.py - line 381
    try:
        with open('modules/daily_tasks.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the specific problematic line
        if '📋 Loaded' in content:
            content = content.replace('📋 Loaded', 'Loaded')
            fixes_applied += 1
            
            with open('modules/daily_tasks.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ Fixed daily_tasks.py Unicode issue")
    except Exception as e:
        print(f"Error fixing daily_tasks.py: {e}")
    
    # Fix alliance_donation.py - line 266
    try:
        with open('modules/alliance_donation.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the specific problematic line
        if '📋 Loaded' in content:
            content = content.replace('📋 Loaded', 'Loaded')
            fixes_applied += 1
            
            with open('modules/alliance_donation.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ Fixed alliance_donation.py Unicode issue")
    except Exception as e:
        print(f"Error fixing alliance_donation.py: {e}")
    
    # Fix zombie_invasion.py - line 43
    try:
        with open('modules/zombie_invasion.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the specific problematic line
        if '🔍 Squad templates configured' in content:
            content = content.replace('🔍 Squad templates configured', 'Squad templates configured')
            fixes_applied += 1
            
            with open('modules/zombie_invasion.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ Fixed zombie_invasion.py Unicode issue")
    except Exception as e:
        print(f"Error fixing zombie_invasion.py: {e}")
    
    # Fix map_trade.py - line 31
    try:
        with open('modules/map_trade.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the specific problematic line
        if '🗺️ Map Trade module initialized' in content:
            content = content.replace('🗺️ Map Trade module initialized', 'Map Trade module initialized')
            fixes_applied += 1
            
            with open('modules/map_trade.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ Fixed map_trade.py Unicode issue")
    except Exception as e:
        print(f"Error fixing map_trade.py: {e}")
    
    return fixes_applied

def main():
    """Apply final Unicode fixes"""
    
    print("Final Unicode Cleanup")
    print("=" * 25)
    
    fixes = fix_specific_unicode_issues()
    
    print(f"\nApplied {fixes} targeted Unicode fixes")
    
    if fixes > 0:
        print("✓ Specific Unicode encoding errors should now be resolved!")
        print("✓ System should start without Unicode errors in the terminal")
    else:
        print("No specific Unicode issues found to fix")

if __name__ == "__main__":
    main()
