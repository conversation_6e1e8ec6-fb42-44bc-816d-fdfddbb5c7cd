#!/usr/bin/env python3
"""
Quick test script to test map reading directly
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.map_trade import MapTradeModule

def test_map_reading():
    print("DIRECT MAP READING TEST")
    print("=" * 50)

    try:
        # Create map trade module
        print("Creating MapTradeModule...")
        map_trade = MapTradeModule()
        print("MapTradeModule created successfully")

        # Test the map reading directly
        print("\nTesting map reading directly...")
        result = map_trade.test_map_reading_now()

        print(f"\nFinal result: {result}")

        # Check if debug images were created
        import glob
        debug_images = glob.glob("debug_M*_capture.png")
        if debug_images:
            print(f"\nDebug images found: {debug_images}")
        else:
            print("\nNo debug images found")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_map_reading()
