#!/usr/bin/env python3
"""
Capture the exact new exchange button coordinate AFTER clicking red thumb
"""

import pyautogui
import time

def capture_new_exchange_after_red_thumb():
 """Capture the exact new exchange button coordinate after red thumb click"""
 print("New Exchange Button Coordinate Capture (After Red Thumb)")
 print("=" * 60)
 print()
 print("IMPORTANT STEPS:")
 print("1. Navigate to the screen where RED THUMB is visible")
 print("2. Click the red thumb manually")
 print("3. Wait for the screen to update")
 print("4. Position mouse EXACTLY over the 'New Exchange' button")
 print("5. Press Enter to capture coordinate")
 print()
 
 input("Press Enter when you have clicked the red thumb and 'New Exchange' button is visible...")
 
 x, y = pyautogui.position()
 print(f"\n New Exchange button coordinate (after red thumb): ({x}, {y})")
 
 # Test the coordinate immediately
 print(f"\nTesting click at ({x}, {y}) in 3 seconds...")
 for i in range(3, 0, -1):
 print(f"{i}...")
 time.sleep(1)
 
 print("Clicking now!")
 pyautogui.click(x, y)
 
 print(f"\n UPDATE THESE COORDINATES:")
 print("=" * 50)
 print(f"In modules/map_trade.py:")
 print(f"Line ~905 and ~912 (in _click_new_exchange_button method):")
 print(f"default_x, default_y = {x}, {y} # After red thumb click")
 print()
 
 return x, y

def test_template_detection():
 """Test what templates are being detected"""
 print("Template Detection Test")
 print("=" * 30)
 print("Make sure 'New Exchange' button is visible after clicking red thumb")
 
 input("Press Enter when New Exchange button is visible...")
 
 import cv2
 import numpy as np
 import os
 
 # Take screenshot
 screenshot = pyautogui.screenshot()
 screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
 
 # Test all template sizes
 template_names = [
 "initiate_exchange_100x40.png",
 "initiate_exchange_80x30.png", 
 "initiate_exchange_50x25.png",
 "initiate_exchange_30x15.png"
 ]
 
 print("\nTemplate Detection Results:")
 print("-" * 40)
 
 for template_name in template_names:
 template_path = os.path.join("templates", template_name)
 if os.path.exists(template_path):
 template = cv2.imread(template_path)
 
 if template is not None:
 result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
 min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
 
 template_h, template_w = template.shape[:2]
 center_x = max_loc[0] + template_w // 2
 center_y = max_loc[1] + template_h // 2
 
 print(f"{template_name}:")
 print(f" Confidence: {max_val:.3f}")
 print(f" Location: ({center_x}, {center_y})")
 print(f" {' GOOD' if max_val > 0.6 else ' LOW'}")
 print()
 else:
 print(f"{template_name}: Template file not found")
 
 print("Which coordinate looks most accurate for the New Exchange button?")

def main():
 """Main menu"""
 while True:
 print("\nNew Exchange Coordinate Capture (After Red Thumb)")
 print("=" * 55)
 print("1. Capture New Exchange Coordinate")
 print("2. Test Template Detection")
 print("0. Exit")
 
 choice = input("\nChoice (0-2): ").strip()
 
 if choice == '0':
 break
 elif choice == '1':
 x, y = capture_new_exchange_after_red_thumb()
 
 # Ask if user wants to update the file
 update = input(f"\nUpdate map_trade.py with ({x}, {y})? (y/n): ").lower().strip()
 if update == 'y':
 update_map_trade_file(x, y)
 elif choice == '2':
 test_template_detection()
 else:
 print("Invalid choice")

def update_map_trade_file(x, y):
 """Update the map_trade.py file with new coordinate"""
 try:
 # Read the file
 with open('modules/map_trade.py', 'r') as f:
 content = f.read()
 
 # Replace both coordinates in _click_new_exchange_button method
 lines = content.split('\n')
 updated_count = 0
 
 for i, line in enumerate(lines):
 # Look for the default coordinate lines
 if 'default_x, default_y =' in line and ('1260, 1144' in line or 'Updated exact coordinate' in line):
 lines[i] = f' default_x, default_y = {x}, {y} # After red thumb click coordinate'
 updated_count += 1
 print(f" Updated line {i+1}: {lines[i].strip()}")
 
 if updated_count > 0:
 # Write back to file
 with open('modules/map_trade.py', 'w') as f:
 f.write('\n'.join(lines))
 print(f" Successfully updated {updated_count} coordinates in modules/map_trade.py")
 else:
 print(" Could not find the coordinate lines to update")
 print("Please update manually:")
 print(f"default_x, default_y = {x}, {y}")
 
 except Exception as e:
 print(f" Error updating file: {e}")
 print("Please update manually:")
 print(f"default_x, default_y = {x}, {y}")

if __name__ == "__main__":
 try:
 main()
 except KeyboardInterrupt:
 print("\nInterrupted")
 except Exception as e:
 print(f"Error: {e}")
 import traceback
 traceback.print_exc()
