"""
Enhanced Base Module for Last War automation
Uses the UnifiedConfigManager for flexible configuration
"""
import time
import logging
import pyautogui
import cv2
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from module_config_manager import ModuleConfigManager
from unified_config_manager import UnifiedConfigManager


class EnhancedBaseModule(ABC):
    """Enhanced base class for all automation modules with configuration support"""
    
    def __init__(self, name: str, priority: int = 5, enabled: bool = True):
        self.name = name
        self.priority = priority
        self.enabled = enabled
        self.last_execution = None
        self.execution_count = 0
        self.logger = logging.getLogger(f"LastWar.{name}")

        # Initialize both configuration managers for transition period
        self.config_manager = ModuleConfigManager()  # Legacy support
        self.unified_config_manager = UnifiedConfigManager()  # New unified system

        # Try to load from unified config first, fallback to legacy
        self.module_config = self.unified_config_manager.get_module_config(name)
        if not self.module_config:
            self.module_config = self.config_manager.get_module_config(name)
            self.logger.info(f"Using legacy configuration for module: {name}")
        else:
            self.logger.info(f"Using unified configuration for module: {name}")

        # Override settings from config if available
        if self.module_config:
            self.enabled = self.module_config.get('enabled', enabled)
            self.priority = self.module_config.get('priority', priority)
            self.cooldown = self.module_config.get('cooldown', getattr(self, 'cooldown', 300))

    def apply_unified_config(self, config: Dict[str, Any]):
        """Apply unified configuration to this module"""
        self.module_config = config
        self.enabled = config.get('enabled', self.enabled)
        self.priority = config.get('priority', self.priority)
        self.cooldown = config.get('cooldown', getattr(self, 'cooldown', 300))

        # Apply module-specific configuration
        self._apply_module_specific_config(config)

        self.logger.info(f"Applied unified configuration to {self.name}")

    def _apply_module_specific_config(self, config: Dict[str, Any]):
        """Override this method in subclasses to apply module-specific configuration"""
        pass

    def get_unified_config(self) -> Optional[Dict[str, Any]]:
        """Get the current unified configuration for this module"""
        return self.unified_config_manager.get_module_config(self.name)

    def update_unified_config(self, updates: Dict[str, Any]):
        """Update the unified configuration for this module"""
        self.unified_config_manager.update_module_config(self.name, updates)
        # Reload the configuration
        updated_config = self.unified_config_manager.get_module_config(self.name)
        if updated_config:
            self.apply_unified_config(updated_config)
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Check if this module can execute based on current screen state
        Uses configured templates and text patterns
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if module can execute, False otherwise
        """
        # Check required templates
        if self._check_required_templates(screen_data):
            return True
        
        # Check required text patterns
        if self._check_required_text_patterns(screen_data):
            return True
        
        # Allow subclasses to add custom logic
        return self.custom_can_execute(screen_data)
    
    def _check_required_templates(self, screen_data: Dict[str, Any]) -> bool:
        """Check if required templates are found"""
        templates = self.config_manager.get_templates(self.name)
        required_templates = [t for t in templates if t.get('required', False)]
        
        if not required_templates:
            return False
        
        templates_found = screen_data.get('templates_found', {})
        
        for template in required_templates:
            template_name = template['name']
            if template_name in templates_found:
                matches = templates_found[template_name]
                if matches:  # At least one match found
                    self.logger.debug(f"Required template found: {template_name}")
                    return True
        
        return False
    
    def _check_required_text_patterns(self, screen_data: Dict[str, Any]) -> bool:
        """Check if required text patterns are found"""
        patterns = self.config_manager.get_text_patterns(self.name)
        required_patterns = [p for p in patterns if p.get('required', False)]
        
        if not required_patterns:
            return False
        
        text_regions = screen_data.get('text_regions', {})
        
        for pattern in required_patterns:
            pattern_text = pattern['text'].lower()
            target_region = pattern.get('region', 'full_screen')
            
            # Check specific region or all regions
            regions_to_check = [target_region] if target_region in text_regions else text_regions.keys()
            
            for region_name in regions_to_check:
                if region_name in text_regions:
                    region_text = text_regions[region_name].lower()
                    if pattern_text in region_text:
                        self.logger.debug(f"Required text pattern found: {pattern_text} in {region_name}")
                        return True
        
        return False
    
    @abstractmethod
    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Custom execution check logic for subclasses
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if module can execute based on custom logic
        """
        pass
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Execute the module's task using configured actions
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if execution was successful, False otherwise
        """
        try:
            self.logger.info(f"Executing {self.name} with configured actions")
            
            actions = self.config_manager.get_actions(self.name)
            
            if not actions:
                self.logger.warning(f"No actions configured for {self.name}, using custom execution")
                return self.custom_execute(screen_data)
            
            # Execute configured actions in sequence
            for i, action in enumerate(actions):
                action_type = action.get('type', '')
                self.logger.debug(f"Executing action {i+1}/{len(actions)}: {action_type}")
                
                success = self._execute_action(action, screen_data)
                
                if not success:
                    self.logger.warning(f"Action {action_type} failed, stopping execution")
                    return False
                
                # Wait between actions if specified
                wait_time = action.get('wait', 0)
                if wait_time > 0:
                    time.sleep(wait_time)
            
            self.logger.info(f"All actions completed successfully for {self.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error executing {self.name}: {str(e)}")
            return False
    
    def _execute_action(self, action: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute a single action"""
        action_type = action.get('type', '')
        target = action.get('target', '')
        
        try:
            if action_type == 'navigate':
                return self._action_navigate(target, action)
            elif action_type == 'click':
                return self._action_click(target, action)
            elif action_type == 'wait':
                return self._action_wait(action)
            elif action_type == 'template_match':
                return self._action_template_match(target, action, screen_data)
            elif action_type == 'text_search':
                return self._action_text_search(target, action, screen_data)
            elif action_type == 'close':
                return self._action_close(action)
            else:
                # Try custom action execution
                return self.custom_execute_action(action, screen_data)
                
        except Exception as e:
            self.logger.error(f"Error executing action {action_type}: {str(e)}")
            return False
    
    def _action_navigate(self, target: str, action: Dict[str, Any]) -> bool:
        """Execute navigate action"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, target)
        if coordinate:
            x, y = coordinate
            pyautogui.click(x, y)
            self.logger.debug(f"Navigated to {target} at ({x}, {y})")
            return True
        else:
            self.logger.warning(f"Navigate target '{target}' not found in coordinates")
            return False
    
    def _action_click(self, target: str, action: Dict[str, Any]) -> bool:
        """Execute click action"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, target)
        if coordinate:
            x, y = coordinate
            pyautogui.click(x, y)
            self.logger.debug(f"Clicked {target} at ({x}, {y})")
            return True
        else:
            self.logger.warning(f"Click target '{target}' not found in coordinates")
            return False
    
    def _action_wait(self, action: Dict[str, Any]) -> bool:
        """Execute wait action"""
        wait_time = action.get('duration', action.get('wait', 1.0))
        time.sleep(wait_time)
        self.logger.debug(f"Waited {wait_time} seconds")
        return True
    
    def _action_template_match(self, target: str, action: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute template matching action"""
        templates_found = screen_data.get('templates_found', {})
        if target in templates_found:
            matches = templates_found[target]
            if matches:
                # Click on first match
                x, y, w, h = matches[0]
                click_x = x + w // 2
                click_y = y + h // 2
                pyautogui.click(click_x, click_y)
                self.logger.debug(f"Clicked template {target} at ({click_x}, {click_y})")
                return True
        
        self.logger.warning(f"Template {target} not found for matching")
        return False
    
    def _action_text_search(self, target: str, action: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """Execute text search action"""
        text_regions = screen_data.get('text_regions', {})
        target_lower = target.lower()
        
        for region_name, text in text_regions.items():
            if target_lower in text.lower():
                self.logger.debug(f"Found text '{target}' in region {region_name}")
                # For text search, we found it but don't click (unless specified)
                if action.get('click', False):
                    # Would need region coordinates to click
                    region = self.config_manager.get_region_by_name(self.name, region_name)
                    if region:
                        x, y, w, h = region
                        pyautogui.click(x + w//2, y + h//2)
                return True
        
        self.logger.warning(f"Text '{target}' not found in any region")
        return False
    
    def _action_close(self, action: Dict[str, Any]) -> bool:
        """Execute close action"""
        method = action.get('method', 'esc')
        target = action.get('target', '')
        
        if method == 'esc':
            pyautogui.press('esc')
            self.logger.debug("Pressed ESC to close")
            return True
        elif method == 'click' and target:
            coordinate = self.config_manager.get_coordinate_by_name(self.name, target)
            if coordinate:
                x, y = coordinate
                pyautogui.click(x, y)
                self.logger.debug(f"Clicked close button at ({x}, {y})")
                return True
        
        self.logger.warning("Close action failed - no valid method or target")
        return False
    
    @abstractmethod
    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Custom execution logic for subclasses when no actions are configured
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if execution was successful
        """
        pass
    
    def custom_execute_action(self, action: Dict[str, Any], screen_data: Dict[str, Any]) -> bool:
        """
        Custom action execution for subclasses
        
        Args:
            action: Action configuration
            screen_data: Screen analysis data
            
        Returns:
            bool: True if action was handled successfully
        """
        # Default implementation - subclasses can override
        self.logger.warning(f"Unknown action type: {action.get('type', 'unknown')}")
        return False
    
    def get_cooldown(self) -> float:
        """Get cooldown time from configuration"""
        return self.module_config.get('cooldown', 60.0)
    
    def is_on_cooldown(self) -> bool:
        """Check if module is currently on cooldown"""
        if self.last_execution is None:
            return False
        
        time_since_last = time.time() - self.last_execution
        return time_since_last < self.get_cooldown()
    
    def can_run(self, screen_data: Dict[str, Any]) -> bool:
        """
        Check if module can run (enabled, not on cooldown, and can execute)
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if module can run, False otherwise
        """
        if not self.enabled:
            return False
        
        if self.is_on_cooldown():
            return False
            
        return self.can_execute(screen_data)
    
    def run(self, screen_data: Dict[str, Any]) -> bool:
        """
        Run the module if conditions are met
        
        Args:
            screen_data: Dictionary containing screen analysis results
            
        Returns:
            bool: True if module ran successfully, False otherwise
        """
        if not self.can_run(screen_data):
            return False
        
        self.logger.info(f"Executing {self.name}")
        
        try:
            success = self.execute(screen_data)
            self.last_execution = time.time()
            self.execution_count += 1
            
            if success:
                self.logger.info(f"{self.name} executed successfully")
            else:
                self.logger.warning(f"{self.name} execution failed")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error executing {self.name}: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the module"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'priority': self.priority,
            'execution_count': self.execution_count,
            'last_execution': self.last_execution,
            'on_cooldown': self.is_on_cooldown(),
            'cooldown_remaining': max(0, self.get_cooldown() - (time.time() - (self.last_execution or 0))),
            'config_summary': self.config_manager.get_module_summary(self.name)
        }
