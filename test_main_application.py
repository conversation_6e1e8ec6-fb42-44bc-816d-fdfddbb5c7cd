#!/usr/bin/env python3
"""
Comprehensive test for the main Last War application with unified configuration
Tests the complete integration including GUI, modules, and configuration management
"""
import sys
import os
import time
import logging
import threading
import tkinter as tk

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from main_controller import MainController
from gui import LastWarGUI
from unified_config_manager import UnifiedConfigManager
from config_helper import ConfigHel<PERSON>

def test_main_application_startup():
    """Test main application startup with unified configuration"""
    print("=" * 60)
    print("TESTING MAIN APPLICATION STARTUP")
    print("=" * 60)
    
    try:
        # Test unified configuration loading
        unified_config = UnifiedConfigManager()
        config_data = unified_config.load_config()
        
        print(f"✅ Unified configuration loaded")
        print(f"   Config version: {config_data.get('config_version')}")
        print(f"   Modules configured: {len(config_data.get('modules', {}))}")
        
        # Test main controller creation
        controller = MainController()
        print(f"✅ MainController created with unified config")
        print(f"   Scan interval: {controller.scan_interval}")
        print(f"   Centralized scanner: {controller.use_centralized_scanner}")
        
        # Test module registration
        module_count = len(controller.modules)
        print(f"✅ {module_count} modules registered")
        
        for module in controller.modules:
            module_config = unified_config.get_module_config(module.name)
            config_source = "UNIFIED" if module_config else "LEGACY"
            print(f"   {module.name}: {config_source} config, priority={module.priority}, enabled={module.enabled}")
        
        return controller
        
    except Exception as e:
        print(f"❌ Error in main application startup: {e}")
        return None

def test_gui_integration():
    """Test GUI integration with unified configuration"""
    print("\n" + "=" * 60)
    print("TESTING GUI INTEGRATION")
    print("=" * 60)
    
    try:
        controller = MainController()
        
        # Create GUI (but don't run it)
        gui = LastWarGUI(controller)
        
        print(f"✅ GUI created successfully")
        print(f"   Unified config manager: {hasattr(gui, 'unified_config')}")
        print(f"   Legacy config support: {hasattr(gui, 'config')}")
        print(f"   Module variables: {len(gui.module_vars)}")
        
        # Test configuration access
        system_settings = gui.config_data.get('system_settings', {})
        print(f"   System settings loaded: {len(system_settings)} settings")
        
        # Test module state loading
        for module_id, var in gui.module_vars.items():
            module_config = gui.unified_config.get_module_config(module_id)
            config_source = "UNIFIED" if module_config else "LEGACY"
            print(f"   {module_id}: {config_source} config, enabled={var.get()}")
        
        # Clean up
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Error in GUI integration: {e}")
        return False

def test_config_helper_integration():
    """Test Config Helper integration"""
    print("\n" + "=" * 60)
    print("TESTING CONFIG HELPER INTEGRATION")
    print("=" * 60)
    
    try:
        # Test Config Helper creation (but don't show GUI)
        config_helper = ConfigHelper()
        
        print(f"✅ Config Helper created successfully")
        print(f"   Unified config manager: {hasattr(config_helper, 'config_manager')}")
        print(f"   Game window management: {hasattr(config_helper, 'focus_game_window')}")
        print(f"   Live preview: {hasattr(config_helper, 'live_preview_enabled')}")

        # Test configuration access
        config_data = config_helper.config_manager.load_config()
        print(f"   Config data loaded: {len(config_data)} sections")
        print(f"   Modules available: {len(config_data.get('modules', {}))}")
        
        # Clean up
        config_helper.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Error in Config Helper integration: {e}")
        return False

def test_configuration_updates():
    """Test configuration update functionality"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION UPDATES")
    print("=" * 60)
    
    try:
        unified_config = UnifiedConfigManager()
        
        # Test module configuration update
        original_config = unified_config.get_module_config('dig')
        if original_config:
            original_enabled = original_config.get('enabled', True)
            print(f"   Original dig enabled: {original_enabled}")
            
            # Update configuration
            updates = {'enabled': not original_enabled, 'test_update': True}
            unified_config.update_module_config('dig', updates)
            
            # Verify update
            updated_config = unified_config.get_module_config('dig')
            new_enabled = updated_config.get('enabled')
            has_test_update = updated_config.get('test_update', False)
            
            print(f"   Updated dig enabled: {new_enabled}")
            print(f"   Test update applied: {has_test_update}")
            
            # Restore original
            unified_config.update_module_config('dig', {'enabled': original_enabled})
            unified_config.save_config()
            
            print(f"✅ Configuration updates working correctly")
            return True
        else:
            print(f"⚠️ No dig configuration found for testing")
            return True
            
    except Exception as e:
        print(f"❌ Error in configuration updates: {e}")
        return False

def test_backward_compatibility():
    """Test backward compatibility with legacy configuration"""
    print("\n" + "=" * 60)
    print("TESTING BACKWARD COMPATIBILITY")
    print("=" * 60)
    
    try:
        # Test legacy config loading
        from module_config_manager import ModuleConfigManager
        legacy_config = ModuleConfigManager()
        
        print(f"✅ Legacy config manager loads successfully")
        
        # Test unified config fallback
        unified_config = UnifiedConfigManager()
        
        # Test modules that might not be in unified config
        test_modules = ['daily_tasks', 'find_truck']
        for module_name in test_modules:
            unified_module_config = unified_config.get_module_config(module_name)
            legacy_module_config = legacy_config.get_module_config(module_name)
            
            if unified_module_config:
                print(f"   {module_name}: UNIFIED config available")
            elif legacy_module_config:
                print(f"   {module_name}: LEGACY config fallback working")
            else:
                print(f"   {module_name}: No configuration found")
        
        print(f"✅ Backward compatibility working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error in backward compatibility: {e}")
        return False

def main():
    """Run comprehensive main application tests"""
    print("🚀 COMPREHENSIVE MAIN APPLICATION TEST")
    print("Testing complete Last War automation system with unified configuration...")
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING, format='%(levelname)s: %(message)s')
    
    tests = [
        ("Main Application Startup", test_main_application_startup),
        ("GUI Integration", test_gui_integration),
        ("Config Helper Integration", test_config_helper_integration),
        ("Configuration Updates", test_configuration_updates),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            if callable(result) and hasattr(result, '__class__'):
                # If it returns an object (like controller), consider it success
                result = True
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Main application fully integrated with unified configuration")
        print("✅ GUI working with unified configuration system")
        print("✅ Config Helper integrated and functional")
        print("✅ Configuration updates working correctly")
        print("✅ Backward compatibility maintained")
        print("\n🚀 READY FOR PRODUCTION USE!")
        return True
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
