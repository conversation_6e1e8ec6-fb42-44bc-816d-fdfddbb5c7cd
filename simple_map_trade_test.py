#!/usr/bin/env python3
"""
Simple Map Trade Test - Following your exact logic
"""

import cv2
import numpy as np
import pyautogui
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def read_quantity_at_coordinate(x, y, size=20):
    """Read quantity number at specific coordinate with 20x20 region"""
    try:
        # Capture 20x20 region around coordinate
        region_x = x - size // 2
        region_y = y - size // 2

        screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Direct OCR (the working method)
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()

        if text.isdigit():
            return int(text)
        else:
            return None

    except Exception as e:
        print(f"Error reading coordinate ({x}, {y}): {e}")
        return None

def simple_red_detection():
    """Simple red color detection test"""
    print("Simple Red Detection Test")
    print("=" * 30)
    
    print("Position yourself on the screen after step 3_trade_option_1")
    input("Press Enter when ready...")
    
    try:
        # Take screenshot
        screenshot = pyautogui.screenshot()
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)
        
        # Red color ranges
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = mask1 + mask2
        
        # Count red pixels
        red_pixels = cv2.countNonZero(red_mask)
        
        print(f"Red pixels detected: {red_pixels}")
        
        if red_pixels > 500:
            print("RESULT: Red thumb likely present")
            return "red_thumb"
        else:
            print("RESULT: No significant red areas")
            return "normal"
            
    except Exception as e:
        print(f"Error: {e}")
        return "error"

def simple_click_test():
    """Test clicking at specific coordinates"""
    print("\nSimple Click Test")
    print("=" * 30)
    
    print("This will test clicking at the center of your screen")
    print("Make sure the game window is visible")
    
    response = input("Continue? (y/n): ").lower().strip()
    if response != 'y':
        return
    
    # Get screen center
    screen_width, screen_height = pyautogui.size()
    center_x = screen_width // 2
    center_y = screen_height // 2
    
    print(f"Clicking at screen center: ({center_x}, {center_y})")
    print("Click in 3 seconds...")
    
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        pyautogui.click(center_x, center_y)
        print("Click executed successfully!")
    except Exception as e:
        print(f"Click failed: {e}")

def manual_coordinate_capture():
    """Manually capture coordinates for map trade elements"""
    print("\nManual Coordinate Capture")
    print("=" * 30)
    
    coordinates = {}
    
    elements = [
        "red_thumb",
        "new_exchange_button", 
        "you_will_lose_area",
        "initiate_exchange_button",
        "M1_map_piece",
        "M2_map_piece",
        "M3_map_piece",
        "M4_map_piece",
        "M5_map_piece",
        "M6_map_piece",
        "M7_map_piece"
    ]
    
    print("For each element, position your mouse and press SPACE to capture")
    print("Press ESC to skip an element")
    
    for element in elements:
        print(f"\nCapturing: {element}")
        print("Position mouse and press SPACE (or ESC to skip)")
        
        while True:
            # Check for space key
            try:
                import keyboard
                if keyboard.is_pressed('space'):
                    x, y = pyautogui.position()
                    coordinates[element] = (x, y)
                    print(f"  Captured: ({x}, {y})")
                    time.sleep(0.5)  # Prevent multiple captures
                    break
                elif keyboard.is_pressed('esc'):
                    print(f"  Skipped: {element}")
                    break
            except ImportError:
                # Fallback without keyboard module
                input("Press Enter to capture current mouse position...")
                x, y = pyautogui.position()
                coordinates[element] = (x, y)
                print(f"  Captured: ({x}, {y})")
                break
            
            time.sleep(0.1)
    
    print("\nCaptured Coordinates:")
    print("=" * 30)
    for element, coord in coordinates.items():
        print(f"{element}: {coord}")
    
    return coordinates

def basic_map_trade_simulation():
    """Basic simulation of intelligent map trade"""
    print("\nBasic Map Trade Simulation")
    print("=" * 30)
    
    print("This will simulate the intelligent map trade process")
    print("Make sure you're on the screen after step 3_trade_option_1")
    
    response = input("Continue? (y/n): ").lower().strip()
    if response != 'y':
        return
    
    # Step 1: Detect screen state
    print("Step 1: Detecting screen state...")
    screen_state = simple_red_detection()
    
    # Step 2: Handle based on state
    if screen_state == "red_thumb":
        print("Step 2: Red thumb detected - would click thumb and new exchange")
        print("  (Simulation - not actually clicking)")
        
        # Simulate clicking red thumb
        print("  Simulating: Click red thumb")
        time.sleep(1)
        
        # Simulate clicking new exchange
        print("  Simulating: Click new exchange button")
        time.sleep(1)
        
        print("Step 3: Now on initiate exchange page - would analyze map pieces")
        
    elif screen_state == "normal":
        print("Step 2: Normal state - would continue with regular trade process")
        print("  (No intelligent trading needed)")
        
    else:
        print("Step 2: Error state - would fall back to normal process")
    
    print("\nSimulation complete!")

def main():
    """Main test menu"""
    while True:
        print("\nSimple Map Trade Test Menu")
        print("=" * 40)
        print("1. Test Red Detection")
        print("2. Test Simple Click")
        print("3. Capture Coordinates Manually")
        print("4. Run Basic Simulation")
        print("0. Exit")
        
        choice = input("\nEnter your choice (0-4): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            simple_red_detection()
        elif choice == '2':
            simple_click_test()
        elif choice == '3':
            manual_coordinate_capture()
        elif choice == '4':
            basic_map_trade_simulation()
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
