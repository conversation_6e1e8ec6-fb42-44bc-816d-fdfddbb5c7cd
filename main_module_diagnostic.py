"""
Main Module Diagnostic Window
Shows real-time screen detection and allows priority management
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import time
import threading
from typing import Dict, Any, List
import json

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from main_controller import MainController
from screen_scanner import ScreenScanner
from module_config_manager import ModuleConfigManager

class MainModuleDiagnostic:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Main Module Diagnostic - Last War")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f0f0f0')

        # Initialize components
        self.scanner = ScreenScanner()
        self.config_manager = ModuleConfigManager()

        # Initialize controller and modules manually
        self.controller = self.setup_controller_with_modules()

        # Current scan data
        self.current_scan_data = None
        self.scanning = False
        self.auto_scan = False
        self.scan_thread = None

        # Create GUI
        self.create_widgets()

        # Load initial data
        self.load_modules()

        # Initialize all tabs
        self.root.after(100, self.initialize_all_tabs)

    def setup_controller_with_modules(self):
        """Setup controller with all modules like the main app - ADAPTIVE"""
        try:
            # Create controller
            controller = MainController()

            # First try to discover modules automatically
            discovered_modules = self.discover_available_modules()

            # Fallback to known modules if discovery fails
            if not discovered_modules:
                modules_to_load = [
                    ('modules.daily_tasks', 'DailyTasksModule'),
                    ('modules.enhanced_help_click', 'EnhancedHelpClickModule'),
                    ('modules.alliance_donation', 'AllianceDonationModule'),
                    ('modules.zombie_invasion', 'ZombieInvasionModule'),
                    ('modules.map_trade', 'MapTradeModule'),  # NEW: Map Trade
                ]
                print("🔍 Using fallback module list")
            else:
                modules_to_load = discovered_modules
                print(f"🎯 Auto-discovered {len(discovered_modules)} modules")

            loaded_modules = []
            for module_path, class_name in modules_to_load:
                try:
                    # Dynamic import
                    module = __import__(module_path, fromlist=[class_name])
                    module_class = getattr(module, class_name)

                    # Create and register module
                    module_instance = module_class()
                    controller.register_module(module_instance)
                    loaded_modules.append(module_instance)
                    print(f"✅ Registered module: {module_instance.name} (priority: {module_instance.priority})")

                except Exception as e:
                    print(f"⚠️ Failed to load {class_name}: {str(e)}")
                    continue

            print(f"🎯 Diagnostic loaded {len(loaded_modules)} modules successfully")
            return controller

        except Exception as e:
            print(f"❌ Error setting up controller: {str(e)}")
            # Fallback to basic controller
            return MainController()

    def discover_available_modules(self):
        """Automatically discover available modules from the modules directory"""
        try:
            import glob
            import importlib.util

            modules_dir = "modules"
            module_files = glob.glob(os.path.join(modules_dir, "*.py"))

            discovered_modules = []

            for module_file in module_files:
                if module_file.endswith("__init__.py"):
                    continue

                module_name = os.path.basename(module_file)[:-3]  # Remove .py

                try:
                    # Try to find a class that ends with "Module"
                    spec = importlib.util.spec_from_file_location(module_name, module_file)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Look for classes ending with "Module"
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if (isinstance(attr, type) and
                            attr_name.endswith("Module") and
                            attr_name != "BaseModule"):

                            discovered_modules.append((f"modules.{module_name}", attr_name))
                            print(f"🔍 Discovered module: {attr_name} in {module_name}")
                            break

                except Exception as e:
                    print(f"⚠️ Could not analyze {module_file}: {str(e)}")
                    continue

            return discovered_modules

        except Exception as e:
            print(f"❌ Error discovering modules: {str(e)}")
            return []
    
    def create_widgets(self):
        """Create the diagnostic interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Main Module Diagnostic", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="🎛️ Advanced Scan Control", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # Top row controls
        top_controls = ttk.Frame(control_frame)
        top_controls.pack(fill=tk.X, pady=(0, 5))

        # Scan buttons
        self.scan_button = ttk.Button(top_controls, text="🔍 Single Scan",
                                     command=self.perform_scan)
        self.scan_button.pack(side=tk.LEFT, padx=(0, 5))

        self.auto_scan_button = ttk.Button(top_controls, text="🔄 Auto Scan",
                                          command=self.toggle_auto_scan)
        self.auto_scan_button.pack(side=tk.LEFT, padx=(0, 5))

        # Module controls
        ttk.Button(top_controls, text="🔄 Reload Modules",
                  command=self.reload_modules).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(top_controls, text="⚙️ Module Config",
                  command=self.open_module_config).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(top_controls, text="📊 Full System Test",
                  command=self.full_system_test).pack(side=tk.LEFT, padx=(0, 5))

        # Status and info
        bottom_controls = ttk.Frame(control_frame)
        bottom_controls.pack(fill=tk.X)

        # Status label
        self.status_var = tk.StringVar(value="Ready to scan")
        status_label = ttk.Label(bottom_controls, textvariable=self.status_var,
                                font=('Arial', 10, 'bold'))
        status_label.pack(side=tk.LEFT)

        # Auto scan interval
        ttk.Label(bottom_controls, text="Auto Scan Interval:").pack(side=tk.RIGHT, padx=(10, 5))
        self.scan_interval_var = tk.StringVar(value="2.0")
        interval_spin = ttk.Spinbox(bottom_controls, from_=0.5, to=10.0, increment=0.5,
                                   textvariable=self.scan_interval_var, width=8)
        interval_spin.pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Label(bottom_controls, text="seconds").pack(side=tk.RIGHT)
        
        # Main content area with notebook
        content_notebook = ttk.Notebook(main_frame)
        content_notebook.pack(fill=tk.BOTH, expand=True)

        # Tab 1: Live Detection
        detection_frame = ttk.Frame(content_notebook)
        content_notebook.add(detection_frame, text="🎯 Live Detection")

        # Detection content
        detection_content = ttk.Frame(detection_frame)
        detection_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Left panel - Detection Results
        left_frame = ttk.LabelFrame(detection_content, text="🎯 Screen Detection Results", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Detection tree
        detection_columns = ('Template', 'Detected', 'Module', 'Priority', 'Status', 'Reason')
        self.detection_tree = ttk.Treeview(left_frame, columns=detection_columns,
                                          show='headings', height=20)
        
        for col in detection_columns:
            self.detection_tree.heading(col, text=col)
            if col == 'Template':
                self.detection_tree.column(col, width=120)
            elif col == 'Detected':
                self.detection_tree.column(col, width=80)
            elif col == 'Module':
                self.detection_tree.column(col, width=100)
            elif col == 'Priority':
                self.detection_tree.column(col, width=60)
            elif col == 'Status':
                self.detection_tree.column(col, width=100)
            elif col == 'Reason':
                self.detection_tree.column(col, width=200)
            else:
                self.detection_tree.column(col, width=100)
        
        # Scrollbar for detection tree
        detection_scroll = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, 
                                        command=self.detection_tree.yview)
        self.detection_tree.configure(yscrollcommand=detection_scroll.set)
        
        self.detection_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detection_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Right panel - Priority Management
        right_frame = ttk.LabelFrame(detection_content, text="⚡ Priority Management", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # Module priority list
        priority_label = ttk.Label(right_frame, text="Module Execution Order:", 
                                  font=('Arial', 12, 'bold'))
        priority_label.pack(pady=(0, 10))
        
        # Priority listbox
        self.priority_listbox = tk.Listbox(right_frame, height=15, width=25)
        self.priority_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Priority control buttons
        priority_buttons = ttk.Frame(right_frame)
        priority_buttons.pack(fill=tk.X)
        
        ttk.Button(priority_buttons, text="↑ Move Up", 
                  command=self.move_priority_up).pack(fill=tk.X, pady=2)
        ttk.Button(priority_buttons, text="↓ Move Down", 
                  command=self.move_priority_down).pack(fill=tk.X, pady=2)
        ttk.Button(priority_buttons, text="💾 Save Priorities", 
                  command=self.save_priorities).pack(fill=tk.X, pady=2)
        
        # Execution simulation
        sim_frame = ttk.LabelFrame(right_frame, text="🎮 Execution Simulation", padding="5")
        sim_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.next_action_var = tk.StringVar(value="No scan performed")
        next_action_label = ttk.Label(sim_frame, text="Next Action:")
        next_action_label.pack(anchor=tk.W)
        
        next_action_value = ttk.Label(sim_frame, textvariable=self.next_action_var, 
                                     foreground="blue", font=('Arial', 10, 'bold'))
        next_action_value.pack(anchor=tk.W, pady=(0, 5))
        
        ttk.Button(sim_frame, text="▶️ Execute Next Action", 
                  command=self.execute_next_action).pack(fill=tk.X)
        
        # Tab 2: Module Details
        modules_frame = ttk.Frame(content_notebook)
        content_notebook.add(modules_frame, text="🔧 Module Details")
        self.create_modules_tab(modules_frame)

        # Tab 3: Template Manager
        templates_frame = ttk.Frame(content_notebook)
        content_notebook.add(templates_frame, text="🖼️ Template Manager")
        self.create_templates_tab(templates_frame)

        # Tab 4: System Logs
        logs_frame = ttk.Frame(content_notebook)
        content_notebook.add(logs_frame, text="📝 System Logs")
        self.create_logs_tab(logs_frame)

        # Bottom info panel
        info_frame = ttk.LabelFrame(main_frame, text="📊 Scan Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(10, 0))

        # Scan stats
        self.scan_info_var = tk.StringVar(value="No scan performed yet")
        scan_info_label = ttk.Label(info_frame, textvariable=self.scan_info_var)
        scan_info_label.pack(anchor=tk.W)
    
    def initialize_all_tabs(self):
        """Initialize all tabs with data"""
        try:
            self.refresh_modules_tab()
            self.refresh_templates_tab()
            self.log_message("🔄 All tabs initialized")
        except Exception as e:
            self.log_message(f"❌ Failed to initialize tabs: {str(e)}")

    def load_modules(self):
        """Load modules and their priorities"""
        try:
            modules = self.controller.modules

            # Sort by priority
            modules.sort(key=lambda x: x.priority)

            # Clear and populate priority listbox
            self.priority_listbox.delete(0, tk.END)
            for module in modules:
                status = "✅ Enabled" if module.enabled else "❌ Disabled"
                self.priority_listbox.insert(tk.END,
                    f"P{module.priority}: {module.name} ({status})")

            self.status_var.set(f"Loaded {len(modules)} modules")
            self.log_message(f"📋 Loaded {len(modules)} modules")

        except Exception as e:
            self.log_message(f"❌ Failed to load modules: {str(e)}")
            messagebox.showerror("Error", f"Failed to load modules: {str(e)}")
    
    def perform_scan(self):
        """Perform a single screen scan and analyze results"""
        if self.scanning:
            return
        
        self.scanning = True
        self.scan_button.configure(state='disabled')
        self.status_var.set("🔍 Scanning screen...")
        
        try:
            # Get all enabled modules
            enabled_modules = [m for m in self.controller.modules if m.enabled]
            
            # Collect all required templates
            required_templates = set()
            for module in enabled_modules:
                if hasattr(module, 'get_required_templates'):
                    module_templates = module.get_required_templates()
                    required_templates.update(module_templates)
                else:
                    # Default templates based on module name
                    if module.name == "zombie_invasion":
                        required_templates.update(['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4', 'squad_4_4', 'events_button'])
                    elif module.name == "alliance_donation":
                        required_templates.update(['events_button'])
                    elif module.name == "daily_tasks":
                        required_templates.update(['events_button'])
                    elif module.name == "help_click":
                        required_templates.update(['Help_template', 'Help_chat'])
            
            # Perform cache-optimized scan
            scan_start = time.time()
            self.current_scan_data = self.scanner.scan_screen_cache_optimized(list(required_templates))
            scan_time = time.time() - scan_start
            
            # Update scan info
            templates_detected = self.current_scan_data.get('templates_detected', {})
            detected_count = sum(1 for detected in templates_detected.values() if detected)
            
            self.scan_info_var.set(
                f"Scan completed in {scan_time:.3f}s | "
                f"Templates scanned: {len(required_templates)} | "
                f"Detected: {detected_count} | "
                f"Screen size: {self.current_scan_data.get('screen_size', 'Unknown')}"
            )
            
            # Update detection results
            self.update_detection_results(enabled_modules, templates_detected)
            
            # Determine next action
            self.determine_next_action(enabled_modules)
            
            self.status_var.set("✅ Scan completed")
            
        except Exception as e:
            messagebox.showerror("Error", f"Scan failed: {str(e)}")
            self.status_var.set("❌ Scan failed")
        
        finally:
            self.scanning = False
            self.scan_button.configure(state='normal')
    
    def update_detection_results(self, modules: List, templates_detected: Dict[str, bool]):
        """Update the detection results tree"""
        # Clear existing items
        for item in self.detection_tree.get_children():
            self.detection_tree.delete(item)
        
        # Add detection results
        for module in modules:
            module_templates = []
            
            # Get templates for this module
            if hasattr(module, 'get_required_templates'):
                module_templates = module.get_required_templates()
            else:
                # Default templates based on module name
                if module.name == "zombie_invasion":
                    module_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4', 'squad_4_4', 'events_button']
                elif module.name == "alliance_donation":
                    module_templates = ['events_button']
                elif module.name == "daily_tasks":
                    module_templates = ['events_button']
                elif module.name == "help_click":
                    module_templates = ['Help_template', 'Help_chat']
            
            # Check if module can execute with detailed logging
            can_execute = False
            execution_reason = "No templates detected"

            if hasattr(module, 'can_execute'):
                try:
                    can_execute = module.can_execute(self.current_scan_data)
                    if can_execute:
                        execution_reason = "Module can_execute returned True"
                    else:
                        execution_reason = "Module can_execute returned False"

                        # For zombie invasion, check specific conditions
                        if module.name == "zombie_invasion":
                            # Check squad status templates
                            squad_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4']
                            squad_detected = any(templates_detected.get(t, False) for t in squad_templates)
                            events_detected = templates_detected.get('events_button', False)

                            if not squad_detected:
                                execution_reason = "No squad status templates detected"
                            elif not events_detected:
                                execution_reason = "Events button not detected"
                            else:
                                execution_reason = "Squad status indicates 4/4 (all squads deployed)"

                except Exception as e:
                    execution_reason = f"can_execute error: {str(e)}"
                    self.log_message(f"❌ {module.name} can_execute error: {str(e)}")
            else:
                # Fallback: Check if any required templates are detected
                detected_templates = []
                for template in module_templates:
                    if templates_detected.get(template, False):
                        can_execute = True
                        detected_templates.append(template)

                if can_execute:
                    execution_reason = f"Templates detected: {', '.join(detected_templates)}"
                else:
                    execution_reason = "No required templates detected"
            
            # Add one entry per module (not per template)
            if module_templates:
                # Count detected templates
                detected_count = sum(1 for t in module_templates if templates_detected.get(t, False))
                template_summary = f"{detected_count}/{len(module_templates)} templates"

                detected_text = "✅ YES" if detected_count > 0 else "❌ NO"
                status = "🎯 CAN EXECUTE" if can_execute else "⏸️ WAITING"

                # Color coding
                if can_execute:
                    tags = ('executable',)
                elif detected_count > 0:
                    tags = ('detected',)
                else:
                    tags = ('not_detected',)

                self.detection_tree.insert('', 'end', values=(
                    template_summary,
                    detected_text,
                    module.name,
                    f"P{module.priority}",
                    status,
                    execution_reason
                ), tags=tags)
            else:
                # Module with no templates
                self.detection_tree.insert('', 'end', values=(
                    "No templates",
                    "N/A",
                    module.name,
                    f"P{module.priority}",
                    "🎯 CAN EXECUTE" if can_execute else "⏸️ WAITING",
                    execution_reason
                ), tags=('executable' if can_execute else 'not_detected',))
        
        # Configure tags for color coding
        self.detection_tree.tag_configure('executable', background='#d4edda', foreground='#155724')
        self.detection_tree.tag_configure('detected', background='#fff3cd', foreground='#856404')
        self.detection_tree.tag_configure('not_detected', background='#f8d7da', foreground='#721c24')
    
    def determine_next_action(self, modules: List):
        """Determine what action would be executed next"""
        if not self.current_scan_data:
            self.next_action_var.set("No scan data available")
            return

        # Sort modules by priority (0 = highest priority)
        modules.sort(key=lambda x: x.priority)

        self.log_message(f"🔍 Checking execution priority order:")

        # Find first executable module
        for module in modules:
            if not module.enabled:
                self.log_message(f"  ❌ {module.name} (P{module.priority}): DISABLED")
                continue

            can_execute = False
            reason = "Unknown"

            try:
                if hasattr(module, 'can_execute'):
                    can_execute = module.can_execute(self.current_scan_data)

                    # Special handling for zombie invasion to debug the issue
                    if module.name == "zombie_invasion":
                        templates_detected = self.current_scan_data.get('templates_detected', {})
                        squad_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4']
                        squad_detected = [t for t in squad_templates if templates_detected.get(t, False)]
                        events_detected = templates_detected.get('events_button', False)
                        squad_4_4_detected = templates_detected.get('squad_4_4', False)

                        reason = f"Squad templates: {squad_detected}, Events: {events_detected}, 4/4: {squad_4_4_detected}"

                        # Override can_execute if we detect squads available
                        if squad_detected and events_detected and not squad_4_4_detected:
                            can_execute = True
                            reason += " -> OVERRIDING to execute"

                else:
                    # Fallback logic
                    templates_detected = self.current_scan_data.get('templates_detected', {})
                    module_templates = []

                    if module.name == "zombie_invasion":
                        module_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4', 'events_button']
                    elif module.name == "alliance_donation":
                        module_templates = ['events_button']
                    elif module.name == "daily_tasks":
                        module_templates = ['events_button']
                    elif module.name == "help_click":
                        module_templates = ['Help_template', 'Help_chat']

                    detected_templates = [t for t in module_templates if templates_detected.get(t, False)]
                    can_execute = len(detected_templates) > 0
                    reason = f"Templates detected: {detected_templates}"

                status = "✅ CAN EXECUTE" if can_execute else "⏸️ WAITING"
                self.log_message(f"  {status} {module.name} (P{module.priority}): {reason}")

                if can_execute:
                    self.next_action_var.set(f"🎯 Execute: {module.name} (Priority {module.priority})")
                    self.log_message(f"🎯 NEXT ACTION: {module.name} (Priority {module.priority})")
                    return

            except Exception as e:
                self.log_message(f"  ❌ {module.name} (P{module.priority}): ERROR - {str(e)}")

        self.next_action_var.set("⏸️ No executable modules found")
        self.log_message("⏸️ No executable modules found")
    
    def move_priority_up(self):
        """Move selected module up in priority"""
        selection = self.priority_listbox.curselection()
        if not selection or selection[0] == 0:
            return
        
        # Swap priorities
        idx = selection[0]
        modules = sorted(self.controller.modules, key=lambda x: x.priority)
        
        if idx > 0:
            # Swap priorities
            modules[idx].priority, modules[idx-1].priority = modules[idx-1].priority, modules[idx].priority
            self.load_modules()
            self.priority_listbox.selection_set(idx-1)
    
    def move_priority_down(self):
        """Move selected module down in priority"""
        selection = self.priority_listbox.curselection()
        if not selection:
            return
        
        idx = selection[0]
        modules = sorted(self.controller.modules, key=lambda x: x.priority)
        
        if idx < len(modules) - 1:
            # Swap priorities
            modules[idx].priority, modules[idx+1].priority = modules[idx+1].priority, modules[idx].priority
            self.load_modules()
            self.priority_listbox.selection_set(idx+1)
    
    def save_priorities(self):
        """Save current priorities to configuration"""
        try:
            for module in self.controller.modules:
                config = self.config_manager.get_module_config(module.name)
                config['priority'] = module.priority
                self.config_manager.update_module_config(module.name, config)
            
            messagebox.showinfo("Success", "Priorities saved successfully!")
            self.status_var.set("✅ Priorities saved")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save priorities: {str(e)}")
    
    def execute_next_action(self):
        """Execute the next action that would be performed"""
        if not self.current_scan_data:
            messagebox.showwarning("Warning", "Please perform a scan first")
            return
        
        # Find the next executable module
        enabled_modules = [m for m in self.controller.modules if m.enabled]
        enabled_modules.sort(key=lambda x: x.priority)
        
        for module in enabled_modules:
            if hasattr(module, 'can_execute') and module.can_execute(self.current_scan_data):
                try:
                    self.status_var.set(f"🎯 Executing {module.name}...")
                    success = module.execute(self.current_scan_data)
                    
                    if success:
                        self.status_var.set(f"✅ {module.name} executed successfully")
                        messagebox.showinfo("Success", f"Module '{module.name}' executed successfully!")
                    else:
                        self.status_var.set(f"❌ {module.name} execution failed")
                        messagebox.showwarning("Warning", f"Module '{module.name}' execution failed")
                    
                    return
                    
                except Exception as e:
                    messagebox.showerror("Error", f"Execution failed: {str(e)}")
                    self.status_var.set("❌ Execution failed")
                    return
        
        messagebox.showinfo("Info", "No executable modules found")
    
    def create_modules_tab(self, parent):
        """Create the modules details tab"""
        modules_content = ttk.Frame(parent, padding="10")
        modules_content.pack(fill=tk.BOTH, expand=True)

        # Module list
        modules_list_frame = ttk.LabelFrame(modules_content, text="📋 Module Configuration", padding="10")
        modules_list_frame.pack(fill=tk.BOTH, expand=True)

        # Module details tree
        modules_columns = ('Module', 'Priority', 'Enabled', 'Cooldown', 'Last Execution', 'Templates', 'Coordinates')
        self.modules_tree = ttk.Treeview(modules_list_frame, columns=modules_columns, show='headings', height=15)

        for col in modules_columns:
            self.modules_tree.heading(col, text=col)
            self.modules_tree.column(col, width=120)

        modules_scroll = ttk.Scrollbar(modules_list_frame, orient=tk.VERTICAL, command=self.modules_tree.yview)
        self.modules_tree.configure(yscrollcommand=modules_scroll.set)

        self.modules_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        modules_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Module control buttons
        modules_buttons = ttk.Frame(modules_list_frame)
        modules_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(modules_buttons, text="🔄 Refresh", command=self.refresh_modules_tab).pack(side=tk.LEFT, padx=5)
        ttk.Button(modules_buttons, text="✅ Enable Selected", command=self.enable_module).pack(side=tk.LEFT, padx=5)
        ttk.Button(modules_buttons, text="❌ Disable Selected", command=self.disable_module).pack(side=tk.LEFT, padx=5)
        ttk.Button(modules_buttons, text="🧪 Test Selected", command=self.test_module).pack(side=tk.LEFT, padx=5)

    def create_templates_tab(self, parent):
        """Create the templates manager tab"""
        templates_content = ttk.Frame(parent, padding="10")
        templates_content.pack(fill=tk.BOTH, expand=True)

        # Templates list
        templates_list_frame = ttk.LabelFrame(templates_content, text="🖼️ Template Status", padding="10")
        templates_list_frame.pack(fill=tk.BOTH, expand=True)

        # Templates tree
        templates_columns = ('Template', 'File Exists', 'Size', 'Used By Modules', 'Last Detection')
        self.templates_tree = ttk.Treeview(templates_list_frame, columns=templates_columns, show='headings', height=15)

        for col in templates_columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=150)

        templates_scroll = ttk.Scrollbar(templates_list_frame, orient=tk.VERTICAL, command=self.templates_tree.yview)
        self.templates_tree.configure(yscrollcommand=templates_scroll.set)

        self.templates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        templates_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Template control buttons
        templates_buttons = ttk.Frame(templates_list_frame)
        templates_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(templates_buttons, text="🔄 Refresh", command=self.refresh_templates_tab).pack(side=tk.LEFT, padx=5)
        ttk.Button(templates_buttons, text="🧪 Test Template", command=self.test_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(templates_buttons, text="📁 Open Templates Folder", command=self.open_templates_folder).pack(side=tk.LEFT, padx=5)

    def create_logs_tab(self, parent):
        """Create the system logs tab"""
        logs_content = ttk.Frame(parent, padding="10")
        logs_content.pack(fill=tk.BOTH, expand=True)

        # Log display
        logs_frame = ttk.LabelFrame(logs_content, text="📝 System Activity Log", padding="10")
        logs_frame.pack(fill=tk.BOTH, expand=True)

        # Log text area
        self.log_text = tk.Text(logs_frame, height=20, wrap=tk.WORD, font=('Consolas', 9))
        log_scroll = ttk.Scrollbar(logs_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scroll.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Log control buttons
        logs_buttons = ttk.Frame(logs_frame)
        logs_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(logs_buttons, text="🔄 Refresh", command=self.refresh_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(logs_buttons, text="🗑️ Clear", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(logs_buttons, text="💾 Save Log", command=self.save_log).pack(side=tk.LEFT, padx=5)

        # Initialize log
        self.log_message("🚀 Diagnostic window started")

    def toggle_auto_scan(self):
        """Toggle auto scanning mode"""
        if self.auto_scan:
            self.auto_scan = False
            self.auto_scan_button.configure(text="🔄 Auto Scan")
            self.status_var.set("Auto scan stopped")
            self.log_message("⏹️ Auto scan stopped")
        else:
            self.auto_scan = True
            self.auto_scan_button.configure(text="⏹️ Stop Auto")
            self.status_var.set("Auto scan started")
            self.log_message("▶️ Auto scan started")
            self.start_auto_scan()

    def start_auto_scan(self):
        """Start auto scanning in a separate thread"""
        if self.scan_thread and self.scan_thread.is_alive():
            return

        self.scan_thread = threading.Thread(target=self.auto_scan_loop, daemon=True)
        self.scan_thread.start()

    def auto_scan_loop(self):
        """Auto scan loop"""
        while self.auto_scan:
            try:
                self.perform_scan()
                interval = float(self.scan_interval_var.get())
                time.sleep(interval)
            except Exception as e:
                self.log_message(f"❌ Auto scan error: {str(e)}")
                break

    def reload_modules(self):
        """Reload all modules"""
        try:
            self.log_message("🔄 Reloading modules...")
            self.controller = self.setup_controller_with_modules()
            self.load_modules()
            self.refresh_modules_tab()
            self.log_message("✅ Modules reloaded successfully")
            self.status_var.set("Modules reloaded")
        except Exception as e:
            self.log_message(f"❌ Failed to reload modules: {str(e)}")
            messagebox.showerror("Error", f"Failed to reload modules: {str(e)}")

    def open_module_config(self):
        """Open module configuration window"""
        try:
            import subprocess
            subprocess.Popen(["python", "config_editor_gui.py"])
            self.log_message("⚙️ Opened module configuration window")
        except Exception as e:
            self.log_message(f"❌ Failed to open config: {str(e)}")
            messagebox.showerror("Error", f"Failed to open config: {str(e)}")

    def full_system_test(self):
        """Perform a comprehensive system test"""
        self.log_message("🧪 Starting full system test...")

        try:
            # Test 1: Scanner
            self.log_message("  📸 Testing screen scanner...")
            test_scan = self.scanner.scan_screen_cache_optimized(['Help_template'])
            if 'error' in test_scan:
                self.log_message(f"  ❌ Scanner test failed: {test_scan['error']}")
            else:
                self.log_message("  ✅ Scanner test passed")

            # Test 2: Modules
            self.log_message("  🔧 Testing modules...")
            for module in self.controller.modules:
                try:
                    can_execute = hasattr(module, 'can_execute')
                    has_execute = hasattr(module, 'execute')
                    self.log_message(f"    {module.name}: can_execute={can_execute}, execute={has_execute}")
                except Exception as e:
                    self.log_message(f"    ❌ {module.name}: {str(e)}")

            # Test 3: Templates
            self.log_message("  🖼️ Testing templates...")
            templates_count = len(self.scanner.templates)
            self.log_message(f"    Templates loaded: {templates_count}")

            # Test 4: Configuration
            self.log_message("  ⚙️ Testing configuration...")
            modules_config = self.config_manager.list_modules()
            self.log_message(f"    Configured modules: {len(modules_config)}")

            self.log_message("✅ Full system test completed")
            self.status_var.set("System test completed")

        except Exception as e:
            self.log_message(f"❌ System test failed: {str(e)}")
            messagebox.showerror("Error", f"System test failed: {str(e)}")

    def refresh_modules_tab(self):
        """Refresh the modules tab"""
        if not hasattr(self, 'modules_tree'):
            return

        # Clear existing items
        for item in self.modules_tree.get_children():
            self.modules_tree.delete(item)

        # Add module data
        for module in self.controller.modules:
            config = self.config_manager.get_module_config(module.name)
            templates_count = len(config.get('templates', []))
            coords_count = len(config.get('click_coordinates', []))

            self.modules_tree.insert('', 'end', values=(
                module.name,
                f"P{module.priority}",
                "✅ Yes" if module.enabled else "❌ No",
                f"{config.get('cooldown', 0)}s",
                getattr(module, 'last_execution', 'Never'),
                templates_count,
                coords_count
            ))

    def refresh_templates_tab(self):
        """Refresh the templates tab"""
        if not hasattr(self, 'templates_tree'):
            return

        try:
            # Clear existing items
            for item in self.templates_tree.get_children():
                self.templates_tree.delete(item)

            # Add template data
            for template_name, template_data in self.scanner.templates.items():
                template_path = os.path.join('templates', f"{template_name}.png")
                file_exists = "✅ Yes" if os.path.exists(template_path) else "❌ No"

                # Get file size
                size = "Unknown"
                if os.path.exists(template_path):
                    try:
                        size_bytes = os.path.getsize(template_path)
                        if size_bytes < 1024:
                            size = f"{size_bytes} B"
                        elif size_bytes < 1024*1024:
                            size = f"{size_bytes//1024} KB"
                        else:
                            size = f"{size_bytes//(1024*1024)} MB"
                    except:
                        size = "Error"

                # Find which modules use this template
                using_modules = []
                try:
                    for module in self.controller.modules:
                        config = self.config_manager.get_module_config(module.name)
                        templates = config.get('templates', [])
                        for template in templates:
                            if template.get('name') == template_name:
                                using_modules.append(module.name)
                                break
                except Exception as e:
                    self.log_message(f"❌ Error checking module usage for {template_name}: {str(e)}")

                # Get last detection status
                last_detection = "Not scanned"
                if self.current_scan_data:
                    templates_detected = self.current_scan_data.get('templates_detected', {})
                    if template_name in templates_detected:
                        last_detection = "✅ Detected" if templates_detected[template_name] else "❌ Not detected"

                self.templates_tree.insert('', 'end', values=(
                    template_name,
                    file_exists,
                    size,
                    ', '.join(using_modules) if using_modules else 'None',
                    last_detection
                ))

            self.log_message(f"🖼️ Refreshed {len(self.scanner.templates)} templates")

        except Exception as e:
            self.log_message(f"❌ Error refreshing templates tab: {str(e)}")

    def enable_module(self):
        """Enable selected module"""
        selection = self.modules_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a module")
            return

        item = self.modules_tree.item(selection[0])
        module_name = item['values'][0]

        for module in self.controller.modules:
            if module.name == module_name:
                module.enabled = True
                self.log_message(f"✅ Enabled module: {module_name}")
                break

        self.refresh_modules_tab()

    def disable_module(self):
        """Disable selected module"""
        selection = self.modules_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a module")
            return

        item = self.modules_tree.item(selection[0])
        module_name = item['values'][0]

        for module in self.controller.modules:
            if module.name == module_name:
                module.enabled = False
                self.log_message(f"❌ Disabled module: {module_name}")
                break

        self.refresh_modules_tab()

    def test_module(self):
        """Test selected module"""
        selection = self.modules_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a module")
            return

        item = self.modules_tree.item(selection[0])
        module_name = item['values'][0]

        for module in self.controller.modules:
            if module.name == module_name:
                try:
                    if self.current_scan_data:
                        can_execute = module.can_execute(self.current_scan_data) if hasattr(module, 'can_execute') else False
                        self.log_message(f"🧪 Test {module_name}: can_execute = {can_execute}")
                        messagebox.showinfo("Test Result", f"Module '{module_name}' can execute: {can_execute}")
                    else:
                        messagebox.showwarning("Warning", "Please perform a scan first")
                except Exception as e:
                    self.log_message(f"❌ Test {module_name} failed: {str(e)}")
                    messagebox.showerror("Error", f"Test failed: {str(e)}")
                break

    def test_template(self):
        """Test selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        if self.current_scan_data:
            detected = self.current_scan_data.get('templates_detected', {}).get(template_name, False)
            self.log_message(f"🧪 Template {template_name}: detected = {detected}")
            messagebox.showinfo("Template Test", f"Template '{template_name}' detected: {detected}")
        else:
            messagebox.showwarning("Warning", "Please perform a scan first")

    def open_templates_folder(self):
        """Open templates folder"""
        try:
            import subprocess
            subprocess.Popen(["explorer", "templates"])
            self.log_message("📁 Opened templates folder")
        except Exception as e:
            self.log_message(f"❌ Failed to open templates folder: {str(e)}")

    def log_message(self, message):
        """Add message to log"""
        if hasattr(self, 'log_text'):
            timestamp = time.strftime("%H:%M:%S")
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.log_text.see(tk.END)

    def refresh_logs(self):
        """Refresh logs display"""
        self.log_message("🔄 Logs refreshed")

    def clear_logs(self):
        """Clear logs display"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
            self.log_message("🗑️ Logs cleared")

    def save_log(self):
        """Save log to file"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"diagnostic_log_{timestamp}.txt"

            with open(filename, 'w') as f:
                f.write(self.log_text.get(1.0, tk.END))

            self.log_message(f"💾 Log saved to {filename}")
            messagebox.showinfo("Success", f"Log saved to {filename}")
        except Exception as e:
            self.log_message(f"❌ Failed to save log: {str(e)}")
            messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def run(self):
        """Start the diagnostic window"""
        self.log_message("🚀 Diagnostic window ready")
        self.root.mainloop()

if __name__ == "__main__":
    app = MainModuleDiagnostic()
    app.run()
