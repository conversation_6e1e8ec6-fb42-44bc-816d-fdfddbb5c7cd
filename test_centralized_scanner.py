#!/usr/bin/env python3
"""
Test script for centralized template scanner
Tests the action method routing functionality
"""

import sys
import logging
from centralized_template_scanner import CentralizedTemplateScanner
from screen_scanner import ScreenScanner

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_centralized_scanner():
    """Test the centralized scanner functionality"""
    print("🧪 Testing Centralized Template Scanner")
    print("=" * 50)
    
    try:
        # Initialize screen scanner
        print("📷 Initializing screen scanner...")
        screen_scanner = ScreenScanner()
        
        # Initialize centralized scanner
        print("🎯 Initializing centralized template scanner...")
        centralized_scanner = CentralizedTemplateScanner(screen_scanner)
        
        # Print loaded mappings
        print(f"✅ Loaded {len(centralized_scanner.template_mappings)} template mappings:")
        for mapping in centralized_scanner.template_mappings:
            status = "✅ ENABLED" if mapping.enabled else "❌ DISABLED"
            print(f"  {mapping.priority:2d}. {mapping.template_name} -> {mapping.module}.{mapping.action} {status}")
        
        # Test module registration and action method availability
        print("\n🔧 Testing module registration...")

        # Register modules manually for testing
        unique_modules = set(mapping.module for mapping in centralized_scanner.template_mappings if mapping.enabled)

        for module_name in unique_modules:
            try:
                # Import and instantiate the module
                if module_name == "help_click":
                    from modules.enhanced_help_click import EnhancedHelpClickModule
                    module_instance = EnhancedHelpClickModule()
                elif module_name == "alliance_donation":
                    from modules.alliance_donation import AllianceDonationModule
                    module_instance = AllianceDonationModule()
                elif module_name == "zombie_invasion":
                    from modules.zombie_invasion import ZombieInvasionModule
                    module_instance = ZombieInvasionModule()
                elif module_name == "map_trade":
                    from modules.map_trade import MapTradeModule
                    module_instance = MapTradeModule()
                elif module_name == "find_truck":
                    from modules.find_truck import FindTruckModule
                    module_instance = FindTruckModule(screen_scanner)
                elif module_name == "daily_tasks":
                    from modules.daily_tasks import DailyTasksModule
                    module_instance = DailyTasksModule()
                else:
                    print(f"  ❓ Unknown module: {module_name}")
                    continue

                # Register the module
                centralized_scanner.register_module(module_name, module_instance)
                print(f"  ✅ Registered {module_name}: {type(module_instance).__name__}")

            except Exception as e:
                print(f"  ❌ Failed to register {module_name}: {e}")

        # Test action method availability
        print("\n🎯 Testing action method availability...")
        for mapping in centralized_scanner.template_mappings:
            if not mapping.enabled:
                continue

            module_name = mapping.module
            action_name = mapping.action

            module_instance = centralized_scanner.module_registry.get(module_name)
            if module_instance and hasattr(module_instance, action_name):
                print(f"  ✅ {module_name}.{action_name} - Method exists")
            elif module_instance:
                print(f"  ❌ {module_name}.{action_name} - Method missing!")
            else:
                print(f"  ❌ {module_name} - Module not registered!")
        
        print("\n🎉 Centralized scanner test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_centralized_scanner()
    sys.exit(0 if success else 1)
