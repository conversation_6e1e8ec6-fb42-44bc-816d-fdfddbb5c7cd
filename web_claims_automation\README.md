# Web Claims Automation

A standalone automation program to help fill out repetitive claims on websites by navigating through web forms and automatically filling appointment details.

## Features

### Main Interface
- **Control Panel**: Go, Stop, and Pause buttons for automation control
- **Appointment Input**: Large text box for entering appointment details
- **Scenario Selection**: Dropdown to choose automation workflows
- **Progress Tracking**: Completed appointments log

### Configuration System
- **Scenario Management**: Create and edit different automation workflows
- **Appointment Types**: Support for <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and custom types with:
  - **Distance Configuration**: Set distance from home for each appointment type
  - **Scenario Linking**: Link appointment types to specific scenarios to avoid repetitive configuration
  - **Edit Functionality**: Modify existing appointment types (distance and scenario)
- **Click Recording**: Record mouse clicks and build automation sequences
- **Visual Management**: Treeview display showing Type, Distance, and Linked Scenario columns

### Smart Form Filling
- **Date Parsing**: Automatically converts "1 Jan", "15 Mar" format to web form dates
- **Time Calculation**: Automatically calculates end time (start time + 1 hour)
- **Year Selection**: Configurable year selection for appointments

## Installation

1. Install Python 3.7 or higher
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. **Start the Application**:
   ```bash
   python main.py
   ```

2. **Create a Scenario**:
   - Go to the "Configuration" tab
   - Enter a scenario name
   - Click "Create New"
   - Click "Record Clicks" to record your automation sequence

3. **Configure Appointment Types**:
   - Add new appointment types with distance and scenario linking
   - Edit existing types to update distance or change linked scenario
   - Link appointment types to scenarios to avoid repetitive configuration

4. **Record Action Sequence** (Professional Recording System):
   - Click "Record Clicks" to open recording control window
   - Navigate to your target website/form (recording not active yet)
   - Click "Start Capture" when ready to begin recording coordinates
   - Position mouse over target element and press SPACEBAR to capture coordinates precisely
   - Configure action in dialog:
     * **Click Types**: Regular, Double, Right-click
     * **Text Input Types**: Static text, Date (auto-fill), Start Time, End Time, Appointment Type
     * **Scroll Actions**: Use "Add Scroll" button for page scrolling
   - Click "Save Action" to add to sequence (or Cancel to discard)
   - Use "Pause" to stop capturing while you navigate or check progress
   - Use "Test Selected" to test individual actions
   - Use "Retake Selected" to update coordinates for existing actions
   - Click "Stop Recording" when sequence is complete

5. **Run Automation**:
   - Go to the "Automation" tab
   - Enter appointments in format: "1 Jan 2:30 PM" (one per line)
   - Select your scenario
   - Choose the year
   - Click "Go" to start automation

## Appointment Format

Enter appointments in the text box using this format:
```
1 Jan 2:30 PM
15 Mar 10:00 AM
22 Dec 3:45 PM
```

- **Date**: Short format like "1 Jan", "15 Mar"
- **Time**: 12-hour format with AM/PM

## Scenario Types

### Enhanced Recording System

#### **Professional Recording Workflow**
- **Controlled Start**: Recording window opens but capture is not active until you click "Start Capture"
- **Navigation Time**: Navigate to your target website/form before starting capture
- **Precise Positioning**: Position mouse exactly where you want to click
- **Spacebar Trigger**: Press SPACEBAR to capture coordinates (no rushing with timers)
- **Real-time Position**: Live mouse position display during recording
- **Pause/Resume**: Pause capture to navigate, check progress, or take breaks
- **Action Configuration**: Professional dialog with Save/Cancel buttons
- **Testing**: Test individual actions before completing the sequence
- **Retake Coordinates**: Update coordinates for existing actions if needed

#### **Click Actions**
- **Regular Click**: Single mouse click at captured coordinates
- **Double Click**: Double mouse click at captured coordinates
- **Right Click**: Right mouse click at captured coordinates

#### **Text Input Actions**
- **Static Text**: Type predefined text at captured position
- **Date Input**: Automatically filled from appointment date (MM/DD/YYYY format)
- **Start Time**: Automatically filled from appointment time
- **End Time**: Automatically calculated (start time + 1 hour)
- **Appointment Type**: Select appointment type from configured list

#### **Scroll Actions**
- **Scroll Down/Up**: Configurable scroll direction and amount
- **Page Navigation**: Scroll between form sections during automation
- **Flexible Amount**: Set number of scroll clicks (1-10+)

## Configuration Files

- `claims_config.json`: Stores scenarios and appointment types
- `web_claims_automation.log`: Application log file

## Safety Features

- **Pause/Resume**: Pause automation at any time
- **Stop**: Emergency stop functionality
- **Failsafe**: PyAutoGUI failsafe (move mouse to corner to stop)
- **Error Handling**: Graceful error handling and logging

## Tips

1. **Test First**: Always test your scenarios with a single appointment before running multiple
2. **Screen Resolution**: Record clicks on the same screen resolution you'll use for automation
3. **Timing**: Adjust delays between clicks based on website response times
4. **Backup**: Keep backups of your configuration file
5. **Browser Consistency**: Use the same browser and window size for recording and automation

## Troubleshooting

### Common Issues

1. **Clicks Missing Targets**:
   - Check screen resolution matches recording environment
   - Increase delays between clicks
   - Re-record clicks if website layout changed

2. **Text Not Entering Correctly**:
   - Ensure text fields are properly focused before typing
   - Add click action before text input
   - Check field type configuration

3. **Automation Stops Unexpectedly**:
   - Check log file for error details
   - Verify website hasn't changed layout
   - Ensure all required form fields are configured

### Log Files

Check `web_claims_automation.log` for detailed error information and automation progress.

## Support

This is a standalone automation tool. Modify the code as needed for your specific website and requirements.
