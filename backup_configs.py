"""
Automatic backup system for module configurations
Creates timestamped backups and maintains a history
"""
import shutil
import os
import json
from datetime import datetime
import glob

def create_backup():
    """Create a timestamped backup of module_configs.json"""
    if not os.path.exists("module_configs.json"):
        print("❌ No module_configs.json found to backup")
        return None
    
    # Create backups directory if it doesn't exist
    os.makedirs("backups", exist_ok=True)
    
    # Create timestamped backup
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"backups/module_configs_{timestamp}.json"
    
    try:
        shutil.copy2("module_configs.json", backup_name)
        print(f"✅ Backup created: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ Backup failed: {str(e)}")
        return None

def list_backups():
    """List all available backups"""
    if not os.path.exists("backups"):
        print("No backups directory found")
        return []
    
    backups = glob.glob("backups/module_configs_*.json")
    backups.sort(reverse=True)  # Most recent first
    
    print(f"Found {len(backups)} backups:")
    for i, backup in enumerate(backups):
        # Extract timestamp from filename
        filename = os.path.basename(backup)
        timestamp = filename.replace("module_configs_", "").replace(".json", "")
        
        # Format timestamp for display
        try:
            dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
            formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
            print(f"  {i+1}. {formatted_time} - {backup}")
        except:
            print(f"  {i+1}. {timestamp} - {backup}")
    
    return backups

def restore_backup(backup_path):
    """Restore a backup"""
    if not os.path.exists(backup_path):
        print(f"❌ Backup file not found: {backup_path}")
        return False
    
    try:
        # Create backup of current config before restoring
        current_backup = create_backup()
        if current_backup:
            print(f"Current config backed up as: {current_backup}")
        
        # Restore the backup
        shutil.copy2(backup_path, "module_configs.json")
        print(f"✅ Restored backup: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Restore failed: {str(e)}")
        return False

def cleanup_old_backups(keep_count=10):
    """Keep only the most recent N backups"""
    backups = glob.glob("backups/module_configs_*.json")
    backups.sort(reverse=True)  # Most recent first
    
    if len(backups) <= keep_count:
        print(f"Only {len(backups)} backups found, no cleanup needed")
        return
    
    # Remove old backups
    for backup in backups[keep_count:]:
        try:
            os.remove(backup)
            print(f"Removed old backup: {backup}")
        except Exception as e:
            print(f"Failed to remove {backup}: {str(e)}")

def interactive_restore():
    """Interactive backup restoration"""
    backups = list_backups()
    
    if not backups:
        print("No backups available")
        return
    
    try:
        choice = input(f"\nEnter backup number to restore (1-{len(backups)}) or 'q' to quit: ")
        
        if choice.lower() == 'q':
            return
        
        backup_index = int(choice) - 1
        if 0 <= backup_index < len(backups):
            backup_path = backups[backup_index]
            confirm = input(f"Restore {backup_path}? (y/N): ")
            
            if confirm.lower() == 'y':
                restore_backup(backup_path)
            else:
                print("Restore cancelled")
        else:
            print("Invalid backup number")
            
    except ValueError:
        print("Invalid input")
    except KeyboardInterrupt:
        print("\nCancelled")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "backup":
            create_backup()
        elif command == "list":
            list_backups()
        elif command == "restore":
            interactive_restore()
        elif command == "cleanup":
            cleanup_old_backups()
        else:
            print("Usage: python backup_configs.py [backup|list|restore|cleanup]")
    else:
        # Default: create backup and list existing ones
        create_backup()
        print()
        list_backups()
