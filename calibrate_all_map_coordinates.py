
#!/usr/bin/env python3
"""
Complete Map Coordinate Calibration Tool
Interactive tool to set precise coordinates for all map pieces M1-M7
"""
import cv2
import numpy as np
import pytesseract
import pyautogui
import time
import json
from typing import Dict, Tuple

class MapCoordinateCalibrator:
    def __init__(self):
        # Configure Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # Map pieces to calibrate
        self.map_pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
        self.coordinates = {}
        
        print("=== MAP COORDINATE CALIBRATION TOOL ===")
        print("This tool will help you set precise coordinates for all map pieces")
        print("Make sure your game is visible and you're on the map trade screen")
        print()

    def test_ocr_at_position(self, x: int, y: int, size: int = 35) -> str:
        """Test OCR at a specific position with the proven method"""
        try:
            # Capture region
            region_x = x - size // 2
            region_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply 3x scaling (proven method)
            scaled = cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
            
            # Use proven PSM 8 configuration
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(scaled, config=config).strip()
            
            return text if text.isdigit() else f"'{text}'"
            
        except Exception as e:
            return f"ERROR: {e}"

    def capture_and_save_debug(self, map_name: str, x: int, y: int, size: int = 35):
        """Capture and save debug image for inspection"""
        try:
            region_x = x - size // 2
            region_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Save original
            cv2.imwrite(f"debug_{map_name}_calibration.png", image)
            
            # Save grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            cv2.imwrite(f"debug_{map_name}_gray.png", gray)
            
            # Save 3x scaled
            scaled = cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
            cv2.imwrite(f"debug_{map_name}_3x.png", scaled)
            
            print(f"  Debug images saved: debug_{map_name}_calibration.png, debug_{map_name}_gray.png, debug_{map_name}_3x.png")
            
        except Exception as e:
            print(f"  Error saving debug images: {e}")

    def calibrate_single_piece(self, map_name: str) -> Tuple[int, int]:
        """Calibrate coordinates for a single map piece"""
        print(f"\n=== CALIBRATING {map_name} ===")
        print("Instructions:")
        print("1. Position your mouse over the NUMBER for this map piece")
        print("2. Press ENTER to capture the coordinate")
        print("3. The tool will test OCR and show results")
        print("4. If OCR fails, try again with better positioning")
        print()
        
        while True:
            print(f"Position mouse over {map_name} number and press ENTER...")
            input("Press ENTER when mouse is positioned correctly: ")

            # Get mouse position
            x, y = pyautogui.position()
            print(f"Captured position: ({x}, {y})")
            
            # Test OCR at this position
            print("Testing OCR...")
            ocr_result = self.test_ocr_at_position(x, y)
            print(f"OCR Result: {ocr_result}")
            
            # Save debug images
            self.capture_and_save_debug(map_name, x, y)
            
            # Ask if this is good
            if ocr_result.isdigit():
                print(f"✅ SUCCESS! OCR detected number: {ocr_result}")
                confirm = input(f"Accept this coordinate for {map_name}? (y/n): ").lower().strip()
                if confirm == 'y':
                    return x, y
            else:
                print(f"❌ OCR failed or detected non-digit: {ocr_result}")
            
            retry = input("Try again? (y/n): ").lower().strip()
            if retry != 'y':
                # Use current position anyway
                return x, y

    def calibrate_all_pieces(self):
        """Calibrate all map pieces"""
        print("Starting calibration for all map pieces...")
        print("Make sure you can see all map pieces M1-M7 on screen")
        input("Press ENTER when ready...")
        
        for map_name in self.map_pieces:
            quantity_x, quantity_y = self.calibrate_single_piece(map_name)
            
            # Calculate click position (slightly above and left of quantity)
            click_x = quantity_x - 20
            click_y = quantity_y - 30
            
            self.coordinates[map_name] = {
                'quantity_pos': (quantity_x, quantity_y),
                'click_pos': (click_x, click_y),
                'quantity': None
            }
            
            print(f"✅ {map_name} calibrated:")
            print(f"   Quantity: ({quantity_x}, {quantity_y})")
            print(f"   Click: ({click_x}, {click_y})")

    def test_all_coordinates(self):
        """Test OCR on all calibrated coordinates"""
        print("\n=== TESTING ALL COORDINATES ===")
        
        success_count = 0
        for map_name, coords in self.coordinates.items():
            x, y = coords['quantity_pos']
            ocr_result = self.test_ocr_at_position(x, y)
            
            if ocr_result.isdigit():
                print(f"✅ {map_name}: {ocr_result}")
                success_count += 1
            else:
                print(f"❌ {map_name}: {ocr_result}")
        
        print(f"\nSuccess Rate: {success_count}/{len(self.map_pieces)} ({success_count/len(self.map_pieces)*100:.1f}%)")
        return success_count == len(self.map_pieces)

    def save_coordinates(self):
        """Save coordinates to file and display for code update"""
        # Save to JSON file
        with open('calibrated_map_coordinates.json', 'w') as f:
            json.dump(self.coordinates, f, indent=2)
        
        print(f"\n=== CALIBRATION COMPLETE ===")
        print("Coordinates saved to: calibrated_map_coordinates.json")
        print("\nCopy this code to your _define_map_piece_regions method:")
        print("=" * 60)
        print("return {")
        for map_name, coords in self.coordinates.items():
            quantity_pos = coords['quantity_pos']
            click_pos = coords['click_pos']
            print(f"    '{map_name}': {{")
            print(f"        'quantity_pos': {quantity_pos},")
            print(f"        'click_pos': {click_pos},")
            print(f"        'quantity': None")
            print(f"    }},")
        print("}")
        print("=" * 60)

    def run_calibration(self):
        """Run the complete calibration process"""
        try:
            # Calibrate all pieces
            self.calibrate_all_pieces()
            
            # Test all coordinates
            if self.test_all_coordinates():
                print("\n🎉 ALL COORDINATES WORKING! 100% SUCCESS RATE!")
            else:
                print("\n⚠️  Some coordinates need adjustment")
                retry = input("Recalibrate failed pieces? (y/n): ").lower().strip()
                if retry == 'y':
                    # TODO: Add selective recalibration
                    pass
            
            # Save results
            self.save_coordinates()
            
        except KeyboardInterrupt:
            print("\nCalibration interrupted by user")
        except Exception as e:
            print(f"Error during calibration: {e}")

if __name__ == "__main__":
    calibrator = MapCoordinateCalibrator()
    calibrator.run_calibration()
