#!/usr/bin/env python3
"""
Fix indentation issues caused by Unicode cleanup
"""

import os
import re

def fix_indentation_in_file(file_path):
    """Fix indentation issues in a Python file"""
    
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixed_lines = []
        changes_made = 0
        
        for i, line in enumerate(lines):
            original_line = line
            
            # Fix common indentation issues
            # If line starts with a single space followed by code, convert to 4 spaces
            if re.match(r'^ [a-zA-Z_]', line):
                line = '    ' + line[1:]
                changes_made += 1
            
            # If line starts with 2 spaces followed by code, convert to 8 spaces  
            elif re.match(r'^  [a-zA-Z_]', line):
                line = '        ' + line[2:]
                changes_made += 1
            
            # If line starts with 3 spaces followed by code, convert to 12 spaces
            elif re.match(r'^   [a-zA-Z_]', line):
                line = '            ' + line[3:]
                changes_made += 1
            
            fixed_lines.append(line)
        
        # Write back if changes were made
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(fixed_lines)
            print(f"Fixed {changes_made} indentation issues in {file_path}")
            return changes_made
        else:
            print(f"No indentation issues found in {file_path}")
            return 0
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0

def main():
    """Fix indentation in all module files"""
    
    print("Indentation Fix Tool")
    print("=" * 25)
    
    # Files to fix
    module_files = [
        "modules/daily_tasks.py",
        "modules/dig.py", 
        "modules/enhanced_help_click.py",
        "modules/help_click.py",
        "modules/alliance_donation.py",
        "modules/gathering_troops.py",
        "modules/zombie_invasion.py",
        "modules/map_trade.py"
    ]
    
    total_changes = 0
    
    for file_path in module_files:
        if os.path.exists(file_path):
            changes = fix_indentation_in_file(file_path)
            total_changes += changes
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nTotal indentation issues fixed: {total_changes}")
    
    if total_changes > 0:
        print("Indentation issues fixed! System should work now.")
    else:
        print("No indentation issues found.")

if __name__ == "__main__":
    main()
