#!/usr/bin/env python3
"""
Live OCR Diagnostic Tool - Multi-Engine Version
Tests Tesseract, EasyOCR, PaddleOCR, and TrOCR
"""
import cv2
import numpy as np
import pytesseract
import pyautogui
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import threading
import time
import io
import base64

# Advanced OCR imports (with fallbacks)
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

try:
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    TROCR_AVAILABLE = True
except ImportError:
    TROCR_AVAILABLE = False

class LiveOCRDiagnostic:
    def __init__(self):
        # Configure Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Initialize OCR engines
        self.init_ocr_engines()

        # Test sizes
        self.sizes = [10, 15, 20, 25, 30, 35, 40, 45, 50]

        # GUI setup
        self.root = tk.Tk()
        self.root.title("Multi-Engine OCR Diagnostic Tool")
        self.root.geometry("1600x1000")

        # Current mouse position
        self.current_x = 0
        self.current_y = 0

        # Running flag
        self.running = False

        self.setup_gui()
        self.setup_keyboard_bindings()

    def init_ocr_engines(self):
        """Initialize all available OCR engines"""
        print("Initializing OCR engines...")

        # EasyOCR
        if EASYOCR_AVAILABLE:
            try:
                self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
                print("✅ EasyOCR initialized")
            except Exception as e:
                print(f"❌ EasyOCR failed: {e}")
                self.easyocr_reader = None
        else:
            self.easyocr_reader = None
            print("❌ EasyOCR not available")

        # PaddleOCR
        if PADDLEOCR_AVAILABLE:
            try:
                self.paddle_ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                print("✅ PaddleOCR initialized")
            except Exception as e:
                print(f"❌ PaddleOCR failed: {e}")
                self.paddle_ocr = None
        else:
            self.paddle_ocr = None
            print("❌ PaddleOCR not available")

        # TrOCR
        if TROCR_AVAILABLE:
            try:
                self.trocr_processor = TrOCRProcessor.from_pretrained('microsoft/trocr-base-printed')
                self.trocr_model = VisionEncoderDecoderModel.from_pretrained('microsoft/trocr-base-printed')
                print("✅ TrOCR initialized")
            except Exception as e:
                print(f"❌ TrOCR failed: {e}")
                self.trocr_processor = None
                self.trocr_model = None
        else:
            self.trocr_processor = None
            self.trocr_model = None
            print("❌ TrOCR not available")
        
    def setup_gui(self):
        """Setup the GUI layout"""
        
        # Control frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Position display
        self.pos_label = ttk.Label(control_frame, text="Position: (0, 0)", font=("Arial", 12, "bold"))
        self.pos_label.pack(side=tk.LEFT, padx=10)
        
        # Start/Stop button
        self.start_button = ttk.Button(control_frame, text="Start Live Capture", command=self.toggle_capture)
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        # Instructions
        instructions = ttk.Label(control_frame, text="Position mouse over number, then press SPACEBAR to capture",
                                font=("Arial", 11, "bold"), foreground="blue")
        instructions.pack(side=tk.RIGHT, padx=10)
        
        # Main content frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Canvas and scrollbar
        self.canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Create grid for images and results
        self.image_labels = {}
        self.result_labels = {}
        
        # Header row
        ttk.Label(self.scrollable_frame, text="Size", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="Original", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="Enhanced", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="Tesseract", font=("Arial", 10, "bold")).grid(row=0, column=3, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="EasyOCR", font=("Arial", 10, "bold")).grid(row=0, column=4, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="PaddleOCR", font=("Arial", 10, "bold")).grid(row=0, column=5, padx=5, pady=5)
        ttk.Label(self.scrollable_frame, text="TrOCR", font=("Arial", 10, "bold")).grid(row=0, column=6, padx=5, pady=5)
        
        # Create rows for each size
        for i, size in enumerate(self.sizes):
            row = i + 1
            
            # Size label
            ttk.Label(self.scrollable_frame, text=f"{size}x{size}", font=("Arial", 9, "bold")).grid(row=row, column=0, padx=5, pady=5)
            
            # Image labels
            self.image_labels[size] = {
                'original': ttk.Label(self.scrollable_frame, text="No image"),
                'enhanced': ttk.Label(self.scrollable_frame, text="No image")
            }

            self.image_labels[size]['original'].grid(row=row, column=1, padx=5, pady=5)
            self.image_labels[size]['enhanced'].grid(row=row, column=2, padx=5, pady=5)

            # OCR Results labels (one for each engine)
            self.result_labels[size] = {
                'tesseract': ttk.Label(self.scrollable_frame, text="No results", font=("Arial", 8), wraplength=120),
                'easyocr': ttk.Label(self.scrollable_frame, text="No results", font=("Arial", 8), wraplength=120),
                'paddleocr': ttk.Label(self.scrollable_frame, text="No results", font=("Arial", 8), wraplength=120),
                'trocr': ttk.Label(self.scrollable_frame, text="No results", font=("Arial", 8), wraplength=120)
            }

            self.result_labels[size]['tesseract'].grid(row=row, column=3, padx=5, pady=5, sticky="w")
            self.result_labels[size]['easyocr'].grid(row=row, column=4, padx=5, pady=5, sticky="w")
            self.result_labels[size]['paddleocr'].grid(row=row, column=5, padx=5, pady=5, sticky="w")
            self.result_labels[size]['trocr'].grid(row=row, column=6, padx=5, pady=5, sticky="w")

    def setup_keyboard_bindings(self):
        """Setup keyboard shortcuts"""
        self.root.bind('<KeyPress-space>', self.on_spacebar)
        self.root.focus_set()  # Make sure window can receive key events

    def on_spacebar(self, event):
        """Handle spacebar press"""
        self.capture_current()
    
    def toggle_capture(self):
        """Toggle live capture on/off"""
        if not self.running:
            self.running = True
            self.start_button.config(text="Stop Live Capture")
            self.capture_thread = threading.Thread(target=self.live_capture_loop, daemon=True)
            self.capture_thread.start()
        else:
            self.running = False
            self.start_button.config(text="Start Live Capture")
    
    def live_capture_loop(self):
        """Live capture loop that updates every 500ms"""
        while self.running:
            try:
                # Get current mouse position
                self.current_x, self.current_y = pyautogui.position()
                
                # Update position display
                self.root.after(0, lambda: self.pos_label.config(text=f"Position: ({self.current_x}, {self.current_y})"))
                
                # Capture and analyze
                self.root.after(0, self.capture_and_analyze)
                
                time.sleep(0.5)  # Update every 500ms
            except Exception as e:
                print(f"Error in live capture: {e}")
                break
    
    def capture_current(self):
        """Capture at current mouse position"""
        self.current_x, self.current_y = pyautogui.position()
        self.pos_label.config(text=f"CAPTURED: ({self.current_x}, {self.current_y})")
        self.capture_and_analyze()
    
    def capture_and_analyze(self):
        """Capture screenshots at different sizes and run OCR"""
        try:
            for size in self.sizes:
                # Capture region
                region_x = self.current_x - size // 2
                region_y = self.current_y - size // 2
                
                screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
                image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                
                # Apply contrast enhancement
                enhanced = cv2.convertScaleAbs(gray, alpha=2.0, beta=0)
                
                # Convert images for display (scale up for visibility)
                display_scale = max(1, 60 // size)  # Scale small images up

                # Original image
                original_display = cv2.resize(image, (size * display_scale, size * display_scale), interpolation=cv2.INTER_NEAREST)
                original_pil = Image.fromarray(cv2.cvtColor(original_display, cv2.COLOR_BGR2RGB))
                original_tk = ImageTk.PhotoImage(original_pil)

                # Enhanced image
                enhanced_display = cv2.resize(enhanced, (size * display_scale, size * display_scale), interpolation=cv2.INTER_NEAREST)
                enhanced_pil = Image.fromarray(enhanced_display, mode='L')
                enhanced_tk = ImageTk.PhotoImage(enhanced_pil)

                # Update image displays
                self.image_labels[size]['original'].config(image=original_tk)
                self.image_labels[size]['original'].image = original_tk  # Keep reference

                self.image_labels[size]['enhanced'].config(image=enhanced_tk)
                self.image_labels[size]['enhanced'].image = enhanced_tk

                # Run OCR tests with all engines
                ocr_results = self.run_all_ocr_tests(image, gray, enhanced)

                # Update results display for each engine
                self.result_labels[size]['tesseract'].config(text=f"PSM 8: '{ocr_results['tesseract_psm8']}'\nPSM 6: '{ocr_results['tesseract_psm6']}'\nPSM 7: '{ocr_results['tesseract_psm7']}'")
                self.result_labels[size]['easyocr'].config(text=f"Result: '{ocr_results['easyocr']}'\nConfidence: {ocr_results['easyocr_conf']}")
                self.result_labels[size]['paddleocr'].config(text=f"Result: '{ocr_results['paddleocr']}'\nConfidence: {ocr_results['paddleocr_conf']}")
                self.result_labels[size]['trocr'].config(text=f"Result: '{ocr_results['trocr']}'")

                
        except Exception as e:
            print(f"Error in capture and analyze: {e}")
    
    def run_all_ocr_tests(self, image, gray, enhanced):
        """Run OCR tests with all available engines"""
        results = {}

        # Tesseract tests
        try:
            config8 = '--psm 8 -c tessedit_char_whitelist=0123456789'
            config6 = '--psm 6 -c tessedit_char_whitelist=0123456789'
            config7 = '--psm 7 -c tessedit_char_whitelist=0123456789'

            results['tesseract_psm8'] = pytesseract.image_to_string(enhanced, config=config8).strip()
            results['tesseract_psm6'] = pytesseract.image_to_string(enhanced, config=config6).strip()
            results['tesseract_psm7'] = pytesseract.image_to_string(enhanced, config=config7).strip()
        except Exception as e:
            results['tesseract_psm8'] = f"ERROR: {e}"
            results['tesseract_psm6'] = f"ERROR: {e}"
            results['tesseract_psm7'] = f"ERROR: {e}"

        # EasyOCR test
        if self.easyocr_reader:
            try:
                # Convert to RGB for EasyOCR
                rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                easyocr_results = self.easyocr_reader.readtext(rgb_image, allowlist='0123456789')

                if easyocr_results:
                    # Get the result with highest confidence
                    best_result = max(easyocr_results, key=lambda x: x[2])
                    results['easyocr'] = best_result[1]
                    results['easyocr_conf'] = f"{best_result[2]:.2f}"
                else:
                    results['easyocr'] = ""
                    results['easyocr_conf'] = "0.00"
            except Exception as e:
                results['easyocr'] = f"ERROR: {e}"
                results['easyocr_conf'] = "0.00"
        else:
            results['easyocr'] = "Not available"
            results['easyocr_conf'] = "N/A"

        # PaddleOCR test
        if self.paddle_ocr:
            try:
                paddle_results = self.paddle_ocr.ocr(image, cls=True)

                if paddle_results and paddle_results[0]:
                    # Extract text from results
                    texts = []
                    for line in paddle_results[0]:
                        if line[1][0].isdigit():
                            texts.append((line[1][0], line[1][1]))

                    if texts:
                        best_result = max(texts, key=lambda x: x[1])
                        results['paddleocr'] = best_result[0]
                        results['paddleocr_conf'] = f"{best_result[1]:.2f}"
                    else:
                        results['paddleocr'] = ""
                        results['paddleocr_conf'] = "0.00"
                else:
                    results['paddleocr'] = ""
                    results['paddleocr_conf'] = "0.00"
            except Exception as e:
                results['paddleocr'] = f"ERROR: {e}"
                results['paddleocr_conf'] = "0.00"
        else:
            results['paddleocr'] = "Not available"
            results['paddleocr_conf'] = "N/A"

        # TrOCR test
        if self.trocr_processor and self.trocr_model:
            try:
                # Convert to PIL Image
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

                # Process with TrOCR
                pixel_values = self.trocr_processor(images=pil_image, return_tensors="pt").pixel_values
                generated_ids = self.trocr_model.generate(pixel_values)
                generated_text = self.trocr_processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

                # Filter to only digits
                filtered_text = ''.join(filter(str.isdigit, generated_text))
                results['trocr'] = filtered_text
            except Exception as e:
                results['trocr'] = f"ERROR: {e}"
        else:
            results['trocr'] = "Not available"

        return results
    
    def run(self):
        """Start the GUI"""
        print("Live OCR Diagnostic Tool")
        print("=" * 50)
        print("Instructions:")
        print("1. Position your mouse over a number in the game")
        print("2. Press SPACEBAR to capture (don't move mouse!)")
        print("3. Or click 'Start Live Capture' for real-time updates")
        print("4. Look for the size and method that gives the correct number")
        print("5. Make sure this window has focus to receive spacebar presses")
        print()
        
        self.root.mainloop()

if __name__ == "__main__":
    app = LiveOCRDiagnostic()
    app.run()
