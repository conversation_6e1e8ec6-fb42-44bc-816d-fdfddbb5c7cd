#!/usr/bin/env python3
"""
Map Trade Coordinate Tool - Simple mouse capture for all coordinates
Mouse over coordinate field, press ENTER to capture current mouse position
"""
import tkinter as tk
from tkinter import ttk
import pyautogui
import json
import threading
import time

class MapTradeCoordinateTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Map Trade Coordinate Tool")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Load current coordinates
        self.load_current_coordinates()
        
        # Setup UI
        self.setup_ui()
        
        # Start mouse position monitoring
        self.monitoring = True
        self.start_mouse_monitor()
        
        # Bind global Enter key
        self.root.bind('<Return>', self.capture_coordinate)
        self.root.focus_set()
        
    def load_current_coordinates(self):
        """Load current coordinates from config and module"""
        try:
            with open('module_configs.json', 'r') as f:
                config = json.load(f)
            
            map_trade_config = config.get('map_trade', {})
            click_coords = map_trade_config.get('click_coordinates', [])
            
            # Sequence coordinates from config
            self.coordinates = {}
            for coord in click_coords:
                name = coord.get('name', '')
                if name:
                    self.coordinates[name] = {
                        'x': coord.get('x', 0),
                        'y': coord.get('y', 0),
                        'description': coord.get('description', ''),
                        'source': 'config'
                    }
            
            # Hardcoded coordinates from module
            self.coordinates.update({
                'M1_quantity': {'x': 1068, 'y': 817, 'description': 'M1 quantity reading position', 'source': 'module'},
                'M1_click': {'x': 1026, 'y': 765, 'description': 'M1 click position', 'source': 'module'},
                'M2_quantity': {'x': 1224, 'y': 817, 'description': 'M2 quantity reading position', 'source': 'module'},
                'M2_click': {'x': 1188, 'y': 772, 'description': 'M2 click position', 'source': 'module'},
                'M3_quantity': {'x': 1384, 'y': 820, 'description': 'M3 quantity reading position', 'source': 'module'},
                'M3_click': {'x': 1342, 'y': 773, 'description': 'M3 click position', 'source': 'module'},
                'M4_quantity': {'x': 1533, 'y': 816, 'description': 'M4 quantity reading position', 'source': 'module'},
                'M4_click': {'x': 1497, 'y': 773, 'description': 'M4 click position', 'source': 'module'},
                'M5_quantity': {'x': 1144, 'y': 971, 'description': 'M5 quantity reading position', 'source': 'module'},
                'M5_click': {'x': 1106, 'y': 923, 'description': 'M5 click position', 'source': 'module'},
                'M6_quantity': {'x': 1298, 'y': 970, 'description': 'M6 quantity reading position', 'source': 'module'},
                'M6_click': {'x': 1265, 'y': 919, 'description': 'M6 click position', 'source': 'module'},
                'M7_quantity': {'x': 1457, 'y': 968, 'description': 'M7 quantity reading position', 'source': 'module'},
                'M7_click': {'x': 1420, 'y': 924, 'description': 'M7 click position', 'source': 'module'},
                'you_will_lose': {'x': 1448, 'y': 468, 'description': 'You will lose button', 'source': 'module'},
                'initiate_exchange': {'x': 1258, 'y': 1145, 'description': 'Initiate exchange button', 'source': 'module'},
                'confirm_exchange': {'x': 1258, 'y': 933, 'description': 'Confirm exchange button', 'source': 'module'},
                'new_exchange': {'x': 1255, 'y': 1142, 'description': 'New exchange button', 'source': 'module'},
                'red_thumb_fallback1': {'x': 1200, 'y': 600, 'description': 'Red thumb fallback location 1', 'source': 'module'},
                'red_thumb_fallback2': {'x': 1300, 'y': 650, 'description': 'Red thumb fallback location 2', 'source': 'module'},
                'red_thumb_fallback3': {'x': 1250, 'y': 625, 'description': 'Red thumb fallback location 3', 'source': 'module'},
            })
            
        except Exception as e:
            print(f"Error loading coordinates: {e}")
            self.coordinates = {}
    
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title = tk.Label(self.root, text="Map Trade Coordinate Tool", 
                        font=("Arial", 16, "bold"), fg='white', bg='#2b2b2b')
        title.pack(pady=10)
        
        # Instructions
        instructions = tk.Label(self.root, 
                               text="Mouse over any coordinate field and press ENTER to capture current mouse position",
                               font=("Arial", 12), fg='#cccccc', bg='#2b2b2b')
        instructions.pack(pady=5)
        
        # Mouse position display
        self.mouse_pos_label = tk.Label(self.root, text="Mouse: (0, 0)", 
                                       font=("Arial", 10), fg='yellow', bg='#2b2b2b')
        self.mouse_pos_label.pack(pady=5)
        
        # Main frame with scrollbar
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        canvas = tk.Canvas(main_frame, bg='#2b2b2b', highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2b2b2b')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Create coordinate entries
        self.entries = {}
        self.focused_entry = None
        
        # Group coordinates
        groups = [
            ("12-Step Sequence", [
                '1_map_button', '2_hidden_treasure', '3_trade_area', '4_confirm_1', '5_trade_option_2',
                '6_confirm_2', '7_trade_option_3', '8_confirm_3', '9_trade_option_4', '10_confirm_4'
            ]),
            ("Map Pieces - Quantity Reading", [
                'M1_quantity', 'M2_quantity', 'M3_quantity', 'M4_quantity', 
                'M5_quantity', 'M6_quantity', 'M7_quantity'
            ]),
            ("Map Pieces - Click Positions", [
                'M1_click', 'M2_click', 'M3_click', 'M4_click', 
                'M5_click', 'M6_click', 'M7_click'
            ]),
            ("Trade Buttons", [
                'you_will_lose', 'initiate_exchange', 'confirm_exchange', 'new_exchange'
            ]),
            ("Red Thumb Fallbacks", [
                'red_thumb_fallback1', 'red_thumb_fallback2', 'red_thumb_fallback3'
            ])
        ]
        
        row = 0
        for group_name, coord_names in groups:
            # Group header
            group_label = tk.Label(scrollable_frame, text=group_name, 
                                  font=("Arial", 14, "bold"), fg='#4CAF50', bg='#2b2b2b')
            group_label.grid(row=row, column=0, columnspan=4, sticky='w', pady=(15, 5))
            row += 1
            
            # Coordinates in this group
            for coord_name in coord_names:
                if coord_name in self.coordinates:
                    coord = self.coordinates[coord_name]
                    
                    # Name
                    name_label = tk.Label(scrollable_frame, text=coord_name, 
                                         font=("Arial", 10, "bold"), fg='white', bg='#2b2b2b', width=20, anchor='w')
                    name_label.grid(row=row, column=0, padx=5, pady=2, sticky='w')
                    
                    # Coordinate field (X, Y)
                    coord_var = tk.StringVar(value=f"({coord['x']}, {coord['y']})")
                    coord_entry = tk.Entry(scrollable_frame, textvariable=coord_var, width=12,
                                          bg='#404040', fg='white', insertbackground='white')
                    coord_entry.grid(row=row, column=1, padx=5, pady=2)
                    coord_entry.bind('<FocusIn>', lambda e, name=coord_name: self.set_focused_entry(name))

                    # Description
                    desc_label = tk.Label(scrollable_frame, text=coord['description'],
                                         font=("Arial", 9), fg='#cccccc', bg='#2b2b2b', anchor='w')
                    desc_label.grid(row=row, column=2, padx=10, pady=2, sticky='w')

                    # Store references
                    self.entries[coord_name] = {
                        'coord_var': coord_var,
                        'coord_entry': coord_entry
                    }
                    
                    row += 1
        
        # Bottom buttons
        button_frame = tk.Frame(self.root, bg='#2b2b2b')
        button_frame.pack(pady=10)
        
        save_btn = tk.Button(button_frame, text="Save All Coordinates", 
                            command=self.save_coordinates, bg='#4CAF50', fg='white', 
                            font=("Arial", 12, "bold"), padx=20)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        test_btn = tk.Button(button_frame, text="Test Current Mouse Position", 
                            command=self.test_mouse_position, bg='#2196F3', fg='white', 
                            font=("Arial", 12, "bold"), padx=20)
        test_btn.pack(side=tk.LEFT, padx=10)
    
    def set_focused_entry(self, coord_name):
        """Set which entry is currently focused"""
        self.focused_entry = coord_name
    
    def start_mouse_monitor(self):
        """Start monitoring mouse position"""
        def monitor():
            while self.monitoring:
                try:
                    x, y = pyautogui.position()
                    self.mouse_pos_label.config(text=f"Mouse: ({x}, {y})")
                    time.sleep(0.1)
                except:
                    pass
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def capture_coordinate(self, event=None):
        """Capture current mouse position to focused entry"""
        if self.focused_entry:
            coord_name = self.focused_entry
            x, y = pyautogui.position()

            if coord_name in self.entries:
                self.entries[coord_name]['coord_var'].set(f"({x}, {y})")
                print(f"Captured {coord_name}: ({x}, {y})")
    
    def test_mouse_position(self):
        """Show current mouse position"""
        x, y = pyautogui.position()
        print(f"Current mouse position: ({x}, {y})")
    
    def save_coordinates(self):
        """Save all coordinates back to files"""
        try:
            # Update coordinates dict
            for coord_name, entry_data in self.entries.items():
                coord_text = entry_data['coord_var'].get()
                # Parse (x, y) format
                coord_text = coord_text.strip('()')
                x, y = map(int, coord_text.split(','))
                self.coordinates[coord_name]['x'] = x
                self.coordinates[coord_name]['y'] = y
            
            # Save to config file
            self.save_to_config()
            
            # Save to module file
            self.save_to_module()
            
            print("All coordinates saved successfully!")
            
        except Exception as e:
            print(f"Error saving coordinates: {e}")
            import traceback
            traceback.print_exc()
    
    def save_to_config(self):
        """Save sequence coordinates to config file"""
        with open('module_configs.json', 'r') as f:
            config = json.load(f)
        
        click_coords = config['map_trade']['click_coordinates']
        
        for coord in click_coords:
            name = coord.get('name', '')
            if name in self.coordinates:
                coord['x'] = self.coordinates[name]['x']
                coord['y'] = self.coordinates[name]['y']
        
        with open('module_configs.json', 'w') as f:
            json.dump(config, f, indent=2)
    
    def save_to_module(self):
        """Save map piece coordinates to module file"""
        try:
            print("Skipping module file update for now - config file updated successfully")
            print("You can manually update the module coordinates if needed")
        except Exception as e:
            print(f"Module update error: {e}")
    
    def run(self):
        """Run the application"""
        self.root.mainloop()
        self.monitoring = False

if __name__ == "__main__":
    app = MapTradeCoordinateTool()
    app.run()
