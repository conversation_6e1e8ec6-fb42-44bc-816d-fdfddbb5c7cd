#!/usr/bin/env python3
"""
Test script for intelligent map trade functionality
"""

import sys
import time
import logging
from modules.map_trade import MapTradeModule

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_screen_detection():
    """Test screen state detection"""
    print("Testing Screen State Detection")
    print("=" * 40)
    
    module = MapTradeModule()
    
    print("Make sure you're on the screen after clicking 3_trade_option_1")
    input("Press Enter when ready...")
    
    screen_state = module._detect_screen_state()
    print(f"Detected screen state: {screen_state}")
    
    return screen_state

def test_map_analysis():
    """Test map piece analysis"""
    print("\nTesting Map Piece Analysis")
    print("=" * 40)
    
    module = MapTradeModule()
    
    print("Make sure you're on the initiate exchange screen with all 7 map pieces visible")
    input("Press Enter when ready...")
    
    map_numbers = module._analyze_map_pieces()
    print(f"Detected map numbers: {map_numbers}")
    
    if map_numbers:
        give_map, receive_map = module._find_optimal_trade(map_numbers)
        print(f"Optimal trade: Give {give_map} → Receive {receive_map}")
    
    return map_numbers

def test_intelligent_trading():
    """Test full intelligent trading sequence"""
    print("\nTesting Full Intelligent Trading")
    print("=" * 40)
    
    module = MapTradeModule()
    
    print("Make sure you're on the screen after clicking 3_trade_option_1")
    input("Press Enter when ready...")
    
    result = module._intelligent_trade_analysis()
    print(f"Intelligent trading result: {result}")
    
    return result

def calibrate_regions():
    """Run calibration tool"""
    print("Running Calibration Tool")
    print("=" * 40)
    
    module = MapTradeModule()
    new_regions = module.calibrate_map_regions()
    
    return new_regions

def main():
    """Main test menu"""
    setup_logging()
    
    while True:
        print("\nIntelligent Map Trade Test Menu")
        print("=" * 40)
        print("1. Test Screen State Detection")
        print("2. Test Map Piece Analysis")
        print("3. Test Full Intelligent Trading")
        print("4. Calibrate Map Regions")
        print("5. Run All Tests")
        print("0. Exit")
        
        choice = input("\nEnter your choice (0-5): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            test_screen_detection()
        elif choice == '2':
            test_map_analysis()
        elif choice == '3':
            test_intelligent_trading()
        elif choice == '4':
            calibrate_regions()
        elif choice == '5':
            print("Running all tests...")
            test_screen_detection()
            test_map_analysis()
            test_intelligent_trading()
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
