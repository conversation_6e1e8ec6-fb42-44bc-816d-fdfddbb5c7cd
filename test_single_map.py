#!/usr/bin/env python3
"""
Test single map piece reading with detailed OCR output
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.map_trade import MapTradeModule

def test_single_map():
    """Test reading a single map piece with detailed output"""
    print("SINGLE MAP PIECE TEST")
    print("=" * 50)
    
    try:
        # Create module
        map_trade = MapTradeModule()
        
        # Test M1 specifically
        map_name = "M1"
        map_data = map_trade.map_piece_regions[map_name]
        quantity_x, quantity_y = map_data['quantity_pos']
        
        print(f"Testing {map_name} at coordinates ({quantity_x}, {quantity_y})")
        
        # Capture image
        image = map_trade._capture_number_region(quantity_x, quantity_y, 50)
        
        # Save debug image
        import cv2
        debug_path = f"debug_{map_name}_single_test.png"
        cv2.imwrite(debug_path, image)
        print(f"Debug image saved: {debug_path}")
        
        # Test OCR with detailed output
        print("\n--- DETAILED OCR TEST ---")
        result = map_trade._extract_number_from_image(image)
        print(f"Final OCR result: {result}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_map()
