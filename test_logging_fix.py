"""
Test script to verify logging fixes
Tests that logging no longer has Unicode encoding errors
"""

import sys
import os
import logging

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def setup_logging():
    """Setup logging similar to main application"""
    # Configure logging similar to main app
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_logging_messages():
    """Test various logging messages that previously caused Unicode errors"""
    logger = logging.getLogger("TestLogger")
    
    print("Testing logging messages that previously caused Unicode errors...")
    
    # Test messages that should now work without Unicode errors
    logger.info("[SUCCESS] Centralized: Help_template -> help_click (3.10s)")
    logger.info("[FOUND] 1 help buttons for template 'Help_template'")
    logger.info("[CLICK] Help button 1 at (2374, 1017) for template 'Help_template'")
    logger.info("[CENTRALIZED] Help template detected - clicking main help buttons")
    logger.info("[CENTRALIZED] Main help action completed - 1 helps clicked")
    logger.info("[MOUSE] Override re-enabled after help_click.action_1_HelpButton execution")
    logger.info("[CLEANUP] Emergency cleanup: 42 objects collected")
    logger.info("[RECOVERY] Waiting 10 seconds for recovery...")
    logger.info("[NAV] Attempting to navigate to map trade location...")
    logger.info("[INPUT] Pressing ESC to close menus")
    
    print("✓ All logging messages completed without Unicode errors!")
    print("✓ Terminal should now be clear of Unicode encoding errors")

if __name__ == "__main__":
    setup_logging()
    test_logging_messages()
