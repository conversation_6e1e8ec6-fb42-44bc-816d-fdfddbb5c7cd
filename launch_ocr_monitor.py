#!/usr/bin/env python3
"""
OCR Monitor Launcher
Simple launcher for the OCR monitoring and configuration GUI
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        from PIL import Image, ImageTk
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import ai_ocr_service
    except ImportError:
        missing_deps.append("ai_ocr_service (ensure it's in the same directory)")
    
    return missing_deps

def main():
    """Main launcher function"""
    print("🚀 OCR Monitor Launcher")
    print("=" * 30)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    if missing_deps:
        error_msg = f"Missing dependencies: {', '.join(missing_deps)}\n\n"
        error_msg += "Please install missing packages:\n"
        for dep in missing_deps:
            if dep != "ai_ocr_service (ensure it's in the same directory)":
                error_msg += f"pip install {dep}\n"
        
        print(f"❌ {error_msg}")
        
        # Show GUI error if possible
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Missing Dependencies", error_msg)
            root.destroy()
        except:
            pass
        
        return False
    
    try:
        # Import and launch the OCR Monitor GUI
        import ocr_monitor_gui
        
        print("✅ All dependencies found")
        print("🖥️ Launching OCR Monitor GUI...")
        
        # Create and run the GUI
        root = tk.Tk()
        app = ocr_monitor_gui.OCRMonitorGUI(root)
        
        print("✅ OCR Monitor GUI started successfully")
        print("💡 Use the GUI to monitor and configure OCR detection")
        print("💡 Check the 'Real-time Monitoring' tab for live OCR results")
        print("💡 Use the 'Configuration' tab to adjust OCR settings")
        print("💡 View performance statistics in the 'Statistics' tab")
        print("💡 Browse debug images in the 'Debug Images' tab")
        
        # Start the GUI main loop
        root.mainloop()
        
        print("👋 OCR Monitor GUI closed")
        return True
        
    except Exception as e:
        error_msg = f"Failed to launch OCR Monitor: {e}"
        print(f"❌ {error_msg}")
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Launch Error", error_msg)
            root.destroy()
        except:
            pass
        
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n⚠️ OCR Monitor failed to start")
        print("💡 Make sure all dependencies are installed")
        print("💡 Ensure ai_ocr_service.py is in the same directory")
        input("Press Enter to exit...")
    
    sys.exit(0 if success else 1)
