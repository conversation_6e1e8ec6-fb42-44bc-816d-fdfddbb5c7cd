#!/usr/bin/env python3
"""
Final OCR test to verify all improvements are working
"""
import sys
import os
import cv2
import numpy as np

def main():
    print("🚀 Final OCR Test - Map Trade Module")
    print("=" * 50)
    
    try:
        # Add modules to path
        sys.path.append('modules')
        
        # Import and initialize module
        from map_trade import MapTradeModule
        module = MapTradeModule()
        print("✅ MapTradeModule initialized successfully")
        
        # Test with multiple numbers
        test_numbers = [1, 2, 5, 7, 9, 15, 23, 42, 99]
        results = {'passed': 0, 'failed': 0, 'total': 0}
        
        print("\n🧪 Testing OCR with various numbers:")
        print("-" * 40)
        
        for number in test_numbers:
            # Create test image
            image = create_number_image(number)
            
            # Test OCR
            try:
                result = module._extract_number_from_image(image)
                results['total'] += 1
                
                if result == number:
                    results['passed'] += 1
                    print(f"Number {number:2d}: ✅ SUCCESS (got {result})")
                else:
                    results['failed'] += 1
                    print(f"Number {number:2d}: ❌ FAILED (got {result})")
                    
            except Exception as e:
                results['total'] += 1
                results['failed'] += 1
                print(f"Number {number:2d}: ❌ ERROR - {e}")
        
        # Calculate accuracy
        accuracy = (results['passed'] / results['total']) * 100 if results['total'] > 0 else 0
        
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Total Tests: {results['total']}")
        print(f"   Passed: {results['passed']}")
        print(f"   Failed: {results['failed']}")
        print(f"   Accuracy: {accuracy:.1f}%")
        
        # Check OCR method availability
        print(f"\n🔍 OCR Method Status:")
        tesseract_available = getattr(module, '_tesseract_available', 'Unknown')
        ai_available = getattr(module, '_ai_ocr_available', 'Unknown')
        
        print(f"   Tesseract OCR: {'✅ Available' if tesseract_available else '❌ Not Available'}")
        print(f"   AI OCR: {'✅ Available' if ai_available else '❌ Not Available'}")
        
        # Recommendations
        print(f"\n🎯 RECOMMENDATIONS:")
        if accuracy >= 80:
            print("✅ OCR system is working well for map trading!")
            if tesseract_available and ai_available:
                print("✅ Both traditional and AI OCR available - excellent reliability")
            elif ai_available:
                print("✅ AI OCR is providing reliable number recognition")
            elif tesseract_available:
                print("✅ Traditional OCR is working adequately")
        else:
            print("⚠️ OCR accuracy needs improvement")
            if not tesseract_available and not ai_available:
                print("❌ No OCR methods available - install Tesseract or AI libraries")
            elif not ai_available:
                print("💡 Consider installing AI OCR: pip install easyocr paddlepaddle paddleocr")
        
        print(f"\n🚀 Map Trade OCR System Status: {'✅ READY' if accuracy >= 70 else '⚠️ NEEDS WORK'}")
        
        return accuracy >= 70
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_number_image(number: int) -> np.ndarray:
    """Create a test image with a number (similar to map piece display)"""
    # Create a 35x35 white image
    image = np.ones((35, 35, 3), dtype=np.uint8) * 255
    
    # Add black text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.8 if number >= 10 else 1.0  # Smaller font for 2-digit numbers
    color = (0, 0, 0)  # Black
    thickness = 2
    
    # Get text size and center it
    text = str(number)
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (image.shape[1] - text_size[0]) // 2
    text_y = (image.shape[0] + text_size[1]) // 2
    
    # Draw the number
    cv2.putText(image, text, (text_x, text_y), font, font_scale, color, thickness)
    
    return image

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 OCR system is ready for map trading!")
        print("✅ The map trade module should now work reliably")
    else:
        print("\n⚠️ OCR system needs attention before using map trade automation")
        print("🔧 Check OCR library installations and configurations")
