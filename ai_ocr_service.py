#!/usr/bin/env python3
"""
AI-Based OCR Service for Map Trade Number Recognition
Provides multiple AI OCR backends for improved accuracy and reliability
"""
import cv2
import numpy as np
import base64
import json
import time
import logging
from typing import Optional, Dict, Any, List
from io import BytesIO
import requests

class AINumberRecognition:
    """AI-powered number recognition service with multiple backends"""
    
    def __init__(self):
        self.logger = logging.getLogger("AINumberRecognition")
        self.backends = {
            'easyocr': self._init_easyocr,
            'paddleocr': self._init_paddleocr,
            'azure_cv': self._init_azure_cv,
            'google_cv': self._init_google_cv
        }
        self.active_backends = []
        self._initialize_backends()
    
    def _initialize_backends(self):
        """Initialize available AI OCR backends"""
        # Try EasyOCR (local, fast, good accuracy)
        try:
            import easyocr
            self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
            self.active_backends.append('easyocr')
            self.logger.info("✅ EasyOCR initialized successfully")
        except ImportError:
            self.logger.info("⚠️ EasyOCR not available (pip install easyocr)")
        except Exception as e:
            self.logger.warning(f"EasyOCR initialization failed: {e}")
        
        # Try PaddleOCR (local, very fast, excellent accuracy)
        try:
            from paddleocr import PaddleOCR
            # PaddleOCR doesn't support show_log parameter in newer versions
            try:
                self.paddle_ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
            except:
                # Fallback without show_log parameter
                self.paddleocr = PaddleOCR(use_angle_cls=True, lang='en')
            self.active_backends.append('paddleocr')
            self.logger.info("✅ PaddleOCR initialized successfully")
        except ImportError:
            self.logger.info("⚠️ PaddleOCR not available (pip install paddlepaddle paddleocr)")
        except Exception as e:
            self.logger.warning(f"PaddleOCR initialization failed: {e}")
        
        # Azure Computer Vision (cloud, excellent accuracy, requires API key)
        if self._check_azure_config():
            self.active_backends.append('azure_cv')
            self.logger.info("✅ Azure Computer Vision available")
        
        # Google Cloud Vision (cloud, excellent accuracy, requires API key)
        if self._check_google_config():
            self.active_backends.append('google_cv')
            self.logger.info("✅ Google Cloud Vision available")
        
        if not self.active_backends:
            self.logger.warning("❌ No AI OCR backends available - install easyocr or paddleocr")
        else:
            self.logger.info(f"🚀 AI OCR ready with backends: {self.active_backends}")
    
    def recognize_number(self, image: np.ndarray, fast_mode: bool = True) -> Optional[int]:
        """Recognize number from image using AI OCR"""
        if not self.active_backends:
            self.logger.error("No AI OCR backends available")
            return None
        
        try:
            # Preprocess image for better AI recognition
            processed_image = self._preprocess_for_ai(image)
            
            # Try backends in order of preference (fastest first if fast_mode)
            backends_to_try = self.active_backends.copy()
            if fast_mode:
                # Prioritize local backends for speed
                local_backends = [b for b in backends_to_try if b in ['easyocr', 'paddleocr']]
                cloud_backends = [b for b in backends_to_try if b not in local_backends]
                backends_to_try = local_backends + cloud_backends
            
            for backend in backends_to_try:
                try:
                    result = self._recognize_with_backend(processed_image, backend)
                    if result is not None:
                        self.logger.debug(f"AI OCR success with {backend}: {result}")
                        return result
                except Exception as e:
                    self.logger.debug(f"Backend {backend} failed: {e}")
                    continue
            
            self.logger.debug("All AI OCR backends failed")
            return None
            
        except Exception as e:
            self.logger.error(f"AI OCR error: {e}")
            return None
    
    def _preprocess_for_ai(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better AI OCR recognition"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Resize if too small (AI works better with larger images)
            height, width = gray.shape
            if height < 32 or width < 32:
                scale_factor = max(32 / height, 32 / width)
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Apply slight blur to reduce noise
            gray = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Preprocessing error: {e}")
            return image
    
    def _recognize_with_backend(self, image: np.ndarray, backend: str) -> Optional[int]:
        """Recognize number using specific backend"""
        if backend == 'easyocr':
            return self._recognize_easyocr(image)
        elif backend == 'paddleocr':
            return self._recognize_paddleocr(image)
        elif backend == 'azure_cv':
            return self._recognize_azure_cv(image)
        elif backend == 'google_cv':
            return self._recognize_google_cv(image)
        else:
            return None
    
    def _recognize_easyocr(self, image: np.ndarray) -> Optional[int]:
        """Recognize using EasyOCR"""
        try:
            results = self.easyocr_reader.readtext(image, allowlist='0123456789')
            
            for (bbox, text, confidence) in results:
                if confidence > 0.5 and text.isdigit():
                    number = int(text)
                    if 0 <= number <= 999:
                        return number
            
            return None
            
        except Exception as e:
            self.logger.debug(f"EasyOCR error: {e}")
            return None
    
    def _recognize_paddleocr(self, image: np.ndarray) -> Optional[int]:
        """Recognize using PaddleOCR"""
        try:
            results = self.paddle_ocr.ocr(image, cls=True)
            
            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        
                        if confidence > 0.5 and text.isdigit():
                            number = int(text)
                            if 0 <= number <= 999:
                                return number
            
            return None
            
        except Exception as e:
            self.logger.debug(f"PaddleOCR error: {e}")
            return None
    
    def _recognize_azure_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Azure Computer Vision (requires API key)"""
        try:
            # This would require Azure API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Azure CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Azure CV error: {e}")
            return None
    
    def _recognize_google_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Google Cloud Vision (requires API key)"""
        try:
            # This would require Google Cloud API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Google CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Google CV error: {e}")
            return None
    
    def _check_azure_config(self) -> bool:
        """Check if Azure Computer Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _check_google_config(self) -> bool:
        """Check if Google Cloud Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _init_easyocr(self):
        """Initialize EasyOCR"""
        pass
    
    def _init_paddleocr(self):
        """Initialize PaddleOCR"""
        pass
    
    def _init_azure_cv(self):
        """Initialize Azure Computer Vision"""
        pass
    
    def _init_google_cv(self):
        """Initialize Google Cloud Vision"""
        pass

# Singleton instance for global use
ai_ocr_service = None

def get_ai_ocr_service() -> AINumberRecognition:
    """Get singleton AI OCR service instance"""
    global ai_ocr_service
    if ai_ocr_service is None:
        ai_ocr_service = AINumberRecognition()
    return ai_ocr_service

def recognize_number_ai(image: np.ndarray, fast_mode: bool = True) -> Optional[int]:
    """Convenience function for AI number recognition"""
    service = get_ai_ocr_service()
    return service.recognize_number(image, fast_mode)

if __name__ == "__main__":
    # Test the AI OCR service
    print("🧪 Testing AI OCR Service")
    service = AINumberRecognition()
    
    if service.active_backends:
        print(f"✅ AI OCR ready with: {service.active_backends}")
    else:
        print("❌ No AI OCR backends available")
        print("Install with: pip install easyocr paddlepaddle paddleocr")
