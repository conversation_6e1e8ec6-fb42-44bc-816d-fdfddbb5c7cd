#!/usr/bin/env python3
"""
AI-Based OCR Service for Map Trade Number Recognition
Provides multiple AI OCR backends with GPU acceleration and improved performance
"""
import cv2
import numpy as np
import base64
import json
import time
import logging
import os
import pickle
import hashlib
from typing import Optional, Dict, Any, List, Tuple
from io import BytesIO
import requests
import platform
from datetime import datetime
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score
    from sklearn.feature_extraction.text import TfidfVectorizer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

class AINumberRecognition:
    """AI-powered number recognition service with GPU acceleration and comprehensive debugging"""

    def __init__(self, debug_config=None):
        self.logger = logging.getLogger("AINumberRecognition")
        self.backends = {
            'easyocr': self._init_easyocr,
            'paddleocr': self._init_paddleocr,
            'azure_cv': self._init_azure_cv,
            'google_cv': self._init_google_cv
        }
        self.active_backends = []
        self.gpu_available = self._check_gpu_availability()

        # Enhanced debugging and monitoring
        self.debug_config = debug_config or self._load_debug_config()
        self.ocr_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'method_usage': {},
            'processing_times': [],
            'confidence_scores': []
        }

        # Recent detections for GUI monitoring
        self.recent_detections = []
        self.max_recent_detections = 50
        self.last_displayed_detection_index = 0

        # Machine Learning System
        self.learning_enabled = SKLEARN_AVAILABLE
        self.training_data = []
        self.trained_model = None
        self.feature_extractor = None
        self.model_accuracy = 0.0
        self.training_data_file = "ai_ocr_training_data.pkl"
        self.model_file = "ai_ocr_model.pkl"
        self._load_training_data()
        self._load_trained_model()

        self._setup_debug_directories()
        self._initialize_backends()

    def _load_debug_config(self):
        """Load OCR debug configuration"""
        default_config = {
            'enable_detailed_logging': True,
            'save_debug_images': True,
            'log_confidence_scores': True,
            'log_processing_times': True,
            'log_preprocessing_steps': True,
            'log_raw_ocr_output': True,
            'confidence_threshold': 0.9,  # Increased for higher accuracy
            'save_failed_detections': True,
            'max_debug_images': 100,
            'debug_image_format': 'png'
        }

        try:
            # Try to load from config file if it exists
            config_path = 'ocr_debug_config.json'
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    self._safe_log("info", f"Loaded OCR debug config from {config_path}")
            else:
                # Save default config for user customization
                with open(config_path, 'w') as f:
                    json.dump(default_config, f, indent=2)
                    self._safe_log("info", f"Created default OCR debug config at {config_path}")
        except Exception as e:
            self._safe_log("warning", f"Could not load OCR debug config: {e}")

        return default_config

    def _setup_debug_directories(self):
        """Setup directories for debug output"""
        if self.debug_config.get('save_debug_images', False):
            self.debug_dirs = {
                'images': 'debug/ocr_images',
                'preprocessing': 'debug/ocr_preprocessing',
                'failed': 'debug/ocr_failed',
                'successful': 'debug/ocr_successful'
            }

            for dir_name, dir_path in self.debug_dirs.items():
                os.makedirs(dir_path, exist_ok=True)

            self._safe_log("info", "OCR debug directories created")

    def _check_gpu_availability(self):
        """Check if GPU acceleration is available for OCR processing"""
        gpu_available = False
        gpu_info = "CPU only"

        try:
            # Check for CUDA/PyTorch
            import torch
            if torch.cuda.is_available():
                gpu_available = True
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                gpu_info = f"CUDA GPU available: {gpu_name} ({gpu_count} device(s))"
            else:
                gpu_info = "CUDA not available"
        except ImportError:
            try:
                # Alternative: Check for NVIDIA GPU via nvidia-ml-py
                import pynvml
                pynvml.nvmlInit()
                gpu_count = pynvml.nvmlDeviceGetCount()
                if gpu_count > 0:
                    gpu_available = True
                    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                    gpu_name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                    gpu_info = f"NVIDIA GPU detected: {gpu_name} ({gpu_count} device(s))"
            except:
                # Final fallback: Check if nvidia-smi exists
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        gpu_available = True
                        gpu_info = f"NVIDIA GPU detected: {result.stdout.strip()}"
                except:
                    pass

        # Use Unicode-safe logging
        self._safe_log("info", f"GPU Detection: {gpu_info}")
        return gpu_available

    def _safe_log(self, level, message):
        """Unicode-safe logging that removes problematic characters"""
        # Remove emoji and other Unicode characters that might cause encoding issues
        safe_message = message.encode('ascii', errors='ignore').decode('ascii')
        if not safe_message.strip():
            # If message becomes empty, use a fallback
            safe_message = "[Unicode message - content removed for compatibility]"

        getattr(self.logger, level)(safe_message)

    def get_ocr_statistics(self) -> Dict[str, Any]:
        """Get comprehensive OCR performance statistics"""
        stats = self.ocr_stats.copy()

        # Calculate derived statistics
        if stats['total_processed'] > 0:
            stats['success_rate'] = stats['successful_detections'] / stats['total_processed']
        else:
            stats['success_rate'] = 0.0

        if stats['processing_times']:
            stats['avg_processing_time'] = sum(stats['processing_times']) / len(stats['processing_times'])
            stats['min_processing_time'] = min(stats['processing_times'])
            stats['max_processing_time'] = max(stats['processing_times'])
        else:
            stats['avg_processing_time'] = 0.0
            stats['min_processing_time'] = 0.0
            stats['max_processing_time'] = 0.0

        if stats['confidence_scores']:
            stats['avg_confidence'] = sum(stats['confidence_scores']) / len(stats['confidence_scores'])
            stats['min_confidence'] = min(stats['confidence_scores'])
            stats['max_confidence'] = max(stats['confidence_scores'])
        else:
            stats['avg_confidence'] = 0.0
            stats['min_confidence'] = 0.0
            stats['max_confidence'] = 0.0

        return stats

    def get_recent_detections(self) -> str:
        """Get recent detection results for GUI monitoring"""
        if not self.recent_detections:
            return ""

        # Return new detections since last display update
        new_detections = []
        if len(self.recent_detections) > self.last_displayed_detection_index:
            new_detections = self.recent_detections[self.last_displayed_detection_index:]
            self.last_displayed_detection_index = len(self.recent_detections)

        return "\n".join(new_detections) if new_detections else ""

    def _add_recent_detection(self, piece_id: str, result: Optional[int], method: str, confidence: float, processing_time: float):
        """Add a detection result to recent detections list"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        if result is not None:
            detection_text = f"[{timestamp}] [{piece_id}] SUCCESS: {result} (method: {method}, confidence: {confidence:.3f}, time: {processing_time:.3f}s)"
        else:
            detection_text = f"[{timestamp}] [{piece_id}] FAILED: No result (method: {method}, time: {processing_time:.3f}s)"

        self.recent_detections.append(detection_text)

        # Keep only the most recent detections
        if len(self.recent_detections) > self.max_recent_detections:
            self.recent_detections = self.recent_detections[-self.max_recent_detections:]

    def update_debug_config(self, new_config: Dict[str, Any]):
        """Update debug configuration from GUI"""
        self.debug_config.update(new_config)

        # Save updated configuration
        try:
            import json
            with open('ocr_debug_config.json', 'w') as f:
                json.dump(self.debug_config, f, indent=2)
        except Exception as e:
            self._safe_log("warning", f"Failed to save debug config: {e}")

    def log_performance_summary(self):
        """Log a comprehensive performance summary"""
        stats = self.get_ocr_statistics()

        self._safe_log("info", "=== OCR Performance Summary ===")
        self._safe_log("info", f"Total processed: {stats['total_processed']}")
        self._safe_log("info", f"Successful detections: {stats['successful_detections']}")
        self._safe_log("info", f"Success rate: {stats['success_rate']:.1%}")

        if stats['avg_processing_time'] > 0:
            self._safe_log("info", f"Avg processing time: {stats['avg_processing_time']:.3f}s")
            self._safe_log("info", f"Processing time range: {stats['min_processing_time']:.3f}s - {stats['max_processing_time']:.3f}s")

        if stats['avg_confidence'] > 0:
            self._safe_log("info", f"Avg confidence: {stats['avg_confidence']:.3f}")
            self._safe_log("info", f"Confidence range: {stats['min_confidence']:.3f} - {stats['max_confidence']:.3f}")

        if stats['method_usage']:
            self._safe_log("info", "Method usage:")
            for method, count in stats['method_usage'].items():
                percentage = (count / stats['total_processed']) * 100 if stats['total_processed'] > 0 else 0
                self._safe_log("info", f"  {method}: {count} ({percentage:.1f}%)")

        self._safe_log("info", "=== End Summary ===")

    def reset_statistics(self):
        """Reset OCR statistics"""
        self.ocr_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'method_usage': {},
            'processing_times': [],
            'confidence_scores': []
        }
        self._safe_log("info", "OCR statistics reset")

    def update_debug_config(self, new_config: Dict[str, Any]):
        """Update debug configuration at runtime"""
        self.debug_config.update(new_config)

        # Save updated config
        try:
            config_path = 'ocr_debug_config.json'
            with open(config_path, 'w') as f:
                json.dump(self.debug_config, f, indent=2)
            self._safe_log("info", f"Updated OCR debug config: {list(new_config.keys())}")
        except Exception as e:
            self._safe_log("warning", f"Could not save updated debug config: {e}")

    def get_debug_config(self) -> Dict[str, Any]:
        """Get current debug configuration"""
        return self.debug_config.copy()

    def _initialize_backends(self):
        """Initialize available AI OCR backends with GPU acceleration"""
        # Try EasyOCR with GPU acceleration
        try:
            import easyocr

            # Initialize with GPU if available, fallback to CPU
            use_gpu = self.gpu_available
            self.easyocr_reader = easyocr.Reader(['en'], gpu=use_gpu)
            self.active_backends.append('easyocr')

            if use_gpu:
                self._safe_log("info", "EasyOCR initialized with GPU acceleration")
            else:
                self._safe_log("info", "EasyOCR initialized with CPU processing")

        except ImportError:
            self._safe_log("info", "EasyOCR not available (pip install easyocr)")
        except Exception as e:
            self._safe_log("warning", f"EasyOCR initialization failed: {e}")
            # Try fallback to CPU if GPU initialization failed
            if self.gpu_available:
                try:
                    import easyocr
                    self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
                    self.active_backends.append('easyocr')
                    self._safe_log("info", "EasyOCR initialized with CPU fallback after GPU failure")
                except Exception as e2:
                    self._safe_log("warning", f"EasyOCR CPU fallback also failed: {e2}")
        
        # Try PaddleOCR with GPU support
        try:
            from paddleocr import PaddleOCR

            # Configure PaddleOCR with GPU if available
            paddle_kwargs = {
                'use_angle_cls': True,
                'lang': 'en',
                'use_gpu': self.gpu_available
            }

            # Handle show_log parameter compatibility
            try:
                paddle_kwargs['show_log'] = False
                self.paddleocr = PaddleOCR(**paddle_kwargs)
            except:
                # Fallback without show_log parameter
                del paddle_kwargs['show_log']
                self.paddleocr = PaddleOCR(**paddle_kwargs)

            self.active_backends.append('paddleocr')

            if self.gpu_available:
                self._safe_log("info", "PaddleOCR initialized with GPU acceleration")
            else:
                self._safe_log("info", "PaddleOCR initialized with CPU processing")

        except ImportError:
            self._safe_log("info", "PaddleOCR not available (pip install paddlepaddle paddleocr)")
        except Exception as e:
            self._safe_log("warning", f"PaddleOCR initialization failed: {e}")
            # Try CPU fallback if GPU failed
            if self.gpu_available:
                try:
                    from paddleocr import PaddleOCR
                    self.paddleocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
                    self.active_backends.append('paddleocr')
                    self._safe_log("info", "PaddleOCR initialized with CPU fallback after GPU failure")
                except Exception as e2:
                    self._safe_log("warning", f"PaddleOCR CPU fallback also failed: {e2}")
        
        # Azure Computer Vision (cloud, excellent accuracy, requires API key)
        if self._check_azure_config():
            self.active_backends.append('azure_cv')
            self._safe_log("info", "Azure Computer Vision available")

        # Google Cloud Vision (cloud, excellent accuracy, requires API key)
        if self._check_google_config():
            self.active_backends.append('google_cv')
            self._safe_log("info", "Google Cloud Vision available")

        if not self.active_backends:
            self._safe_log("warning", "No AI OCR backends available - install easyocr or paddleocr")
        else:
            self._safe_log("info", f"AI OCR ready with backends: {self.active_backends}")

        # Log performance optimization info
        if self.gpu_available and any(backend in self.active_backends for backend in ['easyocr', 'paddleocr']):
            self._safe_log("info", "GPU acceleration enabled for optimal OCR performance")
        elif not self.gpu_available:
            self._safe_log("info", "Using CPU processing - consider GPU for better performance")
    
    def recognize_number(self, image: np.ndarray, fast_mode: bool = True, piece_id: str = "unknown") -> Optional[int]:
        """Enhanced number recognition with comprehensive debugging and monitoring"""
        if not self.active_backends:
            self._safe_log("error", "No AI OCR backends available")
            return None

        start_time = time.time()
        self.ocr_stats['total_processed'] += 1

        # Initialize detection info storage
        self.last_detection_info = {
            'piece_id': piece_id,
            'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3],
            'confidence': 0.0,
            'backend_used': 'None',
            'preprocessing_applied': 'None',
            'brightness': 0.0,
            'image_size': f"{image.shape[1]}x{image.shape[0]}",
            'processing_time': 0.0,
            'detected_text': 'None',
            'success': False
        }

        # Save original image for debugging
        debug_session_id = f"{piece_id}_{datetime.now().strftime('%H%M%S_%f')}"
        if self.debug_config.get('save_debug_images', False):
            self._save_debug_image(image, f"original_{debug_session_id}", "images")

        try:
            # Enhanced preprocessing with logging
            processed_image, preprocessing_info = self._preprocess_for_ai_enhanced(image)
            self.last_detection_info['preprocessing_applied'] = preprocessing_info.get('method_used', 'Standard')
            self.last_detection_info['brightness'] = preprocessing_info.get('original_brightness', 0.0)

            if self.debug_config.get('log_preprocessing_steps', False):
                self._safe_log("debug", f"[{piece_id}] Preprocessing: {preprocessing_info}")

            # Save preprocessed image for debugging
            if self.debug_config.get('save_debug_images', False):
                self._save_debug_image(processed_image, f"processed_{debug_session_id}", "preprocessing")

            # Try Simple Template Recognition first (much more reliable)
            try:
                from simple_number_recognition import get_simple_recognition
                recognizer = get_simple_recognition()

                template_result, template_confidence = recognizer.recognize_number(processed_image)
                self._safe_log("debug", f"[{piece_id}] Template Debug - Result: '{template_result}', Confidence: {template_confidence:.3f}")

                if template_result and template_confidence >= 0.6:  # 60% confidence threshold
                    try:
                        template_number = int(template_result)
                        self.last_detection_info.update({
                            'detected_text': template_result,
                            'confidence': template_confidence,
                            'backend_used': 'Template Recognition',
                            'success': True,
                            'processing_time': time.time() - start_time
                        })

                        # Add to recent detections for monitoring
                        self._add_recent_detection(self.last_detection_info.copy())

                        self._safe_log("info", f"[{piece_id}] Template recognition: {template_result} (confidence: {template_confidence:.2%})")
                        return template_number
                    except ValueError:
                        self._safe_log("warning", f"[{piece_id}] Template result '{template_result}' is not a valid number")
                else:
                    self._safe_log("debug", f"[{piece_id}] Template confidence too low: {template_confidence:.3f} < 0.6")

            except ImportError:
                self._safe_log("debug", f"[{piece_id}] Simple recognition not available")
            except Exception as e:
                self._safe_log("warning", f"[{piece_id}] Template recognition failed: {e}")

            # Try traditional OCR backends with detailed logging
            backends_to_try = self._get_backend_order(fast_mode)
            detection_results = []

            # Add ML result to detection results for comparison
            if ml_result:
                ml_detection_info = {
                    'backend': 'Machine Learning',
                    'result': ml_result,
                    'confidence': ml_confidence,
                    'raw_output': f"ML Prediction: {ml_result}",
                    'processing_time': 0.001  # ML is very fast
                }
                detection_results.append(ml_detection_info)

            for backend in backends_to_try:
                try:
                    backend_start = time.time()
                    result, confidence, raw_output = self._recognize_with_backend_enhanced(processed_image, backend)
                    backend_time = time.time() - backend_start

                    # Log detailed results
                    detection_info = {
                        'backend': backend,
                        'result': result,
                        'confidence': confidence,
                        'raw_output': raw_output,
                        'processing_time': backend_time
                    }
                    detection_results.append(detection_info)

                    if self.debug_config.get('enable_detailed_logging', False):
                        self._log_detection_result(piece_id, detection_info)

                    # Update statistics
                    if backend not in self.ocr_stats['method_usage']:
                        self.ocr_stats['method_usage'][backend] = 0
                    self.ocr_stats['method_usage'][backend] += 1

                    if result is not None:
                        self.ocr_stats['successful_detections'] += 1
                        self.ocr_stats['confidence_scores'].append(confidence)

                        total_time = time.time() - start_time
                        self.ocr_stats['processing_times'].append(total_time)

                        # Update detection info for Config Helper integration
                        self.last_detection_info.update({
                            'confidence': confidence,
                            'backend_used': backend,
                            'processing_time': total_time,
                            'detected_text': str(raw_output) if raw_output else str(result),
                            'success': True
                        })

                        # Save successful detection
                        if self.debug_config.get('save_debug_images', False):
                            self._save_debug_image(image, f"success_{result}_{debug_session_id}", "successful")

                        # Log success summary
                        if self.debug_config.get('enable_detailed_logging', False):
                            self._safe_log("info", f"[{piece_id}] SUCCESS: {result} (method: {backend}, confidence: {confidence:.3f}, time: {total_time:.3f}s)")

                        # Add to recent detections for GUI monitoring
                        self._add_recent_detection(piece_id, result, backend, confidence, total_time)

                        return result

                except Exception as e:
                    self._safe_log("debug", f"[{piece_id}] Backend {backend} failed: {e}")
                    continue

            # FALLBACK: Try alternative preprocessing if original failed and white text was detected
            if preprocessing_info.get('white_text_detected', False):
                self._safe_log("debug", f"[{piece_id}] Trying alternative white text preprocessing as fallback")

                try:
                    alt_processed = self._alternative_white_text_preprocessing(image)

                    # Save alternative preprocessing result
                    if self.debug_config.get('save_debug_images', False):
                        self._save_debug_image(alt_processed, f"alt_processed_{debug_session_id}", "preprocessing")

                    # Try all backends again with alternative preprocessing
                    for backend in backends_to_try:
                        try:
                            backend_start = time.time()
                            result, confidence, raw_output = self._recognize_with_backend_enhanced(alt_processed, backend)
                            backend_time = time.time() - backend_start

                            detection_info = {
                                'backend': f"{backend}_alt",
                                'result': result,
                                'confidence': confidence,
                                'raw_output': raw_output,
                                'processing_time': backend_time,
                                'preprocessing': 'alternative_white_text'
                            }
                            detection_results.append(detection_info)

                            if result is not None:
                                self.ocr_stats['successful_detections'] += 1
                                self.ocr_stats['confidence_scores'].append(confidence)

                                total_time = time.time() - start_time
                                self.ocr_stats['processing_times'].append(total_time)

                                # Update detection info for Config Helper integration
                                self.last_detection_info.update({
                                    'confidence': confidence,
                                    'backend_used': f"{backend}_alt",
                                    'preprocessing_applied': 'Alternative White Text',
                                    'processing_time': total_time,
                                    'detected_text': str(raw_output) if raw_output else str(result),
                                    'success': True
                                })

                                # Save successful detection
                                if self.debug_config.get('save_debug_images', False):
                                    self._save_debug_image(alt_processed, f"success_alt_{result}_{debug_session_id}", "successful")

                                # Log success summary
                                if self.debug_config.get('enable_detailed_logging', False):
                                    self._safe_log("info", f"[{piece_id}] SUCCESS (ALT): {result} (method: {backend}_alt, confidence: {confidence:.3f}, time: {total_time:.3f}s)")

                                # Add to recent detections for GUI monitoring
                                self._add_recent_detection(piece_id, result, f"{backend}_alt", confidence, total_time)

                                return result

                        except Exception as e:
                            self._safe_log("debug", f"[{piece_id}] Alternative backend {backend} failed: {e}")
                            continue

                except Exception as alt_error:
                    self._safe_log("debug", f"[{piece_id}] Alternative preprocessing failed: {alt_error}")

            # All methods failed
            total_time = time.time() - start_time
            self.ocr_stats['processing_times'].append(total_time)

            # Update detection info for failure case
            self.last_detection_info.update({
                'confidence': 0.0,
                'backend_used': 'All methods failed',
                'processing_time': total_time,
                'detected_text': 'No text detected',
                'success': False
            })

            # Save failed detection for analysis
            if self.debug_config.get('save_failed_detections', False):
                self._save_debug_image(image, f"failed_{debug_session_id}", "failed")
                self._save_detection_log(piece_id, detection_results, debug_session_id)

            if self.debug_config.get('enable_detailed_logging', False):
                self._safe_log("warning", f"[{piece_id}] FAILED: All OCR methods failed (time: {total_time:.3f}s)")

            # Add failed detection to recent detections for GUI monitoring
            self._add_recent_detection(piece_id, None, "all_methods", 0.0, total_time)

            return None

        except Exception as e:
            total_time = time.time() - start_time
            self._safe_log("error", f"[{piece_id}] OCR error: {e} (time: {total_time:.3f}s)")
            return None

    def _get_backend_order(self, fast_mode: bool) -> List[str]:
        """Get backend processing order based on mode"""
        backends_to_try = self.active_backends.copy()
        if fast_mode:
            # Prioritize local backends for speed
            local_backends = [b for b in backends_to_try if b in ['easyocr', 'paddleocr']]
            cloud_backends = [b for b in backends_to_try if b not in local_backends]
            backends_to_try = local_backends + cloud_backends
        return backends_to_try

    def _save_debug_image(self, image: np.ndarray, filename: str, category: str):
        """Save debug image to appropriate directory"""
        try:
            if hasattr(self, 'debug_dirs') and category in self.debug_dirs:
                filepath = os.path.join(self.debug_dirs[category], f"{filename}.{self.debug_config.get('debug_image_format', 'png')}")
                cv2.imwrite(filepath, image)

                # Cleanup old images if limit exceeded
                self._cleanup_debug_images(self.debug_dirs[category])
        except Exception as e:
            self._safe_log("debug", f"Could not save debug image: {e}")

    def _cleanup_debug_images(self, directory: str):
        """Remove old debug images if limit exceeded"""
        try:
            max_images = self.debug_config.get('max_debug_images', 100)
            files = [f for f in os.listdir(directory) if f.endswith(('.png', '.jpg', '.jpeg'))]

            if len(files) > max_images:
                # Sort by modification time and remove oldest
                files_with_time = [(f, os.path.getmtime(os.path.join(directory, f))) for f in files]
                files_with_time.sort(key=lambda x: x[1])

                files_to_remove = files_with_time[:len(files) - max_images]
                for filename, _ in files_to_remove:
                    os.remove(os.path.join(directory, filename))
        except Exception as e:
            self._safe_log("debug", f"Could not cleanup debug images: {e}")

    def _log_detection_result(self, piece_id: str, detection_info: Dict):
        """Log detailed detection result"""
        backend = detection_info['backend']
        result = detection_info['result']
        confidence = detection_info['confidence']
        raw_output = detection_info['raw_output']
        processing_time = detection_info['processing_time']

        if result is not None:
            self._safe_log("info", f"[{piece_id}] {backend.upper()}: '{result}' (conf: {confidence:.3f}, time: {processing_time:.3f}s)")
        else:
            self._safe_log("debug", f"[{piece_id}] {backend.upper()}: FAILED (time: {processing_time:.3f}s)")

        if self.debug_config.get('log_raw_ocr_output', False) and raw_output:
            self._safe_log("debug", f"[{piece_id}] {backend.upper()} raw: {raw_output}")

    def _save_detection_log(self, piece_id: str, detection_results: List[Dict], session_id: str):
        """Save detailed detection log for failed cases"""
        try:
            log_data = {
                'piece_id': piece_id,
                'session_id': session_id,
                'timestamp': datetime.now().isoformat(),
                'detection_results': detection_results,
                'config': self.debug_config
            }

            log_file = os.path.join(self.debug_dirs.get('failed', 'debug'), f"detection_log_{session_id}.json")
            with open(log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
        except Exception as e:
            self._safe_log("debug", f"Could not save detection log: {e}")

    def _preprocess_for_ai_enhanced(self, image: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """Enhanced preprocessing with white text detection and contrast optimization"""
        preprocessing_info = {
            'original_shape': image.shape,
            'steps_applied': []
        }

        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                preprocessing_info['steps_applied'].append('bgr_to_gray')
            else:
                gray = image.copy()

            preprocessing_info['gray_shape'] = gray.shape

            # Analyze brightness to detect white text scenarios
            original_brightness = float(np.mean(gray))
            preprocessing_info['original_brightness'] = original_brightness

            # Detect if this is likely white text on bright background
            is_white_text_scenario = original_brightness > 150.0
            preprocessing_info['white_text_detected'] = is_white_text_scenario

            # For small images like map pieces (35x35), scale up for better AI recognition
            height, width = gray.shape
            if height <= 50 or width <= 50:
                # Scale up by 4x for better OCR accuracy on small text (increased from 3x)
                scale_factor = 4.0
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                preprocessing_info['steps_applied'].append(f'upscale_4x_to_{new_width}x{new_height}')

            # WHITE TEXT OPTIMIZATION: Special processing for bright images
            if is_white_text_scenario:
                # Method 1: Color inversion for white text
                inverted = cv2.bitwise_not(gray)
                preprocessing_info['steps_applied'].append('color_inversion_for_white_text')

                # Method 2: Aggressive contrast enhancement for inverted image
                clahe_aggressive = cv2.createCLAHE(clipLimit=5.0, tileGridSize=(2, 2))
                enhanced = clahe_aggressive.apply(inverted)
                preprocessing_info['steps_applied'].append('aggressive_clahe_on_inverted')

                # Method 3: Morphological operations to clean up text
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
                enhanced = cv2.morphologyEx(enhanced, cv2.MORPH_CLOSE, kernel)
                preprocessing_info['steps_applied'].append('morphological_cleanup')

            else:
                # Standard processing for normal contrast images
                if gray.shape[0] > 16 and gray.shape[1] > 16:
                    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))
                    enhanced = clahe.apply(gray)
                    preprocessing_info['steps_applied'].append('standard_clahe_contrast')
                else:
                    enhanced = cv2.equalizeHist(gray)
                    preprocessing_info['steps_applied'].append('histogram_equalization')

            # Apply sharpening for crisp text (after contrast enhancement)
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            enhanced = cv2.filter2D(enhanced, -1, kernel)
            preprocessing_info['steps_applied'].append('sharpening_filter')

            # Gentle denoising while preserving text edges
            enhanced = cv2.bilateralFilter(enhanced, 3, 30, 30)
            preprocessing_info['steps_applied'].append('bilateral_denoising')

            preprocessing_info['final_shape'] = enhanced.shape
            preprocessing_info['final_brightness'] = float(np.mean(enhanced))
            preprocessing_info['contrast_std'] = float(np.std(enhanced))
            preprocessing_info['brightness_improvement'] = preprocessing_info['final_brightness'] - original_brightness

            return enhanced, preprocessing_info

        except Exception as e:
            self._safe_log("error", f"Preprocessing error: {e}")
            preprocessing_info['error'] = str(e)
            return image, preprocessing_info

    def _preprocess_for_ai(self, image: np.ndarray) -> np.ndarray:
        """Optimized preprocessing for small map piece images (35x35 pixels)"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # For small images like map pieces (35x35), scale up for better AI recognition
            height, width = gray.shape
            if height <= 50 or width <= 50:
                # Scale up by 3x for better OCR accuracy on small text
                scale_factor = 3.0
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                # Apply sharpening after upscaling for crisp text
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)

            # Enhance contrast specifically for number recognition
            # Use adaptive histogram equalization for small regions
            if gray.shape[0] > 16 and gray.shape[1] > 16:
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))
                enhanced = clahe.apply(gray)
            else:
                # For very small images, use simple histogram equalization
                enhanced = cv2.equalizeHist(gray)

            # Apply slight denoising while preserving edges
            enhanced = cv2.bilateralFilter(enhanced, 5, 50, 50)

            return enhanced

        except Exception as e:
            self._safe_log("error", f"Preprocessing error: {e}")
            return image

    def _alternative_white_text_preprocessing(self, image: np.ndarray) -> np.ndarray:
        """Alternative preprocessing specifically for white text scenarios"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Scale up significantly for small images
            height, width = gray.shape
            if height <= 50 or width <= 50:
                scale_factor = 5.0  # Even more aggressive scaling
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

            # Method 1: Direct inversion with threshold
            inverted = cv2.bitwise_not(gray)

            # Method 2: Apply binary threshold to create clean black text on white
            _, binary = cv2.threshold(inverted, 127, 255, cv2.THRESH_BINARY)

            # Method 3: Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

            # Method 4: Final dilation to make text bolder
            kernel_dilate = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            final = cv2.dilate(cleaned, kernel_dilate, iterations=1)

            return final

        except Exception as e:
            self._safe_log("error", f"Alternative preprocessing error: {e}")
            return image
    
    def _recognize_with_backend_enhanced(self, image: np.ndarray, backend: str) -> Tuple[Optional[int], float, str]:
        """Enhanced backend recognition with detailed results"""
        if backend == 'easyocr':
            return self._recognize_easyocr_enhanced(image)
        elif backend == 'paddleocr':
            return self._recognize_paddleocr_enhanced(image)
        elif backend == 'azure_cv':
            return self._recognize_azure_cv_enhanced(image)
        elif backend == 'google_cv':
            return self._recognize_google_cv_enhanced(image)
        else:
            return None, 0.0, "Unknown backend"

    def _recognize_with_backend(self, image: np.ndarray, backend: str) -> Optional[int]:
        """Legacy backend recognition for compatibility"""
        result, _, _ = self._recognize_with_backend_enhanced(image, backend)
        return result
    
    def _recognize_easyocr_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced EasyOCR recognition with detailed results"""
        try:
            # Optimize EasyOCR parameters for small number recognition
            results = self.easyocr_reader.readtext(
                image,
                allowlist='0123456789',
                width_ths=0.4,  # Lower width threshold for small text
                height_ths=0.4,  # Lower height threshold for small text
                paragraph=False,  # Don't group text into paragraphs
                detail=1  # Return detailed results with confidence
            )

            # Process results with detailed logging
            raw_output = f"EasyOCR found {len(results)} text regions: "
            all_detections = []

            best_result = None
            best_confidence = 0
            confidence_threshold = self.debug_config.get('confidence_threshold', 0.3)

            for (bbox, text, confidence) in results:
                detection_info = f"'{text.strip()}'({confidence:.3f})"
                all_detections.append(detection_info)

                # Lower confidence threshold for small images but ensure text quality
                if confidence > confidence_threshold and text.strip().isdigit():
                    number = int(text.strip())
                    if 0 <= number <= 999:
                        if confidence > best_confidence:
                            best_result = number
                            best_confidence = confidence

            raw_output += ", ".join(all_detections) if all_detections else "none"

            return best_result, best_confidence, raw_output

        except Exception as e:
            error_msg = f"EasyOCR error: {e}"
            return None, 0.0, error_msg

    def _recognize_easyocr(self, image: np.ndarray) -> Optional[int]:
        """Legacy EasyOCR recognition for compatibility"""
        result, _, _ = self._recognize_easyocr_enhanced(image)
        return result
    
    def _recognize_paddleocr_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced PaddleOCR recognition with detailed results"""
        try:
            # Use PaddleOCR with optimized settings for small text
            results = self.paddleocr.ocr(image, cls=True)

            raw_output = "PaddleOCR results: "
            all_detections = []

            if results and results[0]:
                best_result = None
                best_confidence = 0
                confidence_threshold = self.debug_config.get('confidence_threshold', 0.3)

                for line in results[0]:
                    if len(line) >= 2:
                        text = line[1][0].strip()
                        confidence = line[1][1]

                        detection_info = f"'{text}'({confidence:.3f})"
                        all_detections.append(detection_info)

                        # Lower confidence threshold for small images
                        if confidence > confidence_threshold and text.isdigit():
                            number = int(text)
                            if 0 <= number <= 999:
                                if confidence > best_confidence:
                                    best_result = number
                                    best_confidence = confidence

                raw_output += ", ".join(all_detections) if all_detections else "none"
                return best_result, best_confidence, raw_output

            raw_output += "no text detected"
            return None, 0.0, raw_output

        except Exception as e:
            error_msg = f"PaddleOCR error: {e}"
            return None, 0.0, error_msg

    def _recognize_paddleocr(self, image: np.ndarray) -> Optional[int]:
        """Legacy PaddleOCR recognition for compatibility"""
        result, _, _ = self._recognize_paddleocr_enhanced(image)
        return result

    def _recognize_azure_cv_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced Azure Computer Vision recognition"""
        # Placeholder for Azure CV implementation
        return None, 0.0, "Azure CV not implemented"

    def _recognize_google_cv_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced Google Cloud Vision recognition"""
        # Placeholder for Google CV implementation
        return None, 0.0, "Google CV not implemented"

    def _recognize_azure_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Azure Computer Vision (requires API key)"""
        try:
            # This would require Azure API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Azure CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Azure CV error: {e}")
            return None
    
    def _recognize_google_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Google Cloud Vision (requires API key)"""
        try:
            # This would require Google Cloud API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Google CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Google CV error: {e}")
            return None
    
    def _check_azure_config(self) -> bool:
        """Check if Azure Computer Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _check_google_config(self) -> bool:
        """Check if Google Cloud Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _init_easyocr(self):
        """Initialize EasyOCR"""
        pass
    
    def _init_paddleocr(self):
        """Initialize PaddleOCR"""
        pass
    
    def _init_azure_cv(self):
        """Initialize Azure Computer Vision"""
        pass
    
    def _init_google_cv(self):
        """Initialize Google Cloud Vision"""
        pass

    # ==================== MACHINE LEARNING SYSTEM ====================

    def _load_training_data(self):
        """Load existing training data from file"""
        if os.path.exists(self.training_data_file):
            try:
                with open(self.training_data_file, 'rb') as f:
                    self.training_data = pickle.load(f)
                self.logger.info(f"Loaded {len(self.training_data)} training samples")
            except Exception as e:
                self.logger.error(f"Failed to load training data: {e}")
                self.training_data = []
        else:
            self.training_data = []

    def _save_training_data(self):
        """Save training data to file"""
        try:
            with open(self.training_data_file, 'wb') as f:
                pickle.dump(self.training_data, f)
            self.logger.info(f"Saved {len(self.training_data)} training samples")
        except Exception as e:
            self.logger.error(f"Failed to save training data: {e}")

    def _load_trained_model(self):
        """Load trained model from file"""
        if os.path.exists(self.model_file) and self.learning_enabled:
            try:
                with open(self.model_file, 'rb') as f:
                    model_data = pickle.load(f)
                    self.trained_model = model_data['model']
                    self.feature_extractor = model_data['feature_extractor']
                    self.model_accuracy = model_data.get('accuracy', 0.0)
                self.logger.info(f"Loaded trained model with {self.model_accuracy:.2%} accuracy")
            except Exception as e:
                self.logger.error(f"Failed to load trained model: {e}")
                self.trained_model = None
                self.feature_extractor = None

    def _save_trained_model(self):
        """Save trained model to file"""
        if self.trained_model and self.feature_extractor:
            try:
                model_data = {
                    'model': self.trained_model,
                    'feature_extractor': self.feature_extractor,
                    'accuracy': self.model_accuracy,
                    'training_samples': len(self.training_data),
                    'created_date': datetime.now().isoformat()
                }
                with open(self.model_file, 'wb') as f:
                    pickle.dump(model_data, f)
                self.logger.info(f"Saved trained model with {self.model_accuracy:.2%} accuracy")
            except Exception as e:
                self.logger.error(f"Failed to save trained model: {e}")

    def extract_image_features(self, image: np.ndarray) -> List[float]:
        """Extract numerical features from image for machine learning"""
        try:
            # Ensure image is grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Resize to standard size for consistent features
            resized = cv2.resize(gray, (32, 32))

            # Extract various features
            features = []

            # 1. Pixel intensity features (flattened image)
            features.extend(resized.flatten().astype(float) / 255.0)

            # 2. Statistical features
            features.append(np.mean(resized))
            features.append(np.std(resized))
            features.append(np.min(resized))
            features.append(np.max(resized))

            # 3. Edge features
            edges = cv2.Canny(resized, 50, 150)
            features.append(np.sum(edges > 0) / (32 * 32))  # Edge density

            # 4. Contour features
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            features.append(len(contours))  # Number of contours

            # 5. Histogram features
            hist = cv2.calcHist([resized], [0], None, [16], [0, 256])
            features.extend(hist.flatten() / np.sum(hist))

            return features

        except Exception as e:
            self.logger.error(f"Feature extraction failed: {e}")
            return [0.0] * 1100  # Return default feature vector

    def add_training_sample(self, image: np.ndarray, correct_number: str, piece_id: str = "manual"):
        """Add a training sample with the correct label"""
        if not self.learning_enabled:
            self.logger.warning("Machine learning not available (sklearn not installed)")
            return False

        try:
            # DEBUG: Log image info
            self._safe_log("debug", f"Training Debug - Adding sample for '{correct_number}'")
            self._safe_log("debug", f"Training Debug - Image shape: {image.shape}")
            self._safe_log("debug", f"Training Debug - Image dtype: {image.dtype}")

            # Extract features from image
            features = self.extract_image_features(image)
            self._safe_log("debug", f"Training Debug - Extracted {len(features)} features")

            # Create image hash for deduplication
            image_hash = hashlib.md5(image.tobytes()).hexdigest()
            self._safe_log("debug", f"Training Debug - Image hash: {image_hash[:8]}...")

            # Add training sample
            training_sample = {
                'features': features,
                'label': correct_number,
                'piece_id': piece_id,
                'image_hash': image_hash,
                'timestamp': datetime.now().isoformat(),
                'image_shape': image.shape
            }

            # Check for duplicates
            existing_hashes = [sample['image_hash'] for sample in self.training_data]
            if image_hash not in existing_hashes:
                self.training_data.append(training_sample)
                self._save_training_data()
                self._safe_log("info", f"Added training sample: '{correct_number}' for {piece_id} (Total: {len(self.training_data)})")
                return True
            else:
                self._safe_log("warning", f"Duplicate training sample ignored: '{correct_number}'")
                return False

        except Exception as e:
            self.logger.error(f"Failed to add training sample: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def train_model(self) -> bool:
        """Train the machine learning model on collected data"""
        if not self.learning_enabled:
            self.logger.warning("Machine learning not available (sklearn not installed)")
            return False

        if len(self.training_data) < 10:
            self.logger.warning(f"Need at least 10 training samples, have {len(self.training_data)}")
            return False

        try:
            # Prepare training data
            X = [sample['features'] for sample in self.training_data]
            y = [sample['label'] for sample in self.training_data]

            # Split data for validation
            if len(self.training_data) >= 20:
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            else:
                X_train, X_test, y_train, y_test = X, X, y, y

            # Train Random Forest model
            self.trained_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )

            self.trained_model.fit(X_train, y_train)

            # Calculate accuracy
            y_pred = self.trained_model.predict(X_test)
            self.model_accuracy = accuracy_score(y_test, y_pred)

            # Save the trained model
            self._save_trained_model()

            self.logger.info(f"Model trained successfully! Accuracy: {self.model_accuracy:.2%}")
            self.logger.info(f"Training samples: {len(self.training_data)}")

            return True

        except Exception as e:
            self.logger.error(f"Model training failed: {e}")
            return False

    def predict_with_ml(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Use trained ML model to predict number"""
        if not self.learning_enabled or not self.trained_model:
            self._safe_log("debug", "ML prediction skipped - no model or learning disabled")
            return None, 0.0

        try:
            # Extract features
            features = self.extract_image_features(image)
            self._safe_log("debug", f"ML Debug - Extracted {len(features)} features")

            # Make prediction
            prediction = self.trained_model.predict([features])[0]

            # Get confidence (probability of the predicted class)
            probabilities = self.trained_model.predict_proba([features])[0]
            confidence = np.max(probabilities)

            # Get all predictions with probabilities for debugging
            classes = self.trained_model.classes_
            pred_debug = [(classes[i], probabilities[i]) for i in range(len(classes))]
            pred_debug.sort(key=lambda x: x[1], reverse=True)

            self._safe_log("debug", f"ML Debug - Top predictions: {pred_debug[:3]}")

            return prediction, confidence

        except Exception as e:
            self.logger.error(f"ML prediction failed: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None, 0.0

    def get_training_stats(self) -> Dict[str, Any]:
        """Get statistics about the training data and model"""
        stats = {
            'learning_enabled': self.learning_enabled,
            'training_samples': len(self.training_data),
            'model_trained': self.trained_model is not None,
            'model_accuracy': self.model_accuracy,
            'unique_labels': len(set(sample['label'] for sample in self.training_data)) if self.training_data else 0
        }

        if self.training_data:
            # Label distribution
            label_counts = {}
            for sample in self.training_data:
                label = sample['label']
                label_counts[label] = label_counts.get(label, 0) + 1
            stats['label_distribution'] = label_counts

        return stats

# Singleton instance for global use
ai_ocr_service = None

def get_ai_ocr_service() -> AINumberRecognition:
    """Get singleton AI OCR service instance"""
    global ai_ocr_service
    if ai_ocr_service is None:
        ai_ocr_service = AINumberRecognition()
    return ai_ocr_service

def recognize_number_ai(image: np.ndarray, fast_mode: bool = True) -> Optional[int]:
    """Convenience function for AI number recognition"""
    service = get_ai_ocr_service()
    return service.recognize_number(image, fast_mode)

if __name__ == "__main__":
    # Test the AI OCR service
    print("🧪 Testing AI OCR Service")
    service = AINumberRecognition()
    
    if service.active_backends:
        print(f"AI OCR ready with: {service.active_backends}")
    else:
        print("No AI OCR backends available")
        print("Install with: pip install easyocr paddlepaddle paddleocr")
