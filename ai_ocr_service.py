#!/usr/bin/env python3
"""
AI-Based OCR Service for Map Trade Number Recognition
Provides multiple AI OCR backends with GPU acceleration and improved performance
"""
import cv2
import numpy as np
import base64
import json
import time
import logging
import os
from typing import Optional, Dict, Any, List, Tuple
from io import BytesIO
import requests
import platform
from datetime import datetime

class AINumberRecognition:
    """AI-powered number recognition service with GPU acceleration and comprehensive debugging"""

    def __init__(self, debug_config=None):
        self.logger = logging.getLogger("AINumberRecognition")
        self.backends = {
            'easyocr': self._init_easyocr,
            'paddleocr': self._init_paddleocr,
            'azure_cv': self._init_azure_cv,
            'google_cv': self._init_google_cv
        }
        self.active_backends = []
        self.gpu_available = self._check_gpu_availability()

        # Enhanced debugging and monitoring
        self.debug_config = debug_config or self._load_debug_config()
        self.ocr_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'method_usage': {},
            'processing_times': [],
            'confidence_scores': []
        }
        self._setup_debug_directories()
        self._initialize_backends()

    def _load_debug_config(self):
        """Load OCR debug configuration"""
        default_config = {
            'enable_detailed_logging': True,
            'save_debug_images': True,
            'log_confidence_scores': True,
            'log_processing_times': True,
            'log_preprocessing_steps': True,
            'log_raw_ocr_output': True,
            'confidence_threshold': 0.3,
            'save_failed_detections': True,
            'max_debug_images': 100,
            'debug_image_format': 'png'
        }

        try:
            # Try to load from config file if it exists
            config_path = 'ocr_debug_config.json'
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    self._safe_log("info", f"Loaded OCR debug config from {config_path}")
            else:
                # Save default config for user customization
                with open(config_path, 'w') as f:
                    json.dump(default_config, f, indent=2)
                    self._safe_log("info", f"Created default OCR debug config at {config_path}")
        except Exception as e:
            self._safe_log("warning", f"Could not load OCR debug config: {e}")

        return default_config

    def _setup_debug_directories(self):
        """Setup directories for debug output"""
        if self.debug_config.get('save_debug_images', False):
            self.debug_dirs = {
                'images': 'debug/ocr_images',
                'preprocessing': 'debug/ocr_preprocessing',
                'failed': 'debug/ocr_failed',
                'successful': 'debug/ocr_successful'
            }

            for dir_name, dir_path in self.debug_dirs.items():
                os.makedirs(dir_path, exist_ok=True)

            self._safe_log("info", "OCR debug directories created")

    def _check_gpu_availability(self):
        """Check if GPU acceleration is available for OCR processing"""
        gpu_available = False
        gpu_info = "CPU only"

        try:
            # Check for CUDA/PyTorch
            import torch
            if torch.cuda.is_available():
                gpu_available = True
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                gpu_info = f"CUDA GPU available: {gpu_name} ({gpu_count} device(s))"
            else:
                gpu_info = "CUDA not available"
        except ImportError:
            try:
                # Alternative: Check for NVIDIA GPU via nvidia-ml-py
                import pynvml
                pynvml.nvmlInit()
                gpu_count = pynvml.nvmlDeviceGetCount()
                if gpu_count > 0:
                    gpu_available = True
                    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                    gpu_name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                    gpu_info = f"NVIDIA GPU detected: {gpu_name} ({gpu_count} device(s))"
            except:
                # Final fallback: Check if nvidia-smi exists
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        gpu_available = True
                        gpu_info = f"NVIDIA GPU detected: {result.stdout.strip()}"
                except:
                    pass

        # Use Unicode-safe logging
        self._safe_log("info", f"GPU Detection: {gpu_info}")
        return gpu_available

    def _safe_log(self, level, message):
        """Unicode-safe logging that removes problematic characters"""
        # Remove emoji and other Unicode characters that might cause encoding issues
        safe_message = message.encode('ascii', errors='ignore').decode('ascii')
        if not safe_message.strip():
            # If message becomes empty, use a fallback
            safe_message = "[Unicode message - content removed for compatibility]"

        getattr(self.logger, level)(safe_message)

    def get_ocr_statistics(self) -> Dict[str, Any]:
        """Get comprehensive OCR performance statistics"""
        stats = self.ocr_stats.copy()

        # Calculate derived statistics
        if stats['total_processed'] > 0:
            stats['success_rate'] = stats['successful_detections'] / stats['total_processed']
        else:
            stats['success_rate'] = 0.0

        if stats['processing_times']:
            stats['avg_processing_time'] = sum(stats['processing_times']) / len(stats['processing_times'])
            stats['min_processing_time'] = min(stats['processing_times'])
            stats['max_processing_time'] = max(stats['processing_times'])
        else:
            stats['avg_processing_time'] = 0.0
            stats['min_processing_time'] = 0.0
            stats['max_processing_time'] = 0.0

        if stats['confidence_scores']:
            stats['avg_confidence'] = sum(stats['confidence_scores']) / len(stats['confidence_scores'])
            stats['min_confidence'] = min(stats['confidence_scores'])
            stats['max_confidence'] = max(stats['confidence_scores'])
        else:
            stats['avg_confidence'] = 0.0
            stats['min_confidence'] = 0.0
            stats['max_confidence'] = 0.0

        return stats

    def log_performance_summary(self):
        """Log a comprehensive performance summary"""
        stats = self.get_ocr_statistics()

        self._safe_log("info", "=== OCR Performance Summary ===")
        self._safe_log("info", f"Total processed: {stats['total_processed']}")
        self._safe_log("info", f"Successful detections: {stats['successful_detections']}")
        self._safe_log("info", f"Success rate: {stats['success_rate']:.1%}")

        if stats['avg_processing_time'] > 0:
            self._safe_log("info", f"Avg processing time: {stats['avg_processing_time']:.3f}s")
            self._safe_log("info", f"Processing time range: {stats['min_processing_time']:.3f}s - {stats['max_processing_time']:.3f}s")

        if stats['avg_confidence'] > 0:
            self._safe_log("info", f"Avg confidence: {stats['avg_confidence']:.3f}")
            self._safe_log("info", f"Confidence range: {stats['min_confidence']:.3f} - {stats['max_confidence']:.3f}")

        if stats['method_usage']:
            self._safe_log("info", "Method usage:")
            for method, count in stats['method_usage'].items():
                percentage = (count / stats['total_processed']) * 100 if stats['total_processed'] > 0 else 0
                self._safe_log("info", f"  {method}: {count} ({percentage:.1f}%)")

        self._safe_log("info", "=== End Summary ===")

    def reset_statistics(self):
        """Reset OCR statistics"""
        self.ocr_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'method_usage': {},
            'processing_times': [],
            'confidence_scores': []
        }
        self._safe_log("info", "OCR statistics reset")

    def update_debug_config(self, new_config: Dict[str, Any]):
        """Update debug configuration at runtime"""
        self.debug_config.update(new_config)

        # Save updated config
        try:
            config_path = 'ocr_debug_config.json'
            with open(config_path, 'w') as f:
                json.dump(self.debug_config, f, indent=2)
            self._safe_log("info", f"Updated OCR debug config: {list(new_config.keys())}")
        except Exception as e:
            self._safe_log("warning", f"Could not save updated debug config: {e}")

    def get_debug_config(self) -> Dict[str, Any]:
        """Get current debug configuration"""
        return self.debug_config.copy()

    def _initialize_backends(self):
        """Initialize available AI OCR backends with GPU acceleration"""
        # Try EasyOCR with GPU acceleration
        try:
            import easyocr

            # Initialize with GPU if available, fallback to CPU
            use_gpu = self.gpu_available
            self.easyocr_reader = easyocr.Reader(['en'], gpu=use_gpu)
            self.active_backends.append('easyocr')

            if use_gpu:
                self._safe_log("info", "EasyOCR initialized with GPU acceleration")
            else:
                self._safe_log("info", "EasyOCR initialized with CPU processing")

        except ImportError:
            self._safe_log("info", "EasyOCR not available (pip install easyocr)")
        except Exception as e:
            self._safe_log("warning", f"EasyOCR initialization failed: {e}")
            # Try fallback to CPU if GPU initialization failed
            if self.gpu_available:
                try:
                    import easyocr
                    self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
                    self.active_backends.append('easyocr')
                    self._safe_log("info", "EasyOCR initialized with CPU fallback after GPU failure")
                except Exception as e2:
                    self._safe_log("warning", f"EasyOCR CPU fallback also failed: {e2}")
        
        # Try PaddleOCR with GPU support
        try:
            from paddleocr import PaddleOCR

            # Configure PaddleOCR with GPU if available
            paddle_kwargs = {
                'use_angle_cls': True,
                'lang': 'en',
                'use_gpu': self.gpu_available
            }

            # Handle show_log parameter compatibility
            try:
                paddle_kwargs['show_log'] = False
                self.paddleocr = PaddleOCR(**paddle_kwargs)
            except:
                # Fallback without show_log parameter
                del paddle_kwargs['show_log']
                self.paddleocr = PaddleOCR(**paddle_kwargs)

            self.active_backends.append('paddleocr')

            if self.gpu_available:
                self._safe_log("info", "PaddleOCR initialized with GPU acceleration")
            else:
                self._safe_log("info", "PaddleOCR initialized with CPU processing")

        except ImportError:
            self._safe_log("info", "PaddleOCR not available (pip install paddlepaddle paddleocr)")
        except Exception as e:
            self._safe_log("warning", f"PaddleOCR initialization failed: {e}")
            # Try CPU fallback if GPU failed
            if self.gpu_available:
                try:
                    from paddleocr import PaddleOCR
                    self.paddleocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
                    self.active_backends.append('paddleocr')
                    self._safe_log("info", "PaddleOCR initialized with CPU fallback after GPU failure")
                except Exception as e2:
                    self._safe_log("warning", f"PaddleOCR CPU fallback also failed: {e2}")
        
        # Azure Computer Vision (cloud, excellent accuracy, requires API key)
        if self._check_azure_config():
            self.active_backends.append('azure_cv')
            self._safe_log("info", "Azure Computer Vision available")

        # Google Cloud Vision (cloud, excellent accuracy, requires API key)
        if self._check_google_config():
            self.active_backends.append('google_cv')
            self._safe_log("info", "Google Cloud Vision available")

        if not self.active_backends:
            self._safe_log("warning", "No AI OCR backends available - install easyocr or paddleocr")
        else:
            self._safe_log("info", f"AI OCR ready with backends: {self.active_backends}")

        # Log performance optimization info
        if self.gpu_available and any(backend in self.active_backends for backend in ['easyocr', 'paddleocr']):
            self._safe_log("info", "GPU acceleration enabled for optimal OCR performance")
        elif not self.gpu_available:
            self._safe_log("info", "Using CPU processing - consider GPU for better performance")
    
    def recognize_number(self, image: np.ndarray, fast_mode: bool = True, piece_id: str = "unknown") -> Optional[int]:
        """Enhanced number recognition with comprehensive debugging and monitoring"""
        if not self.active_backends:
            self._safe_log("error", "No AI OCR backends available")
            return None

        start_time = time.time()
        self.ocr_stats['total_processed'] += 1

        # Save original image for debugging
        debug_session_id = f"{piece_id}_{datetime.now().strftime('%H%M%S_%f')}"
        if self.debug_config.get('save_debug_images', False):
            self._save_debug_image(image, f"original_{debug_session_id}", "images")

        try:
            # Enhanced preprocessing with logging
            processed_image, preprocessing_info = self._preprocess_for_ai_enhanced(image)

            if self.debug_config.get('log_preprocessing_steps', False):
                self._safe_log("debug", f"[{piece_id}] Preprocessing: {preprocessing_info}")

            # Save preprocessed image for debugging
            if self.debug_config.get('save_debug_images', False):
                self._save_debug_image(processed_image, f"processed_{debug_session_id}", "preprocessing")

            # Try backends with detailed logging
            backends_to_try = self._get_backend_order(fast_mode)
            detection_results = []

            for backend in backends_to_try:
                try:
                    backend_start = time.time()
                    result, confidence, raw_output = self._recognize_with_backend_enhanced(processed_image, backend)
                    backend_time = time.time() - backend_start

                    # Log detailed results
                    detection_info = {
                        'backend': backend,
                        'result': result,
                        'confidence': confidence,
                        'raw_output': raw_output,
                        'processing_time': backend_time
                    }
                    detection_results.append(detection_info)

                    if self.debug_config.get('enable_detailed_logging', False):
                        self._log_detection_result(piece_id, detection_info)

                    # Update statistics
                    if backend not in self.ocr_stats['method_usage']:
                        self.ocr_stats['method_usage'][backend] = 0
                    self.ocr_stats['method_usage'][backend] += 1

                    if result is not None:
                        self.ocr_stats['successful_detections'] += 1
                        self.ocr_stats['confidence_scores'].append(confidence)

                        total_time = time.time() - start_time
                        self.ocr_stats['processing_times'].append(total_time)

                        # Save successful detection
                        if self.debug_config.get('save_debug_images', False):
                            self._save_debug_image(image, f"success_{result}_{debug_session_id}", "successful")

                        # Log success summary
                        if self.debug_config.get('enable_detailed_logging', False):
                            self._safe_log("info", f"[{piece_id}] SUCCESS: {result} (method: {backend}, confidence: {confidence:.3f}, time: {total_time:.3f}s)")

                        return result

                except Exception as e:
                    self._safe_log("debug", f"[{piece_id}] Backend {backend} failed: {e}")
                    continue

            # All backends failed
            total_time = time.time() - start_time
            self.ocr_stats['processing_times'].append(total_time)

            # Save failed detection for analysis
            if self.debug_config.get('save_failed_detections', False):
                self._save_debug_image(image, f"failed_{debug_session_id}", "failed")
                self._save_detection_log(piece_id, detection_results, debug_session_id)

            if self.debug_config.get('enable_detailed_logging', False):
                self._safe_log("warning", f"[{piece_id}] FAILED: All OCR methods failed (time: {total_time:.3f}s)")

            return None

        except Exception as e:
            total_time = time.time() - start_time
            self._safe_log("error", f"[{piece_id}] OCR error: {e} (time: {total_time:.3f}s)")
            return None

    def _get_backend_order(self, fast_mode: bool) -> List[str]:
        """Get backend processing order based on mode"""
        backends_to_try = self.active_backends.copy()
        if fast_mode:
            # Prioritize local backends for speed
            local_backends = [b for b in backends_to_try if b in ['easyocr', 'paddleocr']]
            cloud_backends = [b for b in backends_to_try if b not in local_backends]
            backends_to_try = local_backends + cloud_backends
        return backends_to_try

    def _save_debug_image(self, image: np.ndarray, filename: str, category: str):
        """Save debug image to appropriate directory"""
        try:
            if hasattr(self, 'debug_dirs') and category in self.debug_dirs:
                filepath = os.path.join(self.debug_dirs[category], f"{filename}.{self.debug_config.get('debug_image_format', 'png')}")
                cv2.imwrite(filepath, image)

                # Cleanup old images if limit exceeded
                self._cleanup_debug_images(self.debug_dirs[category])
        except Exception as e:
            self._safe_log("debug", f"Could not save debug image: {e}")

    def _cleanup_debug_images(self, directory: str):
        """Remove old debug images if limit exceeded"""
        try:
            max_images = self.debug_config.get('max_debug_images', 100)
            files = [f for f in os.listdir(directory) if f.endswith(('.png', '.jpg', '.jpeg'))]

            if len(files) > max_images:
                # Sort by modification time and remove oldest
                files_with_time = [(f, os.path.getmtime(os.path.join(directory, f))) for f in files]
                files_with_time.sort(key=lambda x: x[1])

                files_to_remove = files_with_time[:len(files) - max_images]
                for filename, _ in files_to_remove:
                    os.remove(os.path.join(directory, filename))
        except Exception as e:
            self._safe_log("debug", f"Could not cleanup debug images: {e}")

    def _log_detection_result(self, piece_id: str, detection_info: Dict):
        """Log detailed detection result"""
        backend = detection_info['backend']
        result = detection_info['result']
        confidence = detection_info['confidence']
        raw_output = detection_info['raw_output']
        processing_time = detection_info['processing_time']

        if result is not None:
            self._safe_log("info", f"[{piece_id}] {backend.upper()}: '{result}' (conf: {confidence:.3f}, time: {processing_time:.3f}s)")
        else:
            self._safe_log("debug", f"[{piece_id}] {backend.upper()}: FAILED (time: {processing_time:.3f}s)")

        if self.debug_config.get('log_raw_ocr_output', False) and raw_output:
            self._safe_log("debug", f"[{piece_id}] {backend.upper()} raw: {raw_output}")

    def _save_detection_log(self, piece_id: str, detection_results: List[Dict], session_id: str):
        """Save detailed detection log for failed cases"""
        try:
            log_data = {
                'piece_id': piece_id,
                'session_id': session_id,
                'timestamp': datetime.now().isoformat(),
                'detection_results': detection_results,
                'config': self.debug_config
            }

            log_file = os.path.join(self.debug_dirs.get('failed', 'debug'), f"detection_log_{session_id}.json")
            with open(log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
        except Exception as e:
            self._safe_log("debug", f"Could not save detection log: {e}")

    def _preprocess_for_ai_enhanced(self, image: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """Enhanced preprocessing with detailed logging"""
        preprocessing_info = {
            'original_shape': image.shape,
            'steps_applied': []
        }

        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                preprocessing_info['steps_applied'].append('bgr_to_gray')
            else:
                gray = image.copy()

            preprocessing_info['gray_shape'] = gray.shape

            # For small images like map pieces (35x35), scale up for better AI recognition
            height, width = gray.shape
            if height <= 50 or width <= 50:
                # Scale up by 3x for better OCR accuracy on small text
                scale_factor = 3.0
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                preprocessing_info['steps_applied'].append(f'upscale_3x_to_{new_width}x{new_height}')

                # Apply sharpening after upscaling for crisp text
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)
                preprocessing_info['steps_applied'].append('sharpening_filter')

            # Enhance contrast specifically for number recognition
            if gray.shape[0] > 16 and gray.shape[1] > 16:
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))
                enhanced = clahe.apply(gray)
                preprocessing_info['steps_applied'].append('clahe_contrast_enhancement')
            else:
                # For very small images, use simple histogram equalization
                enhanced = cv2.equalizeHist(gray)
                preprocessing_info['steps_applied'].append('histogram_equalization')

            # Apply slight denoising while preserving edges
            enhanced = cv2.bilateralFilter(enhanced, 5, 50, 50)
            preprocessing_info['steps_applied'].append('bilateral_denoising')

            preprocessing_info['final_shape'] = enhanced.shape
            preprocessing_info['brightness_mean'] = float(np.mean(enhanced))
            preprocessing_info['contrast_std'] = float(np.std(enhanced))

            return enhanced, preprocessing_info

        except Exception as e:
            self._safe_log("error", f"Preprocessing error: {e}")
            preprocessing_info['error'] = str(e)
            return image, preprocessing_info

    def _preprocess_for_ai(self, image: np.ndarray) -> np.ndarray:
        """Optimized preprocessing for small map piece images (35x35 pixels)"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # For small images like map pieces (35x35), scale up for better AI recognition
            height, width = gray.shape
            if height <= 50 or width <= 50:
                # Scale up by 3x for better OCR accuracy on small text
                scale_factor = 3.0
                new_height, new_width = int(height * scale_factor), int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                # Apply sharpening after upscaling for crisp text
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                gray = cv2.filter2D(gray, -1, kernel)

            # Enhance contrast specifically for number recognition
            # Use adaptive histogram equalization for small regions
            if gray.shape[0] > 16 and gray.shape[1] > 16:
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))
                enhanced = clahe.apply(gray)
            else:
                # For very small images, use simple histogram equalization
                enhanced = cv2.equalizeHist(gray)

            # Apply slight denoising while preserving edges
            enhanced = cv2.bilateralFilter(enhanced, 5, 50, 50)

            return enhanced

        except Exception as e:
            self._safe_log("error", f"Preprocessing error: {e}")
            return image
    
    def _recognize_with_backend_enhanced(self, image: np.ndarray, backend: str) -> Tuple[Optional[int], float, str]:
        """Enhanced backend recognition with detailed results"""
        if backend == 'easyocr':
            return self._recognize_easyocr_enhanced(image)
        elif backend == 'paddleocr':
            return self._recognize_paddleocr_enhanced(image)
        elif backend == 'azure_cv':
            return self._recognize_azure_cv_enhanced(image)
        elif backend == 'google_cv':
            return self._recognize_google_cv_enhanced(image)
        else:
            return None, 0.0, "Unknown backend"

    def _recognize_with_backend(self, image: np.ndarray, backend: str) -> Optional[int]:
        """Legacy backend recognition for compatibility"""
        result, _, _ = self._recognize_with_backend_enhanced(image, backend)
        return result
    
    def _recognize_easyocr_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced EasyOCR recognition with detailed results"""
        try:
            # Optimize EasyOCR parameters for small number recognition
            results = self.easyocr_reader.readtext(
                image,
                allowlist='0123456789',
                width_ths=0.4,  # Lower width threshold for small text
                height_ths=0.4,  # Lower height threshold for small text
                paragraph=False,  # Don't group text into paragraphs
                detail=1  # Return detailed results with confidence
            )

            # Process results with detailed logging
            raw_output = f"EasyOCR found {len(results)} text regions: "
            all_detections = []

            best_result = None
            best_confidence = 0
            confidence_threshold = self.debug_config.get('confidence_threshold', 0.3)

            for (bbox, text, confidence) in results:
                detection_info = f"'{text.strip()}'({confidence:.3f})"
                all_detections.append(detection_info)

                # Lower confidence threshold for small images but ensure text quality
                if confidence > confidence_threshold and text.strip().isdigit():
                    number = int(text.strip())
                    if 0 <= number <= 999:
                        if confidence > best_confidence:
                            best_result = number
                            best_confidence = confidence

            raw_output += ", ".join(all_detections) if all_detections else "none"

            return best_result, best_confidence, raw_output

        except Exception as e:
            error_msg = f"EasyOCR error: {e}"
            return None, 0.0, error_msg

    def _recognize_easyocr(self, image: np.ndarray) -> Optional[int]:
        """Legacy EasyOCR recognition for compatibility"""
        result, _, _ = self._recognize_easyocr_enhanced(image)
        return result
    
    def _recognize_paddleocr_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced PaddleOCR recognition with detailed results"""
        try:
            # Use PaddleOCR with optimized settings for small text
            results = self.paddleocr.ocr(image, cls=True)

            raw_output = "PaddleOCR results: "
            all_detections = []

            if results and results[0]:
                best_result = None
                best_confidence = 0
                confidence_threshold = self.debug_config.get('confidence_threshold', 0.3)

                for line in results[0]:
                    if len(line) >= 2:
                        text = line[1][0].strip()
                        confidence = line[1][1]

                        detection_info = f"'{text}'({confidence:.3f})"
                        all_detections.append(detection_info)

                        # Lower confidence threshold for small images
                        if confidence > confidence_threshold and text.isdigit():
                            number = int(text)
                            if 0 <= number <= 999:
                                if confidence > best_confidence:
                                    best_result = number
                                    best_confidence = confidence

                raw_output += ", ".join(all_detections) if all_detections else "none"
                return best_result, best_confidence, raw_output

            raw_output += "no text detected"
            return None, 0.0, raw_output

        except Exception as e:
            error_msg = f"PaddleOCR error: {e}"
            return None, 0.0, error_msg

    def _recognize_paddleocr(self, image: np.ndarray) -> Optional[int]:
        """Legacy PaddleOCR recognition for compatibility"""
        result, _, _ = self._recognize_paddleocr_enhanced(image)
        return result

    def _recognize_azure_cv_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced Azure Computer Vision recognition"""
        # Placeholder for Azure CV implementation
        return None, 0.0, "Azure CV not implemented"

    def _recognize_google_cv_enhanced(self, image: np.ndarray) -> Tuple[Optional[int], float, str]:
        """Enhanced Google Cloud Vision recognition"""
        # Placeholder for Google CV implementation
        return None, 0.0, "Google CV not implemented"

    def _recognize_azure_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Azure Computer Vision (requires API key)"""
        try:
            # This would require Azure API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Azure CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Azure CV error: {e}")
            return None
    
    def _recognize_google_cv(self, image: np.ndarray) -> Optional[int]:
        """Recognize using Google Cloud Vision (requires API key)"""
        try:
            # This would require Google Cloud API key configuration
            # Implementation placeholder for future use
            self.logger.debug("Google CV not implemented yet")
            return None
            
        except Exception as e:
            self.logger.debug(f"Google CV error: {e}")
            return None
    
    def _check_azure_config(self) -> bool:
        """Check if Azure Computer Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _check_google_config(self) -> bool:
        """Check if Google Cloud Vision is configured"""
        # Check for API key in environment or config file
        return False  # Placeholder
    
    def _init_easyocr(self):
        """Initialize EasyOCR"""
        pass
    
    def _init_paddleocr(self):
        """Initialize PaddleOCR"""
        pass
    
    def _init_azure_cv(self):
        """Initialize Azure Computer Vision"""
        pass
    
    def _init_google_cv(self):
        """Initialize Google Cloud Vision"""
        pass

# Singleton instance for global use
ai_ocr_service = None

def get_ai_ocr_service() -> AINumberRecognition:
    """Get singleton AI OCR service instance"""
    global ai_ocr_service
    if ai_ocr_service is None:
        ai_ocr_service = AINumberRecognition()
    return ai_ocr_service

def recognize_number_ai(image: np.ndarray, fast_mode: bool = True) -> Optional[int]:
    """Convenience function for AI number recognition"""
    service = get_ai_ocr_service()
    return service.recognize_number(image, fast_mode)

if __name__ == "__main__":
    # Test the AI OCR service
    print("🧪 Testing AI OCR Service")
    service = AINumberRecognition()
    
    if service.active_backends:
        print(f"AI OCR ready with: {service.active_backends}")
    else:
        print("No AI OCR backends available")
        print("Install with: pip install easyocr paddlepaddle paddleocr")
