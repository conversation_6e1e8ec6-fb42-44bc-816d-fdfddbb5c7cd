# Intelligent Map Trade System Guide

## Overview

The enhanced Map Trade module now includes intelligent image detection and analysis to optimize trading decisions. After step `3_trade_option_1`, the system analyzes the screen and makes smart trading choices.

## Features

### 1. **Screen State Detection**
After step 3, the system detects:
- **Red Thumb**: Previous trade completed, needs to thank and start new exchange
- **Initiate Exchange**: Already on the exchange page, ready to analyze
- **Normal**: Continue with regular trade process

### 2. **Intelligent Map Analysis**
When on the initiate exchange page:
- Detects all 7 map pieces (M1, M2, M3, M4, M5, M6, M7)
- Reads the black numbers below each map piece using OCR
- Finds the lowest number (what you'll give away)
- Finds the highest number (what you'll receive)
- Executes optimal trade automatically

### 3. **Automated Exchange Process**
1. Click on map piece with lowest number
2. Switch to "you will lose" area
3. Click on map piece with highest number
4. Click "Initiate Exchange"
5. Continue with normal trade process

## Setup and Calibration

### Prerequisites
```bash
pip install opencv-python pytesseract
```

### 1. **Calibrate Map Piece Regions**

Run the calibration tool:
```bash
python test_intelligent_map_trade.py
```

Choose option 4 to calibrate map regions:
1. Navigate to the initiate exchange screen
2. Make sure all 7 map pieces (M1-M7) are visible
3. For each map piece, position mouse over the CENTER
4. Press SPACE to capture the position
5. Copy the generated coordinates to the code

### 2. **Update Coordinates**

Edit `modules/map_trade.py` and update the `_define_map_piece_regions` method with your calibrated coordinates:

```python
def _define_map_piece_regions(self) -> Dict[str, Tuple[int, int, int, int]]:
    return {
        'M1': (100, 200, 80, 100),   # Replace with your coordinates
        'M2': (200, 200, 80, 100),   # (x, y, width, height)
        'M3': (300, 200, 80, 100),
        'M4': (400, 200, 80, 100),
        'M5': (500, 200, 80, 100),
        'M6': (600, 200, 80, 100),
        'M7': (700, 200, 80, 100),
    }
```

### 3. **Calibrate Action Coordinates**

You also need to calibrate these coordinates in the code:

**Red Thumb Handling:**
- `new_exchange_x, new_exchange_y` - "New Exchange" button location

**Exchange Process:**
- `lose_area_x, lose_area_y` - "You will lose" area location
- `initiate_x, initiate_y` - "Initiate Exchange" button location

## Testing

### Test Individual Components

1. **Screen Detection Test**:
   ```bash
   python test_intelligent_map_trade.py
   ```
   Choose option 1, navigate to post-step-3 screen

2. **Map Analysis Test**:
   Choose option 2, navigate to initiate exchange screen

3. **Full Intelligence Test**:
   Choose option 3, navigate to post-step-3 screen

### Expected Behavior

**Normal Flow:**
1. Steps 1-3 execute normally
2. After step 3, intelligent analysis begins
3. System detects screen state
4. If red thumb: clicks thumb → new exchange → analyzes maps
5. If initiate exchange: analyzes maps directly
6. If normal: continues with regular process
7. Optimal trade executed automatically

## Configuration Options

### Enable/Disable Intelligence

In `modules/map_trade.py`:
```python
self.intelligent_trading = True      # Enable intelligent trading
self.trade_analysis_enabled = True   # Enable analysis after step 3
```

### OCR Settings

Adjust OCR parameters for better number detection:
```python
# In _extract_number_from_image method
mask = cv2.inRange(gray, 0, 50)  # Adjust threshold for black text
config = '--psm 8 -c tessedit_char_whitelist=0123456789'
```

### Color Detection

Adjust red thumb detection sensitivity:
```python
# In _detect_screen_state method
red_pixels > 1000  # Adjust threshold for red detection
```

## Troubleshooting

### Common Issues

1. **OCR Not Reading Numbers**:
   - Install Tesseract OCR properly
   - Adjust threshold values in `_extract_number_from_image`
   - Check map piece region coordinates

2. **Red Thumb Not Detected**:
   - Adjust red color range in HSV
   - Modify `red_pixels` threshold
   - Check screen resolution/scaling

3. **Wrong Coordinates**:
   - Re-run calibration tool
   - Check screen resolution matches
   - Verify game window position

### Debug Mode

Enable detailed logging:
```python
logging.getLogger("LastWar.map_trade").setLevel(logging.DEBUG)
```

## Advanced Configuration

### Custom Map Regions

For non-standard layouts, manually define regions:
```python
'M1': (x, y, width, height),  # Top-left corner and size
```

### Multiple Screen Resolutions

Create resolution-specific configurations:
```python
if screen_width == 1920:
    # 1920x1080 coordinates
elif screen_width == 2560:
    # 2560x1440 coordinates
```

## Performance Notes

- Image analysis adds ~2-3 seconds to execution
- OCR processing requires good image quality
- System falls back to normal process if analysis fails
- Screenshots are taken only when needed

## Future Enhancements

Planned improvements:
- Template matching for map pieces
- Machine learning for number recognition
- Dynamic coordinate detection
- Multi-resolution support
- Trade history tracking
