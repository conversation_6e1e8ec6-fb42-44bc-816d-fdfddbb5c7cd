#!/usr/bin/env python3
"""
Unified Configuration Helper for Last War Automation System
- Module-specific configuration editing
- Real-time template and coordinate testing
- Unified configuration management
- Live validation and testing
- Integration with main automation system
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import pyautogui
import cv2
import numpy as np
import pytesseract
import os
from datetime import datetime
import threading
import time
from PIL import Image, ImageTk
import keyboard
import json
from unified_config_manager import UnifiedConfigManager

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Create necessary folders
DEBUG_FOLDER = "debug_screenshots"
TEMPLATE_FOLDER = "templates"
COORDS_FOLDER = "coordinates"

# Ensure folders exist
for folder in [DEBUG_FOLDER, TEMPLATE_FOLDER, COORDS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)
COORDS_FOLDER = "debug_coords"

for folder in [DEBUG_FOLDER, TEMPLATE_FOLDER, COORDS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

class ConfigHelper:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Last War Automation - Configuration Helper")

        # Store window dimensions for later use
        self.window_width = 1400
        self.window_height = 900

        # Configure window to always stay on top and appear in foreground
        self.root.attributes('-topmost', True)

        # Set initial geometry and position
        self._setup_window_positioning()

        # Ensure window appears immediately and stays focused
        self.root.after(50, self._ensure_foreground)

        # Initialize unified configuration manager
        self.config_manager = UnifiedConfigManager()
        self.config_data = self.config_manager.load_config()

        # Initialize AI OCR service
        self._initialize_ai_ocr_service()

        # Current module being edited
        self.current_module = None
        self.current_template = None
        self.current_coordinate = None

        # UI state
        self.live_tracking = False
        self.testing_mode = False

        # Advanced debugging state (from original debugger)
        self.capture_size = tk.IntVar(value=25)
        self.detection_method = tk.StringVar(value="OCR")
        self.selected_map = tk.StringVar(value="M1")
        self.use_scaling = tk.BooleanVar(value=False)
        self.scale_factor = tk.DoubleVar(value=2.0)
        self.display_zoom = tk.DoubleVar(value=1.0)
        self.invert_colors = tk.BooleanVar(value=False)
        self.ocr_mode = tk.StringVar(value="Standard")
        self.morphology_enabled = tk.BooleanVar(value=False)
        self.morphology_operation = tk.StringVar(value="Erode")
        self.morphology_strength = tk.IntVar(value=1)

        # AI OCR Configuration Variables
        self.ai_ocr_enabled = tk.BooleanVar(value=True)
        self.ai_ocr_confidence_threshold = tk.DoubleVar(value=0.9)
        self.ai_ocr_backend = tk.StringVar(value="Auto")
        self.ai_ocr_white_text_preprocessing = tk.BooleanVar(value=True)
        self.ai_ocr_debug_images = tk.BooleanVar(value=True)
        self.ai_ocr_detailed_logging = tk.BooleanVar(value=True)

        # Template creation state
        self.template_size = tk.IntVar(value=50)
        self.template_capture_mode = False
        self.live_template_preview = False
        self.template_preview_thread = None
        self.captured_image = None

        # Mouse tracking state
        self.capturing_mouse = False
        self.live_mouse_tracking = False
        self.mouse_thread = None
        self.current_mouse_x = tk.StringVar(value="0")
        self.current_mouse_y = tk.StringVar(value="0")
        self.saved_coords = tk.StringVar(value="Click 'Save Coord' to capture")

        # Results storage
        self.last_results = {}
        self.current_images = {}

        # Live scanning state
        self.live_scanning = False
        self.live_thread = None

        # Coordinate management from original debugger
        self.coordinates = {
            'M1': [1079, 833],
            'M2': [1245, 835],
            'M3': [1412, 835],
            'M4': [1577, 833],
            'M5': [1163, 994],
            'M6': [1329, 994],
            'M7': [1495, 994]
        }

        # Initialize UI
        self.setup_ui()

        # Setup keyboard listener for ENTER key
        self.setup_keyboard_listener()

        # Load saved coordinates
        self.load_saved_coordinates()

        # Setup the user interface
        self.setup_ui()

        # Load AI OCR configuration after UI is created
        self._load_ai_ocr_config()

        # Focus game window if available
        self.focus_game_window()

    def setup_ui(self):
        """Setup the main user interface"""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs
        self.create_module_config_tab()
        self.create_detection_testing_tab()
        self.create_template_testing_tab()
        self.create_coordinate_capture_tab()
        self.create_template_creator_tab()
        self.create_system_settings_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Select a module to configure")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_module_config_tab(self):
        """Create the module configuration tab"""
        self.module_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.module_frame, text="Module Configuration")

        # Left panel - Module selection
        left_panel = ttk.Frame(self.module_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        ttk.Label(left_panel, text="Modules", font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        # Module listbox
        self.module_listbox = tk.Listbox(left_panel, width=20, height=15)
        self.module_listbox.pack(fill=tk.Y, expand=True)
        self.module_listbox.bind('<<ListboxSelect>>', self.on_module_select)

        # Populate module list
        for module_name in self.config_data.get('modules', {}):
            self.module_listbox.insert(tk.END, module_name)

        # Module control buttons
        button_frame = ttk.Frame(left_panel)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="Add Module", command=self.add_module).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Delete Module", command=self.delete_module).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Save Config", command=self.save_config).pack(fill=tk.X, pady=(0, 5))

        # Right panel - Module details
        self.right_panel = ttk.Frame(self.module_frame)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Module details notebook
        self.module_notebook = ttk.Notebook(self.right_panel)
        self.module_notebook.pack(fill=tk.BOTH, expand=True)

        # Create module detail tabs (will be populated when module is selected)
        self.create_module_detail_tabs()

    def create_module_detail_tabs(self):
        """Create tabs for module details"""
        # Basic Settings tab
        self.basic_settings_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.basic_settings_frame, text="Basic Settings")

        # Templates tab
        self.templates_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.templates_frame, text="Templates")

        # Coordinates tab
        self.coordinates_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.coordinates_frame, text="Coordinates")

        # Scan Regions tab
        self.scan_regions_frame = ttk.Frame(self.module_notebook)
        self.module_notebook.add(self.scan_regions_frame, text="Scan Regions")

    def create_detection_testing_tab(self):
        """Create the advanced detection testing tab (from original debugger)"""
        self.detection_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detection_frame, text="Detection Testing")

        # Main frame
        main_frame = ttk.Frame(self.detection_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configure grid weights for proper layout
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.columnconfigure(3, weight=1)
        main_frame.columnconfigure(4, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Detection Settings - MAIN TAB", padding="10")
        settings_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Capture size
        ttk.Label(settings_frame, text="Capture Size:").grid(row=0, column=0, sticky=tk.W)
        size_frame = ttk.Frame(settings_frame)
        size_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        for size in [15, 20, 25, 30, 35, 40, 50]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.capture_size,
                           value=size).pack(side=tk.LEFT, padx=(0, 5))

        # Detection method
        ttk.Label(settings_frame, text="Method:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        method_frame = ttk.Frame(settings_frame)
        method_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(method_frame, text="OCR", variable=self.detection_method,
                       value="OCR").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Pixel", variable=self.detection_method,
                       value="Pixel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Both", variable=self.detection_method,
                       value="Both").pack(side=tk.LEFT)

        # Processing options
        ttk.Label(settings_frame, text="Processing:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        processing_frame = ttk.Frame(settings_frame)
        processing_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(processing_frame, text="Scale for OCR", variable=self.use_scaling).pack(side=tk.LEFT)
        ttk.Label(processing_frame, text="Factor:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=5.0, increment=0.5, width=6,
                   textvariable=self.scale_factor).pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(processing_frame, text="Display Zoom:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=10.0, increment=1.0, width=6,
                   textvariable=self.display_zoom).pack(side=tk.LEFT)

        # Color inversion option
        ttk.Label(settings_frame, text="Colors:").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        color_frame = ttk.Frame(settings_frame)
        color_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(color_frame, text="Invert White to Black",
                       variable=self.invert_colors).pack(side=tk.LEFT)

        # OCR fine-tuning options
        ttk.Label(settings_frame, text="OCR Mode:").grid(row=4, column=0, sticky=tk.W, pady=(10, 0))
        ocr_frame = ttk.Frame(settings_frame)
        ocr_frame.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(ocr_frame, text="Standard", variable=self.ocr_mode,
                       value="Standard").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thick/Bold", variable=self.ocr_mode,
                       value="Thick").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thin/Light", variable=self.ocr_mode,
                       value="Thin").pack(side=tk.LEFT)

        # Morphology options for thick text
        ttk.Label(settings_frame, text="Morphology:").grid(row=5, column=0, sticky=tk.W, pady=(10, 0))
        morph_frame = ttk.Frame(settings_frame)
        morph_frame.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(morph_frame, text="Enable",
                       variable=self.morphology_enabled).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Radiobutton(morph_frame, text="Erode", variable=self.morphology_operation,
                       value="Erode").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Dilate", variable=self.morphology_operation,
                       value="Dilate").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Open", variable=self.morphology_operation,
                       value="Open").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(morph_frame, text="Strength:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(morph_frame, from_=1, to=5, width=4,
                   textvariable=self.morphology_strength).pack(side=tk.LEFT)

        # TEMPORARILY REMOVED AI OCR CONFIGURATION TO TEST DUPLICATE ISSUE

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # First row of buttons
        button_row1 = ttk.Frame(control_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_row1, text="Focus Game", command=self.focus_game_window,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="SCAN ALL", command=self.scan_all,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="Live Scan", command=self.toggle_live_scan,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="Save Results", command=self.save_results,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))

        # Second row of buttons
        button_row2 = ttk.Frame(control_frame)
        button_row2.pack(fill=tk.X)

        ttk.Button(button_row2, text="Load Coords", command=self.load_coordinates_file,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row2, text="Save Coords", command=self.save_coordinates_file,
                  width=12).pack(side=tk.LEFT)

        # Coordinates frame
        coord_frame = ttk.LabelFrame(main_frame, text="Coordinates", padding="10")
        coord_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Map selection
        ttk.Label(coord_frame, text="Selected:").grid(row=0, column=0, sticky=tk.W)
        map_combo = ttk.Combobox(coord_frame, textvariable=self.selected_map,
                                values=list(self.coordinates.keys()), width=10)
        map_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        map_combo.bind('<<ComboboxSelected>>', self.on_map_selected)

        # Add new coordinate
        ttk.Button(coord_frame, text="Add New", command=self.add_new_coordinate,
                  width=10).grid(row=0, column=2, padx=(10, 0))

        # Coordinate adjustment
        ttk.Label(coord_frame, text="X:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.x_var = tk.IntVar(value=self.coordinates['M1'][0])
        x_spin = ttk.Spinbox(coord_frame, from_=0, to=3000, textvariable=self.x_var, width=10)
        x_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Label(coord_frame, text="Y:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.y_var = tk.IntVar(value=self.coordinates['M1'][1])
        y_spin = ttk.Spinbox(coord_frame, from_=0, to=2000, textvariable=self.y_var, width=10)
        y_spin.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # Update coordinate button
        ttk.Button(coord_frame, text="Update Coord",
                  command=self.update_coordinate).grid(row=3, column=0, columnspan=2, pady=(10, 0))

        # Capture current position button
        ttk.Button(coord_frame, text="Capture Mouse (ENTER)",
                  command=self.start_mouse_capture).grid(row=4, column=0, columnspan=2, pady=(5, 0))

        # Status label for mouse capture
        self.capture_status = ttk.Label(coord_frame, text="", foreground="blue")
        self.capture_status.grid(row=5, column=0, columnspan=2, pady=(5, 0))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Image frame
        image_frame = ttk.LabelFrame(main_frame, text="Captured Image", padding="10")
        image_frame.grid(row=3, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # Results text area
        self.results_text_detection = tk.Text(results_frame, width=30, height=20, font=('Consolas', 9))
        scrollbar_detection = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text_detection.yview)
        self.results_text_detection.configure(yscrollcommand=scrollbar_detection.set)

        self.results_text_detection.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_detection.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Image display area
        self.image_label = ttk.Label(image_frame, text="No image captured", anchor="center")
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Image info
        self.image_info = ttk.Label(image_frame, text="", font=('Consolas', 8))
        self.image_info.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # Image buttons
        img_button_frame = ttk.Frame(image_frame)
        img_button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(img_button_frame, text="Save as Template",
                  command=self.save_as_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(img_button_frame, text="Save Debug",
                  command=self.save_debug_image).pack(side=tk.LEFT)

        # Configure grid weights for coordinate frame
        coord_frame.rowconfigure(6, weight=1)

    def create_template_testing_tab(self):
        """Create the template testing tab with visual template preview"""
        self.template_test_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.template_test_frame, text="Template Testing")

        # Template testing controls
        control_frame = ttk.Frame(self.template_test_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(control_frame, text="Template Testing", font=('Arial', 12, 'bold')).pack()

        # Template selection with preview
        selection_frame = ttk.Frame(control_frame)
        selection_frame.pack(fill=tk.X, pady=(10, 0))

        # Left side - Template selection
        left_selection = ttk.Frame(selection_frame)
        left_selection.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        ttk.Label(left_selection, text="Select Template:").pack(anchor=tk.W)
        self.template_var = tk.StringVar()
        self.template_combo = ttk.Combobox(left_selection, textvariable=self.template_var, state="readonly")
        self.template_combo.pack(fill=tk.X, pady=(0, 10))

        # Bind template selection change to update preview
        self.template_combo.bind('<<ComboboxSelected>>', self.on_template_selection_changed)

        # Right side - Template preview
        preview_frame = ttk.LabelFrame(selection_frame, text="Template Preview", padding=10)
        preview_frame.pack(side=tk.RIGHT, padx=(10, 0))

        # Template preview label
        self.template_preview_label = tk.Label(preview_frame, text="No template selected",
                                             width=20, height=10, relief=tk.SUNKEN, bg='white')
        self.template_preview_label.pack()

        # Template info
        self.template_info_label = tk.Label(preview_frame, text="", font=('Arial', 8), fg='gray')
        self.template_info_label.pack(pady=(5, 0))

        # Test controls
        test_controls = ttk.Frame(control_frame)
        test_controls.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(test_controls, text="Focus Game", command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_controls, text="Test Template", command=self.test_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_controls, text="Capture Screenshot", command=self.capture_screenshot).pack(side=tk.LEFT, padx=(0, 10))

        # Threshold control
        threshold_frame = ttk.Frame(test_controls)
        threshold_frame.pack(side=tk.LEFT, padx=(20, 0))

        ttk.Label(threshold_frame, text="Threshold:").pack(side=tk.LEFT)
        self.template_threshold_var = tk.DoubleVar(value=0.8)
        threshold_spinbox = ttk.Spinbox(threshold_frame, from_=0.1, to=1.0, increment=0.05,
                                       textvariable=self.template_threshold_var, width=8)
        threshold_spinbox.pack(side=tk.LEFT, padx=(5, 0))

        # Results area
        results_frame = ttk.Frame(self.template_test_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        ttk.Label(results_frame, text="Test Results", font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Initialize template preview cache
        self.template_preview_cache = {}

    def _setup_window_positioning(self):
        """Setup window size and strategic positioning"""
        try:
            # Force window to update and get accurate screen dimensions
            self.root.update_idletasks()

            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # Position window in center-right of screen to avoid completely obscuring game
            # This assumes game is typically on the left side or center-left
            pos_x = int(screen_width * 0.3)  # 30% from left edge
            pos_y = int((screen_height - self.window_height) / 2)  # Vertically centered

            # Ensure window fits on screen
            if pos_x + self.window_width > screen_width:
                pos_x = screen_width - self.window_width - 50  # 50px margin
            if pos_y + self.window_height > screen_height:
                pos_y = screen_height - self.window_height - 50  # 50px margin

            # Ensure minimum position (not off-screen)
            pos_x = max(0, pos_x)
            pos_y = max(0, pos_y)

            # Set geometry
            geometry_string = f"{self.window_width}x{self.window_height}+{pos_x}+{pos_y}"
            self.root.geometry(geometry_string)

            # Force update to apply geometry
            self.root.update_idletasks()

        except Exception as e:
            print(f"Warning: Could not setup window positioning: {e}")
            # Fallback to default geometry
            self.root.geometry(f"{self.window_width}x{self.window_height}")

    def _ensure_foreground(self):
        """Ensure the Config Helper window appears in the foreground and stays on top"""
        try:
            # Multiple attempts to bring window to foreground
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.focus_force()

            # Brief flash to make window visible if it's hidden
            self.root.deiconify()

            # On Windows, additional methods to ensure foreground
            try:
                import ctypes

                # Get window handle
                hwnd = int(self.root.wm_frame(), 16) if hasattr(self.root, 'wm_frame') else None
                if not hwnd:
                    # Alternative method to get window handle
                    hwnd = ctypes.windll.user32.FindWindowW(None, self.root.title())

                if hwnd:
                    # Force window to foreground using Windows API
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    ctypes.windll.user32.BringWindowToTop(hwnd)
                    ctypes.windll.user32.ShowWindow(hwnd, 9)  # SW_RESTORE

            except (ImportError, AttributeError, OSError):
                # If Windows-specific methods fail, continue with Tkinter methods
                pass

            # Schedule another check to ensure window stays visible
            self.root.after(500, self._maintain_topmost)

        except Exception as e:
            print(f"Warning: Could not ensure foreground display: {e}")

    def _maintain_topmost(self):
        """Maintain topmost status for a short period to ensure visibility"""
        try:
            # Keep topmost for initial period, then allow normal behavior
            if hasattr(self, '_topmost_counter'):
                self._topmost_counter += 1
            else:
                self._topmost_counter = 1

            # Stay topmost for first 3 seconds, then allow normal window behavior
            if self._topmost_counter < 6:  # 6 * 500ms = 3 seconds
                self.root.attributes('-topmost', True)
                self.root.after(500, self._maintain_topmost)
            else:
                # After initial period, allow user to move window behind others if needed
                # but keep it easily accessible
                self.root.attributes('-topmost', False)

        except Exception as e:
            print(f"Warning: Could not maintain topmost status: {e}")

    def create_coordinate_capture_tab(self):
        """Create the coordinate capture tab"""
        self.coord_capture_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.coord_capture_frame, text="Coordinate Capture")

        # Live tracking controls
        control_frame = ttk.Frame(self.coord_capture_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(control_frame, text="Live Mouse Tracking", font=('Arial', 12, 'bold')).pack()

        # Tracking controls
        tracking_frame = ttk.Frame(control_frame)
        tracking_frame.pack(fill=tk.X, pady=10)

        ttk.Button(tracking_frame, text="Focus Game",
                  command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Start Live Tracking",
                  command=self.start_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Stop Tracking",
                  command=self.stop_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(tracking_frame, text="Save Current Coord",
                  command=self.save_current_mouse_coord).pack(side=tk.LEFT)

        # Live coordinates display
        coord_display_frame = ttk.LabelFrame(control_frame, text="Live Mouse Position", padding="10")
        coord_display_frame.pack(fill=tk.X, pady=(10, 0))

        # Current position
        pos_frame = ttk.Frame(coord_display_frame)
        pos_frame.pack(fill=tk.X)

        ttk.Label(pos_frame, text="X:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_x,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(pos_frame, text="Y:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_y,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 0))

        # Saved coordinates
        saved_frame = ttk.LabelFrame(self.coord_capture_frame, text="Saved Coordinates", padding="10")
        saved_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Coordinate input/display
        input_frame = ttk.Frame(saved_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="Coordinates:").pack(side=tk.LEFT)
        self.coord_entry = ttk.Entry(input_frame, textvariable=self.saved_coords, width=30)
        self.coord_entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        ttk.Button(input_frame, text="Copy",
                  command=self.copy_coordinates).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(input_frame, text="Clear",
                  command=self.clear_coordinates).pack(side=tk.LEFT)

        # Coordinate history
        history_frame = ttk.Frame(saved_frame)
        history_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(history_frame, text="Coordinate History:").pack(anchor=tk.W)

        self.coord_history = tk.Listbox(history_frame, height=15, font=('Consolas', 10))
        coord_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.coord_history.yview)
        self.coord_history.configure(yscrollcommand=coord_scrollbar.set)

        self.coord_history.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coord_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # History buttons
        hist_button_frame = ttk.Frame(saved_frame)
        hist_button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(hist_button_frame, text="Use Selected",
                  command=self.use_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Delete Selected",
                  command=self.delete_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Save History",
                  command=self.save_coord_history).pack(side=tk.LEFT)

    def create_template_creator_tab(self):
        """Create the template creation tab with live preview"""
        self.template_creator_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.template_creator_frame, text="Template Creator")

        main_frame = ttk.Frame(self.template_creator_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Capture controls
        capture_frame = ttk.LabelFrame(main_frame, text="Screen Capture", padding="10")
        capture_frame.pack(fill=tk.X, pady=(0, 10))

        # Capture size options
        size_frame = ttk.Frame(capture_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="Capture Size:").pack(side=tk.LEFT)

        for size in [25, 50, 75, 100, 150, 200]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.template_size,
                           value=size).pack(side=tk.LEFT, padx=(10, 5))

        # Capture buttons
        button_frame = ttk.Frame(capture_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Focus Game",
                  command=self.focus_game_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Capture at Mouse",
                  command=self.capture_at_mouse).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Capture at Coordinate",
                  command=self.capture_at_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Full Screen Capture",
                  command=self.capture_full_screen).pack(side=tk.LEFT, padx=(0, 10))

        # Template capture mode toggle
        mode_frame = ttk.Frame(capture_frame)
        mode_frame.pack(fill=tk.X, pady=(10, 0))

        self.template_mode_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(mode_frame, text="Live Capture Mode (Press ENTER at mouse position)",
                       variable=self.template_mode_var,
                       command=self.toggle_template_capture_mode).pack(side=tk.LEFT)

        self.template_mode_status = ttk.Label(mode_frame, text="", foreground="blue")
        self.template_mode_status.pack(side=tk.LEFT, padx=(10, 0))

        # Live preview toggle
        preview_mode_frame = ttk.Frame(capture_frame)
        preview_mode_frame.pack(fill=tk.X, pady=(5, 0))

        self.live_preview_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preview_mode_frame, text="Live Preview Mode (Show what's under mouse)",
                       variable=self.live_preview_var,
                       command=self.toggle_live_template_preview).pack(side=tk.LEFT)

        self.live_preview_status = ttk.Label(preview_mode_frame, text="", foreground="green")
        self.live_preview_status.pack(side=tk.LEFT, padx=(10, 0))

        # Template preview and save
        template_frame = ttk.LabelFrame(main_frame, text="Template Preview", padding="10")
        template_frame.pack(fill=tk.BOTH, expand=True)

        # Preview area
        preview_frame = ttk.Frame(template_frame)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.template_preview = ttk.Label(preview_frame, text="No template captured",
                                         anchor="center", relief="sunken")
        self.template_preview.pack(fill=tk.BOTH, expand=True)

        # Template info
        self.template_info = ttk.Label(template_frame, text="", font=('Consolas', 9))
        self.template_info.pack(fill=tk.X, pady=(0, 10))

        # Save controls
        save_frame = ttk.Frame(template_frame)
        save_frame.pack(fill=tk.X)

        ttk.Label(save_frame, text="Template Name:").pack(side=tk.LEFT)
        self.template_name = tk.StringVar(value="new_template")
        ttk.Entry(save_frame, textvariable=self.template_name, width=20).pack(side=tk.LEFT, padx=(10, 10))

        ttk.Button(save_frame, text="Save to Templates",
                  command=self.save_template_to_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(save_frame, text="Test Template",
                  command=self.test_template_detection).pack(side=tk.LEFT)

    def create_system_settings_tab(self):
        """Create the system settings tab"""
        self.system_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.system_frame, text="System Settings")

        # System settings form
        settings_frame = ttk.LabelFrame(self.system_frame, text="System Configuration", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # Get current system settings
        system_settings = self.config_data.get('system_settings', {})

        # Scan interval
        ttk.Label(settings_frame, text="Scan Interval (seconds):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.scan_interval_var = tk.DoubleVar(value=system_settings.get('scan_interval', 0.1))
        scan_interval_spin = ttk.Spinbox(settings_frame, from_=0.01, to=5.0, increment=0.01,
                                       textvariable=self.scan_interval_var, width=10)
        scan_interval_spin.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Templates path
        ttk.Label(settings_frame, text="Templates Path:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.templates_path_var = tk.StringVar(value=system_settings.get('templates_path', 'templates/'))
        ttk.Entry(settings_frame, textvariable=self.templates_path_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Theme
        ttk.Label(settings_frame, text="Theme:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.theme_var = tk.StringVar(value=system_settings.get('theme', 'dark'))
        theme_combo = ttk.Combobox(settings_frame, textvariable=self.theme_var, values=['light', 'dark'], state="readonly", width=10)
        theme_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Save button
        ttk.Button(settings_frame, text="Save System Settings", command=self.save_system_settings).grid(row=3, column=0, columnspan=2, pady=20)

    def on_module_select(self, event):
        """Handle module selection"""
        selection = self.module_listbox.curselection()
        if selection:
            module_name = self.module_listbox.get(selection[0])
            self.current_module = module_name
            self.load_module_details(module_name)
            self.status_var.set(f"Editing module: {module_name}")

    def load_module_details(self, module_name):
        """Load module details into the detail tabs"""
        module_config = self.config_data['modules'].get(module_name, {})

        # Clear existing content
        for widget in self.basic_settings_frame.winfo_children():
            widget.destroy()
        for widget in self.templates_frame.winfo_children():
            widget.destroy()
        for widget in self.coordinates_frame.winfo_children():
            widget.destroy()
        for widget in self.scan_regions_frame.winfo_children():
            widget.destroy()

        # Load basic settings
        self.load_basic_settings(module_config)

        # Load templates
        self.load_templates(module_config.get('templates', {}))

        # Load coordinates
        self.load_coordinates(module_config.get('coordinates', {}))

        # Load scan regions
        self.load_scan_regions(module_config.get('scan_regions', {}))

        # Update template combo for testing
        self.update_template_combo(module_config.get('templates', {}))

    def load_basic_settings(self, module_config):
        """Load basic module settings"""
        settings_frame = ttk.LabelFrame(self.basic_settings_frame, text="Module Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # Enabled checkbox
        self.module_enabled_var = tk.BooleanVar(value=module_config.get('enabled', True))
        ttk.Checkbutton(settings_frame, text="Module Enabled", variable=self.module_enabled_var).grid(row=0, column=0, sticky=tk.W, pady=5)

        # Priority
        ttk.Label(settings_frame, text="Priority:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.module_priority_var = tk.IntVar(value=module_config.get('priority', 0))
        ttk.Spinbox(settings_frame, from_=-10, to=10, textvariable=self.module_priority_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Cooldown
        ttk.Label(settings_frame, text="Cooldown (seconds):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.module_cooldown_var = tk.DoubleVar(value=module_config.get('cooldown', 0.0))
        ttk.Spinbox(settings_frame, from_=0.0, to=3600.0, increment=1.0, textvariable=self.module_cooldown_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Manual trigger
        self.module_manual_var = tk.BooleanVar(value=module_config.get('manual_trigger', False))
        ttk.Checkbutton(settings_frame, text="Manual Trigger Only", variable=self.module_manual_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

        # Save button
        ttk.Button(settings_frame, text="Save Module Settings", command=self.save_module_settings).grid(row=4, column=0, columnspan=2, pady=20)

    def load_templates(self, templates):
        """Load template configuration"""
        templates_frame = ttk.LabelFrame(self.templates_frame, text="Templates", padding=10)
        templates_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Templates treeview
        columns = ('Name', 'Threshold', 'Required', 'Scanner Priority', 'Scanner Enabled', 'Description')
        self.templates_tree = ttk.Treeview(templates_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=100)

        # Populate templates
        for template_name, template_config in templates.items():
            values = (
                template_name,
                template_config.get('threshold', 0.8),
                'Yes' if template_config.get('required', False) else 'No',
                template_config.get('scanner_priority', 0),
                'Yes' if template_config.get('scanner_enabled', False) else 'No',
                template_config.get('description', '')
            )
            self.templates_tree.insert('', tk.END, values=values)

        templates_scrollbar = ttk.Scrollbar(templates_frame, orient=tk.VERTICAL, command=self.templates_tree.yview)
        self.templates_tree.configure(yscrollcommand=templates_scrollbar.set)

        self.templates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        templates_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Template control buttons
        template_buttons = ttk.Frame(templates_frame)
        template_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(template_buttons, text="Add Template", command=self.add_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Edit Template", command=self.edit_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Delete Template", command=self.delete_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_buttons, text="Test Template", command=self.test_selected_template).pack(side=tk.LEFT, padx=(0, 10))

    def load_coordinates(self, coordinates):
        """Load coordinate configuration"""
        coords_frame = ttk.LabelFrame(self.coordinates_frame, text="Coordinates", padding=10)
        coords_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Coordinates treeview
        columns = ('Name', 'X', 'Y', 'Delay', 'Repeat', 'ESC Key', 'Description')
        self.coords_tree_config = ttk.Treeview(coords_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.coords_tree_config.heading(col, text=col)
            self.coords_tree_config.column(col, width=80)

        # Populate coordinates
        for coord_name, coord_config in coordinates.items():
            values = (
                coord_name,
                coord_config.get('x', 0),
                coord_config.get('y', 0),
                coord_config.get('delay', 1.0),
                coord_config.get('repeat', 1),
                'Yes' if coord_config.get('use_esc_key', False) else 'No',
                coord_config.get('description', '')
            )
            self.coords_tree_config.insert('', tk.END, values=values)

        coords_scrollbar_config = ttk.Scrollbar(coords_frame, orient=tk.VERTICAL, command=self.coords_tree_config.yview)
        self.coords_tree_config.configure(yscrollcommand=coords_scrollbar_config.set)

        self.coords_tree_config.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coords_scrollbar_config.pack(side=tk.RIGHT, fill=tk.Y)

        # Coordinate control buttons
        coord_buttons = ttk.Frame(coords_frame)
        coord_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(coord_buttons, text="Add Coordinate", command=self.add_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Edit Coordinate", command=self.edit_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Delete Coordinate", command=self.delete_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(coord_buttons, text="Capture Live", command=self.capture_coordinate_live).pack(side=tk.LEFT, padx=(0, 10))

    def load_scan_regions(self, scan_regions):
        """Load scan regions configuration"""
        regions_frame = ttk.LabelFrame(self.scan_regions_frame, text="Scan Regions", padding=10)
        regions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Scan regions treeview
        columns = ('Name', 'X', 'Y', 'Width', 'Height', 'Description')
        self.regions_tree = ttk.Treeview(regions_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.regions_tree.heading(col, text=col)
            self.regions_tree.column(col, width=80)

        # Populate scan regions
        for region_name, region_config in scan_regions.items():
            values = (
                region_name,
                region_config.get('x', 0),
                region_config.get('y', 0),
                region_config.get('width', 100),
                region_config.get('height', 100),
                region_config.get('description', '')
            )
            self.regions_tree.insert('', tk.END, values=values)

        regions_scrollbar = ttk.Scrollbar(regions_frame, orient=tk.VERTICAL, command=self.regions_tree.yview)
        self.regions_tree.configure(yscrollcommand=regions_scrollbar.set)

        self.regions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        regions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Region control buttons
        region_buttons = ttk.Frame(regions_frame)
        region_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(region_buttons, text="Add Region", command=self.add_scan_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(region_buttons, text="Edit Region", command=self.edit_scan_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(region_buttons, text="Delete Region", command=self.delete_scan_region).pack(side=tk.LEFT, padx=(0, 10))

    def update_template_combo(self, templates):
        """Update template combo box for testing"""
        template_names = list(templates.keys())
        self.template_combo['values'] = template_names
        if template_names:
            self.template_combo.set(template_names[0])
            # Update preview for the first template
            self.update_template_preview(template_names[0])
        else:
            # Clear preview if no templates
            self.clear_template_preview()

    def on_template_selection_changed(self, event=None):
        """Handle template selection change in combo box"""
        selected_template = self.template_var.get()
        if selected_template:
            self.update_template_preview(selected_template)

    def load_template_preview(self, template_path, max_size=(150, 150)):
        """Load and cache template preview image with modification time checking"""
        try:
            # Check if file exists
            if not os.path.exists(template_path):
                return None

            # Get file modification time
            file_mtime = os.path.getmtime(template_path)
            cache_key = f"{template_path}_{file_mtime}"

            # Check if we have a cached version with the same modification time
            if cache_key in self.template_preview_cache:
                return self.template_preview_cache[cache_key]

            # Remove old cached versions of this file
            old_keys = [k for k in self.template_preview_cache.keys() if k.startswith(template_path)]
            for old_key in old_keys:
                del self.template_preview_cache[old_key]

            # Load fresh image
            with Image.open(template_path) as img:
                # Force load to ensure we get current file data
                img.load()
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                self.template_preview_cache[cache_key] = photo
                return photo

        except Exception as e:
            print(f"Failed to load template preview {template_path}: {e}")
            return None

    def update_template_preview(self, template_name):
        """Update the template preview image"""
        if not template_name:
            self.clear_template_preview()
            return

        # Find template file path
        template_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}.png")

        # Try different extensions if .png doesn't exist
        if not os.path.exists(template_path):
            for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                alt_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}{ext}")
                if os.path.exists(alt_path):
                    template_path = alt_path
                    break

        if not os.path.exists(template_path):
            self.template_preview_label.config(image='', text="Template file not found")
            self.template_info_label.config(text=f"File: {template_name} (not found)")
            return

        # Load and display preview
        try:
            preview_image = self.load_template_preview(template_path, (150, 150))
            if preview_image:
                self.template_preview_label.config(image=preview_image, text="")
                # Keep a reference to prevent garbage collection
                self.template_preview_label.image = preview_image

                # Update info label with file details
                file_size = os.path.getsize(template_path)
                with Image.open(template_path) as img:
                    width, height = img.size
                self.template_info_label.config(text=f"Size: {width}x{height} ({file_size} bytes)")
            else:
                self.template_preview_label.config(image='', text="Failed to load preview")
                self.template_info_label.config(text="Preview error")
        except Exception as e:
            print(f"Failed to update template preview: {e}")
            self.template_preview_label.config(image='', text="Preview error")
            self.template_info_label.config(text="Error loading preview")

    def clear_template_preview(self):
        """Clear the template preview"""
        self.template_preview_label.config(image='', text="No template selected")
        self.template_info_label.config(text="")
        # Clear the image reference
        if hasattr(self.template_preview_label, 'image'):
            self.template_preview_label.image = None

    # Event handlers
    def add_module(self):
        """Add a new module"""
        module_name = simpledialog.askstring("Add Module", "Enter module name:")
        if module_name and module_name not in self.config_data['modules']:
            # Create default module structure
            default_module = {
                'enabled': True,
                'priority': 0,
                'cooldown': 0.0,
                'manual_trigger': False,
                'settings': {},
                'templates': {},
                'coordinates': {},
                'scan_regions': {}
            }

            self.config_data['modules'][module_name] = default_module
            self.module_listbox.insert(tk.END, module_name)
            self.status_var.set(f"Added module: {module_name}")

    def delete_module(self):
        """Delete selected module"""
        selection = self.module_listbox.curselection()
        if selection:
            module_name = self.module_listbox.get(selection[0])
            if messagebox.askyesno("Delete Module", f"Are you sure you want to delete module '{module_name}'?"):
                del self.config_data['modules'][module_name]
                self.module_listbox.delete(selection[0])
                self.status_var.set(f"Deleted module: {module_name}")

    def save_config(self):
        """Save the unified configuration"""
        try:
            self.config_manager.config_data = self.config_data
            self.config_manager.save_config()
            self.status_var.set("Configuration saved successfully")
            messagebox.showinfo("Success", "Configuration saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def save_module_settings(self):
        """Save current module settings"""
        if not self.current_module:
            return

        module_config = self.config_data['modules'][self.current_module]
        module_config['enabled'] = self.module_enabled_var.get()
        module_config['priority'] = self.module_priority_var.get()
        module_config['cooldown'] = self.module_cooldown_var.get()
        module_config['manual_trigger'] = self.module_manual_var.get()

        self.status_var.set(f"Saved settings for module: {self.current_module}")

    def save_system_settings(self):
        """Save system settings"""
        system_settings = self.config_data.setdefault('system_settings', {})
        system_settings['scan_interval'] = self.scan_interval_var.get()
        system_settings['templates_path'] = self.templates_path_var.get()
        system_settings['theme'] = self.theme_var.get()

        self.status_var.set("System settings saved")

    # Template management methods
    def add_template(self):
        """Add a new template"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        template_name = simpledialog.askstring("Add Template", "Enter template name:")
        if template_name:
            default_template = {
                'threshold': 0.8,
                'required': False,
                'description': '',
                'scanner_priority': 0,
                'scanner_enabled': False,
                'scanner_action': 'default'
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('templates', {})[template_name] = default_template

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Added template: {template_name}")

    def edit_template(self):
        """Edit selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to edit")
            return

        # Get selected template
        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        # Open template edit dialog
        self.open_template_edit_dialog(template_name)

    def delete_template(self):
        """Delete selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]

        if messagebox.askyesno("Delete Template", f"Are you sure you want to delete template '{template_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['templates'][template_name]

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Deleted template: {template_name}")

    def test_selected_template(self):
        """Test the selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]
        self.test_template_by_name(template_name)

    def open_template_edit_dialog(self, template_name):
        """Open template editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        template_config = module_config['templates'][template_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Template: {template_name}")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Template settings
        ttk.Label(dialog, text="Threshold:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        threshold_var = tk.DoubleVar(value=template_config.get('threshold', 0.8))
        ttk.Scale(dialog, from_=0.1, to=1.0, variable=threshold_var, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        ttk.Label(dialog, text="Required:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        required_var = tk.BooleanVar(value=template_config.get('required', False))
        ttk.Checkbutton(dialog, variable=required_var).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Scanner Priority:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        priority_var = tk.IntVar(value=template_config.get('scanner_priority', 0))
        ttk.Spinbox(dialog, from_=0, to=20, textvariable=priority_var).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Scanner Enabled:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        scanner_enabled_var = tk.BooleanVar(value=template_config.get('scanner_enabled', False))
        ttk.Checkbutton(dialog, variable=scanner_enabled_var).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Description:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        description_var = tk.StringVar(value=template_config.get('description', ''))
        ttk.Entry(dialog, textvariable=description_var, width=30).grid(row=4, column=1, sticky=tk.EW, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_template():
            template_config['threshold'] = threshold_var.get()
            template_config['required'] = required_var.get()
            template_config['scanner_priority'] = priority_var.get()
            template_config['scanner_enabled'] = scanner_enabled_var.get()
            template_config['description'] = description_var.get()

            # Refresh the templates display
            self.load_templates(module_config['templates'])
            self.status_var.set(f"Updated template: {template_name}")
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        dialog.columnconfigure(1, weight=1)

    def setup_keyboard_listener(self):
        """Setup keyboard listener for ENTER key"""
        def on_enter_pressed(_):
            if self.capturing_mouse:
                self.capture_mouse_position()
            elif self.live_mouse_tracking:
                self.save_current_mouse_coord()
            elif self.template_capture_mode:
                self.capture_template_at_mouse()

        keyboard.on_press_key('enter', on_enter_pressed)

    def load_saved_coordinates(self):
        """Load saved coordinates from file"""
        try:
            coord_file = os.path.join(COORDS_FOLDER, "saved_coordinates.json")
            if os.path.exists(coord_file):
                with open(coord_file, 'r') as f:
                    saved_coords = json.load(f)
                    self.coordinates.update(saved_coords)
        except Exception as e:
            print(f"Could not load saved coordinates: {e}")

    # Advanced detection methods from original debugger
    def detect_number(self, x, y, size, method):
        """Detect number at coordinate using specified method"""
        try:
            # 1. CAPTURE - Always capture at original size
            capture_x = x - size // 2
            capture_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
            original_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # 2. PROCESSING IMAGE - Create copy for OCR processing
            processing_image = original_image.copy()

            # Apply scaling for OCR processing if enabled
            if self.use_scaling.get():
                scale = self.scale_factor.get()
                new_width = int(processing_image.shape[1] * scale)
                new_height = int(processing_image.shape[0] * scale)
                processing_image = cv2.resize(processing_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Apply color inversion for processing if enabled
            if self.invert_colors.get():
                processing_image = cv2.bitwise_not(processing_image)

            # 3. DISPLAY IMAGE - Create copy for display (separate from processing)
            display_image = original_image.copy()

            # Apply color inversion for display if enabled
            if self.invert_colors.get():
                display_image = cv2.bitwise_not(display_image)

            # Apply display zoom (independent of processing scaling)
            zoom = self.display_zoom.get()
            if zoom != 1.0:
                zoom_width = int(display_image.shape[1] * zoom)
                zoom_height = int(display_image.shape[0] * zoom)
                display_image = cv2.resize(display_image, (zoom_width, zoom_height), interpolation=cv2.INTER_NEAREST)

            # Convert processing image to grayscale for analysis
            gray = cv2.cvtColor(processing_image, cv2.COLOR_BGR2GRAY)

            results = {}
            scale_info = f" (OCR scaled {self.scale_factor.get()}x)" if self.use_scaling.get() else ""
            zoom_info = f" (display zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            invert_info = " (inverted)" if self.invert_colors.get() else ""
            processing_info = f"{scale_info}{invert_info}"
            display_info = f"{zoom_info}{invert_info}"

            if method in ["OCR", "Both"]:
                # Traditional OCR detection with fine-tuning
                ocr_image = gray.copy()

                # Apply morphological operations if enabled
                if self.morphology_enabled.get():
                    strength = self.morphology_strength.get()
                    kernel_size = max(2, strength + 1)  # Kernel size based on strength
                    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_size, kernel_size))
                    iterations = strength

                    if self.morphology_operation.get() == "Erode":
                        ocr_image = cv2.erode(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Dilate":
                        ocr_image = cv2.dilate(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Open":
                        ocr_image = cv2.morphologyEx(ocr_image, cv2.MORPH_OPEN, kernel, iterations=iterations)

                # Configure OCR based on text type
                ocr_mode = self.ocr_mode.get()
                if ocr_mode == "Thick":
                    # For thick/bold text - use different PSM and add preprocessing
                    config = '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789'
                    # Additional preprocessing for thick text
                    _, ocr_image = cv2.threshold(ocr_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                elif ocr_mode == "Thin":
                    # For thin/light text
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                    # Enhance thin text
                    ocr_image = cv2.dilate(ocr_image, np.ones((2,2), np.uint8), iterations=1)
                else:
                    # Standard mode
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'

                try:
                    # Get confidence threshold for traditional OCR
                    confidence_threshold = getattr(self, 'traditional_ocr_confidence_threshold', tk.DoubleVar(value=0.7)).get()

                    # Use image_to_data to get confidence information
                    data = pytesseract.image_to_data(ocr_image, config=config, output_type=pytesseract.Output.DICT)

                    # Extract text and confidence
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    texts = [text for text in data['text'] if text.strip()]

                    mode_info = f" [{ocr_mode}]" if ocr_mode != "Standard" else ""
                    morph_info = f" [{self.morphology_operation.get()}{self.morphology_strength.get()}]" if self.morphology_enabled.get() else ""

                    if texts and confidences:
                        # Get the best text with highest confidence
                        best_text = ""
                        best_confidence = 0
                        for i, text in enumerate(texts):
                            if i < len(confidences) and text.strip().isdigit():
                                conf = confidences[i]
                                if conf > best_confidence:
                                    best_confidence = conf
                                    best_text = text.strip()

                        # Convert confidence from 0-100 to 0-1 scale for comparison
                        confidence_normalized = best_confidence / 100.0

                        if best_text and confidence_normalized >= confidence_threshold:
                            results['Traditional OCR'] = f"{int(best_text)}{processing_info}{mode_info}{morph_info} [Conf: {confidence_normalized:.3f}]"
                        else:
                            results['Traditional OCR'] = f"LOW_CONF: '{best_text}' ({confidence_normalized:.3f} < {confidence_threshold:.3f}){processing_info}{mode_info}{morph_info}"
                    else:
                        # Fallback to simple string extraction
                        text = pytesseract.image_to_string(ocr_image, config=config).strip()
                        if text.isdigit():
                            results['Traditional OCR'] = f"{int(text)}{processing_info}{mode_info}{morph_info} [Conf: N/A]"
                        else:
                            results['Traditional OCR'] = f"FAIL: '{text}'{processing_info}{mode_info}{morph_info}"

                except Exception as e:
                    results['Traditional OCR'] = f"ERROR: {str(e)}{processing_info}"

            # AI OCR Analysis (if enabled and available)
            if self.ai_ocr_enabled.get() and hasattr(self, 'ai_ocr_service') and self.ai_ocr_service:
                try:
                    # Use original image for AI OCR (before any processing)
                    ai_ocr_result = self.ai_ocr_service.recognize_number(
                        original_image,
                        fast_mode=False,
                        piece_id=f"DETECTION_TEST_{x}_{y}"
                    )

                    # Get detailed information from AI OCR service
                    if hasattr(self.ai_ocr_service, 'last_detection_info'):
                        detection_info = self.ai_ocr_service.last_detection_info
                        confidence = detection_info.get('confidence', 0.0)
                        backend_used = detection_info.get('backend_used', 'Unknown')
                        preprocessing_applied = detection_info.get('preprocessing_applied', 'None')
                        brightness = detection_info.get('brightness', 0.0)

                        ai_info = f" [Backend: {backend_used}, Confidence: {confidence:.3f}, Brightness: {brightness:.1f}, Preprocessing: {preprocessing_applied}]"
                    else:
                        ai_info = f" [Confidence: N/A]"

                    if ai_ocr_result is not None:
                        results['AI OCR'] = f"{ai_ocr_result}{ai_info}"
                    else:
                        results['AI OCR'] = f"FAIL: No detection{ai_info}"

                except Exception as e:
                    results['AI OCR'] = f"ERROR: {str(e)}"

            if method in ["Pixel", "Both"]:
                # Pixel analysis
                _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
                dark_pixels = cv2.countNonZero(binary)
                total_pixels = gray.shape[0] * gray.shape[1]
                density = dark_pixels / total_pixels if total_pixels > 0 else 0

                if density < 0.02:
                    pixel_result = 1
                elif density < 0.04:
                    pixel_result = 2
                elif density < 0.06:
                    pixel_result = 3
                elif density < 0.08:
                    pixel_result = 4
                elif density < 0.10:
                    pixel_result = 5
                elif density < 0.12:
                    pixel_result = 6
                else:
                    pixel_result = 7

                results['Pixel'] = f"{pixel_result} (d={density:.3f}){processing_info}"

            # Save debug image (save the processing image to see what OCR analyzed)
            timestamp = datetime.now().strftime("%H%M%S")
            scale_suffix = f"_s{self.scale_factor.get()}" if self.use_scaling.get() else ""
            invert_suffix = "_inv" if self.invert_colors.get() else ""
            ocr_suffix = f"_{self.ocr_mode.get().lower()}" if self.ocr_mode.get() != "Standard" else ""
            morph_suffix = f"_{self.morphology_operation.get().lower()}{self.morphology_strength.get()}" if self.morphology_enabled.get() else ""
            filename = f"{DEBUG_FOLDER}/debug_{x}_{y}_{size}{scale_suffix}{invert_suffix}{ocr_suffix}{morph_suffix}_{timestamp}.png"
            cv2.imwrite(filename, processing_image)

            # Store current images for template saving
            self.current_images[f"{x}_{y}_{size}"] = processing_image

            # Display image in GUI (show the display image with zoom)
            zoom_text = f" (zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            info_text = f"Coord: ({x}, {y})\nOriginal: {size}x{size}{zoom_text}"
            self.display_image(display_image, info_text)

            return results, filename

        except Exception as e:
            return {"Error": str(e)}, None

    def display_image(self, image_array, info_text=""):
        """Display captured image in GUI"""
        try:
            # Resize image for display (scale up for better visibility)
            height, width = image_array.shape[:2]
            scale = min(200 // width, 200 // height, 8)  # Max 200px, max 8x scale
            new_width = width * scale
            new_height = height * scale

            resized = cv2.resize(image_array, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Convert to PIL Image
            if len(resized.shape) == 3:
                resized_rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(resized_rgb)
            else:
                pil_image = Image.fromarray(resized)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep reference

            # Update info
            self.image_info.config(text=f"Size: {width}x{height} (scaled {scale}x)\n{info_text}")

        except Exception as e:
            self.image_label.config(text=f"Image error: {e}")
            self.image_info.config(text="")

    # Coordinate management methods
    def add_coordinate(self):
        """Add a new coordinate"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        coord_name = simpledialog.askstring("Add Coordinate", "Enter coordinate name:")
        if coord_name:
            default_coord = {
                'x': 0,
                'y': 0,
                'delay': 1.0,
                'repeat': 1,
                'use_esc_key': False,
                'description': ''
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('coordinates', {})[coord_name] = default_coord

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Added coordinate: {coord_name}")

    def edit_coordinate(self):
        """Edit selected coordinate"""
        selection = self.coords_tree_config.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to edit")
            return

        item = self.coords_tree_config.item(selection[0])
        coord_name = item['values'][0]
        self.open_coordinate_edit_dialog(coord_name)

    def delete_coordinate(self):
        """Delete selected coordinate"""
        selection = self.coords_tree_config.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to delete")
            return

        item = self.coords_tree_config.item(selection[0])
        coord_name = item['values'][0]

        if messagebox.askyesno("Delete Coordinate", f"Are you sure you want to delete coordinate '{coord_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['coordinates'][coord_name]

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Deleted coordinate: {coord_name}")

    def capture_coordinate_live(self):
        """Capture coordinate from live mouse position"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        coord_name = simpledialog.askstring("Capture Coordinate", "Enter coordinate name:")
        if coord_name:
            # Get current mouse position
            x, y = pyautogui.position()

            new_coord = {
                'x': x,
                'y': y,
                'delay': 1.0,
                'repeat': 1,
                'use_esc_key': False,
                'description': f'Captured at {datetime.now().strftime("%H:%M:%S")}'
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('coordinates', {})[coord_name] = new_coord

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Captured coordinate: {coord_name} at ({x}, {y})")

    def open_coordinate_edit_dialog(self, coord_name):
        """Open coordinate editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        coord_config = module_config['coordinates'][coord_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Coordinate: {coord_name}")
        dialog.geometry("400x250")
        dialog.transient(self.root)
        dialog.grab_set()

        # Coordinate settings
        ttk.Label(dialog, text="X Position:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        x_var = tk.IntVar(value=coord_config.get('x', 0))
        ttk.Spinbox(dialog, from_=0, to=3000, textvariable=x_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Y Position:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        y_var = tk.IntVar(value=coord_config.get('y', 0))
        ttk.Spinbox(dialog, from_=0, to=2000, textvariable=y_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Delay (seconds):").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        delay_var = tk.DoubleVar(value=coord_config.get('delay', 1.0))
        ttk.Spinbox(dialog, from_=0.01, to=10.0, increment=0.1, textvariable=delay_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Repeat Count:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        repeat_var = tk.IntVar(value=coord_config.get('repeat', 1))
        ttk.Spinbox(dialog, from_=1, to=100, textvariable=repeat_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Use ESC Key:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        esc_var = tk.BooleanVar(value=coord_config.get('use_esc_key', False))
        ttk.Checkbutton(dialog, variable=esc_var).grid(row=4, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Description:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar(value=coord_config.get('description', ''))
        ttk.Entry(dialog, textvariable=desc_var, width=30).grid(row=5, column=1, sticky=tk.EW, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=6, column=0, columnspan=2, pady=20)

        def save_coordinate():
            coord_config['x'] = x_var.get()
            coord_config['y'] = y_var.get()
            coord_config['delay'] = delay_var.get()
            coord_config['repeat'] = repeat_var.get()
            coord_config['use_esc_key'] = esc_var.get()
            coord_config['description'] = desc_var.get()

            # Refresh the coordinates display
            self.load_coordinates(module_config['coordinates'])
            self.status_var.set(f"Updated coordinate: {coord_name}")
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        dialog.columnconfigure(1, weight=1)

    # Scan region management methods
    def add_scan_region(self):
        """Add a new scan region"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return

        region_name = simpledialog.askstring("Add Scan Region", "Enter region name:")
        if region_name:
            default_region = {
                'x': 0,
                'y': 0,
                'width': 100,
                'height': 100,
                'description': ''
            }

            module_config = self.config_data['modules'][self.current_module]
            module_config.setdefault('scan_regions', {})[region_name] = default_region

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Added scan region: {region_name}")

    def edit_scan_region(self):
        """Edit selected scan region"""
        selection = self.regions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a scan region to edit")
            return

        item = self.regions_tree.item(selection[0])
        region_name = item['values'][0]
        self.open_region_edit_dialog(region_name)

    def delete_scan_region(self):
        """Delete selected scan region"""
        selection = self.regions_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a scan region to delete")
            return

        item = self.regions_tree.item(selection[0])
        region_name = item['values'][0]

        if messagebox.askyesno("Delete Scan Region", f"Are you sure you want to delete scan region '{region_name}'?"):
            module_config = self.config_data['modules'][self.current_module]
            del module_config['scan_regions'][region_name]

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Deleted scan region: {region_name}")

    def open_region_edit_dialog(self, region_name):
        """Open scan region editing dialog"""
        module_config = self.config_data['modules'][self.current_module]
        region_config = module_config['scan_regions'][region_name]

        # Create edit dialog
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Scan Region: {region_name}")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Region settings
        ttk.Label(dialog, text="X Position:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        x_var = tk.IntVar(value=region_config.get('x', 0))
        ttk.Spinbox(dialog, from_=0, to=3000, textvariable=x_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Y Position:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        y_var = tk.IntVar(value=region_config.get('y', 0))
        ttk.Spinbox(dialog, from_=0, to=2000, textvariable=y_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Width:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        width_var = tk.IntVar(value=region_config.get('width', 100))
        ttk.Spinbox(dialog, from_=10, to=1000, textvariable=width_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Height:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        height_var = tk.IntVar(value=region_config.get('height', 100))
        ttk.Spinbox(dialog, from_=10, to=1000, textvariable=height_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=10, pady=5)

        ttk.Label(dialog, text="Description:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar(value=region_config.get('description', ''))
        ttk.Entry(dialog, textvariable=desc_var, width=30).grid(row=4, column=1, sticky=tk.EW, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_region():
            region_config['x'] = x_var.get()
            region_config['y'] = y_var.get()
            region_config['width'] = width_var.get()
            region_config['height'] = height_var.get()
            region_config['description'] = desc_var.get()

            # Refresh the scan regions display
            self.load_scan_regions(module_config['scan_regions'])
            self.status_var.set(f"Updated scan region: {region_name}")
            dialog.destroy()

        ttk.Button(button_frame, text="Save", command=save_region).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT)

        dialog.columnconfigure(1, weight=1)

    # Testing and utility methods
    def test_template(self):
        """Test the selected template"""
        template_name = self.template_var.get()
        if not template_name:
            messagebox.showwarning("Warning", "Please select a template to test")
            return

        self.test_template_by_name(template_name)

    def test_template_by_name(self, template_name):
        """Test a specific template by name"""
        try:
            # Clear previous results
            self.results_text.delete(1.0, tk.END)

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Load template - try different extensions
            template_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}.png")
            if not os.path.exists(template_path):
                for ext in ['.jpg', '.jpeg', '.bmp', '.gif']:
                    alt_path = os.path.join(TEMPLATE_FOLDER, f"{template_name}{ext}")
                    if os.path.exists(alt_path):
                        template_path = alt_path
                        break

            if not os.path.exists(template_path):
                self.results_text.insert(tk.END, f"❌ Template file not found: {template_name}\n")
                self.results_text.insert(tk.END, f"   Searched in: {TEMPLATE_FOLDER}\n")
                return

            template = cv2.imread(template_path)
            if template is None:
                self.results_text.insert(tk.END, f"❌ Failed to load template: {template_path}\n")
                return

            # Get threshold from GUI or config
            threshold = self.template_threshold_var.get()

            # Also check module config for comparison
            config_threshold = 0.8
            if self.current_module:
                module_config = self.config_data['modules'][self.current_module]
                template_config = module_config.get('templates', {}).get(template_name, {})
                config_threshold = template_config.get('threshold', 0.8)

            # Display test info
            self.results_text.insert(tk.END, f"🔍 Testing template: {template_name}\n")
            self.results_text.insert(tk.END, f"   Template size: {template.shape[1]}x{template.shape[0]}\n")
            self.results_text.insert(tk.END, f"   GUI threshold: {threshold:.2f}\n")
            self.results_text.insert(tk.END, f"   Config threshold: {config_threshold:.2f}\n")
            self.results_text.insert(tk.END, f"   Screenshot size: {screenshot_cv.shape[1]}x{screenshot_cv.shape[0]}\n\n")

            # Perform template matching
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # Calculate center coordinates
            template_height, template_width = template.shape[:2]
            center_x = max_loc[0] + template_width // 2
            center_y = max_loc[1] + template_height // 2

            # Display results
            self.results_text.insert(tk.END, f"📊 DETECTION RESULTS:\n")
            self.results_text.insert(tk.END, f"   Best match confidence: {max_val:.4f}\n")
            self.results_text.insert(tk.END, f"   Match threshold: {threshold:.2f}\n")
            self.results_text.insert(tk.END, f"   Top-left corner: ({max_loc[0]}, {max_loc[1]})\n")
            self.results_text.insert(tk.END, f"   Center coordinates: ({center_x}, {center_y})\n")
            self.results_text.insert(tk.END, f"   Template dimensions: {template_width}x{template_height}\n\n")

            if max_val >= threshold:
                self.results_text.insert(tk.END, f"✅ TEMPLATE DETECTED!\n")
                self.results_text.insert(tk.END, f"   Confidence: {max_val:.4f} (≥ {threshold:.2f})\n")
                self.results_text.insert(tk.END, f"   Click coordinates: ({center_x}, {center_y})\n")

                # Show confidence level description
                if max_val >= 0.95:
                    confidence_desc = "Excellent match"
                elif max_val >= 0.9:
                    confidence_desc = "Very good match"
                elif max_val >= 0.85:
                    confidence_desc = "Good match"
                else:
                    confidence_desc = "Acceptable match"
                self.results_text.insert(tk.END, f"   Quality: {confidence_desc}\n")
            else:
                self.results_text.insert(tk.END, f"❌ TEMPLATE NOT DETECTED\n")
                self.results_text.insert(tk.END, f"   Confidence: {max_val:.4f} (< {threshold:.2f})\n")
                self.results_text.insert(tk.END, f"   Suggestion: Lower threshold or check template image\n")

            self.results_text.insert(tk.END, f"\n⏰ Test completed at: {datetime.now().strftime('%H:%M:%S')}\n")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n")
            self.results_text.see(tk.END)

        except Exception as e:
            self.results_text.insert(tk.END, f"Error testing template: {str(e)}\n")
            self.results_text.see(tk.END)

    def capture_screenshot(self):
        """Capture a screenshot for analysis"""
        try:
            screenshot = pyautogui.screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = os.path.join(DEBUG_FOLDER, filename)
            screenshot.save(filepath)

            self.results_text.insert(tk.END, f"\nScreenshot saved: {filepath}\n")
            self.results_text.see(tk.END)
            self.status_var.set(f"Screenshot saved: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screenshot: {str(e)}")

    def toggle_tracking(self):
        """Toggle live mouse tracking"""
        if self.live_tracking:
            self.live_tracking = False
            self.tracking_button.config(text="Start Tracking")
            self.status_var.set("Mouse tracking stopped")
        else:
            self.live_tracking = True
            self.tracking_button.config(text="Stop Tracking")
            self.status_var.set("Mouse tracking started")
            self.start_mouse_tracking()

    def start_mouse_tracking(self):
        """Start mouse tracking in a separate thread"""
        def track_mouse():
            while self.live_tracking:
                try:
                    x, y = pyautogui.position()
                    self.position_var.set(f"Position: ({x}, {y})")
                    time.sleep(0.1)
                except:
                    break

        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()

    def capture_position(self):
        """Capture current mouse position"""
        x, y = pyautogui.position()

        # Ask for coordinate name
        coord_name = simpledialog.askstring("Capture Position", "Enter coordinate name:")
        if coord_name:
            # Add to captured coordinates tree
            description = f"Captured at {datetime.now().strftime('%H:%M:%S')}"
            self.coords_tree.insert('', tk.END, values=(coord_name, x, y, description))
            self.status_var.set(f"Captured: {coord_name} at ({x}, {y})")

    # Advanced scanning methods from original debugger
    def scan_all(self):
        """Scan all coordinates"""
        self.results_text_detection.delete(1.0, tk.END)
        self.results_text_detection.insert(tk.END, f"SCANNING ALL - Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text_detection.insert(tk.END, f"Method: {self.detection_method.get()}\n")
        self.results_text_detection.insert(tk.END, "=" * 50 + "\n\n")

        self.last_results = {}

        for map_name, (x, y) in self.coordinates.items():
            results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
            self.last_results[map_name] = results

            self.results_text_detection.insert(tk.END, f"{map_name} at ({x}, {y}):\n")
            for method, result in results.items():
                self.results_text_detection.insert(tk.END, f"  {method}: {result}\n")
            if filename:
                self.results_text_detection.insert(tk.END, f"  Saved: {os.path.basename(filename)}\n")
            self.results_text_detection.insert(tk.END, "\n")

            self.results_text_detection.see(tk.END)
            self.root.update()

    def toggle_live_scan(self):
        """Toggle live scanning"""
        if not self.live_scanning:
            self.live_scanning = True
            self.live_thread = threading.Thread(target=self.live_scan_loop, daemon=True)
            self.live_thread.start()
        else:
            self.live_scanning = False

    def live_scan_loop(self):
        """Live scanning loop"""
        while self.live_scanning:
            try:
                selected = self.selected_map.get()
                if selected in self.coordinates:
                    x, y = self.coordinates[selected]
                    results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())

                    # Update results display
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.results_text_detection.insert(tk.END, f"[{timestamp}] {selected}: ")
                    for method, result in results.items():
                        self.results_text_detection.insert(tk.END, f"{method}={result} ")
                    self.results_text_detection.insert(tk.END, "\n")
                    self.results_text_detection.see(tk.END)

                time.sleep(1.0)  # Scan every second
            except Exception as e:
                print(f"Live scan error: {e}")
                break

    def save_results(self):
        """Save scan results to file"""
        if not self.last_results:
            messagebox.showwarning("Warning", "No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scan_results_{timestamp}.txt"
        filepath = os.path.join(DEBUG_FOLDER, filename)

        try:
            with open(filepath, 'w') as f:
                f.write(f"Scan Results - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Settings: Size={self.capture_size.get()}, Method={self.detection_method.get()}\n")
                f.write(f"OCR Scaling: {self.use_scaling.get()}, Factor: {self.scale_factor.get()}\n")
                f.write(f"Color Inversion: {self.invert_colors.get()}\n")
                f.write(f"OCR Mode: {self.ocr_mode.get()}\n")
                f.write(f"Morphology: {self.morphology_enabled.get()}, Op: {self.morphology_operation.get()}, Strength: {self.morphology_strength.get()}\n\n")

                for map_name, results in self.last_results.items():
                    x, y = self.coordinates[map_name]
                    f.write(f"{map_name} at ({x}, {y}):\n")
                    for method, result in results.items():
                        f.write(f"  {method}: {result}\n")
                    f.write("\n")

            messagebox.showinfo("Saved", f"Results saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save results: {e}")

    def on_map_selected(self, event):
        """Handle map selection change"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            x, y = self.coordinates[selected]
            self.x_var.set(x)
            self.y_var.set(y)

    def update_coordinate(self):
        """Update selected coordinate"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            self.coordinates[selected] = [self.x_var.get(), self.y_var.get()]
            messagebox.showinfo("Updated", f"Updated {selected} to ({self.x_var.get()}, {self.y_var.get()})")

    def start_mouse_capture(self):
        """Start mouse capture mode"""
        self.capturing_mouse = True
        self.capture_status.config(text="Position mouse and press ENTER", foreground="red")
        self.root.after(10000, self.stop_mouse_capture)  # Auto-stop after 10 seconds

    def stop_mouse_capture(self):
        """Stop mouse capture mode"""
        self.capturing_mouse = False
        self.capture_status.config(text="", foreground="blue")

    def capture_mouse_position(self):
        """Capture current mouse position (called by ENTER key)"""
        if not self.capturing_mouse:
            return

        x, y = pyautogui.position()
        selected = self.selected_map.get()

        if selected in self.coordinates:
            self.coordinates[selected] = [x, y]
            self.x_var.set(x)
            self.y_var.set(y)

            timestamp = datetime.now().strftime("%H:%M:%S")
            self.capture_status.config(text=f"Captured {selected}: ({x}, {y}) at {timestamp}", foreground="green")

            # Auto-stop capture mode
            self.capturing_mouse = False
            self.root.after(3000, lambda: self.capture_status.config(text=""))

    def focus_game_window(self):
        """Focus and resize the Last War game window to 2048x1080"""
        try:
            import pygetwindow as gw
            import time

            # Search for Last War window with various possible titles
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game",
                "Last War - Survival Game",
                "LastWar",
                "last war"
            ]

            game_window = None

            # Try to find the window by exact title match first
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        print(f"Found game window: '{title}'")
                        break
                except:
                    continue

            # If exact match failed, try partial match
            if not game_window:
                all_windows = gw.getAllWindows()
                for window in all_windows:
                    window_title = window.title.lower()
                    if any(title.lower() in window_title for title in ["last war", "survival game"]):
                        game_window = window
                        print(f"Found game window by partial match: '{window.title}'")
                        break

            if not game_window:
                print("Could not find Last War game window")
                self.status_var.set("Game window not found")
                return False

            # Focus the window first
            try:
                game_window.activate()
                time.sleep(0.5)
                print("Game window focused")
            except Exception as e:
                print(f"Could not focus window: {str(e)}")

            # Get current size and screen dimensions
            current_width = game_window.width
            current_height = game_window.height

            # Get screen size for proper full screen dimensions
            import tkinter as tk
            root = tk.Tk()
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            root.destroy()

            # Use full screen dimensions or large window size
            target_width = max(2560, screen_width)  # At least 2560 wide
            target_height = max(1440, screen_height - 40)  # At least 1440 high, minus taskbar

            # Check if resize is needed
            size_tolerance = 20
            if (abs(current_width - target_width) > size_tolerance or
                abs(current_height - target_height) > size_tolerance):

                try:
                    # Move to top-left first, then resize
                    game_window.moveTo(0, 0)
                    time.sleep(0.3)
                    game_window.resizeTo(target_width, target_height)
                    time.sleep(1.0)  # Wait for resize to complete

                    print(f"Game window resized from {current_width}x{current_height} to {target_width}x{target_height}")
                    self.status_var.set(f"Game window resized to {target_width}x{target_height}")
                    return True

                except Exception as e:
                    print(f"Could not resize window: {str(e)}")
                    self.status_var.set("Failed to resize game window")
                    return False
            else:
                print(f"Game window already correct size: {current_width}x{current_height}")
                self.status_var.set("Game window already correct size")
                return True

        except ImportError:
            print("pygetwindow not available - cannot manage game window")
            self.status_var.set("pygetwindow not available")
            return False
        except Exception as e:
            print(f"Error managing game window: {str(e)}")
            self.status_var.set("Error managing game window")
            return False

    # Template creation methods
    def capture_at_mouse(self):
        """Capture template at current mouse position"""
        x, y = pyautogui.position()
        size = self.template_size.get()

        # Calculate capture region
        left = x - size // 2
        top = y - size // 2

        try:
            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture image: {e}")

    def capture_at_coordinate(self):
        """Capture template at specified coordinates"""
        coord_dialog = tk.Toplevel(self.root)
        coord_dialog.title("Enter Coordinates")
        coord_dialog.geometry("300x150")
        coord_dialog.transient(self.root)
        coord_dialog.grab_set()

        ttk.Label(coord_dialog, text="X:").grid(row=0, column=0, padx=10, pady=10)
        x_entry = ttk.Entry(coord_dialog, width=10)
        x_entry.grid(row=0, column=1, padx=10, pady=10)

        ttk.Label(coord_dialog, text="Y:").grid(row=1, column=0, padx=10, pady=10)
        y_entry = ttk.Entry(coord_dialog, width=10)
        y_entry.grid(row=1, column=1, padx=10, pady=10)

        def capture():
            try:
                x = int(x_entry.get())
                y = int(y_entry.get())
                size = self.template_size.get()

                left = x - size // 2
                top = y - size // 2

                screenshot = pyautogui.screenshot(region=(left, top, size, size))
                self.captured_image = screenshot
                self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
                coord_dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Please enter valid coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not capture image: {e}")

        ttk.Button(coord_dialog, text="Capture", command=capture).grid(row=2, column=0, columnspan=2, pady=20)

    def capture_full_screen(self):
        """Capture full screen"""
        try:
            screenshot = pyautogui.screenshot()
            self.captured_image = screenshot
            self.display_template_preview(screenshot, "Full screen capture")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture screen: {e}")

    def display_template_preview(self, image, info_text):
        """Display captured image in preview"""
        try:
            # Resize for display if too large
            display_image = image.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"{info_text}\nSize: {image.width}x{image.height}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not display image: {e}")

    def save_template_to_folder(self):
        """Save captured image as template"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No image captured")
            return

        name = self.template_name.get().strip()
        if not name:
            messagebox.showwarning("Warning", "Please enter a template name")
            return

        if not name.endswith('.png'):
            name += '.png'

        filepath = os.path.join(TEMPLATE_FOLDER, name)

        try:
            self.captured_image.save(filepath)
            messagebox.showinfo("Saved", f"Template saved as {name}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save template: {e}")

    def test_template_detection(self):
        """Test template detection on current screen"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No template captured")
            return

        try:
            # Save temp template
            temp_path = os.path.join(DEBUG_FOLDER, "temp_template.png")
            self.captured_image.save(temp_path)

            # Take screenshot and find template
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.cvtColor(np.array(self.captured_image), cv2.COLOR_RGB2BGR)

            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val > 0.8:
                messagebox.showinfo("Detection Result",
                                  f"Template found!\nConfidence: {max_val:.3f}\nLocation: {max_loc}")
            else:
                messagebox.showinfo("Detection Result",
                                  f"Template not found clearly.\nBest match confidence: {max_val:.3f}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not test template: {e}")

    def toggle_template_capture_mode(self):
        """Toggle template capture mode"""
        self.template_capture_mode = self.template_mode_var.get()
        if self.template_capture_mode:
            self.template_mode_status.configure(text="LIVE MODE: Press ENTER to capture", foreground="green")
        else:
            self.template_mode_status.configure(text="", foreground="blue")

    def capture_template_at_mouse(self):
        """Capture template at current mouse position (called by Enter key)"""
        if not self.template_capture_mode:
            return

        try:
            x, y = pyautogui.position()
            size = self.template_size.get()

            # Calculate capture region
            left = x - size // 2
            top = y - size // 2

            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Live captured at ({x}, {y})")

            # Update status
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.template_mode_status.configure(
                text=f"Captured at ({x}, {y}) - {timestamp}",
                foreground="blue"
            )

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture template: {e}")

    def toggle_live_template_preview(self):
        """Toggle live template preview mode"""
        self.live_template_preview = self.live_preview_var.get()
        if self.live_template_preview:
            self.live_preview_status.configure(text="LIVE PREVIEW: Showing mouse area", foreground="green")
            self.template_preview_thread = threading.Thread(target=self.live_preview_loop, daemon=True)
            self.template_preview_thread.start()
        else:
            self.live_preview_status.configure(text="", foreground="green")

    def live_preview_loop(self):
        """Live template preview loop"""
        while self.live_template_preview:
            try:
                x, y = pyautogui.position()
                size = self.template_size.get()

                # Calculate capture region
                left = x - size // 2
                top = y - size // 2

                # Capture screenshot
                screenshot = pyautogui.screenshot(region=(left, top, size, size))

                # Update preview in main thread
                self.root.after(0, self.update_live_preview, screenshot, x, y)

                time.sleep(0.1)  # Update 10 times per second
            except Exception as e:
                print(f"Live preview error: {e}")
                break

    def update_live_preview(self, screenshot, x, y):
        """Update the live preview display"""
        try:
            if not self.live_template_preview:
                return

            # Resize for display if needed
            display_image = screenshot.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"Live Preview at ({x}, {y})\nSize: {screenshot.width}x{screenshot.height}")

        except Exception as e:
            print(f"Preview update error: {e}")

    # Additional utility methods
    def save_as_template(self):
        """Save current captured image as template"""
        if hasattr(self, 'current_images') and self.current_images:
            # Get the most recent captured image
            latest_key = max(self.current_images.keys())
            image = self.current_images[latest_key]

            name = tk.simpledialog.askstring("Template Name", "Enter template name:")
            if name:
                if not name.endswith('.png'):
                    name += '.png'

                filepath = os.path.join(TEMPLATE_FOLDER, name)
                try:
                    cv2.imwrite(filepath, image)
                    messagebox.showinfo("Saved", f"Template saved as {name}")
                except Exception as e:
                    messagebox.showerror("Error", f"Could not save template: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save as template")

    def save_debug_image(self):
        """Save current image for debugging"""
        if hasattr(self, 'current_images') and self.current_images:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_capture_{timestamp}.png"
            filepath = os.path.join(DEBUG_FOLDER, filename)

            try:
                latest_key = max(self.current_images.keys())
                image = self.current_images[latest_key]
                cv2.imwrite(filepath, image)
                messagebox.showinfo("Saved", f"Debug image saved as {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save debug image: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save")

    def add_new_coordinate(self):
        """Add a new coordinate point"""
        name = tk.simpledialog.askstring("New Coordinate", "Enter coordinate name:")
        if name and name not in self.coordinates:
            x, y = pyautogui.position()
            self.coordinates[name] = [x, y]

            # Update combobox values
            map_combo = None
            for widget in self.detection_frame.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.LabelFrame):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Combobox):
                                    grandchild['values'] = list(self.coordinates.keys())
                                    break

            self.selected_map.set(name)
            self.x_var.set(x)
            self.y_var.set(y)
            messagebox.showinfo("Added", f"Added coordinate '{name}' at ({x}, {y})")

    def load_coordinates_file(self):
        """Load coordinates from JSON file"""
        filename = filedialog.askopenfilename(
            title="Load Coordinates",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    loaded_coords = json.load(f)
                    self.coordinates.update(loaded_coords)
                    messagebox.showinfo("Loaded", f"Loaded {len(loaded_coords)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not load coordinates: {e}")

    def save_coordinates_file(self):
        """Save coordinates to JSON file"""
        filename = filedialog.asksaveasfilename(
            title="Save Coordinates",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.coordinates, f, indent=2)
                    messagebox.showinfo("Saved", f"Saved {len(self.coordinates)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save coordinates: {e}")

    # Live mouse tracking methods
    def start_live_mouse_tracking(self):
        """Start live mouse position tracking"""
        if not self.live_mouse_tracking:
            self.live_mouse_tracking = True
            self.mouse_thread = threading.Thread(target=self.mouse_tracking_loop, daemon=True)
            self.mouse_thread.start()

    def stop_live_mouse_tracking(self):
        """Stop live mouse position tracking"""
        self.live_mouse_tracking = False

    def mouse_tracking_loop(self):
        """Mouse tracking loop"""
        while self.live_mouse_tracking:
            try:
                x, y = pyautogui.position()
                self.current_mouse_x.set(str(x))
                self.current_mouse_y.set(str(y))
                time.sleep(0.1)  # Update 10 times per second
            except:
                break

    def save_current_mouse_coord(self):
        """Save current mouse coordinates"""
        x = self.current_mouse_x.get()
        y = self.current_mouse_y.get()
        timestamp = datetime.now().strftime("%H:%M:%S")
        coord_text = f"({x}, {y}) - {timestamp}"

        self.saved_coords.set(coord_text)
        self.coord_history.insert(0, coord_text)

        # Keep only last 50 entries
        if self.coord_history.size() > 50:
            self.coord_history.delete(50, tk.END)

    def copy_coordinates(self):
        """Copy coordinates to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(self.saved_coords.get())
        messagebox.showinfo("Copied", "Coordinates copied to clipboard")

    def clear_coordinates(self):
        """Clear coordinate display"""
        self.saved_coords.set("Click 'Save Coord' to capture")

    def use_selected_coord(self):
        """Use selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            coord_text = self.coord_history.get(selection[0])
            self.saved_coords.set(coord_text)

    def delete_selected_coord(self):
        """Delete selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            self.coord_history.delete(selection[0])

    def save_coord_history(self):
        """Save coordinate history to file"""
        filename = f"coord_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(COORDS_FOLDER, filename)

        try:
            with open(filepath, 'w') as f:
                f.write(f"Coordinate History - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")

                for i in range(self.coord_history.size()):
                    f.write(f"{self.coord_history.get(i)}\n")

            messagebox.showinfo("Saved", f"History saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save history: {e}")

    # AI OCR Initialization Methods
    def _initialize_ai_ocr_service(self):
        """Initialize AI OCR service"""
        try:
            from ai_ocr_service import AINumberRecognition
            self.ai_ocr_service = AINumberRecognition()
            print("[CONFIG] AI OCR service initialized successfully")
        except Exception as e:
            print(f"[CONFIG] Failed to initialize AI OCR service: {str(e)}")
            self.ai_ocr_service = None

    def _load_ai_ocr_config(self):
        """Load AI OCR configuration from unified config"""
        try:
            # Get AI OCR config from system settings
            system_settings = self.config_data.get('system_settings', {})
            ai_ocr_config = system_settings.get('ai_ocr', {})

            # Set default values if not present
            defaults = {
                'enabled': True,
                'confidence_threshold': 0.9,
                'backend': 'Auto',
                'white_text_preprocessing': True,
                'debug_images': True,
                'detailed_logging': True
            }

            # Update variables with loaded or default values
            self.ai_ocr_enabled.set(ai_ocr_config.get('enabled', defaults['enabled']))
            self.ai_ocr_confidence_threshold.set(ai_ocr_config.get('confidence_threshold', defaults['confidence_threshold']))
            self.ai_ocr_backend.set(ai_ocr_config.get('backend', defaults['backend']))
            self.ai_ocr_white_text_preprocessing.set(ai_ocr_config.get('white_text_preprocessing', defaults['white_text_preprocessing']))
            self.ai_ocr_debug_images.set(ai_ocr_config.get('debug_images', defaults['debug_images']))
            self.ai_ocr_detailed_logging.set(ai_ocr_config.get('detailed_logging', defaults['detailed_logging']))

            # Apply configuration to service
            if self.ai_ocr_service:
                self._apply_ai_ocr_config_to_service(ai_ocr_config or defaults)

            print(f"[CONFIG] AI OCR configuration loaded: {ai_ocr_config or defaults}")

        except Exception as e:
            print(f"[CONFIG] Error loading AI OCR configuration: {str(e)}")

    # AI OCR Configuration Methods
    def _update_traditional_ocr_confidence_label(self, *args):
        """Update traditional OCR confidence label"""
        try:
            confidence_value = self.traditional_ocr_confidence_threshold.get()
            self.traditional_confidence_label.config(text=f"{confidence_value:.2f}")
        except Exception as e:
            print(f"Error updating traditional OCR confidence label: {str(e)}")

    def _update_ai_ocr_config(self, *args):
        """Update AI OCR configuration and save to unified config"""
        try:
            # Update confidence label
            confidence_value = self.ai_ocr_confidence_threshold.get()
            self.confidence_label.config(text=f"{confidence_value:.2f}")

            # Save AI OCR settings to unified configuration
            ai_ocr_config = {
                'enabled': self.ai_ocr_enabled.get(),
                'confidence_threshold': confidence_value,
                'backend': self.ai_ocr_backend.get(),
                'white_text_preprocessing': self.ai_ocr_white_text_preprocessing.get(),
                'debug_images': self.ai_ocr_debug_images.get(),
                'detailed_logging': self.ai_ocr_detailed_logging.get()
            }

            # Update system settings in config data
            if not hasattr(self, 'config_data'):
                self.config_data = {'system_settings': {}}

            if 'system_settings' not in self.config_data:
                self.config_data['system_settings'] = {}

            self.config_data['system_settings']['ai_ocr'] = ai_ocr_config

            # Apply configuration to AI OCR service if available
            self._apply_ai_ocr_config_to_service(ai_ocr_config)

            print(f"[CONFIG] AI OCR configuration updated: {ai_ocr_config}")

        except Exception as e:
            print(f"Error updating AI OCR configuration: {str(e)}")

    def _apply_ai_ocr_config_to_service(self, config):
        """Apply configuration to AI OCR service"""
        try:
            # Try to import and configure the AI OCR service
            from ai_ocr_service import AINumberRecognition

            # Initialize or get existing service
            if not hasattr(self, 'ai_ocr_service'):
                self.ai_ocr_service = AINumberRecognition()

            # Apply configuration
            if hasattr(self.ai_ocr_service, 'update_debug_config'):
                debug_config = {
                    'confidence_threshold': config['confidence_threshold'],
                    'enable_detailed_logging': config['detailed_logging'],
                    'save_debug_images': config['debug_images'],
                    'white_text_preprocessing': config['white_text_preprocessing']
                }
                self.ai_ocr_service.update_debug_config(debug_config)

        except Exception as e:
            print(f"Could not apply configuration to AI OCR service: {str(e)}")

    def _test_ai_ocr(self):
        """Test AI OCR functionality with sample images"""
        try:
            # Initialize AI OCR service if not already done
            if not hasattr(self, 'ai_ocr_service'):
                from ai_ocr_service import AINumberRecognition
                self.ai_ocr_service = AINumberRecognition()

            if not self.ai_ocr_service.active_backends:
                messagebox.showwarning("AI OCR Test", "No OCR backends available")
                return

            import numpy as np
            import cv2
            from datetime import datetime

            # Test on real screen captures from predefined coordinates
            test_coordinates = [
                ("M1", self.coordinates.get('M1', [1079, 833])),
                ("M2", self.coordinates.get('M2', [1245, 835])),
                ("M3", self.coordinates.get('M3', [1412, 835])),
                ("M4", self.coordinates.get('M4', [1577, 833]))
            ]

            results = []
            test_results_text = "🧪 AI OCR Real Screen Test Results:\n" + "="*50 + "\n\n"
            capture_size = 25  # Use 25x25 for map piece detection

            for coord_name, (x, y) in test_coordinates:
                try:
                    # Capture screen region
                    capture_x = x - capture_size // 2
                    capture_y = y - capture_size // 2
                    screenshot = pyautogui.screenshot(region=(capture_x, capture_y, capture_size, capture_size))
                    image_array = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

                    # Test with AI OCR
                    piece_id = f"REAL_TEST_{coord_name}"
                    start_time = time.time()
                    result = self.ai_ocr_service.recognize_number(image_array, fast_mode=False, piece_id=piece_id)
                    test_time = time.time() - start_time

                    # Get detailed detection information
                    if hasattr(self.ai_ocr_service, 'last_detection_info'):
                        info = self.ai_ocr_service.last_detection_info
                        confidence = info.get('confidence', 0.0)
                        backend = info.get('backend_used', 'Unknown')
                        preprocessing = info.get('preprocessing_applied', 'None')
                        brightness = info.get('brightness', 0.0)
                        detected_text = info.get('detected_text', 'None')
                        success = info.get('success', False)

                        test_results_text += f"📍 {coord_name} at ({x}, {y}):\n"
                        if result is not None:
                            test_results_text += f"   ✅ DETECTED: {result}\n"
                            results.append(True)
                        else:
                            test_results_text += f"   ❌ NO DETECTION\n"
                            results.append(False)

                        test_results_text += f"   📊 Details:\n"
                        test_results_text += f"      Backend: {backend}\n"
                        test_results_text += f"      Confidence: {confidence:.3f}\n"
                        test_results_text += f"      Raw Text: '{detected_text}'\n"
                        test_results_text += f"      Preprocessing: {preprocessing}\n"
                        test_results_text += f"      Brightness: {brightness:.1f}\n"
                        test_results_text += f"      Time: {test_time:.3f}s\n"
                        test_results_text += f"      Size: {capture_size}x{capture_size}\n\n"
                    else:
                        test_results_text += f"📍 {coord_name} at ({x}, {y}): "
                        if result is not None:
                            test_results_text += f"✅ {result} (Time: {test_time:.3f}s)\n\n"
                            results.append(True)
                        else:
                            test_results_text += f"❌ No detection (Time: {test_time:.3f}s)\n\n"
                            results.append(False)

                except Exception as e:
                    test_results_text += f"📍 {coord_name} at ({x}, {y}): ❌ ERROR - {str(e)}\n\n"
                    results.append(False)

            success_rate = sum(results) / len(results) * 100 if results else 0
            test_results_text += f"📊 SUMMARY:\n"
            test_results_text += f"   Success Rate: {success_rate:.1f}% ({sum(results)}/{len(results)})\n"
            test_results_text += f"   Active Backends: {', '.join(self.ai_ocr_service.active_backends)}\n"
            test_results_text += f"   Current Settings:\n"
            test_results_text += f"      Confidence Threshold: {self.ai_ocr_confidence_threshold.get():.2f}\n"
            test_results_text += f"      Backend: {self.ai_ocr_backend.get()}\n"
            test_results_text += f"      White Text Preprocessing: {self.ai_ocr_white_text_preprocessing.get()}\n"
            test_results_text += f"      Debug Images: {self.ai_ocr_debug_images.get()}\n"

            # Display results in the results text area
            self.results_text_detection.delete(1.0, tk.END)
            self.results_text_detection.insert(tk.END, test_results_text)

            # Show summary dialog
            if success_rate >= 75:
                messagebox.showinfo("AI OCR Test", f"Test completed successfully!\n\nSuccess Rate: {success_rate:.1f}%\n\nCheck the Results panel for detailed information.")
            else:
                messagebox.showwarning("AI OCR Test", f"Test completed with issues.\n\nSuccess Rate: {success_rate:.1f}%\n\nCheck the Results panel for detailed information.")

        except Exception as e:
            error_msg = f"AI OCR test failed: {str(e)}"
            print(error_msg)
            messagebox.showerror("AI OCR Test Error", error_msg)

    def _open_ai_ocr_monitor(self):
        """Open AI OCR monitoring window"""
        try:
            # Create a simple monitoring window
            monitor_window = tk.Toplevel(self.root)
            monitor_window.title("AI OCR Real-time Monitor")
            monitor_window.geometry("800x600")
            monitor_window.configure(bg='#2b2b2b')

            # Make window stay on top
            monitor_window.attributes('-topmost', True)

            # Main frame
            main_frame = tk.Frame(monitor_window, bg='#2b2b2b')
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Title
            title_label = tk.Label(main_frame, text="AI OCR Real-time Monitoring",
                                  font=('Segoe UI', 14, 'bold'),
                                  fg='white', bg='#2b2b2b')
            title_label.pack(pady=(0, 20))

            # Status frame
            status_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='ridge', bd=1)
            status_frame.pack(fill=tk.X, pady=(0, 10))

            # Initialize AI OCR service if needed
            if not hasattr(self, 'ai_ocr_service'):
                from ai_ocr_service import AINumberRecognition
                self.ai_ocr_service = AINumberRecognition()

            # Status label
            backends = getattr(self.ai_ocr_service, 'active_backends', [])
            status_text = f"AI OCR Status: Active ({', '.join(backends)})" if backends else "AI OCR Status: No backends available"
            status_label = tk.Label(status_frame, text=status_text,
                                   font=('Segoe UI', 10, 'bold'),
                                   fg='white', bg='#3b3b3b')
            status_label.pack(pady=10)

            # Detection log frame
            log_frame = tk.LabelFrame(main_frame, text="Recent AI OCR Detections",
                                     font=('Segoe UI', 10, 'bold'),
                                     fg='white', bg='#2b2b2b')
            log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # Detection text area
            import tkinter.scrolledtext as scrolledtext
            detections_text = scrolledtext.ScrolledText(log_frame,
                                                       height=20,
                                                       bg='#3b3b3b',
                                                       fg='white',
                                                       font=('Consolas', 9))
            detections_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Control buttons
            control_frame = tk.Frame(main_frame, bg='#2b2b2b')
            control_frame.pack(fill=tk.X)

            def refresh_monitor():
                """Refresh the monitoring display"""
                try:
                    if hasattr(self.ai_ocr_service, 'get_recent_detections'):
                        recent = self.ai_ocr_service.get_recent_detections()
                        if recent:
                            detections_text.insert(tk.END, recent + "\n")
                            detections_text.see(tk.END)
                except Exception as e:
                    print(f"Error refreshing monitor: {str(e)}")

            def clear_log():
                """Clear the detection log"""
                detections_text.delete(1.0, tk.END)

            def test_detection():
                """Generate test detection"""
                self._test_ai_ocr()
                refresh_monitor()

            tk.Button(control_frame, text="Refresh", command=refresh_monitor,
                     bg='#4a90e2', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
            tk.Button(control_frame, text="Clear Log", command=clear_log,
                     bg='#e74c3c', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
            tk.Button(control_frame, text="Test Detection", command=test_detection,
                     bg='#27ae60', fg='white', font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)

            # Auto-refresh every 2 seconds
            def auto_refresh():
                if monitor_window.winfo_exists():
                    refresh_monitor()
                    monitor_window.after(2000, auto_refresh)

            monitor_window.after(1000, auto_refresh)  # Start after 1 second

        except Exception as e:
            error_msg = f"Failed to open AI OCR monitor: {str(e)}"
            print(error_msg)
            messagebox.showerror("Monitor Error", error_msg)

    def run(self):
        """Run the Config Helper application"""
        self.root.mainloop()


# Main execution
if __name__ == "__main__":
    try:
        app = ConfigHelper()
        app.run()
    except Exception as e:
        print(f"Error starting Config Helper: {str(e)}")
        import traceback
        traceback.print_exc()
