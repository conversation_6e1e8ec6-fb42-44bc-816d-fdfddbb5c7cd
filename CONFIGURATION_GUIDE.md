# Last War Automation - Configuration Guide

This guide explains how to use the new configuration system to set up image templates, text patterns, click coordinates, and scan regions for each automation module.

## Overview

The configuration system allows you to customize each automation module without editing code. You can:

- **Define image templates** for UI element detection
- **Set text patterns** for OCR-based detection
- **Configure click coordinates** for buttons and actions
- **Specify scan regions** to focus detection on specific screen areas
- **Create action sequences** for complex automation workflows

## Getting Started

### 1. Launch the Configuration Editor

```bash
python config_editor_gui.py
```

Or use the launcher:
```bash
python launcher.py
# Then click "Configuration Editor"
```

### 2. Select a Module

Choose a module from the left panel (e.g., "daily_tasks", "help_click", etc.)

## Configuration Types

### Templates Tab

**Purpose**: Define image templates for UI element detection

**How to use**:
1. Click "Add Template" to manually add a template
2. Click "Capture Template" to screenshot and save a UI element
3. Set the matching threshold (0.0-1.0):
   - 0.8 = Good balance (recommended)
   - 0.9+ = Very strict matching
   - 0.7- = More lenient matching
4. Mark as "Required" if the template must be found for the module to run

**Example**: For daily tasks, capture the "Daily Tasks" button image

### Text Patterns Tab

**Purpose**: Define text strings to search for using OCR

**How to use**:
1. Click "Add Text Pattern"
2. Enter the text to search for (e.g., "Daily Tasks", "Claim", "Complete")
3. Select the scan region where this text appears
4. Mark as "Required" if this text must be found

**Example**: Search for "Claim" text in the center region

### Click Coordinates Tab

**Purpose**: Define specific screen positions to click

**How to use**:
1. Click "Add Coordinate" to manually enter X,Y positions
2. Click "Capture Coordinate" to click on screen and save the position
3. Give each coordinate a descriptive name
4. Add a description for reference

**Example**: Save the position of the "Close" button as "daily_tasks_close"

### Scan Regions Tab

**Purpose**: Define screen areas to focus detection on

**How to use**:
1. Click "Add Region" to manually define a rectangular area
2. Click "Capture Region" to select an area on screen
3. Name each region descriptively
4. Use "Preview Region" to see what the region captures

**Common regions**:
- `top_bar`: Top of screen (0, 0, 800, 100)
- `center`: Middle area (200, 150, 400, 400)
- `bottom_bar`: Bottom of screen (0, 500, 800, 100)

### Actions Tab

**Purpose**: Define step-by-step automation sequences

**Action types**:
- `navigate`: Click on a coordinate to open a menu
- `click`: Click on a specific coordinate
- `wait`: Pause for a specified time
- `template_match`: Find and click on a template
- `text_search`: Search for text in regions
- `close`: Close the current interface

**Example action sequence for daily tasks**:
1. `navigate` → `daily_tasks_button` (wait: 2.0)
2. `template_match` → `claim_reward`
3. `close` → `esc` method

## Best Practices

### Template Creation
1. **Use high-quality screenshots**: Clear, uncompressed PNG images
2. **Crop tightly**: Include only the essential UI element
3. **Avoid dynamic content**: Don't include changing numbers or text
4. **Test different game states**: Capture templates in various UI states

### Coordinate Setup
1. **Use relative positions**: Consider different screen resolutions
2. **Test thoroughly**: Click coordinates may vary between systems
3. **Add descriptions**: Document what each coordinate does
4. **Group related coordinates**: Keep module coordinates organized

### Region Configuration
1. **Start with common regions**: top_bar, center, bottom_bar
2. **Avoid overlapping regions**: Keep regions distinct
3. **Size appropriately**: Not too small (miss content) or too large (slow)
4. **Test with preview**: Always preview regions before using

### Action Sequences
1. **Keep it simple**: Start with basic sequences
2. **Add wait times**: Allow UI to load between actions
3. **Handle failures**: Use fallback actions when possible
4. **Test incrementally**: Test each action individually first

## Module-Specific Examples

### Daily Tasks Module

**Templates needed**:
- `daily_tasks_button.png` - Main daily tasks button
- `claim_reward.png` - Claim button for rewards
- `complete_task.png` - Complete button for tasks

**Text patterns**:
- "Daily Tasks" in `top_bar` region (required)
- "Claim" in `center` region
- "Complete" in `center` region

**Coordinates**:
- `daily_tasks_menu`: (400, 100) - Main menu button
- `close_button`: (750, 50) - Close window button

**Regions**:
- `task_list`: (150, 200, 500, 300) - Area with task list
- `reward_area`: (200, 400, 400, 100) - Reward claim area

### Help Click Module

**Templates needed**:
- `help_button.png` - Individual help buttons
- `help_all_button.png` - Help all button
- `alliance_help.png` - Alliance help icon

**Text patterns**:
- "Help" in `full_screen` region (required)
- "Help All" in `bottom_bar` region

**Coordinates**:
- `help_all`: (400, 500) - Help all button

## Troubleshooting

### Templates Not Found
- Check template file exists in `templates/` folder
- Verify template threshold (try lowering to 0.7)
- Ensure template matches current game UI
- Test template with "Test Template" button

### Coordinates Not Working
- Verify screen resolution matches when coordinates were captured
- Test coordinates with "Test Click" button
- Check if UI layout has changed
- Recapture coordinates if needed

### Text Not Detected
- Test OCR with "Test OCR" button
- Ensure text region covers the text area
- Check if text is clear and readable
- Verify OCR language settings

### Actions Failing
- Test each action individually
- Add appropriate wait times between actions
- Check if UI state matches expected conditions
- Use fallback actions for reliability

## Advanced Features

### Configuration Import/Export
- Export module configs for backup
- Share configurations between systems
- Import pre-made configurations

### Validation
- Use "Validate Config" to check for issues
- Verify all required templates exist
- Check coordinate and region validity

### Batch Operations
- "Enable All" / "Disable All" modules
- "Save All" configurations at once
- "Export All" for complete backup

## Tips for Success

1. **Start simple**: Begin with basic templates and coordinates
2. **Test frequently**: Validate each configuration change
3. **Document everything**: Use descriptions and comments
4. **Backup configurations**: Export configs before major changes
5. **Iterate gradually**: Add complexity step by step
6. **Monitor logs**: Check automation logs for issues
7. **Update regularly**: Keep configs current with game updates

## Getting Help

If you encounter issues:
1. Check the validation results
2. Review the troubleshooting section
3. Test individual components
4. Check log files for error messages
5. Verify all dependencies are installed

The configuration system is designed to be flexible and powerful while remaining user-friendly. Take time to explore each feature and build configurations that work reliably for your specific setup.
