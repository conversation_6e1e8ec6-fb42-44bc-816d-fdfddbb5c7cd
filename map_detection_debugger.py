#!/usr/bin/env python3
"""
Real-time Map Detection Debugger
- GUI with live coordinate adjustment
- Multiple detection methods
- Real-time scanning and results
- Size adjustment controls
"""
import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import cv2
import numpy as np
import pytesseract
import os
from datetime import datetime
import threading
import time
from PIL import Image, ImageTk
import keyboard

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Create debug folder
DEBUG_FOLDER = "debug_screenshots"
if not os.path.exists(DEBUG_FOLDER):
    os.makedirs(DEBUG_FOLDER)

class MapDetectionDebugger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Map Detection Debugger")
        self.root.geometry("800x600")
        
        # Current coordinates
        self.coordinates = {
            'M1': [1079, 833],
            'M2': [1245, 835], 
            'M3': [1412, 835],
            'M4': [1577, 833],
            'M5': [1163, 994],
            'M6': [1329, 994],
            'M7': [1495, 994]
        }
        
        # Settings
        self.capture_size = tk.IntVar(value=25)
        self.detection_method = tk.StringVar(value="OCR")
        self.selected_map = tk.StringVar(value="M1")
        
        # Results storage
        self.last_results = {}
        self.current_images = {}

        # Mouse capture state
        self.capturing_mouse = False

        self.setup_gui()
        self.setup_keyboard_listener()
        
    def setup_gui(self):
        """Setup the GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="10")
        settings_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Capture size
        ttk.Label(settings_frame, text="Capture Size:").grid(row=0, column=0, sticky=tk.W)
        size_frame = ttk.Frame(settings_frame)
        size_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        for size in [15, 20, 25, 30, 35, 40]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.capture_size, 
                           value=size).pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection method
        ttk.Label(settings_frame, text="Method:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        method_frame = ttk.Frame(settings_frame)
        method_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        ttk.Radiobutton(method_frame, text="OCR", variable=self.detection_method, 
                       value="OCR").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Pixel", variable=self.detection_method, 
                       value="Pixel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Both", variable=self.detection_method, 
                       value="Both").pack(side=tk.LEFT)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="SCAN ALL", command=self.scan_all, 
                  width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Live Scan", command=self.toggle_live_scan, 
                  width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Save Results", command=self.save_results, 
                  width=15).pack(side=tk.LEFT)
        
        # Coordinates frame
        coord_frame = ttk.LabelFrame(main_frame, text="Coordinates", padding="10")
        coord_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Map selection
        ttk.Label(coord_frame, text="Selected:").grid(row=0, column=0, sticky=tk.W)
        map_combo = ttk.Combobox(coord_frame, textvariable=self.selected_map, 
                                values=list(self.coordinates.keys()), width=10)
        map_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        map_combo.bind('<<ComboboxSelected>>', self.on_map_selected)
        
        # Coordinate adjustment
        ttk.Label(coord_frame, text="X:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.x_var = tk.IntVar(value=self.coordinates['M1'][0])
        x_spin = ttk.Spinbox(coord_frame, from_=0, to=3000, textvariable=self.x_var, width=10)
        x_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        ttk.Label(coord_frame, text="Y:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.y_var = tk.IntVar(value=self.coordinates['M1'][1])
        y_spin = ttk.Spinbox(coord_frame, from_=0, to=2000, textvariable=self.y_var, width=10)
        y_spin.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # Update coordinate button
        ttk.Button(coord_frame, text="Update Coord", 
                  command=self.update_coordinate).grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        # Capture current position button
        ttk.Button(coord_frame, text="Capture Mouse (Press ENTER)",
                  command=self.start_mouse_capture).grid(row=4, column=0, columnspan=2, pady=(5, 0))

        # Status label for mouse capture
        self.capture_status = ttk.Label(coord_frame, text="", foreground="blue")
        self.capture_status.grid(row=5, column=0, columnspan=2, pady=(5, 0))

        # Right side frame for results and images
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Results frame
        results_frame = ttk.LabelFrame(right_frame, text="Results", padding="10")
        results_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Image frame
        image_frame = ttk.LabelFrame(right_frame, text="Captured Image", padding="10")
        image_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Results text area
        self.results_text = tk.Text(results_frame, width=35, height=20, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Image display area
        self.image_label = ttk.Label(image_frame, text="No image captured", anchor="center")
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Image info
        self.image_info = ttk.Label(image_frame, text="", font=('Consolas', 8))
        self.image_info.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        coord_frame.rowconfigure(6, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.columnconfigure(1, weight=1)
        right_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)
        
        # Live scan state
        self.live_scanning = False
        self.live_thread = None

    def setup_keyboard_listener(self):
        """Setup keyboard listener for ENTER key"""
        def on_key_event(event):
            if event.name == 'enter' and event.event_type == 'down' and self.capturing_mouse:
                self.capture_mouse_position()

        keyboard.on_press_key('enter', lambda _: self.capture_mouse_position() if self.capturing_mouse else None)

    def start_mouse_capture(self):
        """Start mouse capture mode"""
        self.capturing_mouse = True
        self.capture_status.config(text="Position mouse and press ENTER", foreground="red")
        self.root.after(10000, self.stop_mouse_capture)  # Auto-stop after 10 seconds

    def stop_mouse_capture(self):
        """Stop mouse capture mode"""
        self.capturing_mouse = False
        self.capture_status.config(text="", foreground="blue")

    def display_image(self, image_array, info_text=""):
        """Display captured image in GUI"""
        try:
            # Resize image for display (scale up for better visibility)
            height, width = image_array.shape[:2]
            scale = min(200 // width, 200 // height, 8)  # Max 200px, max 8x scale
            new_width = width * scale
            new_height = height * scale

            resized = cv2.resize(image_array, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Convert to PIL Image
            if len(resized.shape) == 3:
                resized_rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(resized_rgb)
            else:
                pil_image = Image.fromarray(resized)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep reference

            # Update info
            self.image_info.config(text=f"Size: {width}x{height} (scaled {scale}x)\n{info_text}")

        except Exception as e:
            self.image_label.config(text=f"Image error: {e}")
            self.image_info.config(text="")
        
    def detect_number(self, x, y, size, method):
        """Detect number at coordinate using specified method"""
        try:
            # Capture region
            capture_x = x - size // 2
            capture_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            results = {}
            
            if method in ["OCR", "Both"]:
                # OCR detection
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(gray, config=config).strip()
                if text.isdigit():
                    results['OCR'] = int(text)
                else:
                    results['OCR'] = f"FAIL: '{text}'"
            
            if method in ["Pixel", "Both"]:
                # Pixel analysis
                _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
                dark_pixels = cv2.countNonZero(binary)
                total_pixels = size * size
                density = dark_pixels / total_pixels if total_pixels > 0 else 0
                
                if density < 0.02:
                    pixel_result = 1
                elif density < 0.04:
                    pixel_result = 2
                elif density < 0.06:
                    pixel_result = 3
                elif density < 0.08:
                    pixel_result = 4
                elif density < 0.10:
                    pixel_result = 5
                elif density < 0.12:
                    pixel_result = 6
                else:
                    pixel_result = 7
                
                results['Pixel'] = f"{pixel_result} (d={density:.3f})"
            
            # Save debug image
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"{DEBUG_FOLDER}/debug_{x}_{y}_{size}_{timestamp}.png"
            cv2.imwrite(filename, image)

            # Display image in GUI
            info_text = f"Coord: ({x}, {y})\nSize: {size}x{size}"
            self.display_image(image, info_text)

            return results, filename
            
        except Exception as e:
            return {"Error": str(e)}, None
    
    def scan_all(self):
        """Scan all coordinates"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"SCANNING ALL - Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text.insert(tk.END, f"Method: {self.detection_method.get()}\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")
        
        self.last_results = {}
        
        for map_name, (x, y) in self.coordinates.items():
            results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
            self.last_results[map_name] = results
            
            self.results_text.insert(tk.END, f"{map_name} at ({x}, {y}):\n")
            for method, result in results.items():
                self.results_text.insert(tk.END, f"  {method}: {result}\n")
            if filename:
                self.results_text.insert(tk.END, f"  Saved: {os.path.basename(filename)}\n")
            self.results_text.insert(tk.END, "\n")
            
            self.results_text.see(tk.END)
            self.root.update()
    
    def toggle_live_scan(self):
        """Toggle live scanning"""
        if not self.live_scanning:
            self.live_scanning = True
            self.live_thread = threading.Thread(target=self.live_scan_loop, daemon=True)
            self.live_thread.start()
        else:
            self.live_scanning = False
    
    def live_scan_loop(self):
        """Live scanning loop"""
        while self.live_scanning:
            try:
                selected = self.selected_map.get()
                if selected in self.coordinates:
                    x, y = self.coordinates[selected]
                    results, _ = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
                    
                    # Update results in main thread
                    self.root.after(0, self.update_live_results, selected, x, y, results)
                
                time.sleep(1)  # Scan every second
            except:
                break
    
    def update_live_results(self, map_name, x, y, results):
        """Update live results display"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"LIVE SCAN - {map_name} at ({x}, {y})\n")
        self.results_text.insert(tk.END, f"Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text.insert(tk.END, "=" * 30 + "\n\n")
        
        for method, result in results.items():
            self.results_text.insert(tk.END, f"{method}: {result}\n")
    
    def on_map_selected(self, event=None):
        """Update coordinate fields when map is selected"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            x, y = self.coordinates[selected]
            self.x_var.set(x)
            self.y_var.set(y)
    
    def update_coordinate(self):
        """Update selected coordinate"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            self.coordinates[selected] = [self.x_var.get(), self.y_var.get()]
            messagebox.showinfo("Updated", f"{selected} updated to ({self.x_var.get()}, {self.y_var.get()})")
    
    def capture_mouse_position(self):
        """Capture current mouse position"""
        if self.capturing_mouse:
            x, y = pyautogui.position()
            self.x_var.set(x)
            self.y_var.set(y)
            self.stop_mouse_capture()

            # Immediately test the new position
            selected = self.selected_map.get()
            if selected in self.coordinates:
                self.coordinates[selected] = [x, y]
                results, _ = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())

                # Show results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, f"CAPTURED {selected} at ({x}, {y})\n")
                self.results_text.insert(tk.END, "=" * 30 + "\n\n")
                for method, result in results.items():
                    self.results_text.insert(tk.END, f"{method}: {result}\n")

            messagebox.showinfo("Captured", f"{selected} position captured: ({x}, {y})")
    
    def save_results(self):
        """Save current results to file"""
        if not self.last_results:
            messagebox.showwarning("No Results", "No scan results to save")
            return
        
        filename = f"map_detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(f"Map Detection Results - {datetime.now()}\n")
            f.write(f"Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
            f.write(f"Method: {self.detection_method.get()}\n")
            f.write("=" * 50 + "\n\n")
            
            for map_name, results in self.last_results.items():
                x, y = self.coordinates[map_name]
                f.write(f"{map_name} at ({x}, {y}):\n")
                for method, result in results.items():
                    f.write(f"  {method}: {result}\n")
                f.write("\n")
        
        messagebox.showinfo("Saved", f"Results saved to {filename}")
    
    def run(self):
        """Run the debugger"""
        self.root.mainloop()

if __name__ == "__main__":
    debugger = MapDetectionDebugger()
    debugger.run()
