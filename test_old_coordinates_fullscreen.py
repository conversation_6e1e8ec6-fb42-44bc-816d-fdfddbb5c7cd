#!/usr/bin/env python3
"""
Test the OLD working coordinates from backup with full screen resolution
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# OLD WORKING COORDINATES from backup_before_scanner_refactor\module_configs_backup.json
OLD_MAP_COORDINATES = {
    'M1': {
        'quantity_pos': (1068, 817),
        'click_pos': (1026, 765)
    },
    'M2': {
        'quantity_pos': (1224, 817),
        'click_pos': (1188, 772)
    },
    'M3': {
        'quantity_pos': (1384, 820),
        'click_pos': (1342, 773)
    },
    'M4': {
        'quantity_pos': (1533, 816),
        'click_pos': (1497, 773)
    },
    'M5': {
        'quantity_pos': (1144, 971),
        'click_pos': (1106, 923)
    },
    'M6': {
        'quantity_pos': (1300, 971),
        'click_pos': (1262, 923)
    },
    'M7': {
        'quantity_pos': (1456, 971),
        'click_pos': (1418, 923)
    }
}

def read_map_number_enhanced(x, y, size=25):
    """Read number with ENHANCED IMAGE PROCESSING for full screen"""
    try:
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save debug image
        cv2.imwrite(f"debug_fullscreen_{x}_{y}.png", image)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Try multiple processing methods
        methods = []
        
        # 1. Simple grayscale
        methods.append(("gray", gray))
        
        # 2. Adaptive threshold
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        methods.append(("adaptive", adaptive))
        
        # 3. OTSU threshold
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        methods.append(("otsu", otsu))
        
        # 4. Morphological processing
        kernel = np.ones((2,2), np.uint8)
        morph = cv2.morphologyEx(otsu, cv2.MORPH_CLOSE, kernel)
        methods.append(("morph", morph))
        
        # 5. Inverted
        inverted = cv2.bitwise_not(gray)
        methods.append(("inverted", inverted))
        
        # Try OCR on each processed image
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789',
            '--psm 10 -c tessedit_char_whitelist=0123456789'
        ]
        
        for method_name, processed_image in methods:
            # Save processed image for debugging
            cv2.imwrite(f"debug_fullscreen_{method_name}_{x}_{y}.png", processed_image)
            
            for config in configs:
                text = pytesseract.image_to_string(processed_image, config=config).strip()
                if text.isdigit():
                    print(f"    ✅ SUCCESS with {method_name} method: '{text}'")
                    return int(text)
        
        return None
        
    except Exception as e:
        print(f"    Error reading {x},{y}: {e}")
        return None

def test_old_coordinates():
    """Test the old working coordinates with full screen"""
    print("🎯 Testing OLD WORKING COORDINATES with Full Screen")
    print("=" * 55)
    print("Make sure the game is in FULL SCREEN mode!")
    print()
    
    results = {}
    
    for map_name, coords in OLD_MAP_COORDINATES.items():
        x, y = coords['quantity_pos']
        print(f"Testing {map_name} at ({x}, {y})...")
        
        number = read_map_number_enhanced(x, y)
        if number is not None:
            results[map_name] = number
            print(f"  ✅ {map_name}: {number} pieces")
        else:
            print(f"  ❌ {map_name}: Failed to read")
    
    print(f"\n📊 RESULTS:")
    print(f"Success rate: {len(results)}/7 pieces ({len(results)*100//7}%)")
    print(f"Detected quantities: {results}")
    
    if len(results) >= 5:  # If we get 5+ pieces working
        print("\n🎉 EXCELLENT! Old coordinates work much better with full screen!")
        print("📁 Check debug images: debug_fullscreen_*.png")
        
        # Find best trade
        if len(results) >= 2:
            lowest = min(results.items(), key=lambda x: x[1])
            highest = max(results.items(), key=lambda x: x[1])
            
            if lowest[1] < highest[1]:
                print(f"\n💡 Optimal Trade:")
                print(f"  Give: {lowest[0]} ({lowest[1]} pieces)")
                print(f"  Get: {highest[0]} ({highest[1]} pieces)")
                print(f"  Benefit: +{highest[1] - lowest[1]} pieces")
    
    return results

def test_specific_coordinate():
    """Test a specific coordinate interactively"""
    print("Position your mouse over a map piece number and press ENTER")
    input("Press ENTER when ready: ")
    
    x, y = pyautogui.position()
    print(f"Testing coordinate ({x}, {y})...")
    
    number = read_map_number_enhanced(x, y)
    if number is not None:
        print(f"✅ Successfully read: {number}")
    else:
        print("❌ Failed to read")
    
    print(f"📁 Check debug images: debug_fullscreen_*_{x}_{y}.png")

if __name__ == "__main__":
    print("Choose test method:")
    print("1. Test all old coordinates automatically")
    print("2. Test specific coordinate (mouse position)")
    
    choice = input("Enter choice (1 or 2): ")
    
    if choice == "1":
        test_old_coordinates()
    else:
        test_specific_coordinate()
    
    print("\n✅ Test completed! Check debug images for visual analysis.")
