﻿#!/usr/bin/env python3
"""
Map Trade Module - Uses configuration from module_configs.json with EXACT BACKUP METHOD
"""

import time
import logging
import pyautogui
import cv2
import numpy as np
import pytesseract
from typing import Dict, Any, Optional, Tuple
from base_module import BaseModule
from module_config_manager import ModuleConfigManager

class MapTradeModule(BaseModule):
    def __init__(self):
        super().__init__(name="map_trade", priority=2, enabled=True)
        
        # Configuration manager
        self.config_manager = ModuleConfigManager()
        
        # Load module configuration
        self.module_config = self.config_manager.get_module_config("map_trade")
        if not self.module_config:
            raise ValueError("Map trade configuration not found in module_configs.json")
        
        # Timing settings from config
        self.execution_interval = self.module_config.get('cooldown', 30.0)
        self.last_execution_time = 0
        
        # Execution state
        self.is_executing = False
        self.current_step = 0

        # Logger for this module
        self.logger = logging.getLogger("LastWar.map_trade")

        # Configure Tesseract path
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Load coordinates from configuration
        self.map_coordinates = self._load_map_coordinates()
        self.button_coordinates = self._load_button_coordinates()

        # Persistent counters
        self.total_trades_completed = 0
        self.total_red_thumb_clicks = 0

        self.logger.info(f"Map Trade module initialized with {len(self.map_coordinates)} map pieces from config")

    def manual_trigger_map_trade(self):
        """Manual trigger for map trade (GUI compatibility method)"""
        try:
            self.logger.info("Manual map trade triggered")
            self.last_execution_time = 0  # Reset cooldown
            return self.execute({})
        except Exception as e:
            self.logger.error(f"Manual trigger failed: {e}")
            return False
