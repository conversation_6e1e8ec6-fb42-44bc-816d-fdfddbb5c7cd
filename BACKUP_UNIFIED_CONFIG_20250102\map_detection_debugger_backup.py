#!/usr/bin/env python3
"""
Comprehensive Automation Debugger Tool
- Live mouse coordinates tracking
- Template detection testing
- OCR and pixel analysis
- Screenshot capture and template creation
- Coordinate capture and management
- Scaling options for detection testing
- Multi-purpose debugging for automation development
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import pyautogui
import cv2
import numpy as np
import pytesseract
import os
from datetime import datetime
import threading
import time
from PIL import Image, ImageTk
import keyboard
import json

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Create necessary folders
DEBUG_FOLDER = "debug_screenshots"
TEMPLATE_FOLDER = "templates"
COORDS_FOLDER = "debug_coords"

for folder in [DEBUG_FOLDER, TEMPLATE_FOLDER, COORDS_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

class AutomationDebugger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Comprehensive Automation Debugger")
        self.root.geometry("1200x800")

        # Current coordinates (expandable)
        self.coordinates = {
            'M1': [1079, 833],
            'M2': [1245, 835],
            'M3': [1412, 835],
            'M4': [1577, 833],
            'M5': [1163, 994],
            'M6': [1329, 994],
            'M7': [1495, 994]
        }

        # Settings
        self.capture_size = tk.IntVar(value=25)
        self.detection_method = tk.StringVar(value="OCR")
        self.selected_map = tk.StringVar(value="M1")
        self.use_scaling = tk.BooleanVar(value=False)
        self.scale_factor = tk.DoubleVar(value=2.0)
        self.display_zoom = tk.DoubleVar(value=1.0)
        self.invert_colors = tk.BooleanVar(value=False)
        self.ocr_mode = tk.StringVar(value="Standard")
        self.morphology_enabled = tk.BooleanVar(value=False)
        self.morphology_operation = tk.StringVar(value="Erode")
        self.morphology_strength = tk.IntVar(value=1)

        # Results storage
        self.last_results = {}
        self.current_images = {}
        self.captured_image = None

        # Mouse tracking
        self.capturing_mouse = False
        self.live_mouse_tracking = False
        self.mouse_thread = None
        self.template_capture_mode = False
        self.live_template_preview = False
        self.template_preview_thread = None

        # Live coordinate display
        self.current_mouse_x = tk.StringVar(value="0")
        self.current_mouse_y = tk.StringVar(value="0")
        self.saved_coords = tk.StringVar(value="Click 'Save Coord' to capture")

        self.setup_gui()
        self.setup_keyboard_listener()
        self.load_saved_coordinates()
        
    def setup_gui(self):
        """Setup the comprehensive GUI interface"""
        # Top control frame for window management
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        ttk.Button(top_frame, text="Focus Game Window",
                  command=self.focus_game_window,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(top_frame, text="Comprehensive Automation Debugger",
                 font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(20, 0))

        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))

        # Tab 1: Detection Testing
        detection_frame = ttk.Frame(notebook)
        notebook.add(detection_frame, text="Detection Testing")

        # Tab 2: Live Mouse Tracking
        mouse_frame = ttk.Frame(notebook)
        notebook.add(mouse_frame, text="Live Mouse Tracking")

        # Tab 3: Template Creator
        template_frame = ttk.Frame(notebook)
        notebook.add(template_frame, text="Template Creator")

        # Setup each tab
        self.setup_detection_tab(detection_frame)
        self.setup_mouse_tab(mouse_frame)
        self.setup_template_tab(template_frame)

    def setup_detection_tab(self, parent):
        """Setup detection testing tab"""
        # Main frame
        main_frame = ttk.Frame(parent, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Detection Settings", padding="10")
        settings_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Capture size
        ttk.Label(settings_frame, text="Capture Size:").grid(row=0, column=0, sticky=tk.W)
        size_frame = ttk.Frame(settings_frame)
        size_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        for size in [15, 20, 25, 30, 35, 40, 50]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.capture_size,
                           value=size).pack(side=tk.LEFT, padx=(0, 5))

        # Detection method
        ttk.Label(settings_frame, text="Method:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        method_frame = ttk.Frame(settings_frame)
        method_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(method_frame, text="OCR", variable=self.detection_method,
                       value="OCR").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Pixel", variable=self.detection_method,
                       value="Pixel").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(method_frame, text="Both", variable=self.detection_method,
                       value="Both").pack(side=tk.LEFT)

        # Processing options
        ttk.Label(settings_frame, text="Processing:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        processing_frame = ttk.Frame(settings_frame)
        processing_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(processing_frame, text="Scale for OCR", variable=self.use_scaling).pack(side=tk.LEFT)
        ttk.Label(processing_frame, text="Factor:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=5.0, increment=0.5, width=6,
                   textvariable=self.scale_factor).pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(processing_frame, text="Display Zoom:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(processing_frame, from_=1.0, to=10.0, increment=1.0, width=6,
                   textvariable=self.display_zoom).pack(side=tk.LEFT)

        # Color inversion option
        ttk.Label(settings_frame, text="Colors:").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        color_frame = ttk.Frame(settings_frame)
        color_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(color_frame, text="Invert White to Black",
                       variable=self.invert_colors).pack(side=tk.LEFT)

        # OCR fine-tuning options
        ttk.Label(settings_frame, text="OCR Mode:").grid(row=4, column=0, sticky=tk.W, pady=(10, 0))
        ocr_frame = ttk.Frame(settings_frame)
        ocr_frame.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Radiobutton(ocr_frame, text="Standard", variable=self.ocr_mode,
                       value="Standard").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thick/Bold", variable=self.ocr_mode,
                       value="Thick").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ocr_frame, text="Thin/Light", variable=self.ocr_mode,
                       value="Thin").pack(side=tk.LEFT)

        # Morphology options for thick text
        ttk.Label(settings_frame, text="Morphology:").grid(row=5, column=0, sticky=tk.W, pady=(10, 0))
        morph_frame = ttk.Frame(settings_frame)
        morph_frame.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Checkbutton(morph_frame, text="Enable",
                       variable=self.morphology_enabled).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Radiobutton(morph_frame, text="Erode", variable=self.morphology_operation,
                       value="Erode").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Dilate", variable=self.morphology_operation,
                       value="Dilate").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(morph_frame, text="Open", variable=self.morphology_operation,
                       value="Open").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(morph_frame, text="Strength:").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Spinbox(morph_frame, from_=1, to=5, width=4,
                   textvariable=self.morphology_strength).pack(side=tk.LEFT)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(control_frame, text="SCAN ALL", command=self.scan_all,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="Live Scan", command=self.toggle_live_scan,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="Save Results", command=self.save_results,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="Load Coords", command=self.load_coordinates_file,
                  width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="Save Coords", command=self.save_coordinates_file,
                  width=12).pack(side=tk.LEFT)
        
        # Coordinates frame
        coord_frame = ttk.LabelFrame(main_frame, text="Coordinates", padding="10")
        coord_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Map selection
        ttk.Label(coord_frame, text="Selected:").grid(row=0, column=0, sticky=tk.W)
        map_combo = ttk.Combobox(coord_frame, textvariable=self.selected_map,
                                values=list(self.coordinates.keys()), width=10)
        map_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        map_combo.bind('<<ComboboxSelected>>', self.on_map_selected)

        # Add new coordinate
        ttk.Button(coord_frame, text="Add New", command=self.add_new_coordinate,
                  width=10).grid(row=0, column=2, padx=(10, 0))

        # Coordinate adjustment
        ttk.Label(coord_frame, text="X:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.x_var = tk.IntVar(value=self.coordinates['M1'][0])
        x_spin = ttk.Spinbox(coord_frame, from_=0, to=3000, textvariable=self.x_var, width=10)
        x_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        ttk.Label(coord_frame, text="Y:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.y_var = tk.IntVar(value=self.coordinates['M1'][1])
        y_spin = ttk.Spinbox(coord_frame, from_=0, to=2000, textvariable=self.y_var, width=10)
        y_spin.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # Update coordinate button
        ttk.Button(coord_frame, text="Update Coord",
                  command=self.update_coordinate).grid(row=3, column=0, columnspan=2, pady=(10, 0))

        # Capture current position button
        ttk.Button(coord_frame, text="Capture Mouse (ENTER)",
                  command=self.start_mouse_capture).grid(row=4, column=0, columnspan=2, pady=(5, 0))

        # Status label for mouse capture
        self.capture_status = ttk.Label(coord_frame, text="", foreground="blue")
        self.capture_status.grid(row=5, column=0, columnspan=2, pady=(5, 0))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))

        # Image frame
        image_frame = ttk.LabelFrame(main_frame, text="Captured Image", padding="10")
        image_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # Results text area
        self.results_text = tk.Text(results_frame, width=30, height=20, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Image display area
        self.image_label = ttk.Label(image_frame, text="No image captured", anchor="center")
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Image info
        self.image_info = ttk.Label(image_frame, text="", font=('Consolas', 8))
        self.image_info.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # Image buttons
        img_button_frame = ttk.Frame(image_frame)
        img_button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(img_button_frame, text="Save as Template",
                  command=self.save_as_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(img_button_frame, text="Save Debug",
                  command=self.save_debug_image).pack(side=tk.LEFT)

        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.rowconfigure(2, weight=1)
        coord_frame.rowconfigure(6, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # Live scan state
        self.live_scanning = False
        self.live_thread = None

    def setup_mouse_tab(self, parent):
        """Setup live mouse tracking tab"""
        main_frame = ttk.Frame(parent, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Control frame
        control_frame = ttk.LabelFrame(main_frame, text="Mouse Tracking Controls", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(control_frame, text="Start Live Tracking",
                  command=self.start_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Stop Tracking",
                  command=self.stop_live_mouse_tracking).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Save Current Coord",
                  command=self.save_current_mouse_coord).pack(side=tk.LEFT)

        # Live coordinates display
        coord_display_frame = ttk.LabelFrame(main_frame, text="Live Mouse Position", padding="10")
        coord_display_frame.pack(fill=tk.X, pady=(0, 10))

        # Current position
        pos_frame = ttk.Frame(coord_display_frame)
        pos_frame.pack(fill=tk.X)

        ttk.Label(pos_frame, text="X:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_x,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(pos_frame, text="Y:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        ttk.Label(pos_frame, textvariable=self.current_mouse_y,
                 font=('Arial', 14, 'bold'), foreground='blue').pack(side=tk.LEFT, padx=(5, 0))

        # Saved coordinates
        saved_frame = ttk.LabelFrame(main_frame, text="Saved Coordinates", padding="10")
        saved_frame.pack(fill=tk.BOTH, expand=True)

        # Coordinate input/display
        input_frame = ttk.Frame(saved_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="Coordinates:").pack(side=tk.LEFT)
        self.coord_entry = ttk.Entry(input_frame, textvariable=self.saved_coords, width=30)
        self.coord_entry.pack(side=tk.LEFT, padx=(10, 10), fill=tk.X, expand=True)

        ttk.Button(input_frame, text="Copy",
                  command=self.copy_coordinates).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(input_frame, text="Clear",
                  command=self.clear_coordinates).pack(side=tk.LEFT)

        # Coordinate history
        history_frame = ttk.Frame(saved_frame)
        history_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(history_frame, text="Coordinate History:").pack(anchor=tk.W)

        self.coord_history = tk.Listbox(history_frame, height=15, font=('Consolas', 10))
        coord_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.coord_history.yview)
        self.coord_history.configure(yscrollcommand=coord_scrollbar.set)

        self.coord_history.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coord_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # History buttons
        hist_button_frame = ttk.Frame(saved_frame)
        hist_button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(hist_button_frame, text="Use Selected",
                  command=self.use_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Delete Selected",
                  command=self.delete_selected_coord).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(hist_button_frame, text="Save History",
                  command=self.save_coord_history).pack(side=tk.LEFT)

    def setup_template_tab(self, parent):
        """Setup template creation tab"""
        main_frame = ttk.Frame(parent, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Capture controls
        capture_frame = ttk.LabelFrame(main_frame, text="Screen Capture", padding="10")
        capture_frame.pack(fill=tk.X, pady=(0, 10))

        # Capture size options
        size_frame = ttk.Frame(capture_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="Capture Size:").pack(side=tk.LEFT)
        self.template_size = tk.IntVar(value=50)

        for size in [25, 50, 75, 100, 150, 200]:
            ttk.Radiobutton(size_frame, text=f"{size}x{size}", variable=self.template_size,
                           value=size).pack(side=tk.LEFT, padx=(10, 5))

        # Capture buttons
        button_frame = ttk.Frame(capture_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Capture at Mouse",
                  command=self.capture_at_mouse).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Capture at Coordinate",
                  command=self.capture_at_coordinate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Full Screen Capture",
                  command=self.capture_full_screen).pack(side=tk.LEFT, padx=(0, 10))

        # Template capture mode toggle
        mode_frame = ttk.Frame(capture_frame)
        mode_frame.pack(fill=tk.X, pady=(10, 0))

        self.template_mode_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(mode_frame, text="Live Capture Mode (Press ENTER at mouse position)",
                       variable=self.template_mode_var,
                       command=self.toggle_template_capture_mode).pack(side=tk.LEFT)

        self.template_mode_status = ttk.Label(mode_frame, text="", foreground="blue")
        self.template_mode_status.pack(side=tk.LEFT, padx=(10, 0))

        # Live preview toggle
        preview_mode_frame = ttk.Frame(capture_frame)
        preview_mode_frame.pack(fill=tk.X, pady=(5, 0))

        self.live_preview_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(preview_mode_frame, text="Live Preview Mode (Show what's under mouse)",
                       variable=self.live_preview_var,
                       command=self.toggle_live_template_preview).pack(side=tk.LEFT)

        self.live_preview_status = ttk.Label(preview_mode_frame, text="", foreground="green")
        self.live_preview_status.pack(side=tk.LEFT, padx=(10, 0))

        # Template preview and save
        template_frame = ttk.LabelFrame(main_frame, text="Template Preview", padding="10")
        template_frame.pack(fill=tk.BOTH, expand=True)

        # Preview area
        preview_frame = ttk.Frame(template_frame)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.template_preview = ttk.Label(preview_frame, text="No template captured",
                                         anchor="center", relief="sunken")
        self.template_preview.pack(fill=tk.BOTH, expand=True)

        # Template info
        self.template_info = ttk.Label(template_frame, text="", font=('Consolas', 9))
        self.template_info.pack(fill=tk.X, pady=(0, 10))

        # Save controls
        save_frame = ttk.Frame(template_frame)
        save_frame.pack(fill=tk.X)

        ttk.Label(save_frame, text="Template Name:").pack(side=tk.LEFT)
        self.template_name = tk.StringVar(value="new_template")
        ttk.Entry(save_frame, textvariable=self.template_name, width=20).pack(side=tk.LEFT, padx=(10, 10))

        ttk.Button(save_frame, text="Save to Templates",
                  command=self.save_template_to_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(save_frame, text="Test Template",
                  command=self.test_template_detection).pack(side=tk.LEFT)

    def setup_keyboard_listener(self):
        """Setup keyboard listener for ENTER key"""
        def on_enter_pressed(_):
            if self.capturing_mouse:
                self.capture_mouse_position()
            elif self.live_mouse_tracking:
                self.save_current_mouse_coord()
            elif self.template_capture_mode:
                self.capture_template_at_mouse()

        keyboard.on_press_key('enter', on_enter_pressed)

    def load_saved_coordinates(self):
        """Load saved coordinates from file"""
        try:
            coord_file = os.path.join(COORDS_FOLDER, "saved_coordinates.json")
            if os.path.exists(coord_file):
                with open(coord_file, 'r') as f:
                    saved_coords = json.load(f)
                    self.coordinates.update(saved_coords)
        except Exception as e:
            print(f"Could not load saved coordinates: {e}")

    def add_new_coordinate(self):
        """Add a new coordinate point"""
        name = tk.simpledialog.askstring("New Coordinate", "Enter coordinate name:")
        if name and name not in self.coordinates:
            x, y = pyautogui.position()
            self.coordinates[name] = [x, y]

            # Update combobox
            map_combo = None
            for widget in self.root.winfo_children():
                if isinstance(widget, ttk.Notebook):
                    for tab in widget.tabs():
                        tab_frame = widget.nametowidget(tab)
                        for child in tab_frame.winfo_children():
                            if hasattr(child, 'winfo_children'):
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, ttk.Combobox):
                                        grandchild['values'] = list(self.coordinates.keys())
                                        break

            self.selected_map.set(name)
            self.x_var.set(x)
            self.y_var.set(y)
            messagebox.showinfo("Added", f"Added coordinate '{name}' at ({x}, {y})")

    def load_coordinates_file(self):
        """Load coordinates from JSON file"""
        filename = filedialog.askopenfilename(
            title="Load Coordinates",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    loaded_coords = json.load(f)
                    self.coordinates.update(loaded_coords)
                    messagebox.showinfo("Loaded", f"Loaded {len(loaded_coords)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not load coordinates: {e}")

    def save_coordinates_file(self):
        """Save coordinates to JSON file"""
        filename = filedialog.asksaveasfilename(
            title="Save Coordinates",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.coordinates, f, indent=2)
                    messagebox.showinfo("Saved", f"Saved {len(self.coordinates)} coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save coordinates: {e}")

    def start_live_mouse_tracking(self):
        """Start live mouse position tracking"""
        if not self.live_mouse_tracking:
            self.live_mouse_tracking = True
            self.mouse_thread = threading.Thread(target=self.mouse_tracking_loop, daemon=True)
            self.mouse_thread.start()

    def stop_live_mouse_tracking(self):
        """Stop live mouse position tracking"""
        self.live_mouse_tracking = False

    def mouse_tracking_loop(self):
        """Mouse tracking loop"""
        while self.live_mouse_tracking:
            try:
                x, y = pyautogui.position()
                self.current_mouse_x.set(str(x))
                self.current_mouse_y.set(str(y))
                time.sleep(0.1)  # Update 10 times per second
            except:
                break

    def save_current_mouse_coord(self):
        """Save current mouse coordinates"""
        x = self.current_mouse_x.get()
        y = self.current_mouse_y.get()
        timestamp = datetime.now().strftime("%H:%M:%S")
        coord_text = f"({x}, {y}) - {timestamp}"

        self.saved_coords.set(coord_text)
        self.coord_history.insert(0, coord_text)

        # Keep only last 50 entries
        if self.coord_history.size() > 50:
            self.coord_history.delete(50, tk.END)

    def copy_coordinates(self):
        """Copy coordinates to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(self.saved_coords.get())
        messagebox.showinfo("Copied", "Coordinates copied to clipboard")

    def clear_coordinates(self):
        """Clear coordinate display"""
        self.saved_coords.set("Click 'Save Coord' to capture")

    def use_selected_coord(self):
        """Use selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            coord_text = self.coord_history.get(selection[0])
            self.saved_coords.set(coord_text)

    def delete_selected_coord(self):
        """Delete selected coordinate from history"""
        selection = self.coord_history.curselection()
        if selection:
            self.coord_history.delete(selection[0])

    def save_coord_history(self):
        """Save coordinate history to file"""
        filename = f"coord_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(COORDS_FOLDER, filename)

        try:
            with open(filepath, 'w') as f:
                f.write(f"Coordinate History - {datetime.now()}\n")
                f.write("=" * 50 + "\n\n")

                for i in range(self.coord_history.size()):
                    f.write(f"{self.coord_history.get(i)}\n")

            messagebox.showinfo("Saved", f"History saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save history: {e}")

    def capture_at_mouse(self):
        """Capture template at current mouse position"""
        x, y = pyautogui.position()
        size = self.template_size.get()

        # Calculate capture region
        left = x - size // 2
        top = y - size // 2

        try:
            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture image: {e}")

    def capture_at_coordinate(self):
        """Capture template at specified coordinates"""
        coord_dialog = tk.Toplevel(self.root)
        coord_dialog.title("Enter Coordinates")
        coord_dialog.geometry("300x150")
        coord_dialog.transient(self.root)
        coord_dialog.grab_set()

        ttk.Label(coord_dialog, text="X:").grid(row=0, column=0, padx=10, pady=10)
        x_entry = ttk.Entry(coord_dialog, width=10)
        x_entry.grid(row=0, column=1, padx=10, pady=10)

        ttk.Label(coord_dialog, text="Y:").grid(row=1, column=0, padx=10, pady=10)
        y_entry = ttk.Entry(coord_dialog, width=10)
        y_entry.grid(row=1, column=1, padx=10, pady=10)

        def capture():
            try:
                x = int(x_entry.get())
                y = int(y_entry.get())
                size = self.template_size.get()

                left = x - size // 2
                top = y - size // 2

                screenshot = pyautogui.screenshot(region=(left, top, size, size))
                self.captured_image = screenshot
                self.display_template_preview(screenshot, f"Captured at ({x}, {y})")
                coord_dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Please enter valid coordinates")
            except Exception as e:
                messagebox.showerror("Error", f"Could not capture image: {e}")

        ttk.Button(coord_dialog, text="Capture", command=capture).grid(row=2, column=0, columnspan=2, pady=20)

    def capture_full_screen(self):
        """Capture full screen"""
        try:
            screenshot = pyautogui.screenshot()
            self.captured_image = screenshot
            self.display_template_preview(screenshot, "Full screen capture")
        except Exception as e:
            messagebox.showerror("Error", f"Could not capture screen: {e}")

    def display_template_preview(self, image, info_text):
        """Display captured image in preview"""
        try:
            # Resize for display if too large
            display_image = image.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"{info_text}\nSize: {image.width}x{image.height}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not display image: {e}")

    def save_template_to_folder(self):
        """Save captured image as template"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No image captured")
            return

        name = self.template_name.get().strip()
        if not name:
            messagebox.showwarning("Warning", "Please enter a template name")
            return

        if not name.endswith('.png'):
            name += '.png'

        filepath = os.path.join(TEMPLATE_FOLDER, name)

        try:
            self.captured_image.save(filepath)
            messagebox.showinfo("Saved", f"Template saved as {name}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not save template: {e}")

    def test_template_detection(self):
        """Test template detection on current screen"""
        if not self.captured_image:
            messagebox.showwarning("Warning", "No template captured")
            return

        try:
            # Save temp template
            temp_path = os.path.join(DEBUG_FOLDER, "temp_template.png")
            self.captured_image.save(temp_path)

            # Take screenshot and find template
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.cvtColor(np.array(self.captured_image), cv2.COLOR_RGB2BGR)

            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val > 0.8:
                messagebox.showinfo("Detection Result",
                                  f"Template found!\nConfidence: {max_val:.3f}\nLocation: {max_loc}")
            else:
                messagebox.showinfo("Detection Result",
                                  f"Template not found clearly.\nBest match confidence: {max_val:.3f}")

        except Exception as e:
            messagebox.showerror("Error", f"Could not test template: {e}")

    def save_as_template(self):
        """Save current captured image as template"""
        if hasattr(self, 'current_images') and self.current_images:
            # Get the most recent captured image
            latest_key = max(self.current_images.keys())
            image = self.current_images[latest_key]

            name = tk.simpledialog.askstring("Template Name", "Enter template name:")
            if name:
                if not name.endswith('.png'):
                    name += '.png'

                filepath = os.path.join(TEMPLATE_FOLDER, name)
                try:
                    cv2.imwrite(filepath, image)
                    messagebox.showinfo("Saved", f"Template saved as {name}")
                except Exception as e:
                    messagebox.showerror("Error", f"Could not save template: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save as template")

    def save_debug_image(self):
        """Save current image for debugging"""
        if hasattr(self, 'current_images') and self.current_images:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_capture_{timestamp}.png"
            filepath = os.path.join(DEBUG_FOLDER, filename)

            try:
                latest_key = max(self.current_images.keys())
                image = self.current_images[latest_key]
                cv2.imwrite(filepath, image)
                messagebox.showinfo("Saved", f"Debug image saved as {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save debug image: {e}")
        else:
            messagebox.showwarning("Warning", "No image to save")

    def toggle_template_capture_mode(self):
        """Toggle template capture mode"""
        self.template_capture_mode = self.template_mode_var.get()
        if self.template_capture_mode:
            self.template_mode_status.configure(text="LIVE MODE: Press ENTER to capture", foreground="green")
        else:
            self.template_mode_status.configure(text="", foreground="blue")

    def capture_template_at_mouse(self):
        """Capture template at current mouse position (called by Enter key)"""
        if not self.template_capture_mode:
            return

        try:
            x, y = pyautogui.position()
            size = self.template_size.get()

            # Calculate capture region
            left = x - size // 2
            top = y - size // 2

            screenshot = pyautogui.screenshot(region=(left, top, size, size))
            self.captured_image = screenshot
            self.display_template_preview(screenshot, f"Live captured at ({x}, {y})")

            # Update status
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.template_mode_status.configure(
                text=f"Captured at ({x}, {y}) - {timestamp}",
                foreground="blue"
            )

        except Exception as e:
            messagebox.showerror("Error", f"Could not capture template: {e}")

    def focus_game_window(self):
        """Focus and resize the Last War game window"""
        try:
            import pygetwindow as gw
            import time

            # Search for Last War window with various possible titles
            possible_titles = [
                "Last War-Survival Game",
                "Last War",
                "Survival Game",
                "Last War - Survival Game",
                "LastWar",
                "last war"
            ]

            game_window = None

            # Try to find the window by exact title match first
            for title in possible_titles:
                try:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        game_window = windows[0]
                        print(f"Found game window: '{title}'")
                        break
                except:
                    continue

            # If exact match failed, try partial match
            if not game_window:
                all_windows = gw.getAllWindows()
                for window in all_windows:
                    window_title = window.title.lower()
                    if any(title.lower() in window_title for title in ["last war", "survival game"]):
                        game_window = window
                        print(f"Found game window by partial match: '{window.title}'")
                        break

            if not game_window:
                messagebox.showwarning("Warning", "Could not find Last War game window")
                return False

            # Focus the window
            try:
                game_window.activate()
                time.sleep(0.5)
                print("Game window focused")
            except Exception as e:
                print(f"Could not focus window: {str(e)}")

            # Get screen dimensions for full screen
            import tkinter as tk
            temp_root = tk.Tk()
            screen_width = temp_root.winfo_screenwidth()
            screen_height = temp_root.winfo_screenheight()
            temp_root.destroy()

            print(f"Screen dimensions: {screen_width}x{screen_height}")

            # Check if window is already fullscreen (with tolerance)
            current_width = game_window.width
            current_height = game_window.height
            current_x = game_window.left
            current_y = game_window.top

            size_tolerance = 20  # Allow some difference for borders/taskbar
            position_tolerance = 10

            is_fullscreen_size = (abs(current_width - screen_width) <= size_tolerance and
                                abs(current_height - screen_height) <= size_tolerance)
            is_fullscreen_position = (abs(current_x) <= position_tolerance and
                                    abs(current_y) <= position_tolerance)

            if is_fullscreen_size and is_fullscreen_position:
                messagebox.showinfo("Already Full Screen", "Game window is already full screen - no changes needed")
                return True

            # Make window borderless and full screen
            try:
                print(f"Setting game window to borderless full screen: {screen_width}x{screen_height}")

                # First maximize the window to get rid of borders
                game_window.maximize()
                time.sleep(0.5)

                # Then resize to exact screen dimensions to cover taskbar
                game_window.resizeTo(screen_width, screen_height)
                game_window.moveTo(0, 0)
                time.sleep(1.0)  # Wait for resize to complete

                messagebox.showinfo("Success", f"Game window set to borderless full screen: {screen_width}x{screen_height}")
                return True

            except Exception as e:
                messagebox.showerror("Error", f"Could not resize/position window: {str(e)}")
                return False

        except ImportError:
            messagebox.showerror("Error", "pygetwindow not available - cannot manage game window")
            return False
        except Exception as e:
            messagebox.showerror("Error", f"Error managing game window: {str(e)}")
            return False

    def toggle_live_template_preview(self):
        """Toggle live template preview mode"""
        self.live_template_preview = self.live_preview_var.get()
        if self.live_template_preview:
            self.live_preview_status.configure(text="LIVE PREVIEW: Showing mouse area", foreground="green")
            self.template_preview_thread = threading.Thread(target=self.live_preview_loop, daemon=True)
            self.template_preview_thread.start()
        else:
            self.live_preview_status.configure(text="", foreground="green")

    def live_preview_loop(self):
        """Live template preview loop"""
        while self.live_template_preview:
            try:
                x, y = pyautogui.position()
                size = self.template_size.get()

                # Calculate capture region
                left = x - size // 2
                top = y - size // 2

                # Capture screenshot
                screenshot = pyautogui.screenshot(region=(left, top, size, size))

                # Update preview in main thread
                self.root.after(0, self.update_live_preview, screenshot, x, y)

                time.sleep(0.1)  # Update 10 times per second
            except Exception as e:
                print(f"Live preview error: {e}")
                break

    def update_live_preview(self, screenshot, x, y):
        """Update the live preview display"""
        try:
            if not self.live_template_preview:
                return

            # Resize for display if needed
            display_image = screenshot.copy()
            if display_image.width > 300 or display_image.height > 300:
                display_image.thumbnail((300, 300), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(display_image)
            self.template_preview.configure(image=photo, text="")
            self.template_preview.image = photo  # Keep a reference

            # Update info
            self.template_info.configure(text=f"Live Preview at ({x}, {y})\nSize: {screenshot.width}x{screenshot.height}")

        except Exception as e:
            print(f"Preview update error: {e}")

    def start_mouse_capture(self):
        """Start mouse capture mode"""
        self.capturing_mouse = True
        self.capture_status.config(text="Position mouse and press ENTER", foreground="red")
        self.root.after(10000, self.stop_mouse_capture)  # Auto-stop after 10 seconds

    def stop_mouse_capture(self):
        """Stop mouse capture mode"""
        self.capturing_mouse = False
        self.capture_status.config(text="", foreground="blue")

    def display_image(self, image_array, info_text=""):
        """Display captured image in GUI"""
        try:
            # Resize image for display (scale up for better visibility)
            height, width = image_array.shape[:2]
            scale = min(200 // width, 200 // height, 8)  # Max 200px, max 8x scale
            new_width = width * scale
            new_height = height * scale

            resized = cv2.resize(image_array, (new_width, new_height), interpolation=cv2.INTER_NEAREST)

            # Convert to PIL Image
            if len(resized.shape) == 3:
                resized_rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(resized_rgb)
            else:
                pil_image = Image.fromarray(resized)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep reference

            # Update info
            self.image_info.config(text=f"Size: {width}x{height} (scaled {scale}x)\n{info_text}")

        except Exception as e:
            self.image_label.config(text=f"Image error: {e}")
            self.image_info.config(text="")
        
    def detect_number(self, x, y, size, method):
        """Detect number at coordinate using specified method"""
        try:
            # 1. CAPTURE - Always capture at original size
            capture_x = x - size // 2
            capture_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
            original_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # 2. PROCESSING IMAGE - Create copy for OCR processing
            processing_image = original_image.copy()

            # Apply scaling for OCR processing if enabled
            if self.use_scaling.get():
                scale = self.scale_factor.get()
                new_width = int(processing_image.shape[1] * scale)
                new_height = int(processing_image.shape[0] * scale)
                processing_image = cv2.resize(processing_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Apply color inversion for processing if enabled
            if self.invert_colors.get():
                processing_image = cv2.bitwise_not(processing_image)

            # 3. DISPLAY IMAGE - Create copy for display (separate from processing)
            display_image = original_image.copy()

            # Apply color inversion for display if enabled
            if self.invert_colors.get():
                display_image = cv2.bitwise_not(display_image)

            # Apply display zoom (independent of processing scaling)
            zoom = self.display_zoom.get()
            if zoom != 1.0:
                zoom_width = int(display_image.shape[1] * zoom)
                zoom_height = int(display_image.shape[0] * zoom)
                display_image = cv2.resize(display_image, (zoom_width, zoom_height), interpolation=cv2.INTER_NEAREST)

            # Convert processing image to grayscale for analysis
            gray = cv2.cvtColor(processing_image, cv2.COLOR_BGR2GRAY)

            results = {}
            scale_info = f" (OCR scaled {self.scale_factor.get()}x)" if self.use_scaling.get() else ""
            zoom_info = f" (display zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            invert_info = " (inverted)" if self.invert_colors.get() else ""
            processing_info = f"{scale_info}{invert_info}"
            display_info = f"{zoom_info}{invert_info}"

            if method in ["OCR", "Both"]:
                # OCR detection with fine-tuning
                ocr_image = gray.copy()

                # Apply morphological operations if enabled
                if self.morphology_enabled.get():
                    strength = self.morphology_strength.get()
                    kernel_size = max(2, strength + 1)  # Kernel size based on strength
                    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_size, kernel_size))
                    iterations = strength

                    if self.morphology_operation.get() == "Erode":
                        ocr_image = cv2.erode(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Dilate":
                        ocr_image = cv2.dilate(ocr_image, kernel, iterations=iterations)
                    elif self.morphology_operation.get() == "Open":
                        ocr_image = cv2.morphologyEx(ocr_image, cv2.MORPH_OPEN, kernel, iterations=iterations)

                # Configure OCR based on text type
                ocr_mode = self.ocr_mode.get()
                if ocr_mode == "Thick":
                    # For thick/bold text - use different PSM and add preprocessing
                    config = '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789'
                    # Additional preprocessing for thick text
                    _, ocr_image = cv2.threshold(ocr_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                elif ocr_mode == "Thin":
                    # For thin/light text
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                    # Enhance thin text
                    ocr_image = cv2.dilate(ocr_image, np.ones((2,2), np.uint8), iterations=1)
                else:
                    # Standard mode
                    config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'

                try:
                    text = pytesseract.image_to_string(ocr_image, config=config).strip()
                    mode_info = f" [{ocr_mode}]" if ocr_mode != "Standard" else ""
                    morph_info = f" [{self.morphology_operation.get()}{self.morphology_strength.get()}]" if self.morphology_enabled.get() else ""

                    if text.isdigit():
                        results['OCR'] = f"{int(text)}{processing_info}{mode_info}{morph_info}"
                    else:
                        results['OCR'] = f"FAIL: '{text}'{processing_info}{mode_info}{morph_info}"
                except Exception as e:
                    results['OCR'] = f"ERROR: {str(e)}{processing_info}"

            if method in ["Pixel", "Both"]:
                # Pixel analysis
                _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
                dark_pixels = cv2.countNonZero(binary)
                total_pixels = gray.shape[0] * gray.shape[1]
                density = dark_pixels / total_pixels if total_pixels > 0 else 0

                if density < 0.02:
                    pixel_result = 1
                elif density < 0.04:
                    pixel_result = 2
                elif density < 0.06:
                    pixel_result = 3
                elif density < 0.08:
                    pixel_result = 4
                elif density < 0.10:
                    pixel_result = 5
                elif density < 0.12:
                    pixel_result = 6
                else:
                    pixel_result = 7

                results['Pixel'] = f"{pixel_result} (d={density:.3f}){processing_info}"

            # Save debug image (save the processing image to see what OCR analyzed)
            timestamp = datetime.now().strftime("%H%M%S")
            scale_suffix = f"_s{self.scale_factor.get()}" if self.use_scaling.get() else ""
            invert_suffix = "_inv" if self.invert_colors.get() else ""
            ocr_suffix = f"_{self.ocr_mode.get().lower()}" if self.ocr_mode.get() != "Standard" else ""
            morph_suffix = f"_{self.morphology_operation.get().lower()}{self.morphology_strength.get()}" if self.morphology_enabled.get() else ""
            filename = f"{DEBUG_FOLDER}/debug_{x}_{y}_{size}{scale_suffix}{invert_suffix}{ocr_suffix}{morph_suffix}_{timestamp}.png"
            cv2.imwrite(filename, processing_image)

            # Display image in GUI (show the display image with zoom)
            zoom_text = f" (zoom {self.display_zoom.get()}x)" if self.display_zoom.get() != 1.0 else ""
            info_text = f"Coord: ({x}, {y})\nOriginal: {size}x{size}{zoom_text}"
            self.display_image(display_image, info_text)

            return results, filename
            
        except Exception as e:
            return {"Error": str(e)}, None
    
    def scan_all(self):
        """Scan all coordinates"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"SCANNING ALL - Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text.insert(tk.END, f"Method: {self.detection_method.get()}\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")
        
        self.last_results = {}
        
        for map_name, (x, y) in self.coordinates.items():
            results, filename = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
            self.last_results[map_name] = results
            
            self.results_text.insert(tk.END, f"{map_name} at ({x}, {y}):\n")
            for method, result in results.items():
                self.results_text.insert(tk.END, f"  {method}: {result}\n")
            if filename:
                self.results_text.insert(tk.END, f"  Saved: {os.path.basename(filename)}\n")
            self.results_text.insert(tk.END, "\n")
            
            self.results_text.see(tk.END)
            self.root.update()
    
    def toggle_live_scan(self):
        """Toggle live scanning"""
        if not self.live_scanning:
            self.live_scanning = True
            self.live_thread = threading.Thread(target=self.live_scan_loop, daemon=True)
            self.live_thread.start()
        else:
            self.live_scanning = False
    
    def live_scan_loop(self):
        """Live scanning loop"""
        while self.live_scanning:
            try:
                selected = self.selected_map.get()
                if selected in self.coordinates:
                    x, y = self.coordinates[selected]
                    results, _ = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())
                    
                    # Update results in main thread
                    self.root.after(0, self.update_live_results, selected, x, y, results)
                
                time.sleep(1)  # Scan every second
            except:
                break
    
    def update_live_results(self, map_name, x, y, results):
        """Update live results display"""
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"LIVE SCAN - {map_name} at ({x}, {y})\n")
        self.results_text.insert(tk.END, f"Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
        self.results_text.insert(tk.END, "=" * 30 + "\n\n")
        
        for method, result in results.items():
            self.results_text.insert(tk.END, f"{method}: {result}\n")
    
    def on_map_selected(self, event=None):
        """Update coordinate fields when map is selected"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            x, y = self.coordinates[selected]
            self.x_var.set(x)
            self.y_var.set(y)
    
    def update_coordinate(self):
        """Update selected coordinate"""
        selected = self.selected_map.get()
        if selected in self.coordinates:
            self.coordinates[selected] = [self.x_var.get(), self.y_var.get()]
            messagebox.showinfo("Updated", f"{selected} updated to ({self.x_var.get()}, {self.y_var.get()})")
    
    def capture_mouse_position(self):
        """Capture current mouse position"""
        if self.capturing_mouse:
            x, y = pyautogui.position()
            self.x_var.set(x)
            self.y_var.set(y)
            self.stop_mouse_capture()

            # Immediately test the new position
            selected = self.selected_map.get()
            if selected in self.coordinates:
                self.coordinates[selected] = [x, y]
                results, _ = self.detect_number(x, y, self.capture_size.get(), self.detection_method.get())

                # Show results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, f"CAPTURED {selected} at ({x}, {y})\n")
                self.results_text.insert(tk.END, "=" * 30 + "\n\n")
                for method, result in results.items():
                    self.results_text.insert(tk.END, f"{method}: {result}\n")

            messagebox.showinfo("Captured", f"{selected} position captured: ({x}, {y})")
    
    def save_results(self):
        """Save current results to file"""
        if not self.last_results:
            messagebox.showwarning("No Results", "No scan results to save")
            return
        
        filename = f"map_detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, 'w') as f:
            f.write(f"Map Detection Results - {datetime.now()}\n")
            f.write(f"Size: {self.capture_size.get()}x{self.capture_size.get()}\n")
            f.write(f"Method: {self.detection_method.get()}\n")
            f.write("=" * 50 + "\n\n")
            
            for map_name, results in self.last_results.items():
                x, y = self.coordinates[map_name]
                f.write(f"{map_name} at ({x}, {y}):\n")
                for method, result in results.items():
                    f.write(f"  {method}: {result}\n")
                f.write("\n")
        
        messagebox.showinfo("Saved", f"Results saved to {filename}")
    
    def run(self):
        """Run the debugger"""
        self.root.mainloop()

if __name__ == "__main__":
    debugger = AutomationDebugger()
    debugger.run()
