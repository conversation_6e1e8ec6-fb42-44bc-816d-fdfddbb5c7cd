"""
Test script to verify all the fixes are working correctly
"""

import sys
import os
import json

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_map_trade_coordinates():
    """Test that map trade coordinates are properly configured"""
    print("=== Testing Map Trade Coordinates ===")
    
    try:
        from map_trade import MapTradeModule
        from module_config_manager import ModuleConfigManager
        
        config_manager = ModuleConfigManager()
        module = MapTradeModule(config_manager)
        
        # Check that coordinates are defined
        regions = module._define_map_piece_regions()
        print(f"✓ Map piece regions defined: {len(regions)} pieces")
        
        for map_name, data in regions.items():
            print(f"  {map_name}: big_map_pos={data['big_map_pos']}, click_pos={data['click_pos']}")
        
        print("✓ Map trade coordinates test passed")
        return True
        
    except Exception as e:
        print(f"✗ Map trade coordinates test failed: {e}")
        return False

def test_dig_configuration():
    """Test that dig module configuration is properly set up"""
    print("\n=== Testing Dig Configuration ===")
    
    try:
        # Check module_configs.json
        with open('module_configs.json', 'r') as f:
            config = json.load(f)
        
        dig_config = config.get('dig', {})
        
        # Check new configuration options
        rapid_click_speed = dig_config.get('rapid_click_speed', 0)
        rapid_click_duration = dig_config.get('rapid_click_duration', 0)
        timer_threshold = dig_config.get('timer_threshold', 0)
        
        print(f"✓ Rapid click speed: {rapid_click_speed}s")
        print(f"✓ Rapid click duration: {rapid_click_duration}s")
        print(f"✓ Timer threshold: {timer_threshold}s")
        
        # Calculate expected clicks
        if rapid_click_speed > 0:
            expected_clicks = int(rapid_click_duration / rapid_click_speed)
            print(f"✓ Expected clicks: {expected_clicks} clicks over {rapid_click_duration} seconds")
        
        # Test dig module initialization
        from dig import DigModule
        from module_config_manager import ModuleConfigManager
        
        config_manager = ModuleConfigManager()
        dig_module = DigModule(config_manager)
        
        print(f"✓ Dig module initialized with:")
        print(f"  Speed: {dig_module.rapid_click_speed}s")
        print(f"  Duration: {dig_module.rapid_click_duration}s")
        print(f"  Threshold: {dig_module.timer_threshold}s")
        
        print("✓ Dig configuration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Dig configuration test failed: {e}")
        return False

def test_mouse_override_improvements():
    """Test that mouse override improvements are in place"""
    print("\n=== Testing Mouse Override Improvements ===")
    
    try:
        from main_controller import MainController
        
        # Create controller instance
        controller = MainController()
        
        # Check that new attributes exist
        assert hasattr(controller, 'programmatic_movements'), "Missing programmatic_movements attribute"
        assert hasattr(controller, 'movement_tolerance'), "Missing movement_tolerance attribute"
        assert hasattr(controller, 'movement_timeout'), "Missing movement_timeout attribute"
        
        print(f"✓ Movement tolerance: {controller.movement_tolerance} pixels")
        print(f"✓ Movement timeout: {controller.movement_timeout} seconds")
        print(f"✓ Programmatic movements tracking: {len(controller.programmatic_movements)} movements")
        
        # Test programmatic movement registration
        controller._register_programmatic_movement(100, 200)
        assert len(controller.programmatic_movements) == 1, "Failed to register programmatic movement"
        
        # Test programmatic movement detection
        is_programmatic = controller._is_programmatic_movement(105, 205)  # Within tolerance
        assert is_programmatic, "Failed to detect programmatic movement within tolerance"
        
        is_programmatic = controller._is_programmatic_movement(200, 300)  # Outside tolerance
        assert not is_programmatic, "Incorrectly detected programmatic movement outside tolerance"
        
        print("✓ Mouse override improvements test passed")
        return True
        
    except Exception as e:
        print(f"✗ Mouse override improvements test failed: {e}")
        return False

def test_libpng_warning_suppression():
    """Test that libpng warnings are suppressed"""
    print("\n=== Testing libpng Warning Suppression ===")
    
    try:
        # Import map_trade module which should have warning suppression
        import warnings
        
        # Capture warnings
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # Import the module that should suppress libpng warnings
            from map_trade import MapTradeModule
            
            # Check if any libpng warnings were captured
            libpng_warnings = [warning for warning in w if 'libpng' in str(warning.message)]
            
            if libpng_warnings:
                print(f"⚠ Found {len(libpng_warnings)} libpng warnings (suppression may not be working)")
            else:
                print("✓ No libpng warnings detected - suppression working")
        
        print("✓ libpng warning suppression test completed")
        return True
        
    except Exception as e:
        print(f"✗ libpng warning suppression test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Running fix verification tests...\n")
    
    tests = [
        test_map_trade_coordinates,
        test_dig_configuration,
        test_mouse_override_improvements,
        test_libpng_warning_suppression
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Fixes are working correctly.")
    else:
        print("⚠ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
