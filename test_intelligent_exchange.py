#!/usr/bin/env python3
"""
Test the intelligent exchange logic
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from map_trade import MapTradeModule

def test_intelligent_exchange():
    """Test the intelligent exchange sequence"""
    print("Testing Intelligent Exchange Logic")
    print("=" * 50)
    
    try:
        # Create module
        print("Creating MapTradeModule...")
        map_trade = MapTradeModule()
        print("✅ MapTradeModule created successfully")
        
        # Test map reading first
        print("\n1. Testing map reading...")
        result = map_trade.test_map_reading_now()
        
        if not result:
            print("❌ Map reading failed - cannot proceed with exchange test")
            return
        
        print(f"✅ Map reading successful: {result}")
        
        # Show the trade decision
        give_map, receive_map = map_trade._find_optimal_trade(result)
        print(f"\n2. Trade Decision:")
        print(f"   Give: {give_map} ({result[give_map]} pieces)")
        print(f"   Receive: {receive_map} ({result[receive_map]} pieces)")
        
        # Show the click sequence that will be executed
        print(f"\n3. Click Sequence:")
        print(f"   Step 1: Click {give_map} at {map_trade.map_piece_regions[give_map]['click_pos']}")
        print(f"   Step 2: Click 'you will lose' area at (732, 261)")
        print(f"   Step 3: Click {receive_map} at {map_trade.map_piece_regions[receive_map]['click_pos']}")
        print(f"   Step 4: Click 'initiate exchange' at (636, 611)")
        print(f"   Step 5: Click 'confirm exchange' at (633, 498)")
        
        print(f"\n4. Ready to execute intelligent exchange!")
        print(f"   The module will now:")
        print(f"   - Select the lowest quantity map piece ({give_map})")
        print(f"   - Switch to 'you will lose' area")
        print(f"   - Select the highest quantity map piece ({receive_map})")
        print(f"   - Click initiate and confirm")
        
        # Ask user if they want to proceed
        response = input("\nDo you want to execute the intelligent exchange? (y/n): ")
        
        if response.lower() == 'y':
            print("\n🚀 Executing intelligent exchange...")
            success = map_trade._execute_intelligent_exchange()
            
            if success:
                print("✅ Intelligent exchange completed successfully!")
            else:
                print("❌ Intelligent exchange failed")
        else:
            print("Test completed without execution.")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_intelligent_exchange()
