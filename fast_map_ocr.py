"""
Fast Map Piece OCR System
Optimized for speed and accuracy on 25x25 pixel map piece captures
Focuses on PSM6 OCR with enhanced preprocessing and validation
"""

import cv2
import numpy as np
import pytesseract
from typing import Optional, Dict, Any, List, Tuple
import time
import json
import os

class FastMapOCR:
    """
    Lightweight OCR system optimized for map piece number detection
    Designed for speed and accuracy on small captures (25x25 pixels)
    """
    
    def __init__(self):
        self.config = {
            'white_text_threshold': 170,  # Brightness threshold for white text
            'scale_factor': 2,  # Scale up factor for 35x35 images (reduced from 3)
            'capture_size': 35,  # Expected capture size (35x35 pixels)
            'max_number': 999,  # Maximum expected map piece number
            'min_number': 1,    # Minimum expected map piece number
            'validation_enabled': True,  # Enable number validation
            'debug_mode': False  # Enable debug logging
        }
        
        # OCR configurations optimized for 35x35 captures (fastest first)
        self.ocr_configs = [
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',  # Single uniform block
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',  # Single word
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789',  # Single text line
            '--oem 3 --psm 13 -c tessedit_char_whitelist=0123456789', # Raw line (no layout)
            '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789', # Single character (for single digits)
            '--oem 1 --psm 6 -c tessedit_char_whitelist=0123456789',  # Legacy engine + block
            '--oem 1 --psm 8 -c tessedit_char_whitelist=0123456789'   # Legacy engine + word
        ]
        
        # Statistics tracking
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def recognize_map_piece(self, image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
        """
        Fast recognition of map piece numbers
        Returns the detected number or None if detection fails
        """
        start_time = time.time()
        self.stats['total_attempts'] += 1
        
        try:
            # Enhanced preprocessing for map pieces
            processed = self._preprocess_map_image(image)
            
            # Collect results from all OCR configurations
            ocr_results = []

            for config_idx, config in enumerate(self.ocr_configs):
                try:
                    # Perform OCR
                    text = pytesseract.image_to_string(processed, config=config).strip()

                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Config {config_idx}: '{text}'")

                    # Validate and convert result
                    if text and text.isdigit():
                        number = int(text)

                        # Calculate confidence score for this result
                        confidence = self._calculate_confidence(number, text, config_idx, piece_id)

                        # Apply validation if enabled
                        if self.config['validation_enabled']:
                            if self._validate_number(number, text, piece_id):
                                ocr_results.append((number, text, confidence, config_idx))
                            else:
                                self.stats['validation_rejections'] += 1
                        else:
                            # Basic range check only
                            if self.config['min_number'] <= number <= self.config['max_number']:
                                ocr_results.append((number, text, confidence, config_idx))

                except Exception as e:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] OCR config {config_idx} failed: {e}")
                    continue

            # Choose the best result based on confidence
            if ocr_results:
                # Sort by confidence (highest first)
                ocr_results.sort(key=lambda x: x[2], reverse=True)
                best_number, best_text, best_confidence, best_config = ocr_results[0]

                if self.config['debug_mode']:
                    print(f"[{piece_id}] Best result: {best_number} (confidence: {best_confidence:.2f}, config: {best_config})")

                self._update_success_stats(time.time() - start_time)
                return best_number
            
            # All configurations failed
            self.stats['failed_detections'] += 1
            processing_time = time.time() - start_time
            self._update_average_time(processing_time)
            
            if self.config['debug_mode']:
                print(f"[{piece_id}] All OCR attempts failed (time: {processing_time:.3f}s)")
            
            return None
            
        except Exception as e:
            self.stats['failed_detections'] += 1
            if self.config['debug_mode']:
                print(f"[{piece_id}] Recognition error: {e}")
            return None
    
    def _preprocess_map_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhanced preprocessing specifically optimized for 35x35 map piece captures
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Scale up 35x35 images for better OCR accuracy
            height, width = gray.shape
            expected_size = self.config['capture_size']

            # Scale up if image is around expected capture size or smaller
            if width <= expected_size + 10 or height <= expected_size + 10:
                scale_factor = self.config['scale_factor']
                new_width = width * scale_factor
                new_height = height * scale_factor
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                if self.config['debug_mode']:
                    print(f"Scaled {width}x{height} -> {new_width}x{new_height}")

            # Enhanced white text extraction with multiple thresholds
            # Try primary threshold first
            white_mask = cv2.threshold(gray, self.config['white_text_threshold'], 255, cv2.THRESH_BINARY)[1]

            # If not enough white pixels, try lower threshold
            white_pixel_ratio = np.sum(white_mask == 255) / white_mask.size
            if white_pixel_ratio < 0.1:  # Less than 10% white pixels
                white_mask = cv2.threshold(gray, self.config['white_text_threshold'] - 20, 255, cv2.THRESH_BINARY)[1]
                if self.config['debug_mode']:
                    print(f"Used lower threshold: {self.config['white_text_threshold'] - 20}")

            # Enhanced noise cleanup for 35x35 captures
            kernel_small = np.ones((1, 1), np.uint8)
            kernel_medium = np.ones((2, 2), np.uint8)

            # Close small gaps in characters
            cleaned = cv2.morphologyEx(white_mask, cv2.MORPH_CLOSE, kernel_small)
            # Remove small noise
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small)
            # Final cleanup for better character definition
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_medium)

            # Ensure white text on black background for OCR
            result = np.zeros_like(gray)
            result[cleaned == 255] = 255

            return result

        except Exception as e:
            if self.config['debug_mode']:
                print(f"Preprocessing error: {e}")
            return image  # Return original on error
    
    def _validate_number(self, number: int, text: str, piece_id: str) -> bool:
        """
        ENHANCED validation to catch OCR errors that confidence scoring missed
        Stricter validation to prevent false positives and improve accuracy
        """
        try:
            # Basic range check
            if not (self.config['min_number'] <= number <= self.config['max_number']):
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Out of range: {number}")
                return False

            # Relaxed validation rules - focus on obvious errors only

            # Rule 1: Reject large numbers (much stricter threshold)
            if number > 100:
                # Map pieces rarely exceed 100, be very strict
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: Number too large: {number} (likely OCR error)")
                return False

            # Rule 2: Text length validation (but more lenient)
            expected_length = len(str(number))
            if len(text) != expected_length:
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Text length mismatch: '{text}' -> {number}")
                return False

            # Rule 3: Reject obvious OCR artifacts (expanded and stricter)
            if len(text) >= 2:
                # Check for repeated digits that are suspicious (expanded list)
                if text in ['00', '11', '22', '33', '44', '55', '66', '77', '88', '99', '111', '888', '000', '1111', '8888']:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] VALIDATION FAILED: OCR artifact: '{text}'")
                    return False

                # Check for patterns that are common OCR errors (much expanded)
                ocr_error_patterns = ['17', '18', '81', '71', '78', '87', '118', '181', '811', '881', '188', '818', '138', '183', '381', '831']
                if text in ocr_error_patterns:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] VALIDATION FAILED: Common OCR error pattern: '{text}'")
                    return False

            # Rule 4: Additional pattern validation for common OCR errors
            # Check for digit repetition patterns
            digit_counts = {}
            for digit in text:
                digit_counts[digit] = digit_counts.get(digit, 0) + 1

            # Reject if more than 60% of digits are the same (stricter than before)
            max_count = max(digit_counts.values())
            if len(text) > 1 and max_count / len(text) > 0.6:
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: Too many repeated digits: '{text}'")
                return False

            # Rule 5: Final acceptance for reasonable numbers
            if 1 <= number <= 50:
                # Most common range for map pieces
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION PASSED: {number} ('{text}') - common range")
                return True
            elif 51 <= number <= 100:
                # Less common but still reasonable
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION PASSED: {number} ('{text}') - higher range")
                return True
            else:
                # Should not reach here due to earlier checks, but be safe
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: Unexpected number: {number}")
                return False

        except Exception as e:
            if self.config['debug_mode']:
                print(f"[{piece_id}] VALIDATION ERROR: {e}")
            return False  # Reject if validation fails (changed from True)

    def _calculate_confidence(self, number: int, text: str, config_idx: int, piece_id: str) -> float:
        """
        Calculate REALISTIC confidence score for OCR result
        Fixed to prevent artificially high confidence scores that mask poor OCR
        """
        # Start with VERY low base confidence to prevent false positives
        confidence = 0.1  # Much lower starting point

        try:
            # Factor 1: OCR configuration preference (minimal impact)
            config_bonus = max(0.0, (len(self.ocr_configs) - config_idx) / len(self.ocr_configs) * 0.05)  # Much reduced
            confidence += config_bonus

            # Factor 2: Number characteristics (very conservative)
            if 1 <= number <= 20:
                # Most common single digits and low doubles
                confidence += 0.15
            elif 21 <= number <= 50:
                # Less common but reasonable
                confidence += 0.08
            elif 51 <= number <= 99:
                # Higher numbers - be skeptical
                confidence += 0.02
            elif 100 <= number <= 200:
                # Triple digits - be more skeptical
                confidence += 0.02  # Reduced from 0.1
            else:
                # Very large numbers are highly suspicious
                confidence -= 0.4  # Increased penalty from -0.2

            # Factor 3: Text consistency (reduced bonus)
            if len(text) == len(str(number)):
                confidence += 0.05  # Reduced from 0.1

            # Factor 4: Very conservative number bonuses
            if number in [1, 2, 3, 4, 5, 6, 7, 8, 9]:
                # Single digits only - most reliable
                confidence += 0.05
            elif number in [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]:
                # Common double digits - but be careful with 17, 18 (OCR errors)
                if text in ['17', '18']:
                    confidence -= 0.1  # Penalize common OCR error patterns
                else:
                    confidence += 0.03

            # Factor 5: HEAVILY penalize obvious OCR errors
            if text in ['11', '88', '00', '111', '888', '999', '777', '17', '18', '81', '138']:
                confidence -= 0.8  # Massive penalty for common OCR errors

            # Factor 6: Pattern-based penalties for OCR artifacts
            if number > 50:
                # Any number above 50 is suspicious for map pieces
                confidence -= 0.2

            if len(text) > 2:
                # Multi-digit numbers are often OCR errors
                confidence -= 0.15

            if text.count('1') > 1 or text.count('8') > 0:
                # 1s and 8s are common OCR artifacts
                confidence -= 0.3

            # Factor 7: VERY conservative confidence cap
            # Never allow confidence above 0.5 to ensure fallback methods have a chance
            confidence = max(0.0, min(0.5, confidence))  # Much lower cap

            if self.config['debug_mode']:
                print(f"[{piece_id}] REALISTIC Confidence for {number}: {confidence:.2f} (text: '{text}', config: {config_idx})")

            return confidence

        except Exception:
            return 0.2  # Much lower default confidence

    def _update_success_stats(self, processing_time: float):
        """Update statistics for successful detection"""
        self.stats['successful_detections'] += 1
        self._update_average_time(processing_time)
    
    def _update_average_time(self, processing_time: float):
        """Update average processing time"""
        total_attempts = self.stats['total_attempts']
        current_avg = self.stats['average_processing_time']
        self.stats['average_processing_time'] = ((current_avg * (total_attempts - 1)) + processing_time) / total_attempts
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        success_rate = (self.stats['successful_detections'] / max(1, self.stats['total_attempts'])) * 100
        return {
            **self.stats,
            'success_rate_percent': round(success_rate, 1)
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def set_debug_mode(self, enabled: bool):
        """Enable or disable debug logging"""
        self.config['debug_mode'] = enabled
    
    def set_validation(self, enabled: bool):
        """Enable or disable number validation"""
        self.config['validation_enabled'] = enabled


# Global instance for easy access
_fast_map_ocr_instance = None

def get_fast_map_ocr() -> FastMapOCR:
    """Get the global FastMapOCR instance"""
    global _fast_map_ocr_instance
    if _fast_map_ocr_instance is None:
        _fast_map_ocr_instance = FastMapOCR()
    return _fast_map_ocr_instance

def recognize_map_piece_fast(image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
    """Convenience function for fast map piece recognition"""
    ocr = get_fast_map_ocr()
    return ocr.recognize_map_piece(image, piece_id)


if __name__ == "__main__":
    # Test the fast map OCR system
    print("🚀 Fast Map OCR System Ready - 35x35 Optimized")
    ocr = FastMapOCR()
    ocr.set_debug_mode(True)
    print("Features:")
    print("- Optimized for 35x35 pixel captures")
    print("- 7 OCR configurations (PSM 6,8,7,13,10 + Legacy)")
    print("- Enhanced preprocessing with adaptive scaling")
    print("- Confidence-based result selection")
    print("- Smart validation rules (relaxed for better detection)")
    print("- Performance statistics tracking")
    print("- Minimal processing time")
    print("- Improved from 50% -> targeting 80%+ accuracy")
