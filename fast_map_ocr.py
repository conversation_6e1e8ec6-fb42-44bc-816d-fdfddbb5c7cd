"""
Fast Map Piece OCR System
Optimized for speed and accuracy on 25x25 pixel map piece captures
Focuses on PSM6 OCR with enhanced preprocessing and validation
"""

import cv2
import numpy as np
import pytesseract
from typing import Optional, Dict, Any, List, Tuple
import time
import json
import os

class FastMapOCR:
    """
    Lightweight OCR system optimized for map piece number detection
    Designed for speed and accuracy on small captures (25x25 pixels)
    """
    
    def __init__(self):
        self.config = {
            'white_text_threshold': 170,  # Brightness threshold for white text
            'scale_factor': 2,  # Scale up factor for 35x35 images (reduced from 3)
            'capture_size': 35,  # Expected capture size (35x35 pixels)
            'max_number': 999,  # Maximum expected map piece number
            'min_number': 1,    # Minimum expected map piece number
            'validation_enabled': True,  # Enable number validation
            'debug_mode': False  # Enable debug logging
        }
        
        # OCR configurations optimized for 35x35 captures (fastest first)
        self.ocr_configs = [
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',  # Single uniform block
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',  # Single word
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789',  # Single text line
            '--oem 3 --psm 13 -c tessedit_char_whitelist=0123456789', # Raw line (no layout)
            '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789', # Single character (for single digits)
            '--oem 1 --psm 6 -c tessedit_char_whitelist=0123456789',  # Legacy engine + block
            '--oem 1 --psm 8 -c tessedit_char_whitelist=0123456789'   # Legacy engine + word
        ]
        
        # Statistics tracking
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def recognize_map_piece(self, image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
        """
        Fast recognition of map piece numbers
        Returns the detected number or None if detection fails
        """
        start_time = time.time()
        self.stats['total_attempts'] += 1
        
        try:
            # Enhanced preprocessing for map pieces
            processed = self._preprocess_map_image(image)
            
            # Collect results from all OCR configurations
            ocr_results = []

            for config_idx, config in enumerate(self.ocr_configs):
                try:
                    # Perform OCR
                    text = pytesseract.image_to_string(processed, config=config).strip()

                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Config {config_idx}: '{text}'")

                    # Validate and convert result
                    if text and text.isdigit():
                        number = int(text)

                        # Calculate confidence score for this result
                        confidence = self._calculate_confidence(number, text, config_idx, piece_id)

                        # Apply validation if enabled
                        if self.config['validation_enabled']:
                            if self._validate_number(number, text, piece_id):
                                ocr_results.append((number, text, confidence, config_idx))
                            else:
                                self.stats['validation_rejections'] += 1
                        else:
                            # Basic range check only
                            if self.config['min_number'] <= number <= self.config['max_number']:
                                ocr_results.append((number, text, confidence, config_idx))

                except Exception as e:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] OCR config {config_idx} failed: {e}")
                    continue

            # Choose the best result based on confidence
            if ocr_results:
                # Sort by confidence (highest first)
                ocr_results.sort(key=lambda x: x[2], reverse=True)
                best_number, best_text, best_confidence, best_config = ocr_results[0]

                if self.config['debug_mode']:
                    print(f"[{piece_id}] Best result: {best_number} (confidence: {best_confidence:.2f}, config: {best_config})")

                self._update_success_stats(time.time() - start_time)
                return best_number
            
            # All configurations failed
            self.stats['failed_detections'] += 1
            processing_time = time.time() - start_time
            self._update_average_time(processing_time)
            
            if self.config['debug_mode']:
                print(f"[{piece_id}] All OCR attempts failed (time: {processing_time:.3f}s)")
            
            return None
            
        except Exception as e:
            self.stats['failed_detections'] += 1
            if self.config['debug_mode']:
                print(f"[{piece_id}] Recognition error: {e}")
            return None
    
    def _preprocess_map_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhanced preprocessing specifically optimized for 35x35 map piece captures
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Scale up 35x35 images for better OCR accuracy
            height, width = gray.shape
            expected_size = self.config['capture_size']

            # Scale up if image is around expected capture size or smaller
            if width <= expected_size + 10 or height <= expected_size + 10:
                scale_factor = self.config['scale_factor']
                new_width = width * scale_factor
                new_height = height * scale_factor
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                if self.config['debug_mode']:
                    print(f"Scaled {width}x{height} -> {new_width}x{new_height}")

            # Enhanced white text extraction with multiple thresholds
            # Try primary threshold first
            white_mask = cv2.threshold(gray, self.config['white_text_threshold'], 255, cv2.THRESH_BINARY)[1]

            # If not enough white pixels, try lower threshold
            white_pixel_ratio = np.sum(white_mask == 255) / white_mask.size
            if white_pixel_ratio < 0.1:  # Less than 10% white pixels
                white_mask = cv2.threshold(gray, self.config['white_text_threshold'] - 20, 255, cv2.THRESH_BINARY)[1]
                if self.config['debug_mode']:
                    print(f"Used lower threshold: {self.config['white_text_threshold'] - 20}")

            # Enhanced noise cleanup for 35x35 captures
            kernel_small = np.ones((1, 1), np.uint8)
            kernel_medium = np.ones((2, 2), np.uint8)

            # Close small gaps in characters
            cleaned = cv2.morphologyEx(white_mask, cv2.MORPH_CLOSE, kernel_small)
            # Remove small noise
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small)
            # Final cleanup for better character definition
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_medium)

            # Ensure white text on black background for OCR
            result = np.zeros_like(gray)
            result[cleaned == 255] = 255

            return result

        except Exception as e:
            if self.config['debug_mode']:
                print(f"Preprocessing error: {e}")
            return image  # Return original on error
    
    def _validate_number(self, number: int, text: str, piece_id: str) -> bool:
        """
        Validate detected number based on common OCR error patterns
        Relaxed validation to improve 50% -> higher accuracy
        """
        try:
            # Basic range check
            if not (self.config['min_number'] <= number <= self.config['max_number']):
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Out of range: {number}")
                return False

            # Relaxed validation rules - focus on obvious errors only

            # Rule 1: Reject extremely large numbers (likely OCR artifacts)
            if number > 500:
                # Very large numbers are likely OCR errors (like 138 for 17)
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Rejected very large number: {number} (likely OCR error)")
                return False

            # Rule 2: Text length validation (but more lenient)
            expected_length = len(str(number))
            if len(text) != expected_length:
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Text length mismatch: '{text}' -> {number}")
                return False

            # Rule 3: Reject obvious OCR artifacts (repeated digits that are unlikely)
            if len(text) >= 2:
                # Check for repeated digits that are suspicious
                if text in ['00', '11', '22', '33', '44', '55', '66', '77', '88', '99']:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Suspicious repeated digits: '{text}'")
                    return False

                # Check for patterns that are common OCR errors
                if text in ['18', '81', '111', '888', '000']:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Common OCR error pattern: '{text}'")
                    return False

            # Rule 4: Accept most single and double digit numbers (they're usually correct)
            if 1 <= number <= 99:
                return True

            # Rule 5: Be more careful with triple digits
            if number >= 100:
                # Only accept if it looks reasonable
                if number <= 200:  # Most map pieces shouldn't exceed 200
                    return True
                else:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Large number rejected: {number}")
                    return False

            return True

        except Exception:
            return True  # Accept if validation fails

    def _calculate_confidence(self, number: int, text: str, config_idx: int, piece_id: str) -> float:
        """
        Calculate confidence score for OCR result to choose best among multiple results
        """
        confidence = 0.5  # Base confidence

        try:
            # Factor 1: OCR configuration preference (earlier configs are more reliable)
            config_bonus = max(0.0, (len(self.ocr_configs) - config_idx) / len(self.ocr_configs) * 0.3)
            confidence += config_bonus

            # Factor 2: Number characteristics
            if 1 <= number <= 99:
                # Single and double digits are usually more reliable
                confidence += 0.2
            elif 100 <= number <= 200:
                # Triple digits in reasonable range
                confidence += 0.1
            else:
                # Very large numbers are suspicious
                confidence -= 0.2

            # Factor 3: Text consistency
            if len(text) == len(str(number)):
                # Text length matches expected
                confidence += 0.1

            # Factor 4: Common reasonable numbers get bonus
            if number in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]:
                # Very common map piece numbers
                confidence += 0.15
            elif 21 <= number <= 50:
                # Reasonably common
                confidence += 0.1
            elif 51 <= number <= 100:
                # Less common but possible
                confidence += 0.05

            # Factor 5: Avoid obvious OCR errors
            if text in ['11', '88', '00', '111', '888']:
                confidence -= 0.3  # Penalize common OCR artifacts

            # Ensure confidence stays in reasonable range
            confidence = max(0.0, min(1.0, confidence))

            if self.config['debug_mode']:
                print(f"[{piece_id}] Confidence for {number}: {confidence:.2f}")

            return confidence

        except Exception:
            return 0.5  # Default confidence if calculation fails

    def _update_success_stats(self, processing_time: float):
        """Update statistics for successful detection"""
        self.stats['successful_detections'] += 1
        self._update_average_time(processing_time)
    
    def _update_average_time(self, processing_time: float):
        """Update average processing time"""
        total_attempts = self.stats['total_attempts']
        current_avg = self.stats['average_processing_time']
        self.stats['average_processing_time'] = ((current_avg * (total_attempts - 1)) + processing_time) / total_attempts
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        success_rate = (self.stats['successful_detections'] / max(1, self.stats['total_attempts'])) * 100
        return {
            **self.stats,
            'success_rate_percent': round(success_rate, 1)
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def set_debug_mode(self, enabled: bool):
        """Enable or disable debug logging"""
        self.config['debug_mode'] = enabled
    
    def set_validation(self, enabled: bool):
        """Enable or disable number validation"""
        self.config['validation_enabled'] = enabled


# Global instance for easy access
_fast_map_ocr_instance = None

def get_fast_map_ocr() -> FastMapOCR:
    """Get the global FastMapOCR instance"""
    global _fast_map_ocr_instance
    if _fast_map_ocr_instance is None:
        _fast_map_ocr_instance = FastMapOCR()
    return _fast_map_ocr_instance

def recognize_map_piece_fast(image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
    """Convenience function for fast map piece recognition"""
    ocr = get_fast_map_ocr()
    return ocr.recognize_map_piece(image, piece_id)


if __name__ == "__main__":
    # Test the fast map OCR system
    print("🚀 Fast Map OCR System Ready - 35x35 Optimized")
    ocr = FastMapOCR()
    ocr.set_debug_mode(True)
    print("Features:")
    print("- Optimized for 35x35 pixel captures")
    print("- 7 OCR configurations (PSM 6,8,7,13,10 + Legacy)")
    print("- Enhanced preprocessing with adaptive scaling")
    print("- Confidence-based result selection")
    print("- Smart validation rules (relaxed for better detection)")
    print("- Performance statistics tracking")
    print("- Minimal processing time")
    print("- Improved from 50% -> targeting 80%+ accuracy")
