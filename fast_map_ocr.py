"""
Fast Map Piece OCR System
Optimized for speed and accuracy on 25x25 pixel map piece captures
Focuses on PSM6 OCR with enhanced preprocessing and validation
"""

import cv2
import numpy as np
import pytesseract
from typing import Optional, Dict, Any, List, Tuple
import time
import json
import os

class FastMapOCR:
    """
    Lightweight OCR system optimized for map piece number detection
    Designed for speed and accuracy on small captures (25x25 pixels)
    """
    
    def __init__(self):
        self.config = {
            'white_text_threshold': 170,  # Brightness threshold for white text
            'scale_factor': 2,  # Scale up factor for 35x35 images (reduced from 3)
            'capture_size': 35,  # Expected capture size (35x35 pixels)
            'max_number': 999,  # Maximum expected map piece number
            'min_number': 1,    # Minimum expected map piece number
            'validation_enabled': True,  # Enable number validation
            'debug_mode': False  # Enable debug logging
        }
        
        # OCR configurations optimized for 35x35 captures (fastest first)
        self.ocr_configs = [
            '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',  # Single uniform block
            '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',  # Single word
            '--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789',  # Single text line
            '--oem 3 --psm 13 -c tessedit_char_whitelist=0123456789', # Raw line (no layout)
            '--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789', # Single character (for single digits)
            '--oem 1 --psm 6 -c tessedit_char_whitelist=0123456789',  # Legacy engine + block
            '--oem 1 --psm 8 -c tessedit_char_whitelist=0123456789'   # Legacy engine + word
        ]
        
        # Statistics tracking
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def recognize_map_piece(self, image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
        """
        Fast recognition of map piece numbers
        Returns the detected number or None if detection fails
        """
        start_time = time.time()
        self.stats['total_attempts'] += 1
        
        try:
            # Enhanced preprocessing for map pieces
            processed = self._preprocess_map_image(image)
            
            # Collect results from all OCR configurations
            ocr_results = []

            for config_idx, config in enumerate(self.ocr_configs):
                try:
                    # Perform OCR
                    text = pytesseract.image_to_string(processed, config=config).strip()

                    if self.config['debug_mode']:
                        print(f"[{piece_id}] Config {config_idx}: '{text}'")

                    # Validate and convert result
                    if text and text.isdigit():
                        number = int(text)

                        # Calculate confidence score for this result
                        confidence = self._calculate_confidence(number, text, config_idx, piece_id)

                        # Apply validation if enabled
                        if self.config['validation_enabled']:
                            if self._validate_number(number, text, piece_id):
                                ocr_results.append((number, text, confidence, config_idx))
                            else:
                                self.stats['validation_rejections'] += 1
                        else:
                            # Basic range check only
                            if self.config['min_number'] <= number <= self.config['max_number']:
                                ocr_results.append((number, text, confidence, config_idx))

                except Exception as e:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] OCR config {config_idx} failed: {e}")
                    continue

            # Choose the best result based on confidence
            if ocr_results:
                # Sort by confidence (highest first)
                ocr_results.sort(key=lambda x: x[2], reverse=True)
                best_number, best_text, best_confidence, best_config = ocr_results[0]

                if self.config['debug_mode']:
                    print(f"[{piece_id}] Best result: {best_number} (confidence: {best_confidence:.2f}, config: {best_config})")

                self._update_success_stats(time.time() - start_time)
                return best_number
            
            # All configurations failed
            self.stats['failed_detections'] += 1
            processing_time = time.time() - start_time
            self._update_average_time(processing_time)
            
            if self.config['debug_mode']:
                print(f"[{piece_id}] All OCR attempts failed (time: {processing_time:.3f}s)")
            
            return None
            
        except Exception as e:
            self.stats['failed_detections'] += 1
            if self.config['debug_mode']:
                print(f"[{piece_id}] Recognition error: {e}")
            return None
    
    def _preprocess_map_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhanced preprocessing specifically optimized for 35x35 map piece captures
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Scale up 35x35 images for better OCR accuracy
            height, width = gray.shape
            expected_size = self.config['capture_size']

            # Scale up if image is around expected capture size or smaller
            if width <= expected_size + 10 or height <= expected_size + 10:
                scale_factor = self.config['scale_factor']
                new_width = width * scale_factor
                new_height = height * scale_factor
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

                if self.config['debug_mode']:
                    print(f"Scaled {width}x{height} -> {new_width}x{new_height}")

            # Enhanced white text extraction with multiple thresholds
            # Try primary threshold first
            white_mask = cv2.threshold(gray, self.config['white_text_threshold'], 255, cv2.THRESH_BINARY)[1]

            # If not enough white pixels, try lower threshold
            white_pixel_ratio = np.sum(white_mask == 255) / white_mask.size
            if white_pixel_ratio < 0.1:  # Less than 10% white pixels
                white_mask = cv2.threshold(gray, self.config['white_text_threshold'] - 20, 255, cv2.THRESH_BINARY)[1]
                if self.config['debug_mode']:
                    print(f"Used lower threshold: {self.config['white_text_threshold'] - 20}")

            # Enhanced noise cleanup for 35x35 captures
            kernel_small = np.ones((1, 1), np.uint8)
            kernel_medium = np.ones((2, 2), np.uint8)

            # Close small gaps in characters
            cleaned = cv2.morphologyEx(white_mask, cv2.MORPH_CLOSE, kernel_small)
            # Remove small noise
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small)
            # Final cleanup for better character definition
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_medium)

            # Ensure white text on black background for OCR
            result = np.zeros_like(gray)
            result[cleaned == 255] = 255

            return result

        except Exception as e:
            if self.config['debug_mode']:
                print(f"Preprocessing error: {e}")
            return image  # Return original on error
    
    def _validate_number(self, number: int, text: str, piece_id: str) -> bool:
        """
        CORRECTED validation - Accept valid map piece numbers (0-200)
        Only reject obvious OCR artifacts, not legitimate numbers like 17, 18
        """
        try:
            # Basic range check
            if not (self.config['min_number'] <= number <= self.config['max_number']):
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Out of range: {number}")
                return False

            # Relaxed validation rules - focus on obvious errors only

            # Rule 1: Accept reasonable map piece numbers (user confirmed 0-200 is valid)
            if number > 200:
                # Only reject truly impossible numbers
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: Number too large: {number} (above 200)")
                return False

            # Rule 2: Text length validation (but more lenient)
            expected_length = len(str(number))
            if len(text) != expected_length:
                if self.config['debug_mode']:
                    print(f"[{piece_id}] Text length mismatch: '{text}' -> {number}")
                return False

            # Rule 3: Reject obvious OCR artifacts (expanded and stricter)
            if len(text) >= 2:
                # Check for repeated digits that are suspicious (expanded list)
                if text in ['00', '11', '22', '33', '44', '55', '66', '77', '88', '99', '111', '888', '000', '1111', '8888']:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] VALIDATION FAILED: OCR artifact: '{text}'")
                    return False

                # Check for ONLY obvious OCR artifacts, not valid numbers
                obvious_artifacts = ['111', '888', '000', '1111', '8888', '0000']
                if text in obvious_artifacts:
                    if self.config['debug_mode']:
                        print(f"[{piece_id}] VALIDATION FAILED: Obvious OCR artifact: '{text}'")
                    return False

            # Rule 4: Only reject extreme repetition patterns
            digit_counts = {}
            for digit in text:
                digit_counts[digit] = digit_counts.get(digit, 0) + 1

            # Only reject if ALL digits are the same (like 111, 888)
            max_count = max(digit_counts.values())
            if len(text) > 1 and max_count == len(text):
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: All digits the same: '{text}'")
                return False

            # Rule 5: Accept all reasonable numbers (0-200 as user confirmed)
            if 0 <= number <= 200:
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION PASSED: {number} ('{text}') - valid range")
                return True
            else:
                # Should not reach here due to earlier checks
                if self.config['debug_mode']:
                    print(f"[{piece_id}] VALIDATION FAILED: Out of valid range: {number}")
                return False

        except Exception as e:
            if self.config['debug_mode']:
                print(f"[{piece_id}] VALIDATION ERROR: {e}")
            return True  # Accept if validation fails (be permissive)

    def _calculate_confidence(self, number: int, text: str, config_idx: int, piece_id: str) -> float:
        """
        CORRECTED confidence calculation - Prioritize Config 2 (user confirmed 100% accurate)
        Give Config 2 the highest confidence, accept valid numbers 0-200
        """
        # FIXED: Prioritize Config 2 since user confirmed it's 100% accurate
        confidence = 0.3  # Reasonable base confidence

        try:
            # Factor 1: PRIORITIZE CONFIG 2 - User confirmed it's 100% accurate
            if config_idx == 2:
                # Config 2 is the most reliable according to user
                confidence = 0.9  # Very high confidence for Config 2
            elif config_idx < 2:
                # Configs 0,1 are before the reliable Config 2
                confidence += 0.1
            else:
                # Configs after 2 are less reliable
                confidence += 0.05

            # Factor 2: All reasonable map piece numbers are valid (0-200)
            if 0 <= number <= 200:
                # All numbers in valid range for map pieces
                confidence += 0.1
            else:
                # Only penalize truly impossible numbers
                confidence -= 0.3

            # Factor 3: Text consistency bonus
            if len(text) == len(str(number)):
                confidence += 0.05

            # Factor 4: Only penalize obvious OCR artifacts, not valid numbers
            if text in ['111', '888', '000', '1111', '8888']:
                # Only clear OCR artifacts
                confidence -= 0.4

            # Factor 5: Ensure Config 2 always wins when it has a result
            if config_idx == 2 and text and text.isdigit():
                confidence = 0.95  # Almost maximum confidence for Config 2

            # Factor 6: Reasonable confidence cap
            confidence = max(0.0, min(1.0, confidence))

            if self.config['debug_mode']:
                print(f"[{piece_id}] REALISTIC Confidence for {number}: {confidence:.2f} (text: '{text}', config: {config_idx})")

            return confidence

        except Exception:
            return 0.2  # Much lower default confidence

    def _update_success_stats(self, processing_time: float):
        """Update statistics for successful detection"""
        self.stats['successful_detections'] += 1
        self._update_average_time(processing_time)
    
    def _update_average_time(self, processing_time: float):
        """Update average processing time"""
        total_attempts = self.stats['total_attempts']
        current_avg = self.stats['average_processing_time']
        self.stats['average_processing_time'] = ((current_avg * (total_attempts - 1)) + processing_time) / total_attempts
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        success_rate = (self.stats['successful_detections'] / max(1, self.stats['total_attempts'])) * 100
        return {
            **self.stats,
            'success_rate_percent': round(success_rate, 1)
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.stats = {
            'total_attempts': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'validation_rejections': 0,
            'average_processing_time': 0.0
        }
    
    def set_debug_mode(self, enabled: bool):
        """Enable or disable debug logging"""
        self.config['debug_mode'] = enabled
    
    def set_validation(self, enabled: bool):
        """Enable or disable number validation"""
        self.config['validation_enabled'] = enabled


# Global instance for easy access
_fast_map_ocr_instance = None

def get_fast_map_ocr() -> FastMapOCR:
    """Get the global FastMapOCR instance"""
    global _fast_map_ocr_instance
    if _fast_map_ocr_instance is None:
        _fast_map_ocr_instance = FastMapOCR()
    return _fast_map_ocr_instance

def recognize_map_piece_fast(image: np.ndarray, piece_id: str = "Unknown") -> Optional[int]:
    """Convenience function for fast map piece recognition"""
    ocr = get_fast_map_ocr()
    return ocr.recognize_map_piece(image, piece_id)


if __name__ == "__main__":
    # Test the fast map OCR system
    print("🚀 Fast Map OCR System Ready - 35x35 Optimized")
    ocr = FastMapOCR()
    ocr.set_debug_mode(True)
    print("Features:")
    print("- Optimized for 35x35 pixel captures")
    print("- 7 OCR configurations (PSM 6,8,7,13,10 + Legacy)")
    print("- Enhanced preprocessing with adaptive scaling")
    print("- Confidence-based result selection")
    print("- Smart validation rules (relaxed for better detection)")
    print("- Performance statistics tracking")
    print("- Minimal processing time")
    print("- Improved from 50% -> targeting 80%+ accuracy")
