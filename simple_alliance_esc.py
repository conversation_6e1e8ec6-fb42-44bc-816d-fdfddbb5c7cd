#!/usr/bin/env python3
"""
Simple alliance donation ESC key test - minimal, clean implementation
"""

import time
import pyautogui
import json

def load_alliance_config():
    """Load alliance donation configuration"""
    try:
        with open('module_configs.json', 'r') as f:
            config = json.load(f)
        return config.get('alliance_donation', {})
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}

def get_esc_steps():
    """Get only the ESC key steps from configuration"""
    config = load_alliance_config()
    click_coordinates = config.get('click_coordinates', [])
    
    esc_steps = []
    for i, coord in enumerate(click_coordinates):
        if coord.get('use_esc_key', False) and coord.get('enabled', True):
            name = coord.get('name', f'step_{i+1}')
            delay = coord.get('delay', 2.0)
            description = coord.get('description', 'ESC key step')
            esc_steps.append({
                'step_num': i + 1,
                'name': name,
                'description': description,
                'delay': delay
            })
    
    return esc_steps

def execute_esc_steps():
    """Execute only the ESC key steps"""
    print("Simple Alliance Donation ESC Key Test")
    print("=" * 50)
    
    # Load ESC steps
    esc_steps = get_esc_steps()
    
    if not esc_steps:
        print("No ESC key steps found in configuration!")
        return
    
    print(f"Found {len(esc_steps)} ESC key steps:")
    for step in esc_steps:
        print(f"  Step {step['step_num']}: {step['name']} - {step['description']}")
    
    # Ask user if they want to execute
    response = input(f"\nExecute {len(esc_steps)} ESC key steps? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("Test cancelled.")
        return
    
    print(f"\nStarting ESC key execution in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("\nExecuting ESC key steps...")
    
    try:
        for i, step in enumerate(esc_steps):
            print(f"\nStep {i+1}/{len(esc_steps)}: {step['name']}")
            print(f"Description: {step['description']}")
            print("Pressing ESC key...")
            
            # Press ESC key
            pyautogui.press('esc')
            print("ESC key pressed")
            
            # Wait for delay
            delay = step['delay']
            if delay > 0:
                print(f"Waiting {delay} seconds...")
                time.sleep(delay)
        
        print(f"\nAll {len(esc_steps)} ESC key steps completed successfully!")
        
    except Exception as e:
        print(f"Error during execution: {str(e)}")

def test_rapid_esc():
    """Test rapid ESC key presses to see if that causes issues"""
    print("\nRapid ESC Key Test")
    print("=" * 30)
    
    response = input("Press ESC key 3 times rapidly (0.1s delay)? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("Test cancelled.")
        return
    
    print("Rapid ESC test in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        print("Rapid ESC presses...")
        for i in range(3):
            print(f"ESC {i+1}/3")
            pyautogui.press('esc')
            time.sleep(0.1)  # Very short delay
        
        print("Rapid ESC test completed!")
        print("Compare this to the slower version to see if timing matters.")
        
    except Exception as e:
        print(f"Error during rapid ESC test: {str(e)}")

if __name__ == "__main__":
    print("Alliance Donation ESC Key Test")
    print("=" * 50)
    
    try:
        # Test 1: Execute configured ESC steps
        execute_esc_steps()
        
        # Test 2: Rapid ESC test
        test_rapid_esc()
        
        print("\n" + "=" * 50)
        print("Test complete!")
        print("This should help identify if:")
        print("1. ESC key steps work when isolated")
        print("2. Timing affects ESC key reliability")
        print("3. Configuration is correct")
        
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
