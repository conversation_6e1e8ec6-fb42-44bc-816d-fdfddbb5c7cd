"""
Test script to verify libpng warning fixes in map trade module
Tests that image processing works without libpng warnings disrupting detection
"""

import sys
import os
import warnings
import time

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_warning_suppression():
    """Test that warnings are properly suppressed"""
    print("=== Testing Warning Suppression ===")
    
    try:
        # Capture warnings during import
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # Import the module that should suppress warnings
            from map_trade import MapTradeModule
            
            # Check if any libpng warnings were captured
            libpng_warnings = [warning for warning in w if 'libpng' in str(warning.message).lower()]
            sbit_warnings = [warning for warning in w if 'sbit' in str(warning.message).lower()]
            
            if libpng_warnings:
                print(f"⚠ Found {len(libpng_warnings)} libpng warnings during import")
                for warning in libpng_warnings:
                    print(f"  - {warning.message}")
            else:
                print("✓ No libpng warnings during import")
                
            if sbit_warnings:
                print(f"⚠ Found {len(sbit_warnings)} sBIT warnings during import")
                for warning in sbit_warnings:
                    print(f"  - {warning.message}")
            else:
                print("✓ No sBIT warnings during import")
        
        print("✓ Warning suppression test completed")
        return True
        
    except Exception as e:
        print(f"✗ Warning suppression test failed: {e}")
        return False

def test_opencv_processing():
    """Test OpenCV image processing without warnings"""
    print("\n=== Testing OpenCV Processing ===")
    
    try:
        import cv2
        import numpy as np
        import pyautogui
        
        # Capture warnings during image processing
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # Simulate the image processing that happens in map trade
            print("Taking screenshot...")
            screenshot = pyautogui.screenshot()
            
            print("Converting to OpenCV format...")
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            print("Converting to grayscale...")
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            
            print("Converting to HSV...")
            hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)
            
            # Check for warnings
            processing_warnings = [warning for warning in w if any(term in str(warning.message).lower() 
                                 for term in ['libpng', 'sbit', 'invalid'])]
            
            if processing_warnings:
                print(f"⚠ Found {len(processing_warnings)} warnings during processing")
                for warning in processing_warnings:
                    print(f"  - {warning.message}")
            else:
                print("✓ No warnings during OpenCV processing")
        
        print("✓ OpenCV processing test completed")
        return True
        
    except Exception as e:
        print(f"✗ OpenCV processing test failed: {e}")
        return False

def test_template_loading():
    """Test template loading without warnings"""
    print("\n=== Testing Template Loading ===")
    
    try:
        import cv2
        import os
        
        # Test template files that might exist
        test_templates = [
            "red_thumb_100x40.png",
            "cancel_exchange_100x40.png",
            "initiate_exchange_100x40.png",
            "new_exchange_100x40.png"
        ]
        
        templates_found = 0
        warnings_found = 0
        
        for template_name in test_templates:
            template_path = os.path.join("templates", template_name)
            
            if os.path.exists(template_path):
                templates_found += 1
                print(f"Testing template: {template_name}")
                
                # Capture warnings during template loading
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    
                    template = cv2.imread(template_path)
                    
                    if template is not None:
                        print(f"  ✓ Loaded successfully: {template.shape}")
                    else:
                        print(f"  ✗ Failed to load template")
                    
                    # Check for warnings
                    template_warnings = [warning for warning in w if any(term in str(warning.message).lower() 
                                       for term in ['libpng', 'sbit', 'invalid'])]
                    
                    if template_warnings:
                        warnings_found += len(template_warnings)
                        print(f"  ⚠ {len(template_warnings)} warnings during loading")
                        for warning in template_warnings:
                            print(f"    - {warning.message}")
                    else:
                        print(f"  ✓ No warnings during loading")
            else:
                print(f"Template not found: {template_name}")
        
        print(f"\nSummary: {templates_found} templates tested, {warnings_found} warnings found")
        
        if warnings_found == 0:
            print("✓ Template loading test passed - no warnings")
        else:
            print(f"⚠ Template loading test found {warnings_found} warnings")
        
        return warnings_found == 0
        
    except Exception as e:
        print(f"✗ Template loading test failed: {e}")
        return False

def test_map_trade_detection():
    """Test map trade detection functions"""
    print("\n=== Testing Map Trade Detection Functions ===")
    
    try:
        from map_trade import MapTradeModule
        from module_config_manager import ModuleConfigManager
        
        config_manager = ModuleConfigManager()
        module = MapTradeModule(config_manager)
        
        # Test screen state detection
        print("Testing screen state detection...")
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            state = module._detect_screen_state()
            print(f"  Detected state: {state}")
            
            # Check for warnings
            detection_warnings = [warning for warning in w if any(term in str(warning.message).lower() 
                                for term in ['libpng', 'sbit', 'invalid'])]
            
            if detection_warnings:
                print(f"  ⚠ {len(detection_warnings)} warnings during detection")
                for warning in detection_warnings:
                    print(f"    - {warning.message}")
                return False
            else:
                print("  ✓ No warnings during detection")
        
        print("✓ Map trade detection test completed")
        return True
        
    except Exception as e:
        print(f"✗ Map trade detection test failed: {e}")
        return False

def main():
    """Run all libpng fix tests"""
    print("Testing libpng Warning Fixes in Map Trade Module...\n")
    
    tests = [
        test_warning_suppression,
        test_opencv_processing,
        test_template_loading,
        test_map_trade_detection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! libpng warnings should no longer disrupt map trade detection.")
        print("\nFixes implemented:")
        print("✓ Comprehensive warning suppression")
        print("✓ Error handling around image processing")
        print("✓ Robust template detection")
        print("✓ Fallback mechanisms for failed operations")
    else:
        print("⚠ Some tests failed. Map trade detection may still be affected by warnings.")
        print("\nIf issues persist, check:")
        print("- Template file integrity")
        print("- OpenCV installation")
        print("- PIL/Pillow version compatibility")

if __name__ == "__main__":
    main()
