"""
Unified Configuration Manager for Last War Automation System
Handles loading, saving, validation, and migration of configuration data
"""

import json
import configparser
import os
import shutil
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging

class UnifiedConfigManager:
    """Manages the unified configuration system"""
    
    def __init__(self, config_file: str = "unified_config.json"):
        self.config_file = config_file
        self.config_data: Dict[str, Any] = {}
        self.logger = logging.getLogger("UnifiedConfig")
        
        # Legacy file paths for migration
        self.legacy_files = {
            'module_configs': 'module_configs.json',
            'template_scanner': 'template_scanner_config.json',
            'basic_config': 'config.ini',
            'map_coordinates': 'templates/Good_setting_map.json'
        }
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from unified file or migrate from legacy files"""
        if os.path.exists(self.config_file):
            self.logger.info(f"Loading unified configuration from {self.config_file}")
            return self._load_unified_config()
        else:
            self.logger.info("Unified config not found, attempting migration from legacy files")
            return self._migrate_from_legacy()
    
    def _load_unified_config(self) -> Dict[str, Any]:
        """Load from unified configuration file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)
            
            # Validate configuration structure
            if not self._validate_config():
                raise ValueError("Configuration validation failed")
                
            self.logger.info("Unified configuration loaded successfully")
            return self.config_data
            
        except Exception as e:
            self.logger.error(f"Failed to load unified config: {str(e)}")
            raise
    
    def _migrate_from_legacy(self) -> Dict[str, Any]:
        """Migrate configuration from legacy files"""
        self.logger.info("Starting migration from legacy configuration files")
        
        # Start with base unified config structure
        self.config_data = {
            "config_version": "1.0.0",
            "last_updated": datetime.now().isoformat(),
            "system_settings": {},
            "global_coordinates": {},
            "modules": {},
            "template_exclusion_rules": []
        }
        
        # Migrate basic settings from config.ini
        self._migrate_basic_config()
        
        # Migrate module configurations
        self._migrate_module_configs()
        
        # Migrate template scanner settings
        self._migrate_template_scanner()
        
        # Migrate user coordinates
        self._migrate_user_coordinates()
        
        # Save migrated configuration
        self.save_config()
        
        self.logger.info("Migration completed successfully")
        return self.config_data
    
    def _migrate_basic_config(self):
        """Migrate settings from config.ini"""
        config_ini_path = self.legacy_files['basic_config']
        if not os.path.exists(config_ini_path):
            self.logger.warning(f"Legacy config file not found: {config_ini_path}")
            return
            
        try:
            config = configparser.ConfigParser()
            config.read(config_ini_path)
            
            # Migrate general settings
            if 'GENERAL' in config:
                general = config['GENERAL']
                self.config_data['system_settings'].update({
                    'scan_interval': general.getfloat('scan_interval', 0.1),
                    'priority_check_interval': general.getfloat('priority_check_interval', 1.0),
                    'screenshot_path': general.get('screenshot_path', 'screenshots/'),
                    'templates_path': general.get('templates_path', 'templates/')
                })
            
            # Migrate GUI settings
            if 'GUI' in config:
                gui = config['GUI']
                self.config_data['system_settings'].update({
                    'window_width': gui.getint('window_width', 800),
                    'window_height': gui.getint('window_height', 600),
                    'theme': gui.get('theme', 'dark')
                })
                
            self.logger.info("Basic configuration migrated successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to migrate basic config: {str(e)}")
    
    def _migrate_module_configs(self):
        """Migrate module configurations from module_configs.json"""
        module_config_path = self.legacy_files['module_configs']
        if not os.path.exists(module_config_path):
            self.logger.warning(f"Legacy module config file not found: {module_config_path}")
            return
            
        try:
            with open(module_config_path, 'r', encoding='utf-8') as f:
                legacy_modules = json.load(f)
            
            for module_name, module_data in legacy_modules.items():
                # Convert legacy module structure to unified format
                unified_module = {
                    'enabled': module_data.get('enabled', True),
                    'priority': module_data.get('priority', 0),
                    'cooldown': module_data.get('cooldown', 0.0),
                    'manual_trigger': module_data.get('manual_trigger', False),
                    'settings': {},
                    'templates': {},
                    'coordinates': {},
                    'scan_regions': {}
                }
                
                # Migrate module-specific settings
                for key, value in module_data.items():
                    if key not in ['enabled', 'priority', 'cooldown', 'manual_trigger', 
                                 'templates', 'click_coordinates', 'scan_regions', 'actions', 'text_patterns']:
                        unified_module['settings'][key] = value
                
                # Migrate templates
                if 'templates' in module_data:
                    for template in module_data['templates']:
                        template_name = template['name']
                        unified_module['templates'][template_name] = {
                            'threshold': template.get('threshold', 0.8),
                            'required': template.get('required', False),
                            'description': template.get('description', ''),
                            'scanner_priority': 0,  # Will be set from template scanner config
                            'scanner_enabled': False,  # Will be set from template scanner config
                            'scanner_action': 'default'  # Will be set from template scanner config
                        }
                
                # Migrate click coordinates
                if 'click_coordinates' in module_data:
                    for coord in module_data['click_coordinates']:
                        coord_name = coord['name']
                        unified_module['coordinates'][coord_name] = {
                            'x': coord['x'],
                            'y': coord['y'],
                            'delay': coord.get('delay', 1.0),
                            'repeat': coord.get('repeat', 1),
                            'use_esc_key': coord.get('use_esc_key', False),
                            'description': coord.get('description', ''),
                            'enabled': coord.get('enabled', True)
                        }
                
                # Migrate scan regions
                if 'scan_regions' in module_data:
                    for region in module_data['scan_regions']:
                        region_name = region['name']
                        unified_module['scan_regions'][region_name] = {
                            'x': region['x'],
                            'y': region['y'],
                            'width': region['width'],
                            'height': region['height'],
                            'description': region.get('description', '')
                        }
                
                # Handle special cases for specific modules
                if module_name == 'map_trade' and 'map_piece_coordinates' in module_data:
                    unified_module['map_piece_coordinates'] = module_data['map_piece_coordinates']
                
                self.config_data['modules'][module_name] = unified_module
            
            self.logger.info(f"Migrated {len(legacy_modules)} modules successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to migrate module configs: {str(e)}")
    
    def _migrate_template_scanner(self):
        """Migrate template scanner configuration"""
        scanner_config_path = self.legacy_files['template_scanner']
        if not os.path.exists(scanner_config_path):
            self.logger.warning(f"Legacy template scanner config not found: {scanner_config_path}")
            return
            
        try:
            with open(scanner_config_path, 'r', encoding='utf-8') as f:
                scanner_config = json.load(f)
            
            # Migrate template mappings to module templates
            if 'template_mappings' in scanner_config:
                for mapping in scanner_config['template_mappings']:
                    template_name = mapping['template_name']
                    module_name = mapping['module']
                    
                    # Find the template in the unified config and update scanner settings
                    if (module_name in self.config_data['modules'] and 
                        template_name in self.config_data['modules'][module_name]['templates']):
                        
                        template_config = self.config_data['modules'][module_name]['templates'][template_name]
                        template_config.update({
                            'scanner_priority': mapping.get('priority', 0),
                            'scanner_enabled': mapping.get('enabled', True),
                            'scanner_action': mapping.get('action', 'default'),
                            'threshold': mapping.get('threshold', template_config['threshold'])
                        })
            
            # Migrate exclusion rules
            if 'exclusion_rules' in scanner_config:
                self.config_data['template_exclusion_rules'] = scanner_config['exclusion_rules']
            
            # Migrate scanner settings
            if 'scanner_settings' in scanner_config:
                scanner_settings = scanner_config['scanner_settings']
                self.config_data['system_settings'].update({
                    'centralized_scanner_enabled': True,
                    'max_concurrent_detections': scanner_settings.get('max_concurrent_detections', 3),
                    'template_cache_size': scanner_settings.get('template_cache_size', 100),
                    'detection_timeout': scanner_settings.get('detection_timeout', 5.0),
                    'debug_mode': scanner_settings.get('debug_mode', False)
                })
            
            self.logger.info("Template scanner configuration migrated successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to migrate template scanner config: {str(e)}")
    
    def _migrate_user_coordinates(self):
        """Migrate user coordinates from Good_setting_map.json"""
        coord_path = self.legacy_files['map_coordinates']
        if not os.path.exists(coord_path):
            self.logger.warning(f"User coordinates file not found: {coord_path}")
            return
            
        try:
            with open(coord_path, 'r', encoding='utf-8') as f:
                user_coords = json.load(f)
            
            # Convert to unified format
            map_pieces = {}
            for piece_name, coords in user_coords.items():
                if isinstance(coords, list) and len(coords) == 2:
                    map_pieces[piece_name] = {
                        'x': coords[0],
                        'y': coords[1],
                        'description': f'User-defined {piece_name} click position'
                    }
            
            self.config_data['global_coordinates']['map_pieces'] = map_pieces
            self.logger.info(f"Migrated {len(map_pieces)} user coordinate sets")
            
        except Exception as e:
            self.logger.error(f"Failed to migrate user coordinates: {str(e)}")
    
    def _validate_config(self) -> bool:
        """Validate the configuration structure"""
        required_keys = ['config_version', 'system_settings', 'modules']
        
        for key in required_keys:
            if key not in self.config_data:
                self.logger.error(f"Missing required configuration key: {key}")
                return False
        
        # Validate modules structure
        for module_name, module_config in self.config_data['modules'].items():
            required_module_keys = ['enabled', 'priority', 'templates', 'coordinates']
            for key in required_module_keys:
                if key not in module_config:
                    self.logger.error(f"Module {module_name} missing required key: {key}")
                    return False
        
        return True
    
    def save_config(self):
        """Save the unified configuration to file"""
        try:
            # Update timestamp
            self.config_data['last_updated'] = datetime.now().isoformat()
            
            # Create backup of existing file
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.config_file, backup_file)
                self.logger.info(f"Created backup: {backup_file}")
            
            # Save new configuration
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {str(e)}")
            raise
    
    def get_module_config(self, module_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific module"""
        return self.config_data.get('modules', {}).get(module_name)
    
    def update_module_config(self, module_name: str, config: Dict[str, Any]):
        """Update configuration for a specific module"""
        if 'modules' not in self.config_data:
            self.config_data['modules'] = {}
        
        self.config_data['modules'][module_name] = config
        self.logger.info(f"Updated configuration for module: {module_name}")
    
    def get_system_settings(self) -> Dict[str, Any]:
        """Get system settings"""
        return self.config_data.get('system_settings', {})
    
    def update_system_settings(self, settings: Dict[str, Any]):
        """Update system settings"""
        if 'system_settings' not in self.config_data:
            self.config_data['system_settings'] = {}
        
        self.config_data['system_settings'].update(settings)
        self.logger.info("Updated system settings")
    
    def get_global_coordinates(self) -> Dict[str, Any]:
        """Get global coordinates"""
        return self.config_data.get('global_coordinates', {})
    
    def backup_legacy_files(self):
        """Create backup of legacy configuration files before migration"""
        backup_dir = f"legacy_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        for file_type, file_path in self.legacy_files.items():
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
                self.logger.info(f"Backed up {file_path} to {backup_path}")
        
        return backup_dir
