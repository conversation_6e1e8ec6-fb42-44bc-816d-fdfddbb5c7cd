#!/usr/bin/env python3
"""
Test different image filters to enhance OCR accuracy
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def apply_filters_and_test(image, x, y):
    """Apply various filters and test OCR on each"""
    results = {}
    
    # Save original
    cv2.imwrite(f"filter_original_{x}_{y}.png", image)
    
    # 1. Simple grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    cv2.imwrite(f"filter_gray_{x}_{y}.png", gray)
    results['gray'] = test_ocr(gray)
    
    # 2. Inverted colors
    inverted = cv2.bitwise_not(gray)
    cv2.imwrite(f"filter_inverted_{x}_{y}.png", inverted)
    results['inverted'] = test_ocr(inverted)
    
    # 3. High contrast threshold
    _, thresh_binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
    cv2.imwrite(f"filter_thresh_binary_{x}_{y}.png", thresh_binary)
    results['thresh_binary'] = test_ocr(thresh_binary)
    
    # 4. Inverted threshold
    _, thresh_binary_inv = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
    cv2.imwrite(f"filter_thresh_inv_{x}_{y}.png", thresh_binary_inv)
    results['thresh_inv'] = test_ocr(thresh_binary_inv)
    
    # 5. OTSU threshold (automatic)
    _, thresh_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    cv2.imwrite(f"filter_otsu_{x}_{y}.png", thresh_otsu)
    results['otsu'] = test_ocr(thresh_otsu)
    
    # 6. OTSU inverted
    _, thresh_otsu_inv = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    cv2.imwrite(f"filter_otsu_inv_{x}_{y}.png", thresh_otsu_inv)
    results['otsu_inv'] = test_ocr(thresh_otsu_inv)
    
    # 7. Adaptive threshold
    adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    cv2.imwrite(f"filter_adaptive_{x}_{y}.png", adaptive)
    results['adaptive'] = test_ocr(adaptive)
    
    # 8. Adaptive inverted
    adaptive_inv = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2)
    cv2.imwrite(f"filter_adaptive_inv_{x}_{y}.png", adaptive_inv)
    results['adaptive_inv'] = test_ocr(adaptive_inv)
    
    # 9. Morphological operations (enhance text)
    kernel = np.ones((2,2), np.uint8)
    morph = cv2.morphologyEx(thresh_otsu, cv2.MORPH_CLOSE, kernel)
    cv2.imwrite(f"filter_morph_{x}_{y}.png", morph)
    results['morph'] = test_ocr(morph)
    
    # 10. Gaussian blur + threshold
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    _, blur_thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    cv2.imwrite(f"filter_blur_thresh_{x}_{y}.png", blur_thresh)
    results['blur_thresh'] = test_ocr(blur_thresh)
    
    # 11. Sharpen filter
    kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(gray, -1, kernel_sharpen)
    cv2.imwrite(f"filter_sharpened_{x}_{y}.png", sharpened)
    results['sharpened'] = test_ocr(sharpened)
    
    # 12. Edge enhancement
    edges = cv2.Canny(gray, 50, 150)
    cv2.imwrite(f"filter_edges_{x}_{y}.png", edges)
    results['edges'] = test_ocr(edges)
    
    return results

def test_ocr(image):
    """Test OCR on processed image"""
    try:
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789',
            '--psm 10 -c tessedit_char_whitelist=0123456789'
        ]
        
        for config in configs:
            text = pytesseract.image_to_string(image, config=config).strip()
            if text.isdigit():
                return int(text)
        
        return None
    except:
        return None

def test_mouse_position():
    """Test filters on current mouse position"""
    print("Testing filters at current mouse position...")

    x, y = pyautogui.position()
    print(f"Mouse at ({x}, {y})")

    # Capture region
    size = 30
    capture_x = x - size // 2
    capture_y = y - size // 2
    screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
    image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    # Test all filters
    results = apply_filters_and_test(image, x, y)

    print(f"\n🎯 FILTER RESULTS for ({x}, {y}):")
    print("-" * 40)

    successful_filters = []
    for filter_name, result in results.items():
        if result is not None:
            print(f"✅ {filter_name:15}: {result}")
            successful_filters.append(filter_name)
        else:
            print(f"❌ {filter_name:15}: Failed")

    if successful_filters:
        print(f"\n🏆 WORKING FILTERS: {', '.join(successful_filters)}")
        print(f"📁 Check saved images: filter_*_{x}_{y}.png")
    else:
        print("\n❌ No filters worked - check saved images manually")

    return successful_filters

def test_specific_coordinates():
    """Test filters on known coordinates"""
    coords = [
        (532, 436),  # M1
        (613, 436),  # M2
        (654, 521),  # M6 - this one worked before
    ]
    
    print("Testing filters on specific coordinates...")
    
    for i, (x, y) in enumerate(coords):
        print(f"\nTesting coordinate {i+1}: ({x}, {y})")
        
        # Capture region
        size = 30
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Test all filters
        results = apply_filters_and_test(image, x, y)
        
        successful = [name for name, result in results.items() if result is not None]
        if successful:
            print(f"✅ Working filters: {', '.join(successful)}")
        else:
            print("❌ No filters worked")

if __name__ == "__main__":
    print("Make sure you're on the map trade screen")
    print()
    print("Choose method:")
    print("1. Test at current mouse position")
    print("2. Test specific coordinates automatically")

    choice = input("Enter choice (1 or 2): ")

    if choice == "1":
        print("Position your mouse over a map piece number, then press ENTER")
        input("Press ENTER when ready: ")
        test_mouse_position()
    else:
        test_specific_coordinates()

    print("\n✅ Check the saved filter images to see which ones make numbers clearest!")
