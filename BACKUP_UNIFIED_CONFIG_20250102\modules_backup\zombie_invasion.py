"""
Zombie Invasion Module for Last War automation
Automatically participates in zombie invasion events by:
1. Checking if squads are available (not out battling)
2. Clicking Events
3. Clicking Search
4. Clicking Sword to attack
5. Clicking March
6. Repeating until all squads are out or no stamina
"""
import time
import pyautogui
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np

from enhanced_base_module import EnhancedBaseModule


class ZombieInvasionModule(EnhancedBaseModule):
    """Zombie invasion event automation module"""
    
    def __init__(self):
        super().__init__(name="zombie_invasion", priority=2, enabled=True)  # Lower priority to allow help_click
        
        # Configuration
        self.events_button = "events_button"
        self.search_button = "search_button"
        self.sword_icon = "sword_icon"
        self.march_button = "march_button"
        self.squad_available_template = "squad_available"
        self.no_stamina_template = "no_stamina"
        self.no_monsters_template = "no_monsters"  # Template for "no monsters found" message

        # Squad status templates - ADD THESE TEMPLATES IN CONFIG
        self.squad_0_4 = "squad_0_4"  # Template for "0/4" or empty squad status
        self.squad_1_4 = "squad_1_4"  # Template for "1/4" squad status
        self.squad_2_4 = "squad_2_4"  # Template for "2/4" squad status
        self.squad_3_4 = "squad_3_4"  # Template for "3/4" squad status
        self.squad_4_4 = "squad_4_4"  # Template for "4/4" squad status

        # Debug: Log the template names we're looking for
        self.logger.info(f"Squad templates configured: {[self.squad_0_4, self.squad_1_4, self.squad_2_4, self.squad_3_4, self.squad_4_4]}")
        
        # Coordinates
        self.events_coordinate = "events_menu"
        self.search_coordinate = "search_zombie"
        self.sword_coordinate = "sword_attack"
        self.march_coordinate = "march_troops"
        
        # Regions
        self.events_region = "events_area"
        self.squad_status_region = "squad_status"
        self.stamina_region = "stamina_area"
        
        # Settings - Reliable execution with proper delays
        self.max_squads = 4  # Correct squad count
        self.wait_between_actions = 0.3  # Fast but reliable clicking
        self.max_attempts_per_cycle = 8  # Fewer attempts, faster cycles
        self.click_delay = 0.2  # Quick but reliable delay
        self.navigation_delay = 1.5  # Proper delay to prevent skipping
        self.no_monster_pause = 300  # 5 minutes pause when no monsters found
        self.manual_wait_until = 0  # Manual wait time override
        self.cooldown = 0.0  # NO cooldown - immediate deployment on every scan

        # Live status tracking
        self.current_activity = "Idle"
        self.current_progress = "N/A"
        self.waiting_for = "N/A"
        self.last_action = "N/A"
        self.current_step = "N/A"
        self.execution_progress = "N/A"
        self.waiting_for_template = "N/A"
        self.is_executing = False

        # Squad deployment counter - SAFETY BACKUP
        self.squads_deployed_count = 0
        self.max_squads_allowed = 4

        # Performance optimization
        self.last_status_check_time = 0
        self.cached_squad_status = None
        self.status_cache_duration = 1.0  # Cache squad status for 1 second

        # Intelligent stamina and page tracking
        self.last_stamina_check = 0
        self.last_known_stamina = 0
        self.min_stamina_required = 10  # Don't try if stamina < 10
        self.stamina_wait_time = 30.0   # Wait 30 seconds before rechecking stamina
        self.last_page_check = 0
        self.page_check_interval = 5.0  # Check page every 5 seconds

    def is_on_cooldown(self) -> bool:
        """OVERRIDE: Never on cooldown - always ready to deploy!"""
        return False

    def get_cooldown(self) -> float:
        """OVERRIDE: Zero cooldown for aggressive deployment"""
        return 0.0

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to can_execute"""
        return self.can_execute(screen_data)

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to execute"""
        return self.execute(screen_data)
        
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """INTELLIGENT: Page detection, stamina checking, and squad status"""
        import time
        current_time = time.time()

        templates_detected = screen_data.get('templates_detected', {})

        # QUICK CHECK 1: If we recently detected 4/4, don't do heavy processing
        if templates_detected.get('squad_4_4', False):
            self.current_activity = "All squads deployed (4/4)"
            self.waiting_for = "Squad return"
            return False

        # INTELLIGENT PAGE CHECK: Only check page periodically to avoid spam
        if current_time - self.last_page_check > self.page_check_interval:
            self.last_page_check = current_time

            # Check if we're on main screen (Events button visible)
            if not templates_detected.get('events_button', False):
                self.current_activity = "Not on main screen"
                self.waiting_for = "Navigate to main screen"
                return False

        # INTELLIGENT STAMINA CHECK: Disabled for now - rely on squad availability
        # TODO: Implement proper OCR-based stamina reading
        # stamina_status = self._check_stamina_intelligent(screen_data, current_time)
        # if not stamina_status:
        #     return False  # Stamina too low or waiting for recheck

        # QUICK CHECK 3: If any available squad templates detected, we can potentially execute
        available_squad_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4']
        has_available_squads = any(templates_detected.get(template, False) for template in available_squad_templates)

        if not has_available_squads:
            # No available squad templates detected - either all deployed or not on right screen
            self.current_activity = "No available squads detected"
            self.waiting_for = "Squad availability or correct page"
            return False

        # HEAVY PROCESSING: Only do detailed squad status check if all quick checks pass
        squad_status = self._get_current_squad_status(screen_data)

        if squad_status:
            used_squads, total_squads = squad_status

            # SMART RESET: If screen shows fewer squads than counter, squads returned
            if used_squads < self.squads_deployed_count:
                old_count = self.squads_deployed_count
                self.squads_deployed_count = used_squads  # Sync counter with screen
                self.logger.info(f"SQUADS RETURNED: Counter reset from {old_count} to {used_squads}")

            # PRIMARY STOP: If screen shows 4/4, stop deployment
            if used_squads >= 4:
                self.current_activity = f"SCREEN: {used_squads}/{total_squads} squads deployed"
                self.waiting_for = "Squad return"
                return False

            # Screen shows less than 4/4 - can deploy more
            self.logger.info(f"SCREEN SHOWS {used_squads}/4 - CAN DEPLOY MORE")
            self.current_activity = f"Ready to deploy (squads: {used_squads}/4)"
            return True

        # 3. BACKUP FAILSAFE: Counter protection (in case screen detection fails)
        if self.squads_deployed_count >= self.max_squads_allowed:
            self.current_activity = f"FAILSAFE: {self.squads_deployed_count}/{self.max_squads_allowed}"
            self.waiting_for = "Squad return"
            self.logger.warning(f"BACKUP FAILSAFE: Counter at {self.squads_deployed_count}/{self.max_squads_allowed} - BLOCKED")
            return False

            # If less than 4/4, DEPLOY IMMEDIATELY on every scan
            remaining = total_squads - used_squads
            next_squad = used_squads + 1
            self.current_activity = f"DEPLOYING: {used_squads}/{total_squads} -> {next_squad}/{total_squads}"
            self.waiting_for = "Immediate deployment"
            self.logger.info(f"SCAN DETECTED {used_squads}/{total_squads} - DEPLOYING SQUAD {next_squad} NOW!")
            return True
        else:
            # No template detected - DEPLOY ANYWAY (aggressive)
            self.current_activity = "DEPLOYING: Status unknown"
            self.waiting_for = "Immediate deployment"
            self.logger.info("NO TEMPLATE DETECTED - DEPLOYING SQUAD NOW!")
            return True
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """SAFE: Deploy one squad with 4/4 safety check"""
        try:
            # Check current status for logging
            squad_status = self._get_current_squad_status(screen_data)
            if squad_status:
                used_squads, total_squads = squad_status
                next_squad = used_squads + 1
                self.logger.info(f"DEPLOYING SQUAD {next_squad}/4 (Current: {used_squads}/4)")
            else:
                self.logger.info("DEPLOYING SQUAD - Events -> Search -> Sword -> March")

            # Execute deployment sequence
            self.logger.info("Starting invasion cycle...")
            cycle_result = self._execute_invasion_cycle()
            self.logger.info(f"Invasion cycle result: {cycle_result}")

            if cycle_result:
                self.squads_deployed_count += 1  # CRITICAL: Increment counter
                self.logger.info(f"Squad deployed successfully! Total deployed: {self.squads_deployed_count}/{self.max_squads_allowed}")
                return True
            else:
                self.logger.warning("Deployment cycle returned False - will retry in 3 seconds")
                # Add small delay before retry to prevent rapid clicking
                import time
                time.sleep(3.0)
                # Don't get stuck - return True so main controller continues scanning
                return True

        except Exception as e:
            self.logger.error(f"Error deploying squad: {str(e)} - will retry on next scan")
            # Don't get stuck - return True so main controller continues
            return True
    
    def _check_squads_available(self, screen_data: Dict[str, Any]) -> bool:
        """Check if squads are available by reading squad ratio (e.g., 1/4, 2/4, etc.)"""
        try:
            # Get fresh screen data for squad status region
            squad_text = self._extract_text_from_region(self.squad_status_region)

            if not squad_text:
                self.logger.info("No text found in squad status region - assuming squads available")
                return True

            self.logger.info(f"Squad status text detected: '{squad_text}'")

            # Look for squad ratio patterns like "1/4", "2/4", "3/4", "4/4"
            import re
            squad_pattern = r'(\d+)/(\d+)'
            matches = re.findall(squad_pattern, squad_text)

            if matches:
                for used_str, total_str in matches:
                    try:
                        used_squads = int(used_str)
                        total_squads = int(total_str)

                        available_squads = total_squads - used_squads
                        self.logger.info(f"Squad status: {used_squads}/{total_squads} - Available: {available_squads}")

                        # Goal: reach 4/4 deployment, so continue if not at max
                        if used_squads < total_squads:
                            self.logger.info(f"Can deploy more squads ({used_squads}/{total_squads}) - continuing")
                            return True
                        else:
                            self.logger.info(f"Target reached: {used_squads}/{total_squads} squads deployed - stopping")
                            return False

                    except ValueError:
                        continue

            # Try alternative patterns for squad detection
            alt_patterns = [
                r'(\d+)\s*of\s*(\d+)',  # "2 of 4"
                r'(\d+)\s*/\s*(\d+)',   # "2 / 4" with spaces
                r'squads?\s*(\d+)/(\d+)',  # "squads 2/4"
            ]

            for pattern in alt_patterns:
                matches = re.findall(pattern, squad_text.lower())
                if matches:
                    for used_str, total_str in matches:
                        try:
                            used_squads = int(used_str)
                            total_squads = int(total_str)
                            available_squads = total_squads - used_squads

                            self.logger.info(f"Squad status (alt pattern): {used_squads}/{total_squads} - Available: {available_squads}")

                            if available_squads > 0:
                                self.logger.info(f"{available_squads} squads available - continuing")
                                return True
                            else:
                                self.logger.info("All squads deployed - stopping")
                                return False
                        except ValueError:
                            continue

            # If no squad ratio found, check for specific text indicators
            squad_text_lower = squad_text.lower()

            # If we see "4/4" or "all deployed" type indicators, no squads available
            if any(indicator in squad_text_lower for indicator in ["4/4", "all deployed", "no squads", "busy"]):
                self.logger.info("Found 'all squads deployed' indicator - stopping")
                return False

            # If no squad indicators found, assume squads are available
            self.logger.info("WARNING: No clear squad ratio found - assuming squads available")
            return True

        except Exception as e:
            self.logger.error(f"Error checking squad availability: {str(e)}")
            return True  # Default to available
    
    def _check_no_stamina(self, screen_data: Dict[str, Any]) -> bool:
        """Check if stamina is below 10"""
        try:
            # Get fresh screen data for stamina region
            stamina_text = self._extract_text_from_region(self.stamina_region)

            if not stamina_text:
                self.logger.debug("No text found in stamina region, assuming stamina available")
                return False

            self.logger.debug(f"Stamina text: '{stamina_text}'")

            # Look for stamina numbers
            import re

            # Try different patterns for stamina
            stamina_patterns = [
                r'stamina[:\s]*(\d+)',  # "Stamina: 25" or "Stamina 25"
                r'(\d+)[/\s]*stamina',  # "25 Stamina" or "25/stamina"
                r'(\d+)[/\s]*\d+',      # "25/100" (current/max)
                r'\b(\d+)\b'            # Any number
            ]

            current_stamina = None

            for pattern in stamina_patterns:
                matches = re.findall(pattern, stamina_text.lower())
                if matches:
                    try:
                        # Take the first number found
                        current_stamina = int(matches[0])
                        break
                    except (ValueError, IndexError):
                        continue

            if current_stamina is not None:
                self.logger.info(f"Current stamina: {current_stamina}")

                # Check if stamina is below 10
                if current_stamina < 10:
                    self.logger.info(f"Stamina too low: {current_stamina} < 10")
                    return True
                else:
                    self.logger.debug(f"Stamina sufficient: {current_stamina} >= 10")
                    return False

            # If no stamina number found, check for text indicators
            stamina_text_lower = stamina_text.lower()

            # Check for explicit "no stamina" or "0" indicators
            no_stamina_indicators = ["no stamina", "0 stamina", "stamina: 0", "stamina 0"]

            for indicator in no_stamina_indicators:
                if indicator in stamina_text_lower:
                    self.logger.info(f"Found no stamina indicator: {indicator}")
                    return True

            # If no clear stamina info, assume we have stamina
            self.logger.debug("No clear stamina info found, assuming stamina available")
            return False

        except Exception as e:
            self.logger.error(f"Error checking stamina: {str(e)}")
            return False
    
    def _execute_invasion_cycle(self) -> bool:
        """RELIABLE: Events → Search → Sword → March with proper timing"""
        try:
            # Step 1: Click Events
            self.logger.info("Clicking Events")
            if not self._click_events():
                self.logger.error("Failed to click Events - aborting")
                return False
            time.sleep(1.5)  # Reliable timing

            # Step 2: Click Search
            self.logger.info("Clicking Search")
            if not self._click_search():
                self.logger.error("Failed to click Search - aborting")
                return False
            time.sleep(1.5)  # Reliable timing

            # Step 3: Click Sword
            self.logger.info("Clicking Sword")
            if not self._click_sword():
                self.logger.error("Failed to click Sword - aborting")
                return False
            time.sleep(1.5)  # Reliable timing

            # Step 4: Click March - CRITICAL STEP
            self.logger.info("Clicking March - CRITICAL STEP")
            if not self._click_march():
                self.logger.error("FAILED TO CLICK MARCH - DEPLOYMENT FAILED")
                return False

            # Step 5: Wait for march to complete
            self.logger.info("Waiting for march to complete...")
            time.sleep(3.0)  # Wait for march confirmation to process

            # Success! Let main controller handle UI recovery naturally
            self.logger.info("All clicks successful - squad deployed!")
            return True

        except Exception as e:
            self.logger.error(f"Error in invasion cycle: {str(e)}")
            return False
    
    def _click_events(self) -> bool:
        """Click the Events button"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.events_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Clicking Events button at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Events coordinate '{self.events_coordinate}' not found")
            return False

    def _click_search(self) -> bool:
        """Click the Search button"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.search_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Clicking Search button at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Search coordinate '{self.search_coordinate}' not found")
            return False
    
    def _click_sword(self) -> bool:
        """Click the Sword icon to attack"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.sword_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Clicking Sword icon at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Sword coordinate '{self.sword_coordinate}' not found")
            return False

    def _click_march(self) -> bool:
        """Click the March button"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.march_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Clicking March button at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"March coordinate '{self.march_coordinate}' not found")
            return False
    
    def _get_fresh_screen_data(self) -> Dict[str, Any]:
        """Get fresh screen data for next iteration"""
        try:
            from screen_scanner import ScreenScanner
            scanner = ScreenScanner()
            return scanner.scan_screen()
        except Exception as e:
            self.logger.error(f"Error getting fresh screen data: {str(e)}")
            return {}

    def _extract_text_from_region(self, region_name: str) -> str:
        """Extract text from a specific region using OCR"""
        try:
            # Get region coordinates
            region = self.config_manager.get_region_by_name(self.name, region_name)
            if not region:
                self.logger.warning(f"Region '{region_name}' not found")
                return ""

            region_x, region_y, region_w, region_h = region

            # Take screenshot of the region
            import pyautogui
            screenshot = pyautogui.screenshot(region=(region_x, region_y, region_w, region_h))

            # Extract text using OCR
            try:
                import pytesseract
                from PIL import Image

                # Convert to grayscale for better OCR
                screenshot = screenshot.convert('L')

                # Extract text
                text = pytesseract.image_to_string(screenshot, config='--psm 6')
                return text.strip()

            except ImportError:
                self.logger.warning("Tesseract OCR not available, cannot extract text")
                return ""
            except Exception as e:
                self.logger.warning(f"OCR extraction failed: {str(e)}")
                return ""

        except Exception as e:
            self.logger.error(f"Error extracting text from region '{region_name}': {str(e)}")
            return ""
    
    def get_cooldown(self) -> float:
        """Get cooldown time for zombie invasion"""
        return 60.0  # 1 minute between invasion sessions
    
    def get_invasion_stats(self) -> Dict[str, Any]:
        """Get zombie invasion statistics"""
        return {
            'module': self.name,
            'total_executions': self.execution_count,
            'last_execution': self.last_execution,
            'max_squads': self.max_squads,
            'cooldown_remaining': max(0, self.get_cooldown() - (time.time() - (self.last_execution or 0))),
            'status': 'active' if self.enabled else 'disabled'
        }

    def _wait_for_template(self, template_name: str, timeout: float = 5.0) -> bool:
        """Wait for a template to appear on screen"""
        try:
            import time
            start_time = time.time()

            while time.time() - start_time < timeout:
                # Get fresh screen data
                screen_data = self._get_fresh_screen_data()
                templates_found = screen_data.get('templates_found', {})

                if template_name in templates_found and templates_found[template_name]:
                    self.logger.debug(f"Template '{template_name}' appeared after {time.time() - start_time:.1f}s")
                    return True

                time.sleep(0.5)  # Check every 500ms to reduce spam

            self.logger.warning(f"Template '{template_name}' did not appear within {timeout}s")
            return False

        except Exception as e:
            self.logger.error(f"Error waiting for template '{template_name}': {str(e)}")
            return False

    def _ensure_main_screen(self) -> bool:
        """Ensure we're back on the main screen with Events button visible"""
        try:
            # Check if Events button is already visible
            screen_data = self._get_fresh_screen_data()
            templates_found = screen_data.get('templates_found', {})

            if self.events_button in templates_found and templates_found[self.events_button]:
                self.logger.debug("Already on main screen")
                return True

            # Try pressing ESC a few times to get back to main screen
            import pyautogui
            for i in range(3):
                self.logger.debug(f"Pressing ESC to return to main screen (attempt {i+1})")
                pyautogui.press('esc')
                time.sleep(0.5)

                # Check if Events button appeared
                if self._wait_for_template(self.events_button, timeout=5.0):
                    self.logger.info("Successfully returned to main screen")
                    return True

            self.logger.warning("Could not return to main screen")
            return False

        except Exception as e:
            self.logger.error(f"Error ensuring main screen: {str(e)}")
            return False

    def _check_no_monsters_found(self) -> bool:
        """Check if 'no monsters found' message is displayed"""
        try:
            # Get fresh screen data to check for no monsters message
            screen_data = self._get_fresh_screen_data()
            templates_found = screen_data.get('templates_found', {})

            # Check for no monsters template
            if self.no_monsters_template in templates_found and templates_found[self.no_monsters_template]:
                self.logger.info("No monsters template detected")
                return True

            # Also check for text-based detection in search results area
            text_regions = screen_data.get('text_regions', {})
            for region_name, text in text_regions.items():
                if text and any(phrase in text.lower() for phrase in [
                    "no monsters", "no targets", "no enemies", "not found",
                    "no results", "empty", "no zombies"
                ]):
                    self.logger.info(f"No monsters text detected: '{text}'")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking for no monsters: {str(e)}")
            return False

    def _return_to_game_window(self):
        """Return to game window by swiping down from top of screen"""
        try:
            import pyautogui
            import time

            # Get screen dimensions
            screen_width, screen_height = pyautogui.size()

            # Calculate swipe coordinates
            # Start: mid-top, 2 inches from top (approximately 192 pixels at 96 DPI)
            start_x = screen_width // 2
            start_y = 192  # About 2 inches from top

            # End: 1 inch down (approximately 96 pixels down)
            end_x = start_x
            end_y = start_y + 96  # 1 inch down

            self.logger.info(f"Returning to game window - swiping from ({start_x}, {start_y}) to ({end_x}, {end_y})")

            # Perform swipe gesture
            pyautogui.mouseDown(start_x, start_y, button='left')
            time.sleep(0.1)  # Brief hold
            pyautogui.moveTo(end_x, end_y, duration=0.3)  # Smooth drag
            pyautogui.mouseUp(button='left')

            # Brief pause to let the gesture complete
            time.sleep(0.5)

            self.logger.info("Swipe gesture completed - should return to game window")

        except Exception as e:
            self.logger.error(f"Error performing return to game window gesture: {str(e)}")

    def _get_current_squad_status(self, screen_data: Dict[str, Any]) -> tuple:
        """CACHED squad status check - avoid heavy processing every scan"""
        import time

        current_time = time.time()

        # Use cached result if recent enough
        if (self.cached_squad_status is not None and
            current_time - self.last_status_check_time < self.status_cache_duration):
            return self.cached_squad_status

        try:
            templates_found = screen_data.get('templates_found', {})

            # Debug: Show all available templates (only when doing fresh check)
            available_templates = [t for t in templates_found.keys() if 'squad' in t.lower()]
            self.logger.info(f"Available squad templates: {available_templates}")

            # Test each squad template in order: 4/4, 3/4, 2/4, 1/4, 0/4
            squad_checks = [
                (self.squad_4_4, 4, "4/4 - All squads deployed"),
                (self.squad_3_4, 3, "3/4 - Three squads deployed"),
                (self.squad_2_4, 2, "2/4 - Two squads deployed"),
                (self.squad_1_4, 1, "1/4 - One squad deployed"),
                (self.squad_0_4, 0, "0/4 - No squads deployed"),
            ]

            for template_name, squad_count, description in squad_checks:
                if template_name in templates_found and templates_found[template_name]:
                    matches = templates_found[template_name]
                    if matches and len(matches) > 0:
                        self.logger.info(f"TEMPLATE MATCH: {template_name} detected - {description}")
                        if squad_count >= 4:
                            self.logger.info(f"STOPPING CONDITION MET: {squad_count}/4 squads detected")

                        # Update cache and return
                        result = (squad_count, 4)
                        self.cached_squad_status = result
                        self.last_status_check_time = current_time
                        return result
                else:
                    self.logger.debug(f"Template {template_name} not detected")

            # If no squad template detected, assume 0/4 (safe for deployment)
            self.logger.warning("WARNING: No squad status template detected - assuming 0/4")
            result = (0, 4)
            self.cached_squad_status = result
            self.last_status_check_time = current_time
            return result

        except Exception as e:
            self.logger.error(f"Error checking squad status: {str(e)}")
            result = (0, 4)
            self.cached_squad_status = result
            self.last_status_check_time = current_time
            return result

    def get_required_templates(self) -> List[str]:
        """
        Return list of templates required by this module
        """
        return [
            self.events_button,      # events_button
            self.squad_0_4,         # squad_0_4
            self.squad_1_4,         # squad_1_4
            self.squad_2_4,         # squad_2_4
            self.squad_3_4,         # squad_3_4
            self.squad_4_4,         # squad_4_4
        ]

    def _check_stamina_intelligent(self, screen_data: Dict[str, Any], current_time: float) -> bool:
        """
        Intelligent stamina checking with waiting logic
        Returns True if stamina is sufficient, False if we should wait
        """
        try:
            # Only check stamina periodically to avoid constant OCR processing
            if current_time - self.last_stamina_check < self.stamina_wait_time:
                # Use cached stamina result
                if self.last_known_stamina < self.min_stamina_required:
                    remaining_wait = self.stamina_wait_time - (current_time - self.last_stamina_check)
                    self.current_activity = f"Waiting for stamina (last: {self.last_known_stamina})"
                    self.waiting_for = f"Stamina recheck in {remaining_wait:.0f}s"
                    return False
                else:
                    return True  # Last check showed sufficient stamina

            # Time to check stamina again
            self.last_stamina_check = current_time

            # Get current stamina using OCR
            current_stamina = self._get_current_stamina(screen_data)

            if current_stamina is not None:
                self.last_known_stamina = current_stamina

                if current_stamina < self.min_stamina_required:
                    self.logger.info(f"⚡ STAMINA CHECK: {current_stamina} < {self.min_stamina_required} - WAITING")
                    self.current_activity = f"Low stamina ({current_stamina}/{self.min_stamina_required})"
                    self.waiting_for = f"Stamina recharge (recheck in {self.stamina_wait_time}s)"
                    return False
                else:
                    self.logger.info(f"⚡ STAMINA CHECK: {current_stamina} >= {self.min_stamina_required} - READY")
                    return True
            else:
                # Couldn't read stamina - assume it's okay to avoid blocking
                self.logger.warning("⚡ STAMINA CHECK: Could not read stamina - assuming sufficient")
                return True

        except Exception as e:
            self.logger.error(f"Error checking stamina: {str(e)}")
            return True  # Don't block on errors

    def _get_current_stamina(self, screen_data: Dict[str, Any]) -> int:
        """
        Get current stamina using OCR
        Returns stamina value or None if couldn't read
        """
        try:
            # For now, let's use a simple approach - check if we can deploy squads
            # If we can see available squads, assume we have stamina
            # This is a placeholder - you can enhance with actual OCR later

            templates_detected = screen_data.get('templates_detected', {})

            # If we can see available squad templates, assume we have stamina
            available_squad_templates = ['squad_0_4', 'squad_1_4', 'squad_2_4', 'squad_3_4']
            has_available_squads = any(templates_detected.get(template, False) for template in available_squad_templates)

            if has_available_squads:
                # Assume we have sufficient stamina if squads are available
                return 15  # Return a value above minimum threshold
            else:
                # If no squads available, might be low stamina or all deployed
                squad_4_4 = templates_detected.get('squad_4_4', False)
                if squad_4_4:
                    # All squads deployed - assume we have stamina but can't use it
                    return 15
                else:
                    # No squads visible - might be low stamina
                    return 5  # Return below threshold to trigger wait

        except Exception as e:
            self.logger.error(f"Error reading stamina: {str(e)}")
            return None  # Couldn't read stamina

    # Centralized Scanner Action Methods
    def check_squads(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for squad_0_4 detection - check squad availability"""
        try:
            self.logger.info("🎯 CENTRALIZED: squad_0_4 detected - checking squad availability")
            # When no squads are available, we can still check if invasion is possible
            success = self.execute(screen_data)
            self.logger.info(f"✅ CENTRALIZED: Squad check completed - success: {success}")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in check_squads action: {e}")
            return False

    def deploy_squad(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for squad_X_4 detection - deploy available squads"""
        try:
            self.logger.info("🎯 CENTRALIZED: Squad available - deploying to zombie invasion")
            success = self.execute(screen_data)
            self.logger.info(f"✅ CENTRALIZED: Squad deployment completed - success: {success}")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in deploy_squad action: {e}")
            return False
