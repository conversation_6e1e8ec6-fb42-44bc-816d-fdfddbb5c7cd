#!/usr/bin/env python3
"""
Quick coordinate capture for map pieces
Simple tool to capture coordinates one by one
"""
import cv2
import numpy as np
import pytesseract
import pya<PERSON><PERSON><PERSON>

def test_coordinate_with_contrast_enhancement(x, y, expected_number):
    """Test coordinate using the proven contrast enhancement method"""
    try:
        # Configure Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Capture 15x15 region (smaller, more precise)
        size = 15
        region_x = x - size // 2
        region_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply contrast enhancement (the proven method)
        enhanced = cv2.convertScaleAbs(gray, alpha=2.0, beta=0)

        # Save debug images
        cv2.imwrite(f"debug_test_{x}_{y}_color.png", image)
        cv2.imwrite(f"debug_test_{x}_{y}_gray.png", gray)
        cv2.imwrite(f"debug_test_{x}_{y}_enhanced.png", enhanced)
        print(f"    Debug images saved: debug_test_{x}_{y}_*.png")

        # Use proven OCR method
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(enhanced, config=config).strip()

        success = text == str(expected_number)
        if success:
            print(f"    ✅ SUCCESS: OCR read '{text}' (expected: {expected_number})")
        elif text:
            print(f"    ❌ FAILED: OCR read '{text}' (expected: {expected_number})")
        else:
            print(f"    ❌ FAILED: OCR read empty string (expected: {expected_number})")

        return success, text

    except Exception as e:
        print(f"    ERROR: {e}")
        return False, f"ERROR: {e}"

def test_coordinate_comprehensive(x, y, expected_number):
    """Test coordinate with ALL OCR methods to see what works"""
    try:
        # Configure Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Capture 35x35 region
        size = 35
        region_x = x - size // 2
        region_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Save debug image
        cv2.imwrite(f"debug_test_position_{x}_{y}.png", image)
        print(f"    Debug image saved: debug_test_position_{x}_{y}.png")

        results = []
        best_result = None

        # Method 1: Original image with different PSM modes
        print("    Method 1: Original image")
        for psm in [8, 7, 6, 13, 10]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(gray, config=config).strip()
            success = text == str(expected_number)
            print(f"      PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"Original PSM {psm}"
            results.append((f"Original PSM {psm}", text, success))

        # Method 2: 2x scaling
        print("    Method 2: 2x scaling")
        scaled_2x = cv2.resize(gray, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
        cv2.imwrite(f"debug_2x_{x}_{y}.png", scaled_2x)
        for psm in [8, 7, 6, 13, 10]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(scaled_2x, config=config).strip()
            success = text == str(expected_number)
            print(f"      2x PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"2x PSM {psm}"
            results.append((f"2x PSM {psm}", text, success))

        # Method 3: 3x scaling
        print("    Method 3: 3x scaling")
        scaled_3x = cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
        cv2.imwrite(f"debug_3x_{x}_{y}.png", scaled_3x)
        for psm in [8, 7, 6, 13, 10]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(scaled_3x, config=config).strip()
            success = text == str(expected_number)
            print(f"      3x PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"3x PSM {psm}"
            results.append((f"3x PSM {psm}", text, success))

        # Method 4: 4x scaling
        print("    Method 4: 4x scaling")
        scaled_4x = cv2.resize(gray, None, fx=4, fy=4, interpolation=cv2.INTER_CUBIC)
        cv2.imwrite(f"debug_4x_{x}_{y}.png", scaled_4x)
        for psm in [8, 7, 6]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(scaled_4x, config=config).strip()
            success = text == str(expected_number)
            print(f"      4x PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"4x PSM {psm}"
            results.append((f"4x PSM {psm}", text, success))

        # Method 5: Binary thresholding
        print("    Method 5: Binary thresholding")
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        cv2.imwrite(f"debug_binary_{x}_{y}.png", binary)
        for psm in [8, 13]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(binary, config=config).strip()
            success = text == str(expected_number)
            print(f"      Binary PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"Binary PSM {psm}"
            results.append((f"Binary PSM {psm}", text, success))

        # Method 6: OTSU thresholding
        print("    Method 6: OTSU thresholding")
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        cv2.imwrite(f"debug_otsu_{x}_{y}.png", otsu)
        for psm in [8, 7, 6]:
            config = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(otsu, config=config).strip()
            success = text == str(expected_number)
            print(f"      OTSU PSM {psm}: '{text}' {'✅' if success else '❌'}")
            if success and not best_result:
                best_result = f"OTSU PSM {psm}"
            results.append((f"OTSU PSM {psm}", text, success))

        # Summary
        successful_methods = [method for method, text, success in results if success]
        if successful_methods:
            print(f"    🎉 WORKING METHODS: {', '.join(successful_methods)}")
            return True, best_result
        else:
            print(f"    ❌ NO METHODS WORKED for expected number {expected_number}")
            return False, "No methods worked"

    except Exception as e:
        print(f"    ERROR: {e}")
        return False, f"ERROR: {e}"

def capture_coordinates():
    """Capture coordinates for all map pieces"""
    
    # Expected numbers for each piece
    expected_numbers = {
        'M1': 4,
        'M2': 5, 
        'M3': 1,
        'M4': 3,
        'M5': 2,
        'M6': 4,
        'M7': 5
    }
    
    coordinates = {}
    
    print("=== QUICK COORDINATE CAPTURE ===")
    print("For each map piece:")
    print("1. Position mouse precisely over the NUMBER")
    print("2. Press ENTER to capture")
    print("3. Tool will test if it reads the correct number")
    print()
    
    for map_name, expected in expected_numbers.items():
        print(f"\n--- {map_name} (should read: {expected}) ---")
        
        while True:
            input(f"Position mouse over {map_name} number ({expected}) and press ENTER: ")
            
            # Get mouse position
            x, y = pyautogui.position()
            print(f"Testing position ({x}, {y})...")
            
            # Test OCR with proven contrast enhancement method
            success, result = test_coordinate_with_contrast_enhancement(x, y, expected)
            
            if success:
                print(f"✅ SUCCESS! OCR read: {result} (expected: {expected})")
                coordinates[map_name] = (x, y)
                break
            else:
                print(f"❌ FAILED! OCR read: {result} (expected: {expected})")
                retry = input("Try again? (y/n): ").lower().strip()
                if retry != 'y':
                    print(f"Using position anyway: ({x}, {y})")
                    coordinates[map_name] = (x, y)
                    break
    
    # Display results
    print("\n=== CALIBRATION RESULTS ===")
    print("Copy this code to your _define_map_piece_regions method:")
    print("=" * 50)
    print("return {")
    
    for map_name, (x, y) in coordinates.items():
        click_x = x - 20  # Click position slightly left and up
        click_y = y - 30
        print(f"    '{map_name}': {{")
        print(f"        'quantity_pos': ({x}, {y}),")
        print(f"        'click_pos': ({click_x}, {click_y}),")
        print(f"        'quantity': None")
        print(f"    }},")
    
    print("}")
    print("=" * 50)
    
    # Test all coordinates
    print("\n=== FINAL VERIFICATION ===")
    success_count = 0
    for map_name, (x, y) in coordinates.items():
        expected = expected_numbers[map_name]
        print(f"\nTesting {map_name} at ({x}, {y}) - expecting {expected}:")
        success, result = test_coordinate_with_contrast_enhancement(x, y, expected)
        if success:
            print(f"✅ {map_name}: {result}")
            success_count += 1
        else:
            print(f"❌ {map_name}: {result} (expected: {expected})")
    
    print(f"\nFINAL SUCCESS RATE: {success_count}/{len(coordinates)} ({success_count/len(coordinates)*100:.1f}%)")

if __name__ == "__main__":
    capture_coordinates()
