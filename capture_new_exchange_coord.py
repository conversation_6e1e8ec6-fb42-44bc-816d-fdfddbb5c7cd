#!/usr/bin/env python3
"""
Quick capture for New Exchange button coordinate
"""

import pyautogui
import time

def capture_new_exchange_coordinate():
 """Capture the exact new exchange button coordinate"""
 print("New Exchange Button Coordinate Capture")
 print("=" * 45)
 print()
 print("Steps:")
 print("1. Navigate to the screen where 'New Exchange' button is visible")
 print("2. This should be <PERSON>FT<PERSON> clicking the red thumb")
 print("3. Position mouse EXACTLY over the 'New Exchange' button")
 print("4. Press Enter to capture coordinate")
 print()
 
 input("Press Enter when 'New Exchange' button is visible and mouse is positioned...")
 
 x, y = pyautogui.position()
 print(f"\n New Exchange button coordinate: ({x}, {y})")
 
 # Test the coordinate immediately
 print(f"\nTesting click at ({x}, {y}) in 3 seconds...")
 for i in range(3, 0, -1):
 print(f"{i}...")
 time.sleep(1)
 
 print("Clicking now!")
 pyautogui.click(x, y)
 
 print(f"\n UPDATE THESE COORDINATES:")
 print("=" * 45)
 print(f"In modules/map_trade.py:")
 print(f"Line ~825 (in _click_new_exchange_button method):")
 print(f"default_x, default_y = {x}, {y}")
 print()
 print(f"Line ~840 (in _has_new_exchange_button method, if needed):")
 print(f"# Update any hardcoded coordinates to ({x}, {y})")
 
 return x, y

def test_current_coordinate():
 """Test clicking the current coordinate"""
 current_x, current_y = 1258, 1145 # Current coordinate
 
 print("Testing Current New Exchange Coordinate")
 print("=" * 40)
 print(f"Current coordinate: ({current_x}, {current_y})")
 print("Make sure 'New Exchange' button is visible")
 
 input("Press Enter to test current coordinate...")
 
 print("Clicking in 3 seconds...")
 for i in range(3, 0, -1):
 print(f"{i}...")
 time.sleep(1)
 
 print("Clicking now!")
 pyautogui.click(current_x, current_y)
 print("Click executed!")

def visual_coordinate_test():
 """Visual test to see where we're clicking"""
 print("Visual New Exchange Coordinate Test")
 print("=" * 40)
 
 print("Position your mouse over the 'New Exchange' button")
 input("Press Enter to capture and show visual...")
 
 x, y = pyautogui.position()
 
 # Take screenshot and mark the position
 import cv2
 import numpy as np
 
 screenshot = pyautogui.screenshot()
 image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
 
 # Draw crosshairs at the coordinate
 cv2.line(image, (x-30, y), (x+30, y), (0, 255, 0), 3) # Horizontal line
 cv2.line(image, (x, y-30), (x, y+30), (0, 255, 0), 3) # Vertical line
 
 # Draw a circle around the point
 cv2.circle(image, (x, y), 20, (255, 0, 0), 3)
 
 # Save the visual
 cv2.imwrite("debug_new_exchange_coordinate.png", image)
 
 print(f"New Exchange coordinate: ({x}, {y})")
 print("Visual saved as: debug_new_exchange_coordinate.png")
 print("Green crosshairs = mouse position")
 print("Blue circle = click area")

def main():
 """Main menu"""
 while True:
 print("\nNew Exchange Coordinate Capture")
 print("=" * 35)
 print("1. Capture New Exchange Coordinate")
 print("2. Test Current Coordinate")
 print("3. Visual Coordinate Test")
 print("0. Exit")
 
 choice = input("\nChoice (0-3): ").strip()
 
 if choice == '0':
 break
 elif choice == '1':
 x, y = capture_new_exchange_coordinate()
 
 # Ask if user wants to update the file
 update = input(f"\nUpdate map_trade.py with ({x}, {y})? (y/n): ").lower().strip()
 if update == 'y':
 update_map_trade_file(x, y)
 elif choice == '2':
 test_current_coordinate()
 elif choice == '3':
 visual_coordinate_test()
 else:
 print("Invalid choice")

def update_map_trade_file(x, y):
 """Update the map_trade.py file with new coordinate"""
 try:
 # Read the file
 with open('modules/map_trade.py', 'r') as f:
 content = f.read()
 
 # Replace the coordinate in _click_new_exchange_button method
 lines = content.split('\n')
 updated = False
 
 for i, line in enumerate(lines):
 # Look for the default coordinate line in _click_new_exchange_button
 if 'default_x, default_y =' in line and 'new exchange' in lines[i-2:i+2]:
 lines[i] = f' default_x, default_y = {x}, {y} # Updated coordinate'
 updated = True
 print(f" Updated line {i+1}: {lines[i].strip()}")
 break
 
 if updated:
 # Write back to file
 with open('modules/map_trade.py', 'w') as f:
 f.write('\n'.join(lines))
 print(" Successfully updated modules/map_trade.py")
 else:
 print(" Could not find the coordinate line to update")
 print("Please update manually:")
 print(f"default_x, default_y = {x}, {y}")
 
 except Exception as e:
 print(f" Error updating file: {e}")
 print("Please update manually:")
 print(f"default_x, default_y = {x}, {y}")

if __name__ == "__main__":
 try:
 main()
 except KeyboardInterrupt:
 print("\nInterrupted")
 except Exception as e:
 print(f"Error: {e}")
 import traceback
 traceback.print_exc()
