#!/usr/bin/env python3
"""
Map Trade Coordinate Diagnostic Tool
Analyzes coordinate loading and persistence issues in the map trade module
"""
import json
import sys
import os
from typing import Dict, Any, Optional

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from unified_config_manager import UnifiedConfigManager

class MapTradeCoordinateDiagnostic:
    def __init__(self):
        self.unified_config = UnifiedConfigManager()
        self.config_data = self.unified_config.load_config()
        
    def analyze_coordinate_sources(self):
        """Analyze all coordinate sources and identify conflicts"""
        print("🔍 MAP TRADE COORDINATE DIAGNOSTIC")
        print("=" * 60)
        
        # 1. Check unified_config.json global coordinates
        print("\n1️⃣ UNIFIED CONFIG - GLOBAL COORDINATES:")
        global_coords = self.config_data.get('global_coordinates', {})
        map_pieces_global = global_coords.get('map_pieces', {})
        
        if map_pieces_global:
            print("✅ Found global map piece coordinates:")
            for piece, coord in map_pieces_global.items():
                print(f"   {piece}: x={coord.get('x')}, y={coord.get('y')}")
        else:
            print("❌ No global map piece coordinates found")
        
        # 2. Check unified_config.json module-specific coordinates
        print("\n2️⃣ UNIFIED CONFIG - MODULE SPECIFIC COORDINATES:")
        map_trade_config = self.config_data.get('modules', {}).get('map_trade', {})
        map_piece_coords = map_trade_config.get('map_piece_coordinates', {})
        
        if map_piece_coords:
            print("✅ Found module-specific map piece coordinates:")
            for piece, coord in map_piece_coords.items():
                print(f"   {piece}: quantity=({coord.get('quantity_x')}, {coord.get('quantity_y')}), "
                      f"click=({coord.get('click_x')}, {coord.get('click_y')})")
        else:
            print("❌ No module-specific map piece coordinates found")
        
        # 3. Check legacy module_configs.json
        print("\n3️⃣ LEGACY CONFIG - MODULE_CONFIGS.JSON:")
        try:
            with open('module_configs.json', 'r') as f:
                legacy_config = json.load(f)
            
            legacy_map_trade = legacy_config.get('map_trade', {})
            legacy_coords = legacy_map_trade.get('map_piece_coordinates', {})
            
            if legacy_coords:
                print("✅ Found legacy map piece coordinates:")
                for piece, coord in legacy_coords.items():
                    print(f"   {piece}: quantity=({coord.get('quantity_x')}, {coord.get('quantity_y')}), "
                          f"click=({coord.get('click_x')}, {coord.get('click_y')})")
            else:
                print("❌ No legacy map piece coordinates found")
                
        except FileNotFoundError:
            print("❌ module_configs.json not found")
        except Exception as e:
            print(f"❌ Error reading legacy config: {e}")
        
        # 4. Check hardcoded coordinates in module
        print("\n4️⃣ HARDCODED COORDINATES IN MODULE:")
        print("⚠️ Module contains hardcoded coordinates in _define_map_piece_regions()")
        hardcoded_coords = {
            'M1': {'quantity_pos': (1083, 843), 'big_map_pos': (1080, 533), 'click_pos': (1031, 780)},
            'M2': {'quantity_pos': (1244, 843), 'big_map_pos': (1485, 585), 'click_pos': (1200, 784)},
            'M3': {'quantity_pos': (1403, 843), 'big_map_pos': (1323, 681), 'click_pos': (1364, 782)},
            'M4': {'quantity_pos': (1566, 844), 'big_map_pos': (1011, 835), 'click_pos': (1525, 786)},
            'M5': {'quantity_pos': (1162, 998), 'big_map_pos': (1157, 1030), 'click_pos': (1118, 946)},
            'M6': {'quantity_pos': (1327, 998), 'big_map_pos': (1412, 988), 'click_pos': (1281, 943)},
            'M7': {'quantity_pos': (1484, 997), 'big_map_pos': (1570, 832), 'click_pos': (1450, 945)}
        }
        
        for piece, coords in hardcoded_coords.items():
            print(f"   {piece}: quantity={coords['quantity_pos']}, "
                  f"big_map={coords['big_map_pos']}, click={coords['click_pos']}")
    
    def compare_coordinate_sources(self):
        """Compare coordinates from different sources to identify conflicts"""
        print("\n🔄 COORDINATE COMPARISON ANALYSIS:")
        print("=" * 60)
        
        # Get coordinates from different sources
        global_coords = self.config_data.get('global_coordinates', {}).get('map_pieces', {})
        module_coords = self.config_data.get('modules', {}).get('map_trade', {}).get('map_piece_coordinates', {})
        
        try:
            with open('module_configs.json', 'r') as f:
                legacy_config = json.load(f)
            legacy_coords = legacy_config.get('map_trade', {}).get('map_piece_coordinates', {})
        except:
            legacy_coords = {}
        
        # Hardcoded coordinates
        hardcoded_coords = {
            'M1': {'click_pos': (1031, 780)}, 'M2': {'click_pos': (1200, 784)},
            'M3': {'click_pos': (1364, 782)}, 'M4': {'click_pos': (1525, 786)},
            'M5': {'click_pos': (1118, 946)}, 'M6': {'click_pos': (1281, 943)},
            'M7': {'click_pos': (1450, 945)}
        }
        
        conflicts_found = False
        
        for piece in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
            print(f"\n📍 {piece} COORDINATE COMPARISON:")
            
            # Global coordinates
            if piece in global_coords:
                global_coord = global_coords[piece]
                print(f"   Global: ({global_coord.get('x')}, {global_coord.get('y')})")
            else:
                print(f"   Global: Not found")
            
            # Module-specific coordinates
            if piece in module_coords:
                module_coord = module_coords[piece]
                print(f"   Module: click=({module_coord.get('click_x')}, {module_coord.get('click_y')}), "
                      f"quantity=({module_coord.get('quantity_x')}, {module_coord.get('quantity_y')})")
            else:
                print(f"   Module: Not found")
            
            # Legacy coordinates
            if piece in legacy_coords:
                legacy_coord = legacy_coords[piece]
                print(f"   Legacy: click=({legacy_coord.get('click_x')}, {legacy_coord.get('click_y')}), "
                      f"quantity=({legacy_coord.get('quantity_x')}, {legacy_coord.get('quantity_y')})")
            else:
                print(f"   Legacy: Not found")
            
            # Hardcoded coordinates
            if piece in hardcoded_coords:
                hardcoded_coord = hardcoded_coords[piece]
                print(f"   Hardcoded: click={hardcoded_coord['click_pos']}")
            
            # Check for conflicts
            coords_list = []
            if piece in global_coords:
                coords_list.append(f"({global_coords[piece].get('x')}, {global_coords[piece].get('y')})")
            if piece in module_coords:
                coords_list.append(f"({module_coords[piece].get('click_x')}, {module_coords[piece].get('click_y')})")
            if piece in legacy_coords:
                coords_list.append(f"({legacy_coords[piece].get('click_x')}, {legacy_coords[piece].get('click_y')})")
            if piece in hardcoded_coords:
                coords_list.append(f"{hardcoded_coords[piece]['click_pos']}")
            
            unique_coords = set(coords_list)
            if len(unique_coords) > 1:
                print(f"   ⚠️ CONFLICT DETECTED: Multiple different coordinates found!")
                conflicts_found = True
            else:
                print(f"   ✅ No conflicts")
        
        if conflicts_found:
            print(f"\n❌ COORDINATE CONFLICTS DETECTED!")
            print("   This explains why updated coordinates aren't taking effect.")
        else:
            print(f"\n✅ No coordinate conflicts found")
    
    def identify_loading_priority(self):
        """Identify which coordinate source has priority in the current implementation"""
        print("\n🎯 COORDINATE LOADING PRIORITY ANALYSIS:")
        print("=" * 60)
        
        print("Current loading order in map_trade.py:")
        print("1. Module loads from unified_config global_coordinates.map_pieces")
        print("2. Module overwrites with hardcoded coordinates in _define_map_piece_regions()")
        print("3. Result: HARDCODED COORDINATES ALWAYS WIN")
        
        print("\n❌ ROOT CAUSE IDENTIFIED:")
        print("   The _define_map_piece_regions() method returns hardcoded coordinates")
        print("   that override any configuration-based coordinates loaded earlier.")
        
        print("\n💡 SOLUTION REQUIRED:")
        print("   Modify _define_map_piece_regions() to use configuration coordinates")
        print("   instead of hardcoded values.")
    
    def recommend_solution(self):
        """Recommend solution for coordinate persistence"""
        print("\n🔧 RECOMMENDED SOLUTION:")
        print("=" * 60)
        
        print("1. MODIFY _define_map_piece_regions() METHOD:")
        print("   - Remove hardcoded coordinates")
        print("   - Load coordinates from unified configuration")
        print("   - Use fallback to hardcoded values only if config missing")
        
        print("\n2. CONSOLIDATE COORDINATE SOURCES:")
        print("   - Use unified_config.json as single source of truth")
        print("   - Migrate any updated coordinates to unified config")
        print("   - Remove duplicate coordinate definitions")
        
        print("\n3. ADD RUNTIME COORDINATE VERIFICATION:")
        print("   - Log which coordinates are being used at runtime")
        print("   - Add coordinate validation and conflict detection")
        print("   - Provide clear error messages for missing coordinates")
        
        print("\n4. CREATE COORDINATE UPDATE MECHANISM:")
        print("   - Allow runtime coordinate updates through Config Helper")
        print("   - Implement coordinate persistence testing")
        print("   - Add coordinate backup and restore functionality")

def main():
    """Run coordinate diagnostic analysis"""
    try:
        print("Starting Map Trade Coordinate Diagnostic...")
        diagnostic = MapTradeCoordinateDiagnostic()

        diagnostic.analyze_coordinate_sources()
        diagnostic.compare_coordinate_sources()
        diagnostic.identify_loading_priority()
        diagnostic.recommend_solution()

        print("\n" + "=" * 60)
        print("🎯 DIAGNOSTIC COMPLETE")
        print("   Review the analysis above to understand coordinate persistence issues")
        print("   Implement the recommended solutions to fix the problems")

    except Exception as e:
        print(f"Error running diagnostic: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
