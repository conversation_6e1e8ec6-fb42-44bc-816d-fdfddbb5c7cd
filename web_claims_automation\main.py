#!/usr/bin/env python3
"""
Web Claims Automation - Standalone Program
Main entry point for the web claims automation system
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
from datetime import datetime, timedelta
import pyautogui
import logging
from automation_engine import AutomationEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('web_claims_automation.log'),
        logging.StreamHandler()
    ]
)

class WebClaimsAutomation:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Web Claims Automation")
        self.root.geometry("1000x700")
        
        # Automation state
        self.is_running = False
        self.is_paused = False
        self.automation_thread = None
        
        # Configuration
        self.config_file = "claims_config.json"
        self.scenarios = {}
        self.appointment_types = {
            "Neuro": {"distance": 15.0, "scenario": ""},
            "Williams": {"distance": 8.5, "scenario": ""},
            "Dr Smith": {"distance": 12.0, "scenario": ""}
        }

        # Initialize automation engine
        self.automation_engine = AutomationEngine(self)

        # Load configuration
        self.load_configuration()

        # Setup GUI
        self.setup_gui()

        # Disable pyautogui failsafe for automation
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1

        logging.info("Web Claims Automation initialized")

    def setup_gui(self):
        """Setup the main GUI interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Main automation tab
        self.main_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.main_frame, text="Automation")
        
        # Configuration tab
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="Configuration")
        
        self.setup_main_tab()
        self.setup_config_tab()

    def setup_main_tab(self):
        """Setup the main automation control tab"""
        # Control Panel
        control_frame = ttk.LabelFrame(self.main_frame, text="Control Panel", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.go_button = ttk.Button(control_frame, text="Go", command=self.start_automation, style="Success.TButton")
        self.go_button.pack(side=tk.LEFT, padx=5)
        
        self.pause_button = ttk.Button(control_frame, text="Pause", command=self.pause_automation, state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop", command=self.stop_automation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text="Status: Ready", foreground="green")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # Scenario Selection
        scenario_frame = ttk.LabelFrame(self.main_frame, text="Scenario Selection", padding=10)
        scenario_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(scenario_frame, text="Select Scenario:").pack(side=tk.LEFT, padx=5)
        self.scenario_var = tk.StringVar()
        self.scenario_combo = ttk.Combobox(scenario_frame, textvariable=self.scenario_var, state="readonly", width=30)
        self.scenario_combo.pack(side=tk.LEFT, padx=5)
        self.update_scenario_list()
        
        # Year Selection
        ttk.Label(scenario_frame, text="Year:").pack(side=tk.LEFT, padx=(20, 5))
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_combo = ttk.Combobox(scenario_frame, textvariable=self.year_var, state="readonly", width=10)
        year_combo['values'] = [str(year) for year in range(2020, 2030)]
        year_combo.pack(side=tk.LEFT, padx=5)
        
        # Appointment Input Section
        input_frame = ttk.LabelFrame(self.main_frame, text="Appointment Input", padding=10)
        input_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create two columns
        left_column = ttk.Frame(input_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_column = ttk.Frame(input_frame)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Appointments to process
        ttk.Label(left_column, text="Appointments to Process (Format: '1 Jan 2:30 PM'):").pack(anchor=tk.W)
        self.appointments_text = tk.Text(left_column, height=20, width=40, font=("Consolas", 10))
        appointments_scroll = ttk.Scrollbar(left_column, orient=tk.VERTICAL, command=self.appointments_text.yview)
        self.appointments_text.configure(yscrollcommand=appointments_scroll.set)
        self.appointments_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        appointments_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Completed appointments
        ttk.Label(right_column, text="Completed Appointments:").pack(anchor=tk.W)
        self.completed_text = tk.Text(right_column, height=20, width=40, font=("Consolas", 10), state=tk.DISABLED)
        completed_scroll = ttk.Scrollbar(right_column, orient=tk.VERTICAL, command=self.completed_text.yview)
        self.completed_text.configure(yscrollcommand=completed_scroll.set)
        self.completed_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        completed_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_config_tab(self):
        """Setup the configuration tab"""
        # Scenario Management
        scenario_mgmt_frame = ttk.LabelFrame(self.config_frame, text="Scenario Management", padding=10)
        scenario_mgmt_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(scenario_mgmt_frame, text="Scenario Name:").pack(side=tk.LEFT, padx=5)
        self.new_scenario_var = tk.StringVar()
        ttk.Entry(scenario_mgmt_frame, textvariable=self.new_scenario_var, width=20).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(scenario_mgmt_frame, text="Create New", command=self.create_scenario).pack(side=tk.LEFT, padx=5)
        ttk.Button(scenario_mgmt_frame, text="Record Clicks", command=self.start_click_recording).pack(side=tk.LEFT, padx=5)
        
        # Appointment Types Configuration
        types_frame = ttk.LabelFrame(self.config_frame, text="Appointment Types", padding=10)
        types_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for appointment types with columns
        columns = ('Type', 'Distance (km)', 'Scenario')
        self.types_tree = ttk.Treeview(types_frame, columns=columns, show='headings', height=8)

        # Define column headings and widths
        self.types_tree.heading('Type', text='Appointment Type')
        self.types_tree.heading('Distance (km)', text='Distance (km)')
        self.types_tree.heading('Scenario', text='Linked Scenario')

        self.types_tree.column('Type', width=150)
        self.types_tree.column('Distance (km)', width=100)
        self.types_tree.column('Scenario', width=200)

        # Add scrollbar for treeview
        types_scrollbar = ttk.Scrollbar(types_frame, orient=tk.VERTICAL, command=self.types_tree.yview)
        self.types_tree.configure(yscrollcommand=types_scrollbar.set)

        self.types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.update_appointment_types_list()

        # Type management buttons frame
        type_buttons_frame = ttk.Frame(self.config_frame)
        type_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Left side - Add new type
        add_frame = ttk.LabelFrame(type_buttons_frame, text="Add New Type", padding=10)
        add_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        add_fields_frame = ttk.Frame(add_frame)
        add_fields_frame.pack(fill=tk.X)

        ttk.Label(add_fields_frame, text="Type Name:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.new_type_var = tk.StringVar()
        ttk.Entry(add_fields_frame, textvariable=self.new_type_var, width=15).grid(row=0, column=1, padx=5)

        ttk.Label(add_fields_frame, text="Distance (km):").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.distance_var = tk.StringVar()
        ttk.Entry(add_fields_frame, textvariable=self.distance_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(add_fields_frame, text="Scenario:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.type_scenario_var = tk.StringVar()
        self.type_scenario_combo = ttk.Combobox(add_fields_frame, textvariable=self.type_scenario_var, state="readonly", width=20)
        self.type_scenario_combo.grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=5, pady=(5, 0))

        ttk.Button(add_frame, text="Add Type", command=self.add_appointment_type).pack(pady=(10, 0))

        # Right side - Edit/Remove
        edit_frame = ttk.LabelFrame(type_buttons_frame, text="Edit Selected Type", padding=10)
        edit_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        ttk.Button(edit_frame, text="Edit Selected", command=self.edit_appointment_type).pack(pady=2)
        ttk.Button(edit_frame, text="Remove Selected", command=self.remove_appointment_type).pack(pady=2)
        
        # Scenario Details
        details_frame = ttk.LabelFrame(self.config_frame, text="Scenario Details", padding=10)
        details_frame.pack(fill=tk.X, padx=5, pady=5)

        # Scenario selection for editing
        edit_frame = ttk.Frame(details_frame)
        edit_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(edit_frame, text="Edit Scenario:").pack(side=tk.LEFT, padx=5)
        self.edit_scenario_var = tk.StringVar()
        self.edit_scenario_combo = ttk.Combobox(edit_frame, textvariable=self.edit_scenario_var, state="readonly")
        self.edit_scenario_combo.pack(side=tk.LEFT, padx=5)
        self.edit_scenario_combo.bind('<<ComboboxSelected>>', self.load_scenario_details)

        # Click sequence display (smaller height)
        sequence_container = ttk.Frame(details_frame)
        sequence_container.pack(fill=tk.X)

        self.sequence_text = tk.Text(sequence_container, height=6, font=("Consolas", 9))
        sequence_scroll = ttk.Scrollbar(sequence_container, orient=tk.VERTICAL, command=self.sequence_text.yview)
        self.sequence_text.configure(yscrollcommand=sequence_scroll.set)
        self.sequence_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sequence_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def load_configuration(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.scenarios = config.get('scenarios', {})

                    # Handle both old and new appointment types format
                    apt_types = config.get('appointment_types', {})
                    if isinstance(apt_types, list):
                        # Convert old format to new format
                        self.appointment_types = {}
                        for apt_type in apt_types:
                            self.appointment_types[apt_type] = {"distance": 0.0, "scenario": ""}
                    else:
                        self.appointment_types = apt_types

                    logging.info("Configuration loaded successfully")
            else:
                # Create default configuration
                self.save_configuration()
                logging.info("Created default configuration")
        except Exception as e:
            logging.error(f"Error loading configuration: {e}")
            messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def save_configuration(self):
        """Save configuration to file"""
        try:
            config = {
                'scenarios': self.scenarios,
                'appointment_types': self.appointment_types
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            logging.info("Configuration saved successfully")
        except Exception as e:
            logging.error(f"Error saving configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def update_scenario_list(self):
        """Update the scenario dropdown list"""
        scenario_names = list(self.scenarios.keys())
        self.scenario_combo['values'] = scenario_names
        if hasattr(self, 'edit_scenario_combo'):
            self.edit_scenario_combo['values'] = scenario_names
        if hasattr(self, 'type_scenario_combo'):
            self.type_scenario_combo['values'] = [''] + scenario_names

    def update_appointment_types_list(self):
        """Update the appointment types treeview"""
        if hasattr(self, 'types_tree'):
            # Clear existing items
            for item in self.types_tree.get_children():
                self.types_tree.delete(item)

            # Add appointment types
            for type_name, type_data in self.appointment_types.items():
                distance = type_data.get('distance', 0.0)
                scenario = type_data.get('scenario', '')
                self.types_tree.insert('', tk.END, values=(type_name, f"{distance:.1f}", scenario))

    def start_automation(self):
        """Start the automation process"""
        if not self.scenario_var.get():
            messagebox.showerror("Error", "Please select a scenario")
            return
            
        appointments = self.parse_appointments()
        if not appointments:
            messagebox.showerror("Error", "Please enter at least one appointment")
            return
            
        self.is_running = True
        self.is_paused = False
        
        # Update button states
        self.go_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="Status: Running", foreground="blue")
        
        # Start automation in separate thread
        self.automation_thread = threading.Thread(target=self.run_automation, args=(appointments,))
        self.automation_thread.daemon = True
        self.automation_thread.start()
        
        logging.info("Automation started")

    def pause_automation(self):
        """Pause/resume automation"""
        if self.is_paused:
            self.is_paused = False
            self.pause_button.config(text="Pause")
            self.status_label.config(text="Status: Running", foreground="blue")
            logging.info("Automation resumed")
        else:
            self.is_paused = True
            self.pause_button.config(text="Resume")
            self.status_label.config(text="Status: Paused", foreground="orange")
            logging.info("Automation paused")

    def stop_automation(self):
        """Stop the automation process"""
        self.is_running = False
        self.is_paused = False
        
        # Update button states
        self.go_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="Pause")
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Status: Stopped", foreground="red")
        
        logging.info("Automation stopped")

    def parse_appointments(self):
        """Parse appointments from the text input"""
        appointments = []
        text = self.appointments_text.get("1.0", tk.END).strip()
        
        for line in text.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            try:
                # Parse format: "1 Jan 2:30 PM"
                parts = line.split()
                if len(parts) >= 3:
                    day = parts[0]
                    month = parts[1]
                    time_part = ' '.join(parts[2:])
                    
                    # Create full date string
                    year = self.year_var.get()
                    date_str = f"{day} {month} {year}"
                    
                    appointments.append({
                        'original': line,
                        'date': date_str,
                        'time': time_part,
                        'day': day,
                        'month': month,
                        'year': year
                    })
                    
            except Exception as e:
                logging.warning(f"Failed to parse appointment: {line} - {e}")
                
        return appointments

    def run_automation(self, appointments):
        """Main automation loop"""
        scenario_name = self.scenario_var.get()
        scenario = self.scenarios.get(scenario_name, {})
        
        if not scenario:
            messagebox.showerror("Error", f"Scenario '{scenario_name}' not found")
            self.stop_automation()
            return
            
        for i, appointment in enumerate(appointments):
            if not self.is_running:
                break
                
            # Wait if paused
            while self.is_paused and self.is_running:
                time.sleep(0.1)
                
            if not self.is_running:
                break
                
            try:
                self.process_appointment(appointment, scenario)
                self.mark_appointment_completed(appointment)
                
                # Update status
                self.root.after(0, lambda: self.status_label.config(
                    text=f"Status: Processing {i+1}/{len(appointments)}", 
                    foreground="blue"
                ))
                
            except Exception as e:
                logging.error(f"Error processing appointment {appointment['original']}: {e}")
                
        # Automation completed
        if self.is_running:
            self.root.after(0, self.stop_automation)
            self.root.after(0, lambda: messagebox.showinfo("Complete", "All appointments processed!"))

    def process_appointment(self, appointment, scenario):
        """Process a single appointment using the scenario"""
        return self.automation_engine.execute_scenario(appointment, scenario)

    def mark_appointment_completed(self, appointment):
        """Mark an appointment as completed"""
        completed_text = f"{appointment['original']} - {datetime.now().strftime('%H:%M:%S')}\n"

        # Update completed appointments text box
        self.completed_text.config(state=tk.NORMAL)
        self.completed_text.insert(tk.END, completed_text)
        self.completed_text.config(state=tk.DISABLED)
        self.completed_text.see(tk.END)

    def create_scenario(self):
        """Create a new scenario"""
        scenario_name = self.new_scenario_var.get().strip()
        if not scenario_name:
            messagebox.showerror("Error", "Please enter a scenario name")
            return

        if scenario_name in self.scenarios:
            if not messagebox.askyesno("Confirm", f"Scenario '{scenario_name}' already exists. Overwrite?"):
                return

        # Create empty scenario
        self.scenarios[scenario_name] = {
            'name': scenario_name,
            'clicks': [],
            'created': datetime.now().isoformat()
        }

        self.save_configuration()
        self.update_scenario_list()
        self.new_scenario_var.set("")

        messagebox.showinfo("Success", f"Scenario '{scenario_name}' created")

    def start_click_recording(self):
        """Start click recording using automation engine"""
        self.automation_engine.start_click_recording()

    def add_appointment_type(self):
        """Add a new appointment type"""
        type_name = self.new_type_var.get().strip()
        distance_str = self.distance_var.get().strip()
        scenario = self.type_scenario_var.get().strip()

        if not type_name:
            messagebox.showerror("Error", "Please enter a type name")
            return

        if type_name in self.appointment_types:
            messagebox.showerror("Error", "Appointment type already exists")
            return

        try:
            # Validate distance is a number
            distance = float(distance_str) if distance_str else 0.0
        except ValueError:
            messagebox.showerror("Error", "Distance must be a number")
            return

        # Add new appointment type
        self.appointment_types[type_name] = {
            "distance": distance,
            "scenario": scenario
        }

        self.save_configuration()
        self.update_appointment_types_list()

        # Clear input fields
        self.new_type_var.set("")
        self.distance_var.set("")
        self.type_scenario_var.set("")

        messagebox.showinfo("Success", f"Appointment type '{type_name}' added")

    def edit_appointment_type(self):
        """Edit selected appointment type"""
        selection = self.types_tree.selection()
        if not selection:
            messagebox.showerror("Error", "Please select an appointment type to edit")
            return

        item = selection[0]
        values = self.types_tree.item(item, 'values')
        type_name = values[0]
        current_distance = float(values[1])
        current_scenario = values[2]

        # Create edit dialog
        self.create_edit_type_dialog(type_name, current_distance, current_scenario)

    def create_edit_type_dialog(self, type_name, current_distance, current_scenario):
        """Create dialog to edit appointment type"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Edit Appointment Type: {type_name}")
        dialog.geometry("400x250")
        dialog.attributes('-topmost', True)
        dialog.grab_set()  # Make dialog modal

        # Type name (read-only)
        ttk.Label(dialog, text="Type Name:").pack(anchor=tk.W, padx=10, pady=(10, 5))
        name_var = tk.StringVar(value=type_name)
        name_entry = ttk.Entry(dialog, textvariable=name_var, width=30, state="readonly")
        name_entry.pack(padx=10, pady=5)

        # Distance
        ttk.Label(dialog, text="Distance (km):").pack(anchor=tk.W, padx=10, pady=(10, 5))
        distance_var = tk.StringVar(value=str(current_distance))
        ttk.Entry(dialog, textvariable=distance_var, width=30).pack(padx=10, pady=5)

        # Scenario
        ttk.Label(dialog, text="Linked Scenario:").pack(anchor=tk.W, padx=10, pady=(10, 5))
        scenario_var = tk.StringVar(value=current_scenario)
        scenario_combo = ttk.Combobox(dialog, textvariable=scenario_var, state="readonly", width=27)
        scenario_combo['values'] = [''] + list(self.scenarios.keys())
        scenario_combo.pack(padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        def save_changes():
            try:
                new_distance = float(distance_var.get())
                new_scenario = scenario_var.get()

                # Update appointment type
                self.appointment_types[type_name] = {
                    "distance": new_distance,
                    "scenario": new_scenario
                }

                self.save_configuration()
                self.update_appointment_types_list()
                dialog.destroy()

                messagebox.showinfo("Success", f"Appointment type '{type_name}' updated")

            except ValueError:
                messagebox.showerror("Error", "Distance must be a valid number")

        ttk.Button(button_frame, text="Save", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def remove_appointment_type(self):
        """Remove selected appointment type"""
        selection = self.types_tree.selection()
        if not selection:
            messagebox.showerror("Error", "Please select an appointment type to remove")
            return

        item = selection[0]
        values = self.types_tree.item(item, 'values')
        type_name = values[0]

        if messagebox.askyesno("Confirm", f"Remove appointment type '{type_name}'?"):
            del self.appointment_types[type_name]
            self.save_configuration()
            self.update_appointment_types_list()

    def load_scenario_details(self, event=None):
        """Load scenario details for editing"""
        # event parameter is used by tkinter callback, suppress warning
        _ = event
        scenario_name = self.edit_scenario_var.get()
        if not scenario_name or scenario_name not in self.scenarios:
            self.sequence_text.delete("1.0", tk.END)
            return

        scenario = self.scenarios[scenario_name]
        clicks = scenario.get('clicks', [])

        # Display click sequence
        self.sequence_text.delete("1.0", tk.END)

        if not clicks:
            self.sequence_text.insert("1.0", "No clicks recorded for this scenario")
            return

        for i, click in enumerate(clicks):
            step_text = f"Step {i+1}: {click.get('description', 'Unknown')}\n"
            step_text += f"  Type: {click.get('type', 'click')}\n"

            if 'x' in click and 'y' in click:
                step_text += f"  Position: ({click['x']}, {click['y']})\n"

            if 'text' in click:
                step_text += f"  Text: {click['text']}\n"

            if 'field_type' in click:
                step_text += f"  Field Type: {click['field_type']}\n"

            step_text += f"  Delay: {click.get('delay', 1.0)}s\n\n"

            self.sequence_text.insert(tk.END, step_text)

if __name__ == "__main__":
    app = WebClaimsAutomation()
    app.root.mainloop()
