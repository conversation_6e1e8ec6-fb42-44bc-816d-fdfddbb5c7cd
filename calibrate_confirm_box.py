#!/usr/bin/env python3
"""
Quick calibration for confirmation box coordinate
"""

import pyautogui
import time

def calibrate_confirm_box():
    """Calibrate the confirmation box coordinate"""
    print("Confirmation Box Calibration")
    print("=" * 40)
    print()
    print("Steps:")
    print("1. Go to initiate exchange screen")
    print("2. Click 'Initiate Exchange' to make confirmation box appear")
    print("3. Position mouse over the 'CONFIRM' button")
    print("4. Press Enter to capture coordinate")
    print()
    
    input("Press Enter when confirmation box is visible and mouse is positioned...")
    
    x, y = pyautogui.position()
    print(f"\nConfirmation box coordinate: ({x}, {y})")
    
    print(f"\nUpdate your script with:")
    print(f"confirm_x, confirm_y = {x}, {y}")
    
    return x, y

def test_confirm_coordinate():
    """Test clicking the confirmation coordinate"""
    print("Test Confirmation Click")
    print("=" * 30)
    
    # Get coordinate
    print("Position mouse over confirmation button")
    input("Press Enter to capture and test...")
    
    x, y = pyautogui.position()
    print(f"Testing click at ({x}, {y}) in 3 seconds...")
    
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    pyautogui.click(x, y)
    print("Click executed!")

def main():
    """Main menu"""
    while True:
        print("\nConfirmation Box Calibration")
        print("=" * 30)
        print("1. Calibrate Confirm Box")
        print("2. Test Confirm Click")
        print("0. Exit")
        
        choice = input("\nChoice (0-2): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            calibrate_confirm_box()
        elif choice == '2':
            test_confirm_coordinate()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
