#!/usr/bin/env python3
"""
Quick verification of GPU OCR improvements and Unicode logging fixes
"""
import sys
import os
import cv2
import numpy as np
import time

def test_basic_functionality():
    """Test basic functionality of the improved OCR system"""
    print("🔍 Testing GPU OCR Improvements & Unicode Logging Fixes")
    print("=" * 60)
    
    try:
        # Test AI OCR service initialization
        from ai_ocr_service import AINumberRecognition
        ai_service = AINumberRecognition()
        
        print(f"✅ AI OCR Service initialized successfully")
        print(f"   GPU Available: {ai_service.gpu_available}")
        print(f"   Active Backends: {ai_service.active_backends}")
        
        # Test Unicode-safe logging
        print("\n🧪 Testing Unicode-safe logging:")
        test_messages = [
            "Regular message",
            "Message with checkmark: ✅",
            "Message with rocket: 🚀",
            "Message with warning: ⚠️"
        ]
        
        for i, msg in enumerate(test_messages):
            try:
                ai_service._safe_log("info", f"Test {i+1}: {msg}")
                print(f"   ✓ Test {i+1}: Unicode logging successful")
            except Exception as e:
                print(f"   ✗ Test {i+1}: Unicode logging failed - {e}")
                return False
        
        # Test OCR with a simple image
        print("\n🔢 Testing OCR performance:")
        
        # Create a simple test image
        image = np.ones((35, 35, 3), dtype=np.uint8) * 255
        cv2.putText(image, "42", (8, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        start_time = time.time()
        result = ai_service.recognize_number(image)
        elapsed_time = time.time() - start_time
        
        if result == 42:
            print(f"   ✅ OCR Test: SUCCESS - Recognized '42' in {elapsed_time:.3f}s")
        else:
            print(f"   ⚠️ OCR Test: Got {result}, expected 42 ({elapsed_time:.3f}s)")
        
        # Test map trade integration
        print("\n🗺️ Testing Map Trade Integration:")
        try:
            sys.path.append('modules')
            from map_trade import MapTradeModule
            
            module = MapTradeModule()
            print("   ✅ MapTradeModule loaded successfully")
            
            # Test with the same image
            result = module._extract_number_from_image(image)
            if result == 42:
                print("   ✅ Map Trade OCR: SUCCESS")
            else:
                print(f"   ⚠️ Map Trade OCR: Got {result}, expected 42")
                
        except Exception as e:
            print(f"   ❌ Map Trade Integration: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    success = test_basic_functionality()
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION RESULTS")
    print("=" * 60)
    
    if success:
        print("✅ ALL IMPROVEMENTS VERIFIED!")
        print("✅ GPU detection working")
        print("✅ Unicode logging fixed")
        print("✅ OCR performance optimized")
        print("✅ Map trade integration working")
        print("\n🚀 Ready for high-performance map trade automation!")
    else:
        print("⚠️ Some issues detected - check the output above")
        print("🔧 Review and fix issues before production use")
    
    return success

if __name__ == "__main__":
    main()
