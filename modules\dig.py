"""
Dig Module for Last War automation
Handles excavation tasks with proper timing and coordination
"""

import time
import pyautogui
import pyperclip
from typing import Dict, Any
from enhanced_base_module import EnhancedBaseModule


class DigModule(EnhancedBaseModule):
    """
    Dig module for excavation automation

    Process:
    1. Main scanner detects dig_icon, dig_up_dropdown, test_flight_treasure, or dig_up_treasure
    2. Opens chat and enters alliance chat
    3. Clicks on dig treasure template or uses chat_click backup
    4. Checks if chat closed, looks for send_to_dig templates
    5. Clicks march_to_dig and select_tile
    6. Detects timer and rapid clicks when under 10 seconds
    7. Exits with ESC until quit_game_dialog
    8. Sends thank you message in alliance chat
    9. Returns to main scanner
    """

    def __init__(self):
        super().__init__(name="dig", priority=-1, enabled=True)  # Highest priority

        # State tracking
        self.dig_in_progress = False
        self.current_step = 0
        self.rapid_click_speed = 0.005  # Configurable rapid click speed
        self.thank_you_message = "Thank you for the dig!"  # Configurable message

        # Step tracking
        self.STEP_DETECT = 0
        self.STEP_OPEN_CHAT = 1
        self.STEP_CLICK_TREASURE = 2
        self.STEP_CHECK_CHAT_CLOSED = 3
        self.STEP_MARCH_TO_DIG = 4
        self.STEP_TIMER_WAIT = 5
        self.STEP_EXIT_GAME = 6
        self.STEP_SEND_MESSAGE = 7
        self.STEP_COMPLETE = 8

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Check if dig templates are detected or dig is in progress"""
        try:
            templates_found = screen_data.get('templates_found', {})

            # If dig is already in progress, continue execution
            if self.dig_in_progress:
                return True

            # Check for any dig trigger templates
            dig_triggers = ['dig_icon', 'dig_up_dropdown', 'test_flight_treasure', 'dig_up_treasure']

            for trigger in dig_triggers:
                if trigger in templates_found:
                    matches = templates_found[trigger]
                    if matches and len(matches) > 0:
                        self.logger.info(f"Dig trigger detected: {trigger} - starting dig sequence")
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking dig availability: {str(e)}")
            return False

    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_can_execute(screen_data)

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Execute dig sequence based on current step"""
        try:
            if not self.dig_in_progress:
                # Start new dig sequence
                self.dig_in_progress = True
                self.current_step = self.STEP_OPEN_CHAT
                self.logger.info("Starting dig sequence - pausing main scanner")

                # Pause main scanner during dig execution
                if hasattr(self, 'controller') and self.controller:
                    self.controller.paused = True

            # Execute current step
            return self._execute_current_step(screen_data)

        except Exception as e:
            self.logger.error(f"Error in dig execution: {str(e)}")
            self._reset_dig_state()
            return False

    def _execute_current_step(self, screen_data: Dict[str, Any]) -> bool:
        """Execute the current step in the dig sequence"""
        try:
            if self.current_step == self.STEP_OPEN_CHAT:
                return self._step_open_chat()
            elif self.current_step == self.STEP_CLICK_TREASURE:
                return self._step_click_treasure(screen_data)
            elif self.current_step == self.STEP_CHECK_CHAT_CLOSED:
                return self._step_check_chat_closed(screen_data)
            elif self.current_step == self.STEP_MARCH_TO_DIG:
                return self._step_march_to_dig(screen_data)
            elif self.current_step == self.STEP_TIMER_WAIT:
                return self._step_timer_wait(screen_data)
            elif self.current_step == self.STEP_EXIT_GAME:
                return self._step_exit_game(screen_data)
            elif self.current_step == self.STEP_SEND_MESSAGE:
                return self._step_send_message()
            elif self.current_step == self.STEP_COMPLETE:
                return self._step_complete()
            else:
                self.logger.error(f"Unknown step: {self.current_step}")
                self._reset_dig_state()
                return False

        except Exception as e:
            self.logger.error(f"Error executing step {self.current_step}: {str(e)}")
            self._reset_dig_state()
            return False

    def _step_open_chat(self) -> bool:
        """Step 2: Click to open chat, then enter alliance chat"""
        try:
            self.logger.info("Step 2: Opening chat and entering alliance chat")

            # Click to open chat at (1267, 1353)
            pyautogui.click(1267, 1353)
            time.sleep(1.0)

            # Click to enter alliance chat at (1176, 170)
            pyautogui.click(1176, 170)
            time.sleep(1.0)

            # Move to next step
            self.current_step = self.STEP_CLICK_TREASURE
            return True

        except Exception as e:
            self.logger.error(f"Error opening chat: {str(e)}")
            return False

    def _step_click_treasure(self, screen_data: Dict[str, Any]) -> bool:
        """Step 3: Scan for dig treasure templates and click"""
        try:
            self.logger.info("Step 3: Looking for dig treasure templates")
            templates_found = screen_data.get('templates_found', {})

            # Try to find dig_up_treasure or test_flight_treasure
            treasure_templates = ['dig_up_treasure', 'test_flight_treasure']

            for template in treasure_templates:
                if template in templates_found:
                    matches = templates_found[template]
                    if matches and len(matches) > 0:
                        # Click on the first match
                        match = matches[0]
                        x, y = match[0] + match[2]//2, match[1] + match[3]//2
                        self.logger.info(f"Clicking {template} at ({x}, {y})")
                        pyautogui.click(x, y)
                        time.sleep(1.0)

                        # Move to next step
                        self.current_step = self.STEP_CHECK_CHAT_CLOSED
                        return True

            # Backup: try chat_click coordinate if templates not found
            self.logger.info("Templates not found, trying chat_click backup")
            # You can add a coordinate for chat_click here if needed
            # For now, move to next step anyway
            self.current_step = self.STEP_CHECK_CHAT_CLOSED
            return True

        except Exception as e:
            self.logger.error(f"Error clicking treasure: {str(e)}")
            return False

    def _step_check_chat_closed(self, screen_data: Dict[str, Any]) -> bool:
        """Step 4: Check if chat closed, look for send_to_dig templates"""
        try:
            self.logger.info("Step 4: Checking if chat closed and looking for send_to_dig templates")
            templates_found = screen_data.get('templates_found', {})

            # Check if alliance_chat_is_on is NOT there (chat closed)
            if 'alliance_chat_is_on' not in templates_found:
                self.logger.info("Chat window closed, looking for send_to_dig templates")

                # Look for send_to_dig templates
                send_templates = ['send_to_dig_1', 'send_to_dig_2', 'send_to_dig_3',
                                'send_to_dig_flight', 'send_to_dig_flight_2']

                for template in send_templates:
                    if template in templates_found:
                        matches = templates_found[template]
                        if matches and len(matches) > 0:
                            # Click on the first match
                            match = matches[0]
                            x, y = match[0] + match[2]//2, match[1] + match[3]//2
                            self.logger.info(f"Clicking {template} at ({x}, {y})")
                            pyautogui.click(x, y)
                            time.sleep(1.0)
                            break

                # Move to next step
                self.current_step = self.STEP_MARCH_TO_DIG
                return True
            else:
                self.logger.info("Chat still open, waiting...")
                return True  # Stay in this step

        except Exception as e:
            self.logger.error(f"Error checking chat closed: {str(e)}")
            return False

    def _step_march_to_dig(self, screen_data: Dict[str, Any]) -> bool:
        """Step 5: Click march_to_dig and select_tile"""
        try:
            self.logger.info("Step 5: Clicking march_to_dig and select_tile")
            templates_found = screen_data.get('templates_found', {})

            # Look for march_to_dig template
            if 'march_to_dig' in templates_found:
                matches = templates_found['march_to_dig']
                if matches and len(matches) > 0:
                    match = matches[0]
                    x, y = match[0] + match[2]//2, match[1] + match[3]//2
                    self.logger.info(f"Clicking march_to_dig at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1.0)
            else:
                # Fallback to coordinate (1277, 1087)
                self.logger.info("march_to_dig template not found, using fallback coordinate")
                pyautogui.click(1277, 1087)
                time.sleep(1.0)

            # Look for select_tile template
            if 'select_tile' in templates_found:
                matches = templates_found['select_tile']
                if matches and len(matches) > 0:
                    match = matches[0]
                    x, y = match[0] + match[2]//2, match[1] + match[3]//2
                    self.logger.info(f"Clicking select_tile at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(1.0)

            # Move to next step
            self.current_step = self.STEP_TIMER_WAIT
            return True

        except Exception as e:
            self.logger.error(f"Error in march to dig: {str(e)}")
            return False

    def _step_timer_wait(self, screen_data: Dict[str, Any]) -> bool:
        """Step 6: Detect timer and rapid click when under 10 seconds"""
        try:
            self.logger.info("Step 6: Waiting for timer and rapid clicking when ready")

            # TODO: Implement timer detection logic here
            # For now, simulate timer detection and rapid clicking

            # Check if we can detect a timer (you'll need to implement OCR or template detection)
            # This is a placeholder - you'll need to add timer detection logic
            timer_seconds = self._detect_timer_seconds(screen_data)

            if timer_seconds is not None and timer_seconds <= 10:
                self.logger.info(f"Timer at {timer_seconds} seconds - starting rapid clicking")

                # Rapid click for 20 seconds
                start_time = time.time()
                clicks = 0

                while time.time() - start_time < 20.0:
                    # Click at the timer location or dig icon
                    # You may need to adjust these coordinates
                    pyautogui.click(1200, 600)  # Placeholder coordinates
                    clicks += 1
                    time.sleep(self.rapid_click_speed)

                self.logger.info(f"Rapid clicking completed - {clicks} clicks in 20 seconds")

                # Move to next step
                self.current_step = self.STEP_EXIT_GAME
                return True
            else:
                # Still waiting for timer
                if timer_seconds is not None:
                    self.logger.info(f"Timer at {timer_seconds} seconds - waiting...")
                else:
                    self.logger.info("Timer not detected - waiting...")
                return True

        except Exception as e:
            self.logger.error(f"Error in timer wait: {str(e)}")
            return False

    def _step_exit_game(self, screen_data: Dict[str, Any]) -> bool:
        """Step 7: Press ESC until quit_game_dialog appears, then ESC once more"""
        try:
            self.logger.info("Step 7: Exiting game dialogs with ESC")
            templates_found = screen_data.get('templates_found', {})

            # Keep pressing ESC until quit_game_dialog appears
            if 'quit_game_dialog' not in templates_found:
                self.logger.info("Pressing ESC to exit dialogs")
                pyautogui.press('escape')
                time.sleep(0.5)
                return True  # Stay in this step
            else:
                # quit_game_dialog detected, press ESC once more to dismiss it
                self.logger.info("quit_game_dialog detected, pressing ESC to dismiss")
                pyautogui.press('escape')
                time.sleep(1.0)

                # Move to next step
                self.current_step = self.STEP_SEND_MESSAGE
                return True

        except Exception as e:
            self.logger.error(f"Error exiting game: {str(e)}")
            return False

    def _step_send_message(self) -> bool:
        """Step 8: Send thank you message in alliance chat"""
        try:
            self.logger.info("Step 8: Sending thank you message in alliance chat")

            # Click to open chat at (1267, 1353)
            pyautogui.click(1267, 1353)
            time.sleep(1.0)

            # Click to enter alliance chat at (1176, 170)
            pyautogui.click(1176, 170)
            time.sleep(1.0)

            # Click in chat input area at (1073, 1327)
            pyautogui.click(1073, 1327)
            time.sleep(0.5)

            # Type the thank you message
            pyautogui.write(self.thank_you_message)
            time.sleep(0.5)

            # Press Enter to send
            pyautogui.press('enter')
            time.sleep(1.0)

            # Move to next step
            self.current_step = self.STEP_COMPLETE
            return True

        except Exception as e:
            self.logger.error(f"Error sending message: {str(e)}")
            return False

    def _step_complete(self) -> bool:
        """Step 9: Complete dig sequence and return to main scanner"""
        try:
            self.logger.info("Step 9: Dig sequence complete - returning to main scanner")

            # Press ESC to ensure we're back to main screen
            pyautogui.press('escape')
            time.sleep(1.0)

            # Reset dig state and resume main scanner
            self._reset_dig_state()
            return True

        except Exception as e:
            self.logger.error(f"Error completing dig: {str(e)}")
            self._reset_dig_state()
            return False

    def _reset_dig_state(self):
        """Reset dig state and resume main scanner"""
        self.dig_in_progress = False
        self.current_step = self.STEP_DETECT

        # Resume main scanner
        if hasattr(self, 'controller') and self.controller:
            self.controller.paused = False
            self.logger.info("Main scanner resumed")

    def _detect_timer_seconds(self, screen_data: Dict[str, Any]) -> int:
        """
        Detect timer seconds from screen
        TODO: Implement OCR or template-based timer detection
        Returns None if timer not detected, otherwise returns seconds
        """
        # Placeholder implementation
        # You'll need to implement actual timer detection here
        # This could use OCR to read timer text or template matching
        return None

    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_execute(screen_data)

    def get_cooldown(self) -> float:
        """Get cooldown time - no cooldown for dig module"""
        return 0.0

    def get_dig_status(self) -> str:
        """Get current dig status as string"""
        if self.dig_in_progress:
            step_names = {
                self.STEP_DETECT: "Detecting",
                self.STEP_OPEN_CHAT: "Opening Chat",
                self.STEP_CLICK_TREASURE: "Clicking Treasure",
                self.STEP_CHECK_CHAT_CLOSED: "Checking Chat",
                self.STEP_MARCH_TO_DIG: "Marching to Dig",
                self.STEP_TIMER_WAIT: "Waiting for Timer",
                self.STEP_EXIT_GAME: "Exiting Dialogs",
                self.STEP_SEND_MESSAGE: "Sending Message",
                self.STEP_COMPLETE: "Completing"
            }
            return f"Dig in progress - {step_names.get(self.current_step, 'Unknown Step')}"
        else:
            return "Ready"

    def get_required_templates(self) -> list:
        """Return list of templates required by this module"""
        return [
            'dig_icon', 'dig_up_dropdown', 'test_flight_treasure', 'dig_up_treasure',
            'alliance_chat_is_on', 'send_to_dig_1', 'send_to_dig_2', 'send_to_dig_3',
            'send_to_dig_flight', 'send_to_dig_flight_2', 'march_to_dig', 'select_tile',
            'quit_game_dialog'
        ]

    def set_rapid_click_speed(self, speed: float):
        """Set the rapid click speed (seconds between clicks)"""
        self.rapid_click_speed = max(0.001, min(1.0, speed))  # Clamp between 1ms and 1s
        self.logger.info(f"Rapid click speed set to {self.rapid_click_speed} seconds")

    def set_thank_you_message(self, message: str):
        """Set the thank you message to send after dig completion"""
        self.thank_you_message = message
        self.logger.info(f"Thank you message set to: {message}")
