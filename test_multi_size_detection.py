#!/usr/bin/env python3
"""
Test multi-size detection (20px and 25px)
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from map_trade import MapTradeModule

def test_multi_size_detection():
    """Test the multi-size detection system"""
    print("Testing Multi-Size Detection (20px and 25px)")
    print("=" * 60)
    
    try:
        # Create module
        print("Creating MapTradeModule...")
        map_trade = MapTradeModule()
        print("✅ MapTradeModule created successfully")
        
        # Check if TrOCR is available
        if hasattr(map_trade, 'trocr_available') and map_trade.trocr_available:
            print("✅ TrOCR is available and loaded")
        else:
            print("⚠️ TrOCR not available, will use Tesseract only")
        
        # Test map reading directly
        print("\n1. Testing map reading with multi-size detection...")
        result = map_trade._analyze_map_pieces()
        
        if result:
            print(f"✅ Map reading successful: {result}")
            
            # Show trade decision
            give_map, receive_map = map_trade._find_optimal_trade(result)
            print(f"\n2. Trade Decision:")
            print(f"   Give: {give_map} ({result[give_map]} pieces)")
            print(f"   Receive: {receive_map} ({result[receive_map]} pieces)")
            
            # Show coordinates that will be used
            print(f"\n3. Click Sequence with CORRECT coordinates:")
            print(f"   Step 1: Click {give_map} at {map_trade.map_piece_regions[give_map]['click_pos']}")
            print(f"   Step 2: Click 'you will lose' area at (732, 261)")
            print(f"   Step 3: Click {receive_map} at {map_trade.map_piece_regions[receive_map]['click_pos']}")
            print(f"   Step 4: Click 'initiate exchange' at (636, 611)")
            print(f"   Step 5: Click 'confirm exchange' at (633, 498)")
            
            print(f"\n✅ Multi-size detection system is working!")
            print(f"   - Tries 20px capture first (TrOCR optimal)")
            print(f"   - Falls back to 25px capture if needed")
            print(f"   - Uses TrOCR first, then Tesseract fallback")
            
        else:
            print("❌ Map reading failed - no numbers detected")
            print("   This could mean:")
            print("   - Not on the correct screen")
            print("   - Coordinates need adjustment")
            print("   - OCR engines having issues")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multi_size_detection()
