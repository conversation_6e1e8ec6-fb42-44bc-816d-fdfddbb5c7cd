# 🎯 Template Setup Guide

## Essential UI Templates

These templates are crucial for automation and UI recovery:

### 1. **events_button.png**
- **Purpose:** Main screen detection, UI recovery
- **Location:** Usually bottom-right of game screen
- **Used by:** Help Click (UI recovery), Alliance Donation, Zombie Invasion
- **Description:** The Events button that appears on the main game interface

### 2. **base_button.png**
- **Purpose:** Base view detection, UI recovery
- **Location:** Usually bottom-left of game screen  
- **Used by:** Help Click (UI recovery), navigation modules
- **Description:** The Base button for returning to base view

### 3. **world_button.png**
- **Purpose:** World map detection, navigation
- **Location:** Usually near other navigation buttons
- **Used by:** Map Trade, Help Click (UI recovery), navigation
- **Description:** The World button for accessing world map

## Monster Detection Templates

For Zombie Invasion smart monster detection:

### 4. **search_button.png** or **search_zombie.png**
- **Purpose:** Monster detection (if search button visible = no monster found)
- **Location:** In Events > Zombie Invasion tab
- **Used by:** Zombie Invasion (monster detection logic)
- **Description:** The Search button that appears when no monster is found

## Help System Templates

For alliance help automation:

### 5. **Help_template.png**
- **Purpose:** Detect help requests
- **Location:** Alliance help notifications
- **Used by:** Help Click module
- **Description:** Help request notification or button

### 6. **Help_chat.png**
- **Purpose:** Alternative help detection
- **Location:** Chat or alliance interface
- **Used by:** Help Click module
- **Description:** Help-related chat or interface element

## Squad Status Templates

For Zombie Invasion squad management:

### 7. **squad_0_4.png** - No squads deployed
### 8. **squad_1_4.png** - One squad deployed  
### 9. **squad_2_4.png** - Two squads deployed
### 10. **squad_3_4.png** - Three squads deployed
### 11. **squad_4_4.png** - All squads deployed

- **Purpose:** Track squad deployment status
- **Location:** Squad status indicator in game
- **Used by:** Zombie Invasion (deployment logic)
- **Description:** Visual indicators showing X/4 squads deployed

## Setup Instructions

### Using the Template Setup Tool:

1. **Run the tool:**
   ```
   python setup_debug_templates.py
   ```
   Or use **🎯 Setup Templates** button in config interface

2. **Two capture methods available:**

   **METHOD 1 - Quick Capture (recommended for buttons):**
   - Position mouse over the UI element center
   - Press **SPACE** to capture 50x50 area around mouse
   - Best for: Small buttons, icons, simple UI elements

   **METHOD 2 - Drag Selection (for precise control):**
   - Position mouse at top-left corner of desired area
   - Press **CTRL+SPACE** to set first corner
   - Move mouse to bottom-right corner
   - Press **CTRL+SPACE** again to capture selection
   - Best for: Large elements, custom sizes, complex shapes

3. **Manual capture (if needed):**
   - Take screenshot of UI element
   - Crop to desired size around the element
   - Save as PNG in `templates/` folder
   - Use exact template names listed above

## Template Quality Tips

- **Clear visibility:** Capture when UI element is clearly visible
- **Consistent state:** Capture in normal/default state (not highlighted)
- **Proper size:** ~50x50 pixels is usually sufficient
- **No overlaps:** Avoid capturing overlapping UI elements
- **Good contrast:** Ensure element stands out from background

## Troubleshooting

- **Template not found:** Check filename matches exactly (case-sensitive)
- **False matches:** Template may be too generic, recapture with more specific area
- **No matches:** Template may be too specific, try capturing larger area
- **Multiple matches:** Template area may include common UI elements

## Future Template Additions

The system is designed to easily add new templates for:
- **Navigation buttons** (inventory, shop, etc.)
- **Status indicators** (resources, notifications)
- **Action confirmations** (OK, Cancel buttons)
- **Game state detection** (battle, peace, events)

Simply add template names to module `get_required_templates()` methods and capture the corresponding PNG files.
