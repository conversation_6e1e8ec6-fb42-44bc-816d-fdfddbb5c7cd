"""
Test script to diagnose and fix config editor issues
"""
import sys
import os
sys.path.append('modules')

from module_config_manager import ModuleConfigManager
import json

def test_config_manager():
    """Test the config manager functionality"""
    print("=== Testing Config Manager ===")
    
    try:
        config_manager = ModuleConfigManager()
        print("✅ Config manager created successfully")
        
        # Test loading modules
        modules = config_manager.list_modules()
        print(f"✅ Modules found: {modules}")
        
        # Test getting module config
        for module_name in modules:
            config = config_manager.get_module_config(module_name)
            print(f"✅ {module_name} config loaded: {len(config)} keys")
            
            # Check if coordinates exist
            coords = config.get('click_coordinates', [])
            print(f"   - Click coordinates: {len(coords)}")
            
            # Check if templates exist
            templates = config.get('templates', [])
            print(f"   - Templates: {len(templates)}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

def create_backup():
    """Create a backup of current config"""
    print("\n=== Creating Backup ===")
    
    try:
        import shutil
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"module_configs_backup_{timestamp}.json"
        
        shutil.copy2("module_configs.json", backup_name)
        print(f"✅ Backup created: {backup_name}")
        
    except Exception as e:
        print(f"❌ Backup failed: {str(e)}")

def test_config_editing():
    """Test if we can edit configurations"""
    print("\n=== Testing Config Editing ===")
    
    try:
        config_manager = ModuleConfigManager()
        
        # Try to add a test coordinate to help_click
        print("Testing coordinate addition...")
        config_manager.add_click_coordinate("help_click", "test_coord", 100, 100, "Test coordinate")
        print("✅ Coordinate added successfully")
        
        # Try to remove it
        print("Testing coordinate removal...")
        config = config_manager.get_module_config("help_click")
        coords = config.get('click_coordinates', [])
        
        # Remove test coordinate
        coords = [c for c in coords if c.get('name') != 'test_coord']
        config['click_coordinates'] = coords
        config_manager.update_module_config("help_click", config)
        print("✅ Coordinate removed successfully")
        
    except Exception as e:
        print(f"❌ Editing failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_manager()
    create_backup()
    test_config_editing()
