#!/usr/bin/env python3
"""
Careful Unicode fix - only replace problematic characters without breaking indentation
"""

import os
import re

def careful_unicode_fix(file_path):
    """Carefully fix Unicode characters without breaking indentation"""
    
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Only replace the most problematic Unicode characters that cause encoding errors
        # Keep the replacements simple and preserve spacing
        problematic_unicode = {
            '🗺️': '',
            '🔍': '',
            '🔄': '',
            '🔴': '',
            '📋': '',
            '➡️': '',
            '✅': '',
            '💰': '',
            '⚠️': '',
            '❌': '',
            '🖱️': '',
            '⌨️': '',
            '🧪': '',
            '⏳': '',
            '🎯': '',
            '🚀': '',
            '⏸️': '',
            '🧟': '',
            '🛑': '',
            '🕒': '',
            '🚫': '',
            '🏠': '',
            '🌍': '',
            '⚔️': '',
            '🚶': '',
            '⏰': '',
        }
        
        changes_made = 0
        
        # Replace each Unicode character
        for unicode_char, replacement in problematic_unicode.items():
            if unicode_char in content:
                content = content.replace(unicode_char, replacement)
                changes_made += 1
        
        # Clean up any double spaces that might result from emoji removal
        # But be very careful not to break indentation
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Only clean up spaces within strings, not at the beginning of lines (indentation)
            if line.strip():  # If line has content
                # Clean up multiple spaces within the line content, but preserve leading whitespace
                leading_whitespace = len(line) - len(line.lstrip())
                line_content = line[leading_whitespace:]
                cleaned_content = re.sub(r'  +', ' ', line_content)
                cleaned_line = line[:leading_whitespace] + cleaned_content
                cleaned_lines.append(cleaned_line)
            else:
                cleaned_lines.append(line)
        
        content = '\n'.join(cleaned_lines)
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed {changes_made} Unicode issues in {file_path}")
            return changes_made
        else:
            print(f"No Unicode issues found in {file_path}")
            return 0
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0

def main():
    """Carefully fix Unicode errors in key files"""
    
    print("Careful Unicode Fix Tool")
    print("=" * 30)
    
    # Only fix the most important files
    files_to_fix = [
        "modules/map_trade.py",
        "modules/enhanced_help_click.py",
        "modules/zombie_invasion.py",
    ]
    
    total_changes = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            changes = careful_unicode_fix(file_path)
            total_changes += changes
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nTotal Unicode issues fixed: {total_changes}")
    
    if total_changes > 0:
        print("Unicode encoding errors fixed without breaking indentation!")
    else:
        print("No Unicode issues found.")

if __name__ == "__main__":
    main()
