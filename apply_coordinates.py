#!/usr/bin/env python3
"""
Apply Captured Coordinates - Update both config and module files
"""
import json
import re

# Your captured coordinates
coordinates = {
    '1_map_button': (53, 913),
    '2_hidden_treasure': (1246, 155),
    '3_trade_area': (1619, 1363),
    'M1_quantity': (1079, 833),  # Using the second capture (more recent)
    'M2_quantity': (1245, 835),
    'M3_quantity': (1412, 835),
    'M4_quantity': (1577, 833),
    'M5_quantity': (1163, 994),
    'M6_quantity': (1329, 994),
    'M7_quantity': (1495, 994),
    'M1_click': (1031, 780),
    'M2_click': (1200, 784),
    'M3_click': (1364, 782),
    'M4_click': (1525, 786),
    'M5_click': (1118, 946),
    'M6_click': (1281, 943),
    'M7_click': (1450, 945),
    'you_will_lose': (1477, 467),
    'initiate_exchange': (1276, 1181),
    'new_exchange': (1269, 1181),
    'confirm_exchange': (1275, 956),
    '4_confirm_1': (1445, 261),
    '5_trade_option_2': (1278, 512),
    '6_confirm_2': (1273, 955),
    'red_thumb_fallback1': (1533, 801),
    'red_thumb_fallback2': (1539, 807),
    'red_thumb_fallback3': (1538, 796),
}

def update_config_file():
    """Update module_configs.json with new coordinates"""
    print("Updating module_configs.json...")
    
    try:
        with open('module_configs.json', 'r') as f:
            config = json.load(f)
        
        # Update map_trade click_coordinates
        click_coords = config['map_trade']['click_coordinates']
        
        for coord in click_coords:
            name = coord.get('name', '')
            if name in coordinates:
                x, y = coordinates[name]
                coord['x'] = x
                coord['y'] = y
                print(f"  Updated {name}: ({x}, {y})")
        
        # Save updated config
        with open('module_configs.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Config file updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating config file: {e}")

def update_module_file():
    """Update modules/map_trade.py with new coordinates"""
    print("\nUpdating modules/map_trade.py...")
    
    try:
        with open('modules/map_trade.py', 'r') as f:
            content = f.read()
        
        # Update map piece coordinates in _define_map_piece_regions
        map_pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
        
        for piece in map_pieces:
            qty_key = f'{piece}_quantity'
            click_key = f'{piece}_click'
            
            if qty_key in coordinates and click_key in coordinates:
                qty_x, qty_y = coordinates[qty_key]
                click_x, click_y = coordinates[click_key]
                
                # Update quantity_pos
                old_qty_pattern = f"'{piece}': {{\\s*'quantity_pos': \\(\\d+, \\d+\\),"
                new_qty = f"'{piece}': {{\n                'quantity_pos': ({qty_x}, {qty_y}),"
                content = re.sub(old_qty_pattern, new_qty, content)
                
                # Update click_pos - need to be more specific to avoid conflicts
                # Find the specific M piece section and update its click_pos
                piece_section_pattern = f"('{piece}': {{[^}}]*'quantity_pos': \\({qty_x}, {qty_y}\\),[^}}]*'click_pos': )\\(\\d+, \\d+\\)"
                piece_section_replacement = f"\\1({click_x}, {click_y})"
                content = re.sub(piece_section_pattern, piece_section_replacement, content)
                
                print(f"  Updated {piece}: quantity=({qty_x}, {qty_y}), click=({click_x}, {click_y})")
        
        # Update hardcoded button coordinates
        button_updates = [
            ('you_will_lose', r'lose_area_x, lose_area_y = \d+, \d+'),
            ('initiate_exchange', r'initiate_x, initiate_y = \d+, \d+'),
            ('confirm_exchange', r'confirm_x, confirm_y = \d+, \d+'),
            ('new_exchange', r'new_exchange_x, new_exchange_y = \d+, \d+'),
        ]
        
        for coord_name, pattern in button_updates:
            if coord_name in coordinates:
                x, y = coordinates[coord_name]
                replacement = f'{coord_name.replace("_", "_area_" if coord_name == "you_will_lose" else "_")}_x, {coord_name.replace("_", "_area_" if coord_name == "you_will_lose" else "_")}_y = {x}, {y}'
                if coord_name == 'you_will_lose':
                    replacement = f'lose_area_x, lose_area_y = {x}, {y}'
                elif coord_name == 'initiate_exchange':
                    replacement = f'initiate_x, initiate_y = {x}, {y}'
                elif coord_name == 'confirm_exchange':
                    replacement = f'confirm_x, confirm_y = {x}, {y}'
                elif coord_name == 'new_exchange':
                    replacement = f'new_exchange_x, new_exchange_y = {x}, {y}'
                
                content = re.sub(pattern, replacement, content)
                print(f"  Updated {coord_name}: ({x}, {y})")
        
        # Update red thumb fallback coordinates
        fallback_pattern = r'fallback_locations = \[\s*\(\d+, \d+\),\s*# Common red thumb location 1\s*\(\d+, \d+\),\s*# Common red thumb location 2\s*\(\d+, \d+\),\s*# Common red thumb location 3\s*\]'
        
        if 'red_thumb_fallback1' in coordinates and 'red_thumb_fallback2' in coordinates and 'red_thumb_fallback3' in coordinates:
            x1, y1 = coordinates['red_thumb_fallback1']
            x2, y2 = coordinates['red_thumb_fallback2']
            x3, y3 = coordinates['red_thumb_fallback3']
            
            fallback_replacement = f'''fallback_locations = [
                        ({x1}, {y1}),  # Common red thumb location 1
                        ({x2}, {y2}),  # Common red thumb location 2
                        ({x3}, {y3}),  # Common red thumb location 3
                    ]'''
            
            content = re.sub(fallback_pattern, fallback_replacement, content, flags=re.MULTILINE | re.DOTALL)
            print(f"  Updated red thumb fallbacks: ({x1}, {y1}), ({x2}, {y2}), ({x3}, {y3})")
        
        # Save updated module
        with open('modules/map_trade.py', 'w') as f:
            f.write(content)
        
        print("✅ Module file updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating module file: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to update all coordinates"""
    print("🔧 Applying captured coordinates...")
    print(f"📊 Total coordinates to apply: {len(coordinates)}")
    print()
    
    # Update config file
    update_config_file()
    
    # Update module file
    update_module_file()
    
    print("\n🎉 All coordinates applied!")
    print("\nCoordinates applied:")
    for name, (x, y) in coordinates.items():
        print(f"  {name}: ({x}, {y})")

if __name__ == "__main__":
    main()
