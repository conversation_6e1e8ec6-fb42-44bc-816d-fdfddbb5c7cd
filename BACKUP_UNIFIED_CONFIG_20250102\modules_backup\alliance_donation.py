"""
Alliance Donation Module for Last War automation
Handles BOTH manual and automatic alliance donations with configurable click sequence
"""
import time
import pyautogui
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import logging
import os

from enhanced_base_module import EnhancedBaseModule


class AllianceDonationModule(EnhancedBaseModule):
    """Module for handling alliance donations - both manual trigger and automatic hourly"""

    def __init__(self):
        super().__init__(name="alliance_donation", priority=3, enabled=True)

        # Dual mode settings
        self.is_manual_trigger = True  # Supports manual trigger
        self.is_executing_donation = False  # Flag to track if donations are running
        self.pause_other_modules = False  # Flag to pause other modules during manual execution

        # Automatic mode settings
        self.cooldown_time = 3600  # 1 hour between automatic donations
        self.last_auto_execution = 0  # Track last automatic execution

        # Default step delay between clicks
        self.default_step_delay = 2.0

        # Base template for smart return-to-base detection
        self.base_template = "Base"  # Template name for Base icon (from config)
        self.base_click_delay = 3.0  # Delay after clicking Base (longer for navigation)
        
        # Load alliance donation click sequence from configuration
        self.donation_click_sequence = self._load_click_sequence_from_config()

        # Templates for alliance donations detection
        self.donation_templates = [
            "Base",  # Base icon for return-to-base
            "donation_button",
            "donate_button",
            "alliance_tech",
            "research_donation",
            "alliance_store"
        ]

        # Logger for this module
        self.logger = logging.getLogger("LastWar.alliance_donation")
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        DUAL MODE: Check if automatic donations should run (hourly)
        Manual donations use manual_trigger_alliance_donation() instead
        """
        # Don't execute automatically if manual donation is running
        if self.is_executing_donation:
            return False

        # Check if enough time has passed for automatic execution (1 hour)
        current_time = time.time()
        if current_time - self.last_auto_execution < self.cooldown_time:
            time_remaining = self.cooldown_time - (current_time - self.last_auto_execution)
            self.current_activity = f"Cooldown: {int(time_remaining/60)}min remaining"
            self.waiting_for = "Hourly timer"
            return False

        # Check if we're on main screen (Events button visible)
        templates_found = screen_data.get('templates_found', {})
        if "events_button" not in templates_found or not templates_found["events_button"]:
            self.current_activity = "Waiting for main screen"
            self.waiting_for = "Main screen"
            return False

        # Ready for automatic hourly donation
        self.current_activity = "Ready for hourly donation"
        self.waiting_for = "Execution"
        return True

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to can_execute"""
        return self.can_execute(screen_data)
    
    def manual_trigger_alliance_donation(self) -> bool:
        """
        MANUAL TRIGGER: Execute alliance donations when button is pressed
        This pauses all other modules during execution
        """
        if self.is_executing_donation:
            self.logger.warning("Alliance donations already running - ignoring trigger")
            return False

        self.logger.info("🎯 MANUAL TRIGGER: Starting alliance donation sequence")
        self.is_executing_donation = True
        self.pause_other_modules = True

        try:
            # Execute the alliance donation sequence
            success = self._execute_donation_sequence()
            return success
        finally:
            # Always resume other modules when done
            self.is_executing_donation = False
            self.pause_other_modules = False
            self.logger.info("🎯 Alliance donations complete - resuming other modules")

    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        AUTOMATIC MODE: Execute hourly alliance donations
        """
        try:
            self.logger.info("🕐 AUTOMATIC: Starting hourly alliance donation")
            self.last_auto_execution = time.time()

            # Execute the same sequence as manual mode
            success = self._execute_donation_sequence()

            if success:
                self.logger.info("[SUCCESS] Automatic alliance donations completed successfully")
            else:
                self.logger.warning("⚠️ Automatic alliance donations failed")

            return success

        except Exception as e:
            self.logger.error(f"❌ Error in automatic alliance donations: {str(e)}")
            return False

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to execute"""
        return self.execute(screen_data)
    
    def get_cooldown(self) -> float:
        """Get cooldown time for automatic donations (1 hour)"""
        return self.cooldown_time

    def _execute_donation_sequence(self) -> bool:
        """
        Execute the configured alliance donation click sequence
        This is the main execution method called by both manual and automatic modes
        """
        try:
            self.logger.info("🚀 Starting alliance donation click sequence")

            # Disable PyAutoGUI safety pause for precise timing
            original_pause = pyautogui.PAUSE
            pyautogui.PAUSE = 0  # Remove default delay

            # Pause all other modules (for manual mode)
            if self.pause_other_modules:
                self._pause_other_modules()

            # STEP 0: Smart Base Detection and Return
            if not self._smart_return_to_base():
                self.logger.error("❌ Failed to return to base - aborting alliance donations")
                return False

            # Execute each click in the numbered sequence
            total_steps = len(self.donation_click_sequence)
            if total_steps == 0:
                self.logger.warning("⚠️ No alliance donation steps configured")
                return False

            for i, step_data in enumerate(self.donation_click_sequence):
                # Handle both old format (4 items) and new format (5 items)
                if len(step_data) == 4:
                    x, y, delay, description = step_data
                    repeat_count = 1
                else:
                    x, y, delay, repeat_count, description = step_data

                self.logger.info(f"[STEP] {description} ({i+1}/{total_steps})")

                # Perform the click(s) with repeat
                for repeat_num in range(repeat_count):
                    if repeat_count > 1:
                        self.logger.info(f"[CLICK] Click {repeat_num+1}/{repeat_count} at ({x}, {y})")
                    else:
                        self.logger.info(f"[CLICK] Clicked at ({x}, {y})")

                    pyautogui.click(x, y)

                    # Use precise delay between repeated clicks (use the same delay as the step)
                    if repeat_count > 1 and repeat_num < repeat_count - 1:
                        repeat_delay = max(0.01, delay / 2)  # Half the step delay, minimum 0.01s
                        self.logger.info(f"[DELAY] Inter-click delay: {repeat_delay}s")
                        time.sleep(repeat_delay)

                # Wait for the specified delay after all repeats
                if delay > 0:
                    self.logger.info(f"[WAIT] Waiting {delay} seconds...")
                    time.sleep(delay)

            self.logger.info("[SUCCESS] Alliance donation sequence completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in alliance donation sequence: {str(e)}")
            return False
        finally:
            # Restore PyAutoGUI original pause
            pyautogui.PAUSE = original_pause

            # Always resume other modules (for manual mode)
            if self.pause_other_modules:
                self._resume_other_modules()
    
    def _load_click_sequence_from_config(self):
        """
        Load the alliance donation click sequence from module configuration
        Returns list of (x, y, delay, description) tuples
        """
        try:
            # Get click coordinates from module config
            if hasattr(self, 'module_config') and self.module_config:
                click_coords = self.module_config.get('click_coordinates', [])

                if click_coords:
                    sequence = []

                    # Sort by numerical order (1_, 2_, 3_, etc.)
                    def get_step_number(coord):
                        name = coord.get('name', '')
                        # Extract number from name (e.g., "1_Alliance" -> 1)
                        try:
                            if '_' in name:
                                return int(name.split('_')[0])
                            else:
                                return 999  # Put non-numbered items at end
                        except:
                            return 999

                    sorted_coords = sorted(click_coords, key=get_step_number)

                    for coord in sorted_coords:
                        # Check if this step is enabled
                        enabled = coord.get('enabled', True)
                        if not enabled:
                            self.logger.info(f"⏭️ Skipping disabled step: {coord.get('name', 'Unknown')}")
                            continue

                        x = coord.get('x', 0)
                        y = coord.get('y', 0)
                        name = coord.get('name', 'Unknown')
                        description = coord.get('description', '')

                        # Get custom delay for this step (fallback to default)
                        custom_delay = coord.get('delay', self.default_step_delay)

                        # Get repeat count for this step (fallback to 1)
                        repeat_count = coord.get('repeat', 1)
                        repeat_count = max(1, int(repeat_count))  # Ensure at least 1

                        # Create description with name, description, and repeat info
                        if repeat_count > 1:
                            full_description = f"{name}: {description} (×{repeat_count})".strip(': ')
                        else:
                            full_description = f"{name}: {description}".strip(': ')

                        # Add to sequence with custom delay and repeat count
                        sequence.append((x, y, custom_delay, repeat_count, full_description))

                    self.logger.info(f"Loaded {len(sequence)} alliance donation steps from configuration")

                    # Log each step for verification with custom delays and repeat counts
                    for i, step_data in enumerate(sequence):
                        if len(step_data) == 4:
                            x, y, delay, desc = step_data
                            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")
                        else:
                            x, y, delay, repeat_count, desc = step_data
                            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

                    return sequence
                else:
                    self.logger.warning("⚠️ No click coordinates found in alliance donation configuration")
            else:
                self.logger.warning("⚠️ No module configuration available for alliance donation")

        except Exception as e:
            self.logger.error(f"❌ Error loading alliance donation sequence from config: {str(e)}")

        # Return default sequence if config loading fails
        self.logger.info("📋 Using default placeholder alliance donation sequence")
        return [
            (100, 100, 2.0, "Default: Click alliance button"),
            (200, 200, 2.0, "Default: Click donation section"),
            (300, 300, 2.0, "Default: Make donation"),
            (400, 400, 2.0, "Default: Close interface"),
        ]

    def _detect_alliance_notifications(self, screen_data: Dict[str, Any]) -> bool:
        """Detect alliance notification indicators"""
        screenshot = screen_data.get('screenshot')
        if screenshot is None:
            return False
        
        try:
            # Look for red notification dots on alliance button
            # This is similar to help notifications but focused on alliance area
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            # Define range for red color (notification indicators)
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = mask1 + mask2
            
            # Focus on alliance button area (typically bottom of screen)
            height, width = screenshot.shape[:2]
            alliance_area = red_mask[height//2:, :]  # Bottom half of screen
            
            contours, _ = cv2.findContours(alliance_area, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 10 < area < 500:
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        if circularity > 0.5:
                            self.logger.debug("Found alliance notification indicator")
                            return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting alliance notifications: {str(e)}")
            return False
    
    def _navigate_to_alliance(self, screen_data: Dict[str, Any]) -> bool:
        """Navigate to the alliance section"""
        templates_found = screen_data.get('templates_found', {})
        
        # Alliance button template removed - using click coordinates instead
        
        # Try common alliance button positions
        common_alliance_positions = [
            (100, 550),  # Bottom left
            (50, 500),   # Left side bottom
            (150, 600),  # Bottom area
        ]
        
        for x, y in common_alliance_positions:
            # In real implementation, verify button exists
            # For now, simulate successful navigation
            pass
        
        return True  # Assume navigation successful
    
    def _process_donations(self) -> int:
        """Process all available donations"""
        donations_made = 0
        
        try:
            # Navigate to different donation sections
            donation_sections = [
                self._process_research_donations,
                self._process_alliance_store_donations,
                self._process_technology_donations
            ]
            
            for donation_function in donation_sections:
                try:
                    section_donations = donation_function()
                    donations_made += section_donations
                    
                    if donations_made >= self.max_donations_per_session:
                        self.logger.info(f"Reached maximum donations per session: {self.max_donations_per_session}")
                        break
                        
                except Exception as e:
                    self.logger.error(f"Error in donation section: {str(e)}")
                    continue
            
            return donations_made
            
        except Exception as e:
            self.logger.error(f"Error processing donations: {str(e)}")
            return donations_made
    
    def _process_research_donations(self) -> int:
        """Process research donations"""
        donations_made = 0
        
        try:
            # Navigate to research section
            research_positions = [
                (400, 200),  # Center top
                (300, 150),  # Left center
            ]
            
            # Look for research donation opportunities
            for attempt in range(5):  # Max 5 research donations
                # Take fresh screenshot
                screenshot = pyautogui.screenshot()
                screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                
                # Find donation buttons
                donation_buttons = self._find_donation_buttons(screenshot_cv)
                
                if not donation_buttons:
                    break
                
                # Click first available donation
                x, y = donation_buttons[0]
                pyautogui.click(x, y)
                time.sleep(self.donation_delay)
                
                # Confirm donation if needed
                self._confirm_donation()
                
                donations_made += 1
                self.logger.debug(f"Made research donation {donations_made}")
            
            return donations_made
            
        except Exception as e:
            self.logger.error(f"Error processing research donations: {str(e)}")
            return donations_made
    
    def _process_alliance_store_donations(self) -> int:
        """Process alliance store donations"""
        donations_made = 0
        
        try:
            # Navigate to alliance store
            store_button_positions = [
                (500, 300),  # Center
                (600, 250),  # Right center
            ]
            
            # Look for store donation opportunities
            for attempt in range(3):  # Max 3 store donations
                # Similar logic to research donations
                # but focused on store items
                
                # For now, simulate donation
                time.sleep(self.donation_delay)
                donations_made += 1
                
                if donations_made >= 3:  # Limit store donations
                    break
            
            return donations_made
            
        except Exception as e:
            self.logger.error(f"Error processing store donations: {str(e)}")
            return donations_made
    
    def _process_technology_donations(self) -> int:
        """Process technology donations"""
        donations_made = 0
        
        try:
            # Navigate to technology section
            tech_positions = [
                (200, 300),  # Left center
                (350, 200),  # Center top
            ]
            
            # Process technology donations
            for attempt in range(5):  # Max 5 tech donations
                # Similar to other donation types
                time.sleep(self.donation_delay)
                donations_made += 1
                
                if donations_made >= 5:
                    break
            
            return donations_made
            
        except Exception as e:
            self.logger.error(f"Error processing technology donations: {str(e)}")
            return donations_made
    
    def _find_donation_buttons(self, screenshot: np.ndarray) -> List[Tuple[int, int]]:
        """Find donation button positions"""
        donation_positions = []
        
        try:
            # Look for typical donation button colors (blue, green)
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            # Blue donation buttons
            lower_blue = np.array([100, 50, 50])
            upper_blue = np.array([130, 255, 255])
            blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)
            
            # Green donation buttons
            lower_green = np.array([40, 50, 50])
            upper_green = np.array([80, 255, 255])
            green_mask = cv2.inRange(hsv, lower_green, upper_green)
            
            # Combine masks
            combined_mask = blue_mask + green_mask
            
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 200 < area < 3000:  # Size range for donation buttons
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Check aspect ratio
                    aspect_ratio = w / h
                    if 1.0 < aspect_ratio < 3.0:
                        center_x = x + w // 2
                        center_y = y + h // 2
                        donation_positions.append((center_x, center_y))
            
            return donation_positions
            
        except Exception as e:
            self.logger.error(f"Error finding donation buttons: {str(e)}")
            return []
    
    def _confirm_donation(self):
        """Confirm donation if confirmation dialog appears"""
        try:
            time.sleep(0.5)  # Wait for dialog to appear
            
            # Look for confirmation buttons
            confirm_positions = [
                (400, 350),  # Center
                (450, 400),  # Center bottom
                (350, 300),  # Left center
            ]
            
            # Try clicking confirm button
            # In real implementation, you'd verify the button exists
            for x, y in confirm_positions:
                # Check if confirm button is present before clicking
                pass
            
            # Press Enter as fallback
            pyautogui.press('enter')
            time.sleep(0.5)
            
        except Exception as e:
            self.logger.error(f"Error confirming donation: {str(e)}")
    
    def _close_alliance_interface(self):
        """Close the alliance interface"""
        try:
            # Try ESC first
            pyautogui.press('esc')
            time.sleep(0.5)
            
            # Try back button positions
            back_positions = [
                (50, 50),   # Top-left back
                (100, 100), # Top-left area
            ]
            
            # In real implementation, verify button exists
            
        except Exception as e:
            self.logger.error(f"Error closing alliance interface: {str(e)}")
    
    def _pause_other_modules(self):
        """Pause all other modules during alliance donations execution"""
        try:
            # This would be implemented by the main controller
            # For now, just log the intent
            self.logger.info("🛑 PAUSING all other modules for alliance donations")
            # TODO: Implement actual module pausing via main controller
        except Exception as e:
            self.logger.error(f"Error pausing modules: {str(e)}")

    def _resume_other_modules(self):
        """Resume all other modules after alliance donations completion"""
        try:
            self.logger.info("▶️ RESUMING all other modules")
            # TODO: Implement actual module resuming via main controller
        except Exception as e:
            self.logger.error(f"Error resuming modules: {str(e)}")

    def _smart_return_to_base(self) -> bool:
        """
        Smart base detection: Click Base icon if visible, skip if already at base
        Returns True if ready to proceed, False if failed
        """
        try:
            self.logger.info("🏠 STEP 0: Checking if we need to return to base...")

            # Use simple template matching for base detection
            base_detected = self._detect_base_template()

            if base_detected:
                # Base icon found - we need to click it to return to base
                base_x, base_y = base_detected

                self.logger.info(f"🏠 Base icon detected - clicking to return to base")
                self.logger.info(f"[CLICK] Clicking Base at ({base_x}, {base_y})")

                # Click the Base icon
                pyautogui.click(base_x, base_y)

                # Wait longer for base navigation
                self.logger.info(f"[WAIT] Waiting {self.base_click_delay} seconds for base navigation...")
                time.sleep(self.base_click_delay)

                self.logger.info("[SUCCESS] Returned to base successfully")
            else:
                # Base icon not found - we're already at base
                self.logger.info("[SUCCESS] Already at base (Base icon not visible) - proceeding with alliance donations")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error in smart base detection: {str(e)}")
            return False

    def _detect_base_template(self):
        """
        Detect base template using simple template matching
        Returns (x, y) coordinates if found, None if not found
        """
        try:
            # Check if base template file exists
            template_path = f"templates/{self.base_template}.png"
            if not os.path.exists(template_path):
                self.logger.warning(f"⚠️ Base template not found: {template_path}")
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Load template
            template = cv2.imread(template_path)
            if template is None:
                self.logger.warning(f"⚠️ Could not load base template: {template_path}")
                return None

            # Perform template matching
            result = cv2.matchTemplate(screenshot_bgr, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # Check if match is good enough (threshold)
            threshold = 0.8
            if max_val >= threshold:
                # Calculate center of template
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2

                self.logger.debug(f"[FOUND] Base template found at ({center_x}, {center_y}) with confidence {max_val:.2f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"🔍 Base template not found (best match: {max_val:.2f})")
                return None

        except Exception as e:
            self.logger.error(f"Error in base template detection: {str(e)}")
            return None

    def reload_configuration(self):
        """
        Reload the alliance donation configuration from file (live reload)
        This allows updating delays without restarting the application
        """
        try:
            self.logger.info("🔄 Reloading alliance donation configuration...")

            # Reload the click sequence from config
            old_count = len(self.donation_click_sequence)
            self.donation_click_sequence = self._load_click_sequence_from_config()
            new_count = len(self.donation_click_sequence)

            self.logger.info(f"[SUCCESS] Configuration reloaded: {old_count} -> {new_count} steps")

            # Log the updated sequence
            for i, (x, y, delay, desc) in enumerate(self.donation_click_sequence):
                self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to reload configuration: {str(e)}")
            return False

    def is_alliance_donation_running(self) -> bool:
        """Check if alliance donations are currently executing"""
        return self.is_executing_donation

    def configure_click_sequence(self, sequence: List[Tuple[int, int, float, str]]):
        """
        Configure the alliance donation click sequence

        Args:
            sequence: List of (x, y, delay_after, description) tuples
        """
        self.donation_click_sequence = sequence
        self.logger.info(f"📝 Configured alliance donation sequence with {len(sequence)} steps")

        # Log the sequence for verification
        for i, (x, y, delay, desc) in enumerate(sequence):
            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

    def get_click_sequence(self) -> List[Tuple[int, int, float, str]]:
        """Get the current click sequence configuration"""
        return self.donation_click_sequence.copy()

    def get_donation_stats(self) -> Dict[str, Any]:
        """Get donation statistics"""
        current_time = time.time()
        return {
            'module': self.name,
            'total_donations': self.execution_count,
            'last_auto_donation': self.last_auto_execution,
            'cooldown_remaining': max(0, self.get_cooldown() - (current_time - self.last_auto_execution)),
            'next_auto_donation': self.last_auto_execution + self.get_cooldown() if self.last_auto_execution > 0 else current_time,
            'is_manual_running': self.is_executing_donation,
            'status': 'active' if self.enabled else 'disabled'
        }

    # Centralized Scanner Action Methods
    def start_sequence(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for Alliance_Icon detection - start donation sequence"""
        try:
            self.logger.info("🎯 CENTRALIZED: Alliance_Icon detected - starting donation sequence")
            success = self.manual_trigger_alliance_donation()
            self.logger.info(f"✅ CENTRALIZED: Alliance donation sequence completed - success: {success}")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in start_sequence action: {e}")
            return False
