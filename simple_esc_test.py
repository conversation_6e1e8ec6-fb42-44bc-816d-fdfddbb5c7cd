#!/usr/bin/env python3
"""
Simple ESC key test - no emojis, no complex logging, just basic ESC key functionality
"""

import time
import pyautogui

def simple_esc_test():
    """Simple test of ESC key functionality"""
    print("Simple ESC Key Test")
    print("=" * 30)
    
    # Ask user if they want to test
    response = input("Press ESC key 3 times with delays? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("Test cancelled.")
        return
    
    print("Starting ESC key test in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("Executing ESC key presses...")
    
    try:
        # ESC key press 1
        print("ESC press 1/3")
        pyautogui.press('esc')
        print("ESC 1 completed")
        time.sleep(2)
        
        # ESC key press 2
        print("ESC press 2/3")
        pyautogui.press('esc')
        print("ESC 2 completed")
        time.sleep(2)
        
        # ESC key press 3
        print("ESC press 3/3")
        pyautogui.press('esc')
        print("ESC 3 completed")
        
        print("All 3 ESC key presses completed successfully!")
        print("If you didn't see 3 separate effects, the issue might be:")
        print("1. No menu/dialog open to close")
        print("2. Game window not in focus")
        print("3. ESC key not affecting the game")
        
    except Exception as e:
        print(f"Error during ESC key test: {str(e)}")

def test_single_esc():
    """Test a single ESC key press"""
    print("\nSingle ESC Key Test")
    print("=" * 30)
    
    response = input("Press ESC key once? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("Test cancelled.")
        return
    
    print("Pressing ESC key in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        print("Pressing ESC key...")
        pyautogui.press('esc')
        print("ESC key pressed successfully!")
        
    except Exception as e:
        print(f"Error during single ESC press: {str(e)}")

if __name__ == "__main__":
    print("ESC Key Functionality Test")
    print("=" * 40)
    
    try:
        # Test 1: Multiple ESC presses
        simple_esc_test()
        
        # Test 2: Single ESC press
        test_single_esc()
        
        print("\nTest complete!")
        print("This should help determine if ESC key works reliably.")
        
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
