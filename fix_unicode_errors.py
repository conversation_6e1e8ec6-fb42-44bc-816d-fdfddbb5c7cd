#!/usr/bin/env python3
"""
Fix Unicode encoding errors across all modules
"""

import os
import re

def fix_unicode_in_file(file_path):
    """Fix Unicode characters in a single file"""
    
    # Unicode characters to replace (emoji -> text)
    unicode_replacements = {
        '🗺️': '',
        '🔍': '',
        '🔄': '',
        '🔴': '',
        '📋': '',
        '➡️': '',
        '✅': '',
        '💰': '',
        '⚠️': '',
        '❌': '',
        '🖱️': '',
        '⌨️': '',
        '🧪': '',
        '⏳': '',
        '🎯': '',
        '🚀': '',
        '🌍': '',
        '⚔️': '',
        '🚶': '',
        '🛑': '',
        '🕒': '',
        '🚫': '',
        '🏠': '',
        '🧟': '',
        '⏸️': '',
        '⏰': '',
        '🎉': '',
        '📁': '',
        '📄': '',
        '🔑': '',
        '⏭️': '',
        '🔧': '',
        '🎮': '',
        '📊': '',
        '🛡️': '',
        '📈': '',
        '🔥': '',
        '💡': '',
        '🎪': '',
        '🎨': '',
        '🎵': '',
        '🎸': '',
        '🎺': '',
        '🎻': '',
        '🥁': '',
        '🎤': '',
        '🎧': '',
        '🎬': '',
        '🎭': '',
        '🎪': '',
        '🎨': '',
        '🎯': '',
        '🎲': '',
        '🎳': '',
        '🎮': '',
        '🎰': '',
        '🎱': '',
        '🎴': '',
        '🎵': '',
        '🎶': '',
        '🎷': '',
        '🎸': '',
        '🎹': '',
        '🎺': '',
        '🎻': '',
        '🎼': '',
        '🎽': '',
        '🎾': '',
        '🎿': '',
        '🏀': '',
        '🏁': '',
        '🏂': '',
        '🏃': '',
        '🏄': '',
        '🏅': '',
        '🏆': '',
        '🏇': '',
        '🏈': '',
        '🏉': '',
        '🏊': '',
        '🏋': '',
        '🏌': '',
        '🏍': '',
        '🏎': '',
        '🏏': '',
        '🏐': '',
        '🏑': '',
        '🏒': '',
        '🏓': '',
        '🏔': '',
        '🏕': '',
        '🏖': '',
        '🏗': '',
        '🏘': '',
        '🏙': '',
        '🏚': '',
        '🏛': '',
        '🏜': '',
        '🏝': '',
        '🏞': '',
        '🏟': '',
        '🏠': '',
        '🏡': '',
        '🏢': '',
        '🏣': '',
        '🏤': '',
        '🏥': '',
        '🏦': '',
        '🏧': '',
        '🏨': '',
        '🏩': '',
        '🏪': '',
        '🏫': '',
        '🏬': '',
        '🏭': '',
        '🏮': '',
        '🏯': '',
        '🏰': '',
    }
    
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Replace Unicode characters
        for unicode_char, replacement in unicode_replacements.items():
            if unicode_char in content:
                content = content.replace(unicode_char, replacement)
                changes_made += 1
        
        # Clean up any double spaces that might result from emoji removal
        content = re.sub(r'  +', ' ', content)
        
        # Clean up any leading/trailing spaces in log messages
        content = re.sub(r'self\.logger\.(info|warning|error|debug)\(f?"([^"]*) +([^"]*)"', 
                        r'self.logger.\1(f"\2\3"', content)
        content = re.sub(r'self\.logger\.(info|warning|error|debug)\("([^"]*) +([^"]*)"', 
                        r'self.logger.\1("\2\3"', content)
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed {changes_made} Unicode issues in {file_path}")
            return changes_made
        else:
            print(f"No Unicode issues found in {file_path}")
            return 0
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0

def main():
    """Fix Unicode errors in all Python files"""
    
    print("Unicode Error Fix Tool")
    print("=" * 30)
    
    # Files to check
    files_to_check = [
        "modules/zombie_invasion.py",
        "modules/enhanced_help_click.py",
        "modules/map_trade.py",
        "modules/alliance_donation.py",
        "modules/daily_tasks.py",
        "modules/help_click.py",
        "modules/gathering_troops.py",
        "modules/dig.py",
        "debug_main_scanning.py",
        "test_config_editor_enhancements.py",
        "comprehensive_esc_test.py",
        "test_esc_execution.py",
        "test_alliance_esc.py",
        "verify_enhanced_detection.py",
        "backup_map_trade_system.py",
        "test_clean_output.py",
        "test_scenario_detection.py",
        "capture_new_exchange_after_red_thumb.py",
        "debug_screen_state.py",
        "capture_new_exchange_coord.py"
    ]
    
    total_changes = 0
    files_processed = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            changes = fix_unicode_in_file(file_path)
            total_changes += changes
            files_processed += 1
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nSummary:")
    print(f"Files processed: {files_processed}")
    print(f"Total Unicode issues fixed: {total_changes}")
    
    if total_changes > 0:
        print("\nAll Unicode encoding errors should now be fixed!")
        print("The system should run without Unicode errors on all systems.")
    else:
        print("\nNo Unicode issues found.")

if __name__ == "__main__":
    main()
