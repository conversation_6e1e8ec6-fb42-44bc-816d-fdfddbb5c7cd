#!/usr/bin/env python3
"""
Fix coordinates for M2, M3, M6 that didn't read
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def capture_and_read_number(x, y, size=25):
    """Capture region and read number"""
    try:
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save image for debugging
        cv2.imwrite(f"debug_{x}_{y}.png", image)
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        print(f"  Image saved as debug_{x}_{y}.png")
        print(f"  OCR result: '{text}'")
        
        if text.isdigit():
            return int(text)
        return None
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_coordinate_adjustments():
    """Test different coordinates for the failing pieces"""
    
    # Original coordinates that failed
    failed_coords = {
        'M2': (613, 436),
        'M3': (696, 436), 
        'M6': (654, 521)
    }
    
    print("Testing coordinate adjustments for failed pieces")
    print("=" * 50)
    
    for map_name, (orig_x, orig_y) in failed_coords.items():
        print(f"\n{map_name} - Original: ({orig_x}, {orig_y})")
        
        # Test original
        result = capture_and_read_number(orig_x, orig_y)
        print(f"  Original result: {result}")
        
        # Test slight adjustments
        adjustments = [
            (-5, 0), (5, 0),    # Left/right
            (0, -5), (0, 5),    # Up/down
            (-3, -3), (3, 3),   # Diagonal
        ]
        
        for dx, dy in adjustments:
            new_x, new_y = orig_x + dx, orig_y + dy
            print(f"  Testing ({new_x}, {new_y})...")
            result = capture_and_read_number(new_x, new_y)
            if result is not None:
                print(f"    ✅ SUCCESS: {result} at ({new_x}, {new_y})")
                break
        else:
            print(f"    ❌ All adjustments failed for {map_name}")

def manual_coordinate_capture():
    """Let user manually position mouse and capture coordinates"""
    print("\nManual Coordinate Capture")
    print("=" * 30)
    print("Position your mouse over each map piece number and press SPACE")
    print("Press ESC to finish")
    
    results = {}
    
    for map_name in ['M2', 'M3', 'M6']:
        print(f"\nPosition mouse over {map_name} number and press SPACE")
        
        while True:
            time.sleep(0.1)
            if pyautogui.keyIsPressed('space'):
                x, y = pyautogui.position()
                print(f"Captured {map_name} at ({x}, {y})")
                
                # Test the coordinate
                result = capture_and_read_number(x, y)
                if result is not None:
                    print(f"✅ Reads: {result}")
                    results[map_name] = (x, y)
                    break
                else:
                    print("❌ Could not read number, try again")
                
                time.sleep(0.5)  # Prevent multiple captures
                
            elif pyautogui.keyIsPressed('esc'):
                return results
    
    return results

if __name__ == "__main__":
    print("Make sure you're on the map trade screen")
    
    choice = input("Choose: (1) Test coordinate adjustments (2) Manual capture: ")
    
    if choice == "1":
        test_coordinate_adjustments()
    else:
        results = manual_coordinate_capture()
        print(f"\nNew coordinates: {results}")
