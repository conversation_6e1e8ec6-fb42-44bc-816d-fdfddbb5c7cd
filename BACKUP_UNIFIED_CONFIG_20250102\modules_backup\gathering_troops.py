"""
Gathering Troops Module for Last War automation
Handles automatic troop sending for resource gathering
"""
import time
import pyautogui
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import configparser

from base_module import BaseModule


class GatheringTroopsModule(BaseModule):
    """Module for sending troops to gather resources"""
    
    def __init__(self):
        super().__init__(name="gathering_troops", priority=4, enabled=True)
        
        # Load configuration
        self.config = configparser.ConfigParser()
        self.config.read("config.ini")
        
        # Module-specific settings
        self.cooldown_time = 180  # 3 minutes between gathering checks
        self.march_timeout = 30   # 30 seconds timeout for march setup
        
        # Resource preferences (from config)
        self.default_resource = self.config.get('GATHERING', 'default_resource', fallback='iron')
        self.troop_count = self.config.getint('GATHERING', 'troop_count', fallback=100000)
        self.march_time_limit = self.config.getint('GATHERING', 'march_time_limit', fallback=300)
        
        # Templates for gathering
        self.gathering_templates = [
            "world_map_button",
            "resource_node",
            "iron_node",
            "food_node", 
            "gold_node",
            "gather_button",
            "march_button",
            "troop_selection"
        ]
        
        # Text patterns for gathering
        self.gathering_texts = [
            "Gather",
            "March",
            "Resource",
            "Iron Mine",
            "Food Farm",
            "Gold Mine",
            "World Map"
        ]
        
        # Resource node colors (BGR format)
        self.resource_colors = {
            "iron": {"color": (128, 128, 128), "tolerance": 30},    # Gray
            "food": {"color": (0, 255, 0), "tolerance": 30},       # Green
            "gold": {"color": (0, 215, 255), "tolerance": 30},     # Yellow/Gold
        }
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Check if gathering can be executed
        
        Args:
            screen_data: Current screen analysis data
            
        Returns:
            bool: True if gathering opportunities are detected
        """
        # Check if we have available marches
        if not self._has_available_marches():
            self.logger.debug("No available marches for gathering")
            return False
        
        # Check for gathering templates
        templates_found = screen_data.get('templates_found', {})
        for template in self.gathering_templates:
            if template in templates_found:
                self.logger.debug(f"Found gathering template: {template}")
                return True
        
        # Check for gathering-related text
        text_regions = screen_data.get('text_regions', {})
        for region_name, text in text_regions.items():
            for gathering_text in self.gathering_texts:
                if gathering_text.lower() in text.lower():
                    self.logger.debug(f"Found gathering text: {gathering_text} in {region_name}")
                    return True
        
        return False
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Execute gathering troops automation
        
        Args:
            screen_data: Current screen analysis data
            
        Returns:
            bool: True if execution was successful
        """
        try:
            self.logger.info("Starting gathering troops execution")
            
            # Step 1: Open world map
            if not self._open_world_map(screen_data):
                self.logger.warning("Failed to open world map")
                return False
            
            # Step 2: Find and gather resources
            resources_gathered = self._gather_resources()
            
            # Step 3: Close world map
            self._close_world_map()
            
            self.logger.info(f"Gathering execution completed. Resources gathered: {resources_gathered}")
            return resources_gathered > 0
            
        except Exception as e:
            self.logger.error(f"Error executing gathering troops: {str(e)}")
            return False
    
    def get_cooldown(self) -> float:
        """Get cooldown time for gathering"""
        return self.cooldown_time
    
    def _has_available_marches(self) -> bool:
        """Check if there are available marches for gathering"""
        try:
            # This would check the march queue/availability
            # For now, assume we have marches available
            # In real implementation, you'd check the march counter on screen
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking available marches: {str(e)}")
            return False
    
    def _open_world_map(self, screen_data: Dict[str, Any]) -> bool:
        """Open the world map interface"""
        templates_found = screen_data.get('templates_found', {})
        
        # Try to find and click world map button
        if "world_map_button" in templates_found:
            matches = templates_found["world_map_button"]
            if matches:
                x, y, w, h = matches[0]
                click_x = x + w // 2
                click_y = y + h // 2
                
                self.logger.debug(f"Clicking world map at ({click_x}, {click_y})")
                pyautogui.click(click_x, click_y)
                time.sleep(3)  # Wait for map to load
                return True
        
        # Try common world map button positions
        map_positions = [
            (400, 50),   # Top center
            (750, 100),  # Top right
            (50, 300),   # Left side
        ]
        
        for x, y in map_positions:
            # In real implementation, verify button exists
            # For now, simulate successful map opening
            pass
        
        return True  # Assume map opened successfully
    
    def _gather_resources(self) -> int:
        """Find and gather available resources"""
        resources_gathered = 0
        max_gathering_attempts = 5  # Maximum gathering operations per session
        
        try:
            for attempt in range(max_gathering_attempts):
                # Check if we still have available marches
                if not self._has_available_marches():
                    self.logger.info("No more available marches")
                    break
                
                # Find resource nodes
                resource_node = self._find_resource_node(self.default_resource)
                
                if not resource_node:
                    # Try other resource types if default not found
                    for resource_type in ["iron", "food", "gold"]:
                        if resource_type != self.default_resource:
                            resource_node = self._find_resource_node(resource_type)
                            if resource_node:
                                break
                
                if not resource_node:
                    self.logger.debug("No suitable resource nodes found")
                    break
                
                # Attempt to gather from the found node
                if self._gather_from_node(resource_node):
                    resources_gathered += 1
                    self.logger.info(f"Successfully sent gathering march {resources_gathered}")
                    time.sleep(2)  # Wait between marches
                else:
                    self.logger.warning("Failed to send gathering march")
            
            return resources_gathered
            
        except Exception as e:
            self.logger.error(f"Error gathering resources: {str(e)}")
            return resources_gathered
    
    def _find_resource_node(self, resource_type: str) -> Tuple[int, int]:
        """Find a resource node of the specified type"""
        try:
            # Take fresh screenshot
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Look for resource nodes using color detection
            if resource_type in self.resource_colors:
                color_info = self.resource_colors[resource_type]
                target_color = color_info["color"]
                tolerance = color_info["tolerance"]
                
                # Create color mask
                lower_bound = np.array([max(0, c - tolerance) for c in target_color])
                upper_bound = np.array([min(255, c + tolerance) for c in target_color])
                
                mask = cv2.inRange(screenshot_cv, lower_bound, upper_bound)
                
                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Look for appropriately sized resource nodes
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 100 < area < 2000:  # Size range for resource nodes
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # Check if it's roughly circular/square (resource nodes)
                        aspect_ratio = w / h
                        if 0.7 < aspect_ratio < 1.3:
                            center_x = x + w // 2
                            center_y = y + h // 2
                            
                            # Verify it's not too close to screen edges
                            screen_height, screen_width = screenshot_cv.shape[:2]
                            if (50 < center_x < screen_width - 50 and 
                                50 < center_y < screen_height - 50):
                                self.logger.debug(f"Found {resource_type} node at ({center_x}, {center_y})")
                                return (center_x, center_y)
            
            # If color detection fails, try template matching
            # This would use pre-saved templates of resource nodes
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding resource node: {str(e)}")
            return None
    
    def _gather_from_node(self, node_position: Tuple[int, int]) -> bool:
        """Send gathering march to a specific resource node"""
        try:
            x, y = node_position
            
            # Click on the resource node
            self.logger.debug(f"Clicking resource node at ({x}, {y})")
            pyautogui.click(x, y)
            time.sleep(1.5)  # Wait for node info to appear
            
            # Look for and click gather button
            if not self._click_gather_button():
                self.logger.warning("Failed to find gather button")
                return False
            
            # Configure troops and march
            if not self._configure_gathering_march():
                self.logger.warning("Failed to configure gathering march")
                return False
            
            # Confirm and send march
            if not self._send_march():
                self.logger.warning("Failed to send march")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error gathering from node: {str(e)}")
            return False
    
    def _click_gather_button(self) -> bool:
        """Find and click the gather button"""
        try:
            # Common positions for gather button
            gather_positions = [
                (400, 400),  # Center
                (500, 450),  # Center bottom
                (300, 350),  # Left center
            ]
            
            # Look for gather button using color detection (typically green/blue)
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)
            
            # Green gather buttons
            lower_green = np.array([40, 50, 50])
            upper_green = np.array([80, 255, 255])
            green_mask = cv2.inRange(hsv, lower_green, upper_green)
            
            contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # Size range for gather button
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Check aspect ratio (buttons are wider than tall)
                    aspect_ratio = w / h
                    if 1.5 < aspect_ratio < 4.0:
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        self.logger.debug(f"Clicking gather button at ({center_x}, {center_y})")
                        pyautogui.click(center_x, center_y)
                        time.sleep(1.5)
                        return True
            
            # Fallback to common positions
            for x, y in gather_positions:
                # In real implementation, verify button exists
                pass
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error clicking gather button: {str(e)}")
            return False
    
    def _configure_gathering_march(self) -> bool:
        """Configure the gathering march (troops, etc.)"""
        try:
            # Set troop count if needed
            # This would involve interacting with troop selection interface
            
            # For now, assume default troop configuration is acceptable
            time.sleep(1)  # Wait for march configuration
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error configuring march: {str(e)}")
            return False
    
    def _send_march(self) -> bool:
        """Send the configured march"""
        try:
            # Look for march/send button
            march_positions = [
                (400, 500),  # Bottom center
                (500, 450),  # Center bottom
                (600, 400),  # Right center
            ]
            
            # Try to find and click march button
            for x, y in march_positions:
                # In real implementation, verify button exists
                # For now, simulate clicking
                pass
            
            # Press Enter as fallback
            pyautogui.press('enter')
            time.sleep(2)  # Wait for march to be sent
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending march: {str(e)}")
            return False
    
    def _close_world_map(self):
        """Close the world map interface"""
        try:
            # Try ESC first
            pyautogui.press('esc')
            time.sleep(1)
            
            # Try back button positions
            back_positions = [
                (50, 50),   # Top-left back
                (100, 100), # Top-left area
            ]
            
            # In real implementation, verify button exists
            
        except Exception as e:
            self.logger.error(f"Error closing world map: {str(e)}")
    
    def get_gathering_stats(self) -> Dict[str, Any]:
        """Get gathering statistics"""
        return {
            'module': self.name,
            'total_marches_sent': self.execution_count,
            'last_gathering_time': self.last_execution,
            'preferred_resource': self.default_resource,
            'troop_count': self.troop_count,
            'cooldown_remaining': max(0, self.get_cooldown() - (time.time() - (self.last_execution or 0))),
            'status': 'active' if self.enabled else 'disabled'
        }
