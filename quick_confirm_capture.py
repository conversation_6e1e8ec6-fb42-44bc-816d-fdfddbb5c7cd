#!/usr/bin/env python3
"""
Quick confirmation button coordinate capture
"""

import pyautogui
import time

def capture_confirm_coordinate():
    """Capture the exact confirmation button coordinate"""
    print("Quick Confirmation Button Capture")
    print("=" * 40)
    print()
    print("Steps:")
    print("1. Navigate to initiate exchange screen")
    print("2. Set up a trade (select pieces)")
    print("3. Click 'Initiate Exchange' to show confirmation dialog")
    print("4. Position mouse EXACTLY over the 'Confirm' button")
    print("5. Press Enter to capture coordinate")
    print()
    
    input("Press Enter when confirmation dialog is visible and mouse is positioned...")
    
    x, y = pyautogui.position()
    print(f"\n✅ Confirmation button coordinate: ({x}, {y})")
    
    # Test the coordinate immediately
    print(f"\nTesting click at ({x}, {y}) in 3 seconds...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("Clicking now!")
    pyautogui.click(x, y)
    
    print(f"\n📋 COPY THESE UPDATES:")
    print("=" * 40)
    print(f"For modules/map_trade.py (line ~742):")
    print(f"confirm_x, confirm_y = {x}, {y}")
    print()
    print(f"For test_simple_logic.py (line ~116):")
    print(f"confirm_x, confirm_y = {x}, {y}")
    print()
    print(f"For test_complete_map_trade.py (line ~30):")
    print(f"self.confirm_coord = ({x}, {y})")
    
    return x, y

def test_current_coordinate():
    """Test clicking the current coordinate"""
    current_x, current_y = 1258, 825
    
    print("Testing Current Coordinate")
    print("=" * 30)
    print(f"Current coordinate: ({current_x}, {current_y})")
    print("Make sure confirmation dialog is visible")
    
    input("Press Enter to test current coordinate...")
    
    print("Clicking in 3 seconds...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("Clicking now!")
    pyautogui.click(current_x, current_y)
    print("Click executed!")

def main():
    """Main menu"""
    while True:
        print("\nQuick Confirm Coordinate Capture")
        print("=" * 35)
        print("1. Capture New Coordinate")
        print("2. Test Current Coordinate")
        print("0. Exit")
        
        choice = input("\nChoice (0-2): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            x, y = capture_confirm_coordinate()
            
            # Ask if user wants to update files automatically
            update = input(f"\nUpdate files automatically with ({x}, {y})? (y/n): ").lower().strip()
            if update == 'y':
                update_files(x, y)
        elif choice == '2':
            test_current_coordinate()
        else:
            print("Invalid choice")

def update_files(x, y):
    """Update all files with the new coordinate"""
    try:
        # Update modules/map_trade.py
        with open('modules/map_trade.py', 'r') as f:
            content = f.read()
        
        # Find and replace the confirmation coordinate line
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'confirm_x, confirm_y =' in line and 'Step 5' in lines[i-1]:
                lines[i] = f'            confirm_x, confirm_y = {x}, {y}  # Auto-updated coordinate'
                break
        
        with open('modules/map_trade.py', 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ Updated modules/map_trade.py")
        
        # Update test_simple_logic.py
        with open('test_simple_logic.py', 'r') as f:
            content = f.read()
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'confirm_x, confirm_y =' in line and 'confirmation' in line.lower():
                lines[i] = f'        confirm_x, confirm_y = {x}, {y}  # Auto-updated coordinate'
                break
        
        with open('test_simple_logic.py', 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ Updated test_simple_logic.py")
        
        # Update test_complete_map_trade.py
        with open('test_complete_map_trade.py', 'r') as f:
            content = f.read()
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'self.confirm_coord =' in line:
                lines[i] = f'        self.confirm_coord = ({x}, {y})  # Auto-updated coordinate'
                break
        
        with open('test_complete_map_trade.py', 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ Updated test_complete_map_trade.py")
        print(f"\n🎉 All files updated with coordinate ({x}, {y})!")
        
    except Exception as e:
        print(f"❌ Error updating files: {e}")
        print("Please update manually using the coordinates shown above.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
