#!/usr/bin/env python3
"""
EXACT BACKUP METHOD - Using the exact OCR method from the working backup
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time
from typing import Optional, Dict

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# EXACT coordinates from backup_before_scanner_refactor\module_configs_backup.json
BACKUP_COORDINATES = {
    'M1': {
        'quantity_pos': (1068, 817),
        'click_pos': (1026, 765)
    },
    'M2': {
        'quantity_pos': (1224, 817),
        'click_pos': (1188, 772)
    },
    'M3': {
        'quantity_pos': (1384, 820),
        'click_pos': (1342, 773)
    },
    'M4': {
        'quantity_pos': (1533, 816),
        'click_pos': (1497, 773)
    },
    'M5': {
        'quantity_pos': (1144, 971),
        'click_pos': (1106, 923)
    },
    'M6': {
        'quantity_pos': (1298, 970),  # Exact from backup
        'click_pos': (1265, 919)     # Exact from backup
    },
    'M7': {
        'quantity_pos': (1457, 968),  # Exact from backup
        'click_pos': (1420, 924)     # Exact from backup
    }
}

def _extract_number_from_image(image: np.ndarray) -> Optional[int]:
    """EXACT method from backup - Extract multi-digit number using direct OCR"""
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Use direct OCR on the entire region (this works best according to user)
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()

        if text.isdigit():
            number = int(text)
            print(f"    ✅ Direct OCR success: {number}")
            return number
        else:
            print(f"    ❌ Direct OCR failed: '{text}' (not a valid number)")
            return None

    except Exception as e:
        print(f"    ❌ Error extracting number from image: {e}")
        return None

def _analyze_number_by_pixels(image: np.ndarray) -> Optional[int]:
    """EXACT backup fallback method: analyze number by pixel patterns"""
    try:
        # Create binary image
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)

        # Count dark pixels
        dark_pixels = cv2.countNonZero(binary)
        total_pixels = image.shape[0] * image.shape[1]
        density = dark_pixels / total_pixels if total_pixels > 0 else 0

        print(f"    📊 Pixel density: {density:.4f}")

        # Heuristic mapping (from backup)
        if density < 0.02:
            return 1
        elif density < 0.04:
            return 2
        elif density < 0.06:
            return 3
        elif density < 0.08:
            return 4
        elif density < 0.10:
            return 5
        elif density < 0.12:
            return 6
        else:
            return 7

    except Exception as e:
        print(f"    ❌ Error in pixel analysis: {e}")
        return None

def read_map_piece_backup_method(map_name: str, quantity_pos: tuple) -> Optional[int]:
    """EXACT backup method for reading map pieces"""
    try:
        x, y = quantity_pos
        print(f"  Reading {map_name} at ({x}, {y}) using BACKUP METHOD...")

        # EXACT capture method from backup - using 25x25 region
        capture_size = 25
        capture_x = x - capture_size // 2
        capture_y = y - capture_size // 2
        
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, capture_size, capture_size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save debug image
        cv2.imwrite(f"backup_debug_{map_name}_{x}_{y}.png", image)

        # Method 1: Direct OCR (primary method from backup)
        result = _extract_number_from_image(image)
        if result is not None:
            return result

        # Method 2: Pixel analysis fallback (from backup)
        print(f"    🔄 Trying pixel analysis fallback for {map_name}...")
        result = _analyze_number_by_pixels(image)
        if result is not None:
            print(f"    ✅ Pixel analysis success: {result}")
            return result

        print(f"    ❌ Both methods failed for {map_name}")
        return None

    except Exception as e:
        print(f"    ❌ Error reading {map_name}: {e}")
        return None

def analyze_map_pieces_backup() -> Dict[str, int]:
    """EXACT backup method - Read map piece quantities with proven coordinates"""
    map_quantities = {}

    print("🎯 Using EXACT BACKUP METHOD to read map pieces...")
    print("=" * 50)

    for map_name, coords in BACKUP_COORDINATES.items():
        quantity_pos = coords['quantity_pos']
        
        result = read_map_piece_backup_method(map_name, quantity_pos)
        if result is not None:
            map_quantities[map_name] = result
            print(f"  ✅ {map_name}: {result} pieces")
        else:
            print(f"  ❌ {map_name}: Failed to read")

    return map_quantities

def find_optimal_trade_backup(map_numbers: Dict[str, int]) -> tuple:
    """EXACT backup method - Find optimal trade"""
    if len(map_numbers) < 2:
        return None, None

    # Find piece with lowest quantity (give away)
    give_map = min(map_numbers.items(), key=lambda x: x[1])
    
    # Find piece with highest quantity (receive)
    receive_map = max(map_numbers.items(), key=lambda x: x[1])

    # Only trade if there's a benefit
    if give_map[1] >= receive_map[1]:
        return None, None

    return give_map[0], receive_map[0]

def execute_backup_trade(give_map: str, receive_map: str):
    """Execute trade using backup click coordinates"""
    print(f"\n🔄 Executing BACKUP trade: Give {give_map} → Get {receive_map}")
    
    give_coords = BACKUP_COORDINATES[give_map]['click_pos']
    receive_coords = BACKUP_COORDINATES[receive_map]['click_pos']
    
    print(f"Step 1: Clicking {give_map} at {give_coords}")
    pyautogui.click(give_coords[0], give_coords[1])
    time.sleep(0.5)
    
    # You'll need to calibrate these button coordinates for full screen
    print("⚠️  Button coordinates need to be calibrated for full screen!")
    print("Step 2: Click 'you will lose' button")
    print("Step 3: Click receive map piece")
    print("Step 4: Click 'initiate exchange'")
    print("Step 5: Click 'confirm exchange'")

def run_backup_method():
    """Main function using EXACT backup method"""
    print("🎯 EXACT BACKUP METHOD Map Trade")
    print("=" * 35)
    print("⚠️  Make sure the game is in FULL SCREEN mode!")
    print()

    # Analyze map pieces using backup method
    map_numbers = analyze_map_pieces_backup()

    if not map_numbers:
        print("❌ Could not analyze any map pieces")
        return False

    print(f"\n📊 BACKUP METHOD RESULTS:")
    print(f"Success rate: {len(map_numbers)}/7 pieces ({len(map_numbers)*100//7}%)")
    print(f"Detected quantities: {map_numbers}")

    # Find optimal trade
    give_map, receive_map = find_optimal_trade_backup(map_numbers)

    if not give_map or not receive_map:
        print("❌ No beneficial trade found")
        return False

    give_qty = map_numbers[give_map]
    receive_qty = map_numbers[receive_map]

    print(f"\n💡 BACKUP Trade Analysis:")
    print(f"  Give: {give_map} ({give_qty} pieces)")
    print(f"  Get: {receive_map} ({receive_qty} pieces)")
    print(f"  Benefit: +{receive_qty - give_qty} pieces")

    # Execute trade (coordinates need calibration)
    execute_backup_trade(give_map, receive_map)

    return True

if __name__ == "__main__":
    print("Make sure you're on the map trade screen in FULL SCREEN mode")
    input("Press ENTER to start EXACT BACKUP METHOD: ")
    
    success = run_backup_method()
    
    if success:
        print("\n🎉 Backup method completed!")
        print("📁 Check debug images: backup_debug_*.png")
    else:
        print("\n❌ Backup method failed")
