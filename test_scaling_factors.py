#!/usr/bin/env python3
"""
Test different scaling factors on the failed map pieces
"""
import cv2
import numpy as np
import pytesseract
import pyautogui
import os

def test_scaling_factors():
    """Test different scaling factors on M1, M3, M5 that are failing"""
    
    # Configure Tesseract path
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    # Failed map pieces coordinates
    failed_pieces = {
        'M1': (531, 435),
        'M3': (695, 436), 
        'M5': (572, 514)
    }
    
    # Test different scaling factors
    scaling_factors = [1, 2, 3, 4, 5]
    
    for map_name, (x, y) in failed_pieces.items():
        print(f"\n=== TESTING {map_name} at ({x}, {y}) ===")
        
        # Capture 35x35 region
        size = 35
        region_x = x - size // 2
        region_y = y - size // 2
        
        try:
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Save original for inspection
            cv2.imwrite(f"debug_{map_name}_original.png", gray)
            print(f"Original image saved: debug_{map_name}_original.png")
            
            # Test each scaling factor
            for scale in scaling_factors:
                print(f"\n  --- Testing {scale}x scaling ---")
                
                if scale == 1:
                    scaled = gray
                else:
                    scaled = cv2.resize(gray, None, fx=scale, fy=scale, interpolation=cv2.INTER_CUBIC)
                
                # Save scaled image
                cv2.imwrite(f"debug_{map_name}_{scale}x.png", scaled)
                
                # Try OCR with PSM 8 (proven method)
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(scaled, config=config).strip()
                
                if text.isdigit():
                    print(f"  {scale}x: SUCCESS -> {text}")
                elif text:
                    print(f"  {scale}x: '{text}' (not digit)")
                else:
                    print(f"  {scale}x: (empty)")
                    
        except Exception as e:
            print(f"Error testing {map_name}: {e}")

if __name__ == "__main__":
    test_scaling_factors()
