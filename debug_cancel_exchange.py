#!/usr/bin/env python3
"""
Debug what happens when cancel exchange is visible
"""

from modules.map_trade import MapTradeModule
import logging

def debug_cancel_exchange_scenario():
    """Debug the cancel exchange scenario"""
    print("Cancel Exchange Debug")
    print("=" * 30)
    print("Make sure 'Cancel Exchange' button is visible on screen")
    print("This will test what the system detects and does")
    
    input("Press Enter when Cancel Exchange is visible...")
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Create module
    module = MapTradeModule()
    
    print("\n1. Testing screen state detection...")
    screen_state = module._detect_screen_state()
    print(f"   Detected state: {screen_state}")
    
    print("\n2. Testing post-step-3 analysis...")
    try:
        result = module._post_step3_analysis()
        print(f"   Post-step-3 result: {result}")
    except Exception as e:
        print(f"   Error in post-step-3 analysis: {e}")
    
    print("\n3. Testing continue trade detection sequence...")
    try:
        result = module._continue_trade_detection_sequence()
        print(f"   Continue sequence result: {result}")
    except Exception as e:
        print(f"   Error in continue sequence: {e}")

def test_manual_trigger_with_cancel():
    """Test manual trigger when cancel exchange is visible"""
    print("Manual Trigger with Cancel Exchange Test")
    print("=" * 45)
    print("Make sure 'Cancel Exchange' button is visible on screen")
    print("This will test the full manual trigger flow")
    
    input("Press Enter when Cancel Exchange is visible...")
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Create module and trigger
    module = MapTradeModule()
    
    print("\nExecuting manual trigger...")
    try:
        result = module.manual_trigger_map_trade()
        print(f"Manual trigger result: {result}")
    except Exception as e:
        print(f"Error in manual trigger: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main menu"""
    while True:
        print("\nCancel Exchange Debug")
        print("=" * 25)
        print("1. Debug Cancel Exchange Scenario")
        print("2. Test Manual Trigger with Cancel")
        print("0. Exit")
        
        choice = input("\nChoice (0-2): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            debug_cancel_exchange_scenario()
        elif choice == '2':
            test_manual_trigger_with_cancel()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
