#!/usr/bin/env python3
"""
Demo script to show Web Claims Automation functionality
This creates a simple demo web form for testing the automation
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import tempfile
import os

def create_demo_html():
    """Create a demo HTML form for testing automation"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Demo Claims Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, select:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background-color: #45a049;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Demo Claims Form</h1>
        <form id="claimsForm">
            <div class="form-group">
                <label for="appointmentDate">Appointment Date:</label>
                <input type="text" id="appointmentDate" name="appointmentDate" placeholder="MM/DD/YYYY">
            </div>
            
            <div class="form-group">
                <label for="startTime">Start Time:</label>
                <input type="text" id="startTime" name="startTime" placeholder="HH:MM AM/PM">
            </div>
            
            <div class="form-group">
                <label for="endTime">End Time:</label>
                <input type="text" id="endTime" name="endTime" placeholder="HH:MM AM/PM">
            </div>
            
            <div class="form-group">
                <label for="appointmentType">Appointment Type:</label>
                <select id="appointmentType" name="appointmentType">
                    <option value="">Select Type</option>
                    <option value="neuro">Neuro</option>
                    <option value="williams">Williams</option>
                    <option value="drsmith">Dr Smith</option>
                    <option value="general">General Consultation</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="patientName">Patient Name:</label>
                <input type="text" id="patientName" name="patientName" placeholder="Enter patient name">
            </div>
            
            <div class="form-group">
                <label for="notes">Additional Notes:</label>
                <input type="text" id="notes" name="notes" placeholder="Optional notes">
            </div>
            
            <button type="submit" class="submit-btn">Submit Claim</button>
        </form>
        
        <div id="successMessage" class="success-message">
            Claim submitted successfully!
        </div>
    </div>

    <script>
        document.getElementById('claimsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            document.getElementById('successMessage').style.display = 'block';
            
            // Clear form after 2 seconds
            setTimeout(function() {
                document.getElementById('claimsForm').reset();
                document.getElementById('successMessage').style.display = 'none';
            }, 2000);
        });
        
        // Auto-focus first field
        document.getElementById('appointmentDate').focus();
    </script>
</body>
</html>
    """
    
    # Create temporary HTML file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False)
    temp_file.write(html_content)
    temp_file.close()
    
    return temp_file.name

def create_demo_gui():
    """Create demo control GUI"""
    root = tk.Tk()
    root.title("Web Claims Automation - Demo")
    root.geometry("500x400")
    
    # Title
    title_label = tk.Label(root, text="Web Claims Automation Demo", 
                          font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    # Instructions
    instructions = tk.Text(root, height=15, width=60, wrap=tk.WORD)
    instructions.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
    
    demo_text = """Welcome to the Web Claims Automation Demo!

This demo shows how to use the automation system:

1. SETUP:
   • Click "Open Demo Form" to open a test web form
   • Start the main automation application (main.py)

2. RECORD A SCENARIO:
   • Go to Configuration tab in main app
   • Enter scenario name: "Demo Form"
   • Click "Record Clicks"
   • Record this sequence:
     - Click date field, add "date" text input
     - Click start time field, add "start_time" text input  
     - Click end time field, add "end_time" text input
     - Click appointment type dropdown, select option
     - Click submit button

3. TEST AUTOMATION:
   • Go to Automation tab
   • Enter sample appointments:
     1 Jan 2:30 PM
     15 Mar 10:00 AM
   • Select "Demo Form" scenario
   • Click "Go" to start automation

4. WATCH THE MAGIC:
   • The system will automatically fill the form
   • Dates and times will be calculated automatically
   • Each appointment will be processed in sequence

Tips:
• Use the same browser window size for recording and automation
• Adjust delays if the form loads slowly
• Test with one appointment first before running multiple"""
    
    instructions.insert("1.0", demo_text)
    instructions.config(state=tk.DISABLED)
    
    # Buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    def open_demo_form():
        html_file = create_demo_html()
        webbrowser.open(f'file://{html_file}')
        messagebox.showinfo("Demo Form", f"Demo form opened in browser!\n\nFile: {html_file}")
    
    def open_main_app():
        os.system("python main.py")
    
    tk.Button(button_frame, text="Open Demo Form", command=open_demo_form,
              bg="green", fg="white", font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
    
    tk.Button(button_frame, text="Start Main App", command=open_main_app,
              bg="blue", fg="white", font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
    
    # Footer
    footer_label = tk.Label(root, text="Use this demo to learn the automation system before using on real websites",
                           font=("Arial", 10), fg="gray")
    footer_label.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    create_demo_gui()
