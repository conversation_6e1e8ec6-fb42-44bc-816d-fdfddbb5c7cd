#!/usr/bin/env python3
"""
Debug screen state detection
"""

from modules.map_trade import MapTradeModule
import logging

def debug_screen_state():
 """Debug what screen state is detected"""
 print("Screen State Debug")
 print("=" * 25)
 
 # Set up logging
 logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
 
 # Create module
 module = MapTradeModule()
 
 print("1. Testing screen state detection...")
 try:
 screen_state = module._detect_screen_state()
 print(f" Screen state: {screen_state}")
 except Exception as e:
 print(f" Error: {e}")
 
 print("\n2. Testing red thumb detection...")
 try:
 has_red_thumb = module._has_red_thumb()
 print(f" Has red thumb: {has_red_thumb}")
 except Exception as e:
 print(f" Error: {e}")
 
 print("\n3. Testing new exchange button detection...")
 try:
 has_new_exchange = module._has_new_exchange_button()
 print(f" Has new exchange: {has_new_exchange}")
 except Exception as e:
 print(f" Error: {e}")
 
 print(f"\n{'='*40}")
 print("EXPECTED BEHAVIOR:")
 print(f"{'='*40}")
 
 if screen_state == "trade_in_progress":
 print(" Cancel Exchange visible → Should go to Step 4")
 print(" Should NOT check red thumb")
 print(" Should NOT do intelligent trade")
 else:
 print(f"Screen state: {screen_state}")
 print("Will check red thumb and other options")

if __name__ == "__main__":
 try:
 debug_screen_state()
 except KeyboardInterrupt:
 print("\nInterrupted")
 except Exception as e:
 print(f"Error: {e}")
 import traceback
 traceback.print_exc()
