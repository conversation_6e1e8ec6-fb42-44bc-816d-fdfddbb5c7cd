#!/usr/bin/env python3
"""
Simple Events Button Template Capture Tool
"""

import pyautogui
import cv2
import numpy as np
import os
from tkinter import messagebox
import tkinter as tk

def capture_events_button():
    """Capture the events button template"""
    print("=" * 60)
    print("EVENTS BUTTON TEMPLATE CAPTURE")
    print("=" * 60)
    print()
    print("INSTRUCTIONS:")
    print("1. Make sure Last War game is open and visible")
    print("2. Go to the main game screen")
    print("3. Make sure the Events button is visible at the bottom")
    print("4. Press SPACE when ready to capture")
    print("5. Click and drag to select the Events button area")
    print()
    
    # Wait for user to be ready
    input("Press ENTER when you're ready to start capture...")
    
    try:
        # Create a simple GUI for capture
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Show instructions
        messagebox.showinfo("Capture Instructions", 
                          "1. Position your mouse at the TOP-LEFT corner of the Events button\n"
                          "2. Press SPACE to start capture\n"
                          "3. Drag to the BOTTOM-RIGHT corner of the Events button\n"
                          "4. Release mouse to complete capture")
        
        print("Position mouse at TOP-LEFT of Events button and press SPACE...")
        
        # Wait for space key
        while True:
            if pyautogui.keyDown('space'):
                break
        
        # Get starting position
        start_x, start_y = pyautogui.position()
        print(f"Start position: ({start_x}, {start_y})")
        
        print("Now drag to BOTTOM-RIGHT of Events button...")
        
        # Wait for mouse drag
        while True:
            current_x, current_y = pyautogui.position()
            if abs(current_x - start_x) > 10 or abs(current_y - start_y) > 10:
                break
        
        # Get end position
        end_x, end_y = pyautogui.position()
        print(f"End position: ({end_x}, {end_y})")
        
        # Calculate region
        left = min(start_x, end_x)
        top = min(start_y, end_y)
        width = abs(end_x - start_x)
        height = abs(end_y - start_y)
        
        print(f"Capture region: ({left}, {top}, {width}, {height})")
        
        if width < 10 or height < 10:
            print("❌ Region too small! Please try again with a larger area.")
            return False
        
        # Take screenshot of the region
        screenshot = pyautogui.screenshot(region=(left, top, width, height))
        
        # Convert to OpenCV format
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save the template
        template_path = os.path.join("templates", "events_button.png")
        cv2.imwrite(template_path, screenshot_cv)
        
        print(f"✅ Events button template saved to: {template_path}")
        print(f"   Template size: {width}x{height} pixels")
        
        # Show confirmation
        messagebox.showinfo("Capture Complete", 
                          f"Events button template captured successfully!\n"
                          f"Size: {width}x{height} pixels\n"
                          f"Saved to: {template_path}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error during capture: {str(e)}")
        return False

def test_new_template():
    """Test the newly captured template"""
    print("\n" + "=" * 60)
    print("TESTING NEW EVENTS BUTTON TEMPLATE")
    print("=" * 60)
    
    try:
        from screen_scanner import ScreenScanner
        
        # Create scanner and test
        scanner = ScreenScanner()
        
        if 'events_button' not in scanner.templates:
            print("❌ events_button template not found after capture")
            return False
        
        template = scanner.templates['events_button']
        if template is None:
            print("❌ events_button template is None")
            return False
        
        print(f"✅ Template loaded: {template.shape}")
        
        # Test detection
        templates_to_check = ['events_button']
        screen_data = scanner.scan_screen_cache_optimized(templates_to_check)
        
        if 'templates_detected' in screen_data:
            events_detected = screen_data['templates_detected'].get('events_button', False)
            
            if events_detected:
                print("✅ SUCCESS: New events button template is working!")
                print("   The efficient debug system should now work correctly.")
                return True
            else:
                print("⚠️  New template captured but not detected on current screen")
                print("   Make sure you're on the main game screen with Events button visible")
                return False
        else:
            print("❌ No detection data returned")
            return False
            
    except Exception as e:
        print(f"❌ Error testing template: {str(e)}")
        return False

def main():
    """Main capture process"""
    print("Last War Automation - Events Button Template Capture")
    print()
    
    # Step 1: Capture the template
    success = capture_events_button()
    
    if not success:
        print("\n❌ Template capture failed. Please try again.")
        return
    
    # Step 2: Test the new template
    print("\nTesting the new template...")
    test_success = test_new_template()
    
    print("\n" + "=" * 60)
    print("CAPTURE RESULTS")
    print("=" * 60)
    
    if success and test_success:
        print("🎯 SUCCESS: Events button template captured and working!")
        print("   The efficient debug system is now ready to use.")
    elif success:
        print("⚠️  Template captured but needs adjustment:")
        print("   1. Make sure game is on main screen")
        print("   2. Ensure Events button is clearly visible")
        print("   3. Try capturing again if needed")
    else:
        print("❌ Capture failed. Please try again.")
    
    print("\n📋 NEXT STEPS:")
    print("1. Run the automation system")
    print("2. The debug system will now check for Events button after each module")
    print("3. If Events button is missing, it will automatically press ESC to recover")

if __name__ == "__main__":
    main()
