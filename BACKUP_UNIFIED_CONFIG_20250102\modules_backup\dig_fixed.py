"""
Dig Module for Last War automation
Handles excavation tasks with proper timing and coordination
"""

import time
import pya<PERSON><PERSON><PERSON>
from typing import Dict, Any
from enhanced_base_module import EnhancedBaseModule


class DigModule(EnhancedBaseModule):
    """
    Dig module for excavation automation
    
    Process:
    1. Detects dig icon in chat
    2. Clicks the dig icon
    3. Selects squad
    4. Sends the squad
    5. Waits for timer to hit 1
    6. Spam clicks icon to get loot
    7. Closes the window
    """

    def __init__(self):
        super().__init__(name="dig", priority=-1, enabled=True)  # Highest priority
        
        # Dig-specific configuration
        self.dig_icon_template = "dig_icon"
        self.chat_link_coordinate = "chat_link"
        self.dig_icon_coordinate = "dig_icon_click"
        self.squad_select_coordinate = "squad_select"
        self.send_squad_coordinate = "send_squad"
        self.loot_icon_coordinate = "loot_icon"
        
        # Timing settings
        self.timer_check_interval = 1.0  # Check timer every second
        self.loot_spam_duration = 5.0    # Spam loot for 5 seconds
        self.loot_click_interval = 0.1   # Click every 100ms during spam
        
        # State tracking
        self.dig_in_progress = False
        self.dig_start_time = 0
        self.expected_dig_duration = 300  # 5 minutes default

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Check if dig icon is available in chat"""
        try:
            templates_found = screen_data.get('templates_found', {})
            
            # Check if dig icon template is detected
            if self.dig_icon_template in templates_found:
                matches = templates_found[self.dig_icon_template]
                if matches and len(matches) > 0:
                    self.logger.info("Dig icon detected in chat - ready to excavate")
                    return True
            
            # If dig is in progress, check if we need to collect loot
            if self.dig_in_progress:
                elapsed_time = time.time() - self.dig_start_time
                if elapsed_time >= self.expected_dig_duration:
                    self.logger.info("Dig timer completed - ready to collect loot")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking dig availability: {str(e)}")
            return False

    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Bridge method for base module compatibility"""
        return self.custom_can_execute(screen_data)

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Execute dig sequence"""
        try:
            # If dig is in progress, try to collect loot
            if self.dig_in_progress:
                return self._collect_loot()
            
            # Otherwise, start new dig
            return self._start_dig_sequence()
            
        except Exception as e:
            self.logger.error(f"Error in dig execution: {str(e)}")
            return False

    def _start_dig_sequence(self) -> bool:
        """Start the dig sequence"""
        try:
            self.logger.info("Starting dig sequence")
            
            # Step 1: Click dig icon in chat
            if not self._click_dig_icon():
                return False
            
            time.sleep(2.0)  # Wait for dig window to open
            
            # Step 2: Select squad
            if not self._select_squad():
                return False
            
            time.sleep(1.0)
            
            # Step 3: Send squad
            if not self._send_squad():
                return False
            
            # Mark dig as in progress
            self.dig_in_progress = True
            self.dig_start_time = time.time()
            
            self.logger.info("Dig sequence started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting dig sequence: {str(e)}")
            return False

    def _click_dig_icon(self) -> bool:
        """Click the dig icon in chat"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.dig_icon_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Clicking dig icon at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Dig icon coordinate '{self.dig_icon_coordinate}' not found")
            return False

    def _select_squad(self) -> bool:
        """Select squad for dig"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.squad_select_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Selecting squad at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Squad select coordinate '{self.squad_select_coordinate}' not found")
            return False

    def _send_squad(self) -> bool:
        """Send squad to dig"""
        coordinate = self.config_manager.get_coordinate_by_name(self.name, self.send_squad_coordinate)
        if coordinate:
            x, y = coordinate
            self.logger.info(f"Sending squad at ({x}, {y})")
            pyautogui.click(x, y)
            return True
        else:
            self.logger.warning(f"Send squad coordinate '{self.send_squad_coordinate}' not found")
            return False

    def _collect_loot(self) -> bool:
        """Collect loot when dig is complete"""
        try:
            self.logger.info("Collecting dig loot")
            
            coordinate = self.config_manager.get_coordinate_by_name(self.name, self.loot_icon_coordinate)
            if not coordinate:
                self.logger.warning(f"Loot icon coordinate '{self.loot_icon_coordinate}' not found")
                return False
            
            x, y = coordinate
            
            # Spam click the loot icon
            start_time = time.time()
            clicks = 0
            
            while time.time() - start_time < self.loot_spam_duration:
                pyautogui.click(x, y)
                clicks += 1
                time.sleep(self.loot_click_interval)
            
            self.logger.info(f"Loot collection completed - {clicks} clicks")
            
            # Reset dig state
            self.dig_in_progress = False
            self.dig_start_time = 0
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error collecting loot: {str(e)}")
            return False

    def get_status(self) -> str:
        """Get current dig status"""
        if self.dig_in_progress:
            elapsed = time.time() - self.dig_start_time
            remaining = max(0, self.expected_dig_duration - elapsed)
            minutes = int(remaining // 60)
            seconds = int(remaining % 60)
            return f"Digging - {minutes}m {seconds}s remaining"
        else:
            return "Ready"

    def get_cooldown(self) -> float:
        """Get cooldown time"""
        if self.dig_in_progress:
            elapsed = time.time() - self.dig_start_time
            remaining = max(0, self.expected_dig_duration - elapsed)
            return remaining
        return 0.0

    def get_required_templates(self) -> list:
        """Return list of templates required by this module"""
        return [self.dig_icon_template]
