#!/usr/bin/env python3
"""
Debug and fix all map piece coordinates
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def capture_and_test(x, y, size=25):
    """Capture and test OCR at coordinates"""
    try:
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save debug image
        cv2.imwrite(f"debug_{x}_{y}.png", image)
        
        # Try different OCR configs
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789',
            '--psm 6 -c tessedit_char_whitelist=0123456789',
            '--psm 10 -c tessedit_char_whitelist=0123456789'
        ]
        
        for i, config in enumerate(configs):
            text = pytesseract.image_to_string(gray, config=config).strip()
            if text.isdigit():
                print(f"  ✅ Config {i+1}: '{text}' (saved debug_{x}_{y}.png)")
                return int(text)
            else:
                print(f"  ❌ Config {i+1}: '{text}'")
        
        return None
        
    except Exception as e:
        print(f"  Error: {e}")
        return None

def test_coordinate_grid():
    """Test a grid of coordinates around each map piece"""
    
    # Current coordinates that mostly fail
    base_coords = {
        'M1': (532, 436),
        'M2': (613, 436),
        'M3': (696, 436), 
        'M4': (775, 436),
        'M5': (577, 513),
        'M6': (654, 521),  # This one works
        'M7': (735, 519),
    }
    
    print("Testing coordinate grids for each map piece")
    print("=" * 50)
    
    working_coords = {}
    
    for map_name, (base_x, base_y) in base_coords.items():
        print(f"\n{map_name} - Testing around ({base_x}, {base_y})")
        
        found = False
        
        # Test grid around base coordinate
        for dx in range(-20, 21, 5):  # -20 to +20 in steps of 5
            for dy in range(-20, 21, 5):
                test_x = base_x + dx
                test_y = base_y + dy
                
                print(f"  Testing ({test_x}, {test_y})...")
                result = capture_and_test(test_x, test_y)
                
                if result is not None:
                    print(f"    🎯 FOUND: {result} at ({test_x}, {test_y})")
                    working_coords[map_name] = (test_x, test_y)
                    found = True
                    break
            
            if found:
                break
        
        if not found:
            print(f"    ❌ No working coordinate found for {map_name}")
    
    print(f"\n🎯 WORKING COORDINATES:")
    print("=" * 30)
    for map_name, coords in working_coords.items():
        print(f"'{map_name}': {coords},")
    
    return working_coords

def manual_capture_all():
    """Manually capture coordinates for all pieces"""
    print("Manual Coordinate Capture for All Pieces")
    print("=" * 40)
    print("Position mouse over each number and press SPACE")
    print("Press ESC when done")
    
    pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
    results = {}
    current_piece = 0
    
    print(f"\nPosition mouse over {pieces[current_piece]} and press SPACE")
    
    while current_piece < len(pieces):
        time.sleep(0.1)
        
        if pyautogui.keyIsPressed('space'):
            x, y = pyautogui.position()
            map_name = pieces[current_piece]
            
            print(f"Testing {map_name} at ({x}, {y})...")
            result = capture_and_test(x, y)
            
            if result is not None:
                print(f"✅ {map_name}: {result} at ({x}, {y})")
                results[map_name] = (x, y)
                current_piece += 1
                
                if current_piece < len(pieces):
                    print(f"\nNext: Position mouse over {pieces[current_piece]} and press SPACE")
                else:
                    print("\n✅ All pieces captured!")
            else:
                print("❌ Could not read number, try again")
            
            time.sleep(0.5)  # Prevent multiple captures
            
        elif pyautogui.keyIsPressed('esc'):
            break
    
    print(f"\n🎯 CAPTURED COORDINATES:")
    print("=" * 25)
    for map_name, coords in results.items():
        print(f"'{map_name}': {coords},")
    
    return results

if __name__ == "__main__":
    print("Make sure you're on the map trade screen with all 7 pieces visible")
    print()
    print("Choose method:")
    print("1. Auto-test grid around current coordinates")
    print("2. Manual capture (position mouse and press SPACE)")
    
    choice = input("Enter choice (1 or 2): ")
    
    if choice == "1":
        working_coords = test_coordinate_grid()
    else:
        working_coords = manual_capture_all()
    
    if working_coords:
        print(f"\n📋 Copy these coordinates to working_map_trade.py:")
        print("MAP_COORDINATES = {")
        for map_name, coords in working_coords.items():
            print(f"    '{map_name}': {coords},")
        print("}")
