"""
Help Click Module for Last War automation
High-performance alliance help clicking with configurable coordinates
"""
import time
import pyautogui
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import logging
import os

from enhanced_base_module import EnhancedBaseModule


class HelpClickModule(EnhancedBaseModule):
    """High-performance module for clicking alliance help requests"""

    def __init__(self):
        super().__init__(name="help_click", priority=0, enabled=True)  # HIGHEST priority - executes every scan!

        # High-performance settings
        self.cooldown_time = 0.0   # NO cooldown - execute every scan cycle!
        self.click_delay = 0.01   # MAXIMUM speed clicking (0.01s between clicks)
        self.max_helps_per_session = 100  # More helps per session

        # Load help click sequence from configuration
        self.help_click_sequence = self._load_click_sequence_from_config()

        # Base template for smart return-to-base detection
        self.base_template = "Base"  # Template name for Base icon
        self.base_click_delay = 1.0  # Faster base navigation

        # Logger for this module
        self.logger = logging.getLogger("LastWar.help_click")
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Check if help templates are detected and determine which action to take
        Priority: Help_Template (1_HelpButton) > Help_chat (2_HelpChat)
        """
        templates_found = screen_data.get('templates_found', {})

        # Check for Help_Template first (highest priority)
        if 'Help_Template' in templates_found and templates_found['Help_Template']:
            self.current_activity = "Help_Template detected - will click 1_HelpButton"
            self.waiting_for = "1_HelpButton click"
            return True

        # Check for Help_chat only if Help_Template is not found
        if 'Help_chat' in templates_found and templates_found['Help_chat']:
            self.current_activity = "Help_chat detected - will click 2_HelpChat"
            self.waiting_for = "2_HelpChat click"
            return True

        # No help templates found
        self.current_activity = "Waiting for help templates"
        self.waiting_for = "Help_Template or Help_chat detection"
        return False

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to can_execute"""
        return self.can_execute(screen_data)
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Execute template-based help clicking with priority logic
        """
        try:
            templates_found = screen_data.get('templates_found', {})

            # Priority 1: Help_Template -> click 1_HelpButton
            if 'Help_Template' in templates_found and templates_found['Help_Template']:
                self.logger.info("🆘 Help_Template detected - clicking 1_HelpButton")
                return self._click_coordinate("1_HelpButton")

            # Priority 2: Help_chat -> click 2_HelpChat (only if Help_Template not found)
            elif 'Help_chat' in templates_found and templates_found['Help_chat']:
                self.logger.info("💬 Help_chat detected - clicking 2_HelpChat")
                return self._click_coordinate("2_HelpChat")

            else:
                self.logger.warning("⚠️ No help templates detected")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error in template-based help clicking: {str(e)}")
            return False

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to execute"""
        return self.execute(screen_data)

    def _click_coordinate(self, coord_name: str) -> bool:
        """Click a specific coordinate by name"""
        try:
            coordinate = self.config_manager.get_coordinate_by_name(self.name, coord_name)
            if coordinate:
                x, y = coordinate
                self.logger.info(f"🖱️ Clicking {coord_name} at ({x}, {y})")
                pyautogui.click(x, y)
                time.sleep(0.5)  # Brief delay after click
                return True
            else:
                self.logger.warning(f"⚠️ Coordinate '{coord_name}' not found in configuration")
                return False
        except Exception as e:
            self.logger.error(f"❌ Error clicking {coord_name}: {str(e)}")
            return False
    
    def get_cooldown(self) -> float:
        """Get cooldown time for help clicking (10 seconds for fast response)"""
        return self.cooldown_time

    def _load_click_sequence_from_config(self):
        """
        Load the help click sequence from module configuration
        Returns list of (x, y, delay, repeat, description) tuples
        """
        try:
            # Get click coordinates from module config
            if hasattr(self, 'module_config') and self.module_config:
                click_coords = self.module_config.get('click_coordinates', [])

                if click_coords:
                    sequence = []

                    # Sort by numerical order (1_, 2_, 3_, etc.)
                    def get_step_number(coord):
                        name = coord.get('name', '')
                        try:
                            if '_' in name:
                                return int(name.split('_')[0])
                            else:
                                return 999
                        except:
                            return 999

                    sorted_coords = sorted(click_coords, key=get_step_number)

                    for coord in sorted_coords:
                        # Check if this step is enabled
                        enabled = coord.get('enabled', True)
                        if not enabled:
                            self.logger.info(f"⏭️ Skipping disabled step: {coord.get('name', 'Unknown')}")
                            continue

                        x = coord.get('x', 0)
                        y = coord.get('y', 0)
                        name = coord.get('name', 'Unknown')
                        description = coord.get('description', '')

                        # Get custom delay for this step (fallback to ultra-fast default)
                        custom_delay = coord.get('delay', self.click_delay)

                        # Get repeat count for this step (fallback to 1)
                        repeat_count = coord.get('repeat', 1)
                        repeat_count = max(1, int(repeat_count))

                        # Create description with name, description, and repeat info
                        if repeat_count > 1:
                            full_description = f"{name}: {description} (×{repeat_count})".strip(': ')
                        else:
                            full_description = f"{name}: {description}".strip(': ')

                        # Add to sequence with custom delay and repeat count
                        sequence.append((x, y, custom_delay, repeat_count, full_description))

                    self.logger.info(f"📋 Loaded {len(sequence)} help click steps from configuration")

                    # Log each step for verification
                    for i, step_data in enumerate(sequence):
                        x, y, delay, repeat_count, desc = step_data
                        self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

                    return sequence
                else:
                    self.logger.warning("⚠️ No click coordinates found in help click configuration")
            else:
                self.logger.warning("⚠️ No module configuration available for help click")

        except Exception as e:
            self.logger.error(f"❌ Error loading help click sequence from config: {str(e)}")

        # Return default fast sequence if config loading fails
        self.logger.info("📋 Using default high-performance help click sequence")
        return [
            (100, 100, 0.05, 1, "Default: Open alliance"),
            (200, 200, 0.05, 1, "Default: Click help section"),
            (300, 300, 0.05, 10, "Default: Click help buttons rapidly"),
            (400, 400, 0.05, 1, "Default: Close interface"),
        ]

    def _execute_help_sequence(self) -> bool:
        """
        Execute the configured help click sequence with ultra-fast performance
        """
        try:
            self.logger.info("🚀 Starting help click sequence")

            # Disable PyAutoGUI safety pause for maximum speed
            original_pause = pyautogui.PAUSE
            pyautogui.PAUSE = 0  # Remove all delays

            # SKIP BASE DETECTION - too slow! Use coordinates only
            self.logger.info("🚀 Skipping base detection for maximum speed")

            # Execute each click in the numbered sequence
            total_steps = len(self.help_click_sequence)
            if total_steps == 0:
                self.logger.warning("⚠️ No help click steps configured")
                return False

            for i, step_data in enumerate(self.help_click_sequence):
                # Handle both old format (4 items) and new format (5 items)
                if len(step_data) == 4:
                    x, y, delay, description = step_data
                    repeat_count = 1
                else:
                    x, y, delay, repeat_count, description = step_data

                self.logger.info(f"📍 {description} ({i+1}/{total_steps})")

                # Perform the click(s) with repeat - ULTRA FAST
                for repeat_num in range(repeat_count):
                    if repeat_count > 1:
                        self.logger.info(f"🖱️ Click {repeat_num+1}/{repeat_count} at ({x}, {y})")
                    else:
                        self.logger.info(f"🖱️ Clicked at ({x}, {y})")

                    pyautogui.click(x, y)

                    # MAXIMUM speed between repeated clicks
                    if repeat_count > 1 and repeat_num < repeat_count - 1:
                        time.sleep(0.005)  # 0.005s between repeats = 200 clicks/second

                # Wait for the specified delay (can be very small)
                if delay > 0:
                    self.logger.info(f"⏳ Waiting {delay} seconds...")
                    time.sleep(delay)

            self.logger.info("✅ Help click sequence completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in help click sequence: {str(e)}")
            return False
        finally:
            # Restore PyAutoGUI original pause
            pyautogui.PAUSE = original_pause

    def _smart_return_to_base(self) -> bool:
        """
        Smart base detection: Click Base icon if visible, skip if already at base
        Returns True if ready to proceed, False if failed
        """
        try:
            self.logger.info("🏠 STEP 0: Checking if we need to return to base...")

            # Use simple template matching for base detection
            base_detected = self._detect_base_template()

            if base_detected:
                # Base icon found - we need to click it to return to base
                base_x, base_y = base_detected

                self.logger.info(f"🏠 Base icon detected - clicking to return to base")
                self.logger.info(f"🖱️ Clicking Base at ({base_x}, {base_y})")

                # Click the Base icon
                pyautogui.click(base_x, base_y)

                # Wait for base navigation
                self.logger.info(f"⏳ Waiting {self.base_click_delay} seconds for base navigation...")
                time.sleep(self.base_click_delay)

                self.logger.info("✅ Returned to base successfully")
            else:
                # Base icon not found - we're already at base
                self.logger.info("✅ Already at base (Base icon not visible) - proceeding with help clicking")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error in smart base detection: {str(e)}")
            return False

    def _detect_base_template(self):
        """
        Detect base template using simple template matching
        Returns (x, y) coordinates if found, None if not found
        """
        try:
            # Check if base template file exists
            template_path = f"templates/{self.base_template}.png"
            if not os.path.exists(template_path):
                self.logger.warning(f"⚠️ Base template not found: {template_path}")
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Load template
            template = cv2.imread(template_path)
            if template is None:
                self.logger.warning(f"⚠️ Could not load base template: {template_path}")
                return None

            # Perform template matching
            result = cv2.matchTemplate(screenshot_bgr, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # Check if match is good enough (threshold)
            threshold = 0.8
            if max_val >= threshold:
                # Calculate center of template
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2

                self.logger.debug(f"📍 Base template found at ({center_x}, {center_y}) with confidence {max_val:.2f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"🔍 Base template not found (best match: {max_val:.2f})")
                return None

        except Exception as e:
            self.logger.error(f"Error in base template detection: {str(e)}")
            return None

    def reload_configuration(self):
        """
        Reload the help click configuration from file (live reload)
        """
        try:
            self.logger.info("🔄 Reloading help click configuration...")

            # Reload the click sequence from config
            old_count = len(self.help_click_sequence)
            self.help_click_sequence = self._load_click_sequence_from_config()
            new_count = len(self.help_click_sequence)

            self.logger.info(f"✅ Configuration reloaded: {old_count} → {new_count} steps")

            # Log the updated sequence
            for i, step_data in enumerate(self.help_click_sequence):
                if len(step_data) == 4:
                    x, y, delay, desc = step_data
                    self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")
                else:
                    x, y, delay, repeat_count, desc = step_data
                    self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to reload configuration: {str(e)}")
            return False
            return False
        
        try:
            # Look for red notification dots or numbers
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            # Define range for red color (notification indicators)
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            
            # Create masks for red color
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = mask1 + mask2
            
            # Find contours in the red mask
            contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Check for small circular contours (notification dots)
            for contour in contours:
                area = cv2.contourArea(contour)
                if 10 < area < 500:  # Size range for notification dots
                    # Check if contour is roughly circular
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        if circularity > 0.5:  # Reasonably circular
                            self.logger.debug("Found help notification indicator")
                            return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting help notifications: {str(e)}")
            return False
    
    def _navigate_to_help_section(self, screen_data: Dict[str, Any]) -> bool:
        """Navigate to the alliance help section"""
        templates_found = screen_data.get('templates_found', {})
        
        # Try to find and click alliance/help button
        navigation_templates = ["alliance_help", "help_button", "help_icon"]
        for template in navigation_templates:
            if template in templates_found:
                matches = templates_found[template]
                if matches:
                    x, y, w, h = matches[0]
                    click_x = x + w // 2
                    click_y = y + h // 2
                    
                    self.logger.debug(f"Clicking help navigation at ({click_x}, {click_y})")
                    pyautogui.click(click_x, click_y)
                    time.sleep(2)  # Wait for interface to load
                    return True
        
        # Try common alliance button locations
        common_alliance_positions = [
            (50, 200),   # Left side alliance button
            (750, 100),  # Top right alliance area
            (400, 50),   # Top center
        ]
        
        for x, y in common_alliance_positions:
            # In real implementation, you'd verify these positions
            # For now, we'll simulate successful navigation
            pass
        
        return True  # Assume navigation successful for demo
    
    def _click_all_help_requests(self) -> int:
        """Click all available help requests"""
        helps_clicked = 0
        
        try:
            # Look for "Help All" button first (most efficient)
            if self._click_help_all_button():
                self.logger.info("Clicked 'Help All' button")
                return 1  # Count as one successful operation
            
            # If no "Help All" button, click individual help requests
            helps_clicked = self._click_individual_help_requests()
            
            return helps_clicked
            
        except Exception as e:
            self.logger.error(f"Error clicking help requests: {str(e)}")
            return helps_clicked
    
    def _click_help_all_button(self) -> bool:
        """Try to click the 'Help All' button if available"""
        try:
            # Take a fresh screenshot
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Look for "Help All" text using OCR
            # This is a simplified implementation
            # In practice, you'd use more sophisticated template matching
            
            # Common positions for "Help All" button
            help_all_positions = [
                (400, 500),  # Bottom center
                (600, 400),  # Right side
                (200, 400),  # Left side
            ]
            
            for x, y in help_all_positions:
                # In real implementation, verify button exists before clicking
                # For now, simulate clicking
                pass
            
            return False  # No Help All button found
            
        except Exception as e:
            self.logger.error(f"Error clicking Help All button: {str(e)}")
            return False
    
    def _click_individual_help_requests(self) -> int:
        """Click individual help request buttons"""
        helps_clicked = 0
        
        try:
            for attempt in range(self.max_helps_per_session):
                # Take fresh screenshot
                screenshot = pyautogui.screenshot()
                screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                
                # Find help buttons using color detection
                help_positions = self._find_help_buttons(screenshot_cv)
                
                if not help_positions:
                    self.logger.debug("No more help buttons found")
                    break
                
                # Click the first help button found
                x, y = help_positions[0]
                pyautogui.click(x, y)
                helps_clicked += 1
                
                self.logger.debug(f"Clicked help button at ({x}, {y})")
                time.sleep(self.click_delay)
                
                # Check if we've reached the maximum
                if helps_clicked >= self.max_helps_per_session:
                    self.logger.info(f"Reached maximum helps per session: {self.max_helps_per_session}")
                    break
            
            return helps_clicked
            
        except Exception as e:
            self.logger.error(f"Error clicking individual help requests: {str(e)}")
            return helps_clicked
    
    def _find_help_buttons(self, screenshot: np.ndarray) -> List[Tuple[int, int]]:
        """Find help button positions using color detection"""
        help_positions = []
        
        try:
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            # Define color ranges for typical help buttons (green/orange)
            color_ranges = [
                # Green help buttons
                (np.array([40, 50, 50]), np.array([80, 255, 255])),
                # Orange help buttons  
                (np.array([10, 50, 50]), np.array([25, 255, 255])),
            ]
            
            for lower, upper in color_ranges:
                mask = cv2.inRange(hsv, lower, upper)
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 100 < area < 5000:  # Size range for help buttons
                        # Get bounding rectangle
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # Check aspect ratio (help buttons are usually wider than tall)
                        aspect_ratio = w / h
                        if 1.5 < aspect_ratio < 4.0:
                            center_x = x + w // 2
                            center_y = y + h // 2
                            help_positions.append((center_x, center_y))
            
            # Remove duplicate positions (too close to each other)
            help_positions = self._remove_duplicate_positions(help_positions)
            
            return help_positions
            
        except Exception as e:
            self.logger.error(f"Error finding help buttons: {str(e)}")
            return []
    
    def _remove_duplicate_positions(self, positions: List[Tuple[int, int]], 
                                  min_distance: int = 50) -> List[Tuple[int, int]]:
        """Remove positions that are too close to each other"""
        if len(positions) <= 1:
            return positions
        
        filtered_positions = []
        
        for pos in positions:
            x1, y1 = pos
            is_duplicate = False
            
            for existing_pos in filtered_positions:
                x2, y2 = existing_pos
                distance = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5
                
                if distance < min_distance:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered_positions.append(pos)
        
        return filtered_positions
    
    def _close_help_interface(self):
        """Close the help interface"""
        try:
            # Try common close methods
            pyautogui.press('esc')
            time.sleep(0.5)
            
            # Or click back button if ESC doesn't work
            # Common back button positions
            back_positions = [
                (50, 50),   # Top-left
                (100, 100), # Top-left area
            ]
            
            # In real implementation, verify button exists before clicking
            
        except Exception as e:
            self.logger.error(f"Error closing help interface: {str(e)}")

    def get_required_templates(self) -> List[str]:
        """
        Return list of templates required by this module
        help_click uses coordinates only - no templates needed!
        """
        return []  # No templates needed - coordinates only!
