#!/usr/bin/env python3
"""
Quick debug - capture what's at each map piece coordinate
"""
import pyautogui
import cv2
import numpy as np
import pytesseract

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Your current coordinates
coordinates = {
    'M1': (1079, 833),
    'M2': (1245, 835), 
    'M3': (1412, 835),
    'M4': (1577, 833),
    'M5': (1163, 994),
    'M6': (1329, 994),
    'M7': (1495, 994)
}

def debug_coordinate(name, x, y):
    """Debug what's at a coordinate"""
    print(f"\n=== {name} at ({x}, {y}) ===")
    
    # Capture 25x25 region
    capture_size = 25
    capture_x = x - capture_size // 2
    capture_y = y - capture_size // 2
    
    try:
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, capture_size, capture_size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save debug image
        filename = f"debug_{name}_{x}_{y}.png"
        cv2.imwrite(filename, image)
        print(f"Saved: {filename}")
        
        # Try OCR
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        if text.isdigit():
            print(f"OCR Result: {text}")
        else:
            print(f"OCR Failed: '{text}'")
            
        # Try pixel analysis
        _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
        dark_pixels = cv2.countNonZero(binary)
        total_pixels = image.shape[0] * image.shape[1]
        density = dark_pixels / total_pixels if total_pixels > 0 else 0
        
        if density < 0.02:
            pixel_result = 1
        elif density < 0.04:
            pixel_result = 2
        elif density < 0.06:
            pixel_result = 3
        elif density < 0.08:
            pixel_result = 4
        elif density < 0.10:
            pixel_result = 5
        elif density < 0.12:
            pixel_result = 6
        else:
            pixel_result = 7
            
        print(f"Pixel: density={density:.4f} -> {pixel_result}")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    print("🔍 Quick Debug - Map Piece Coordinates")
    print("=" * 50)
    
    for name, (x, y) in coordinates.items():
        debug_coordinate(name, x, y)
    
    print(f"\n📁 Debug images saved")
    print("Check the images to see what's being captured")

if __name__ == "__main__":
    main()
