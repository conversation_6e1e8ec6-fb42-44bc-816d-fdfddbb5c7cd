#!/usr/bin/env python3
"""
Test Events Button Detection
"""

import sys
import os
import time

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from screen_scanner import ScreenScanner
import logging

def test_events_button_detection():
    """Test if events button can be detected"""
    print("=" * 60)
    print("EVENTS BUTTON DETECTION TEST")
    print("=" * 60)
    
    # Set up logging
    logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
    
    try:
        # Create screen scanner
        print("1. Creating screen scanner...")
        scanner = ScreenScanner()
        print(f"   Templates loaded: {len(scanner.templates)}")
        
        # Check if events_button template exists
        print("\n2. Checking events_button template...")
        if 'events_button' in scanner.templates:
            template = scanner.templates['events_button']
            if template is not None:
                print(f"   ✅ events_button template found: {template.shape}")
            else:
                print("   ❌ events_button template is None")
                return False
        else:
            print("   ❌ events_button template not found in templates")
            return False
        
        # Test single template detection
        print("\n3. Testing events_button detection...")
        templates_to_check = ['events_button']
        screen_data = scanner.scan_screen_cache_optimized(templates_to_check)
        
        print(f"   Screen data keys: {list(screen_data.keys())}")
        
        if 'templates_detected' in screen_data:
            templates_detected = screen_data['templates_detected']
            print(f"   Templates detected: {templates_detected}")
            
            events_detected = templates_detected.get('events_button', False)
            print(f"   Events button detected: {events_detected}")
            
            if events_detected:
                print("   ✅ SUCCESS: Events button is visible on screen!")
                return True
            else:
                print("   ⚠️  Events button not detected on current screen")
                print("   This could mean:")
                print("   - Game is not open")
                print("   - Events button is not visible")
                print("   - Template needs recapturing")
                return False
        else:
            print("   ❌ No templates_detected in screen_data")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during detection test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_quick_events_check():
    """Test the quick events check method like the main controller uses"""
    print("\n" + "=" * 60)
    print("QUICK EVENTS CHECK TEST (Main Controller Method)")
    print("=" * 60)
    
    try:
        # Simulate the main controller's quick check method
        scanner = ScreenScanner()
        
        print("1. Running quick events button check...")
        templates_to_check = ['events_button']
        screen_data = scanner.scan_screen_cache_optimized(templates_to_check)
        
        if 'templates_detected' in screen_data:
            events_detected = screen_data['templates_detected'].get('events_button', False)
            
            if events_detected:
                print("   ✅ QUICK CHECK SUCCESS: Events button detected!")
                print("   → UI state would be considered OK")
                return True
            else:
                print("   ⚠️  QUICK CHECK: Events button not detected")
                print("   → ESC recovery would be triggered")
                return False
        else:
            print("   ❌ QUICK CHECK FAILED: No template detection data")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during quick check: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Last War Automation - Events Button Detection Test")
    print("Make sure the game is open and visible on screen!")
    print()
    
    # Test 1: Basic events button detection
    success1 = test_events_button_detection()
    
    # Test 2: Quick events check (main controller method)
    success2 = test_quick_events_check()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Basic Detection Test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Quick Check Test:     {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎯 RESULT: Events button detection is working correctly!")
        print("The efficient debug system should work properly.")
    else:
        print("\n⚠️  RESULT: Events button detection has issues.")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Make sure Last War game is open and visible")
        print("2. Ensure you're on the main game screen")
        print("3. Check if Events button is visible at bottom of screen")
        print("4. If still failing, the events_button.png template may need recapturing")
        print("\n📋 TO RECAPTURE EVENTS BUTTON TEMPLATE:")
        print("1. Open the game to main screen")
        print("2. Use the calibration tool to capture the Events button")
        print("3. Save as 'events_button.png' in templates folder")

if __name__ == "__main__":
    main()
