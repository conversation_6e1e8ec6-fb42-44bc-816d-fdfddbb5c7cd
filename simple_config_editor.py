"""
Simple, reliable config editor for Last War automation
Focuses on the essential editing functions that work
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
from module_config_manager import ModuleConfigManager

class SimpleConfigEditor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Simple Config Editor - Last War")
        self.root.geometry("1000x700")
        
        self.config_manager = ModuleConfigManager()
        self.current_module = None
        
        self.create_widgets()
        self.load_modules()
    
    def create_widgets(self):
        """Create the GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Module selection
        module_frame = ttk.LabelFrame(main_frame, text="Select Module", padding="5")
        module_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.module_var = tk.StringVar()
        self.module_combo = ttk.Combobox(module_frame, textvariable=self.module_var, state="readonly")
        self.module_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.module_combo.bind('<<ComboboxSelected>>', self.on_module_selected)
        
        ttk.Button(module_frame, text="Refresh", command=self.load_modules).pack(side=tk.LEFT)
        
        # Notebook for different config sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Click Coordinates tab
        self.coords_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.coords_frame, text="Click Coordinates")
        self.create_coordinates_tab()
        
        # Templates tab
        self.templates_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.templates_frame, text="Templates")
        self.create_templates_tab()
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def create_coordinates_tab(self):
        """Create coordinates editing tab"""
        # Coordinates list
        coords_list_frame = ttk.Frame(self.coords_frame)
        coords_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Treeview for coordinates
        columns = ('Name', 'X', 'Y', 'Delay', 'Description')
        self.coords_tree = ttk.Treeview(coords_list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.coords_tree.heading(col, text=col)
            self.coords_tree.column(col, width=100)
        
        # Scrollbar
        coords_scrollbar = ttk.Scrollbar(coords_list_frame, orient=tk.VERTICAL, command=self.coords_tree.yview)
        self.coords_tree.configure(yscrollcommand=coords_scrollbar.set)
        
        self.coords_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        coords_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons
        coords_buttons = ttk.Frame(self.coords_frame)
        coords_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(coords_buttons, text="Add Coordinate", command=self.add_coordinate).pack(side=tk.LEFT, padx=5)
        ttk.Button(coords_buttons, text="Edit Selected", command=self.edit_coordinate).pack(side=tk.LEFT, padx=5)
        ttk.Button(coords_buttons, text="Delete Selected", command=self.delete_coordinate).pack(side=tk.LEFT, padx=5)
        ttk.Button(coords_buttons, text="Refresh", command=self.load_coordinates).pack(side=tk.LEFT, padx=5)
    
    def create_templates_tab(self):
        """Create templates editing tab"""
        # Templates list
        templates_list_frame = ttk.Frame(self.templates_frame)
        templates_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Treeview for templates
        columns = ('Name', 'Threshold', 'Required', 'File Exists')
        self.templates_tree = ttk.Treeview(templates_list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.templates_tree.heading(col, text=col)
            self.templates_tree.column(col, width=100)
        
        # Scrollbar
        templates_scrollbar = ttk.Scrollbar(templates_list_frame, orient=tk.VERTICAL, command=self.templates_tree.yview)
        self.templates_tree.configure(yscrollcommand=templates_scrollbar.set)
        
        self.templates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        templates_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons
        templates_buttons = ttk.Frame(self.templates_frame)
        templates_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(templates_buttons, text="Add Template", command=self.add_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(templates_buttons, text="Edit Selected", command=self.edit_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(templates_buttons, text="Delete Selected", command=self.delete_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(templates_buttons, text="Refresh", command=self.load_templates).pack(side=tk.LEFT, padx=5)
    
    def load_modules(self):
        """Load available modules"""
        try:
            modules = self.config_manager.list_modules()
            self.module_combo['values'] = modules
            if modules:
                self.module_combo.set(modules[0])
                self.on_module_selected()
            self.status_var.set(f"Loaded {len(modules)} modules")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load modules: {str(e)}")
            self.status_var.set("Error loading modules")
    
    def on_module_selected(self, event=None):
        """Handle module selection"""
        self.current_module = self.module_var.get()
        if self.current_module:
            self.load_coordinates()
            self.load_templates()
            self.status_var.set(f"Selected module: {self.current_module}")
    
    def load_coordinates(self):
        """Load coordinates for current module"""
        if not self.current_module:
            return
        
        # Clear existing items
        for item in self.coords_tree.get_children():
            self.coords_tree.delete(item)
        
        try:
            config = self.config_manager.get_module_config(self.current_module)
            coordinates = config.get('click_coordinates', [])
            
            for coord in coordinates:
                self.coords_tree.insert('', 'end', values=(
                    coord.get('name', ''),
                    coord.get('x', ''),
                    coord.get('y', ''),
                    coord.get('delay', ''),
                    coord.get('description', '')
                ))
            
            self.status_var.set(f"Loaded {len(coordinates)} coordinates")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load coordinates: {str(e)}")
    
    def load_templates(self):
        """Load templates for current module"""
        if not self.current_module:
            return
        
        # Clear existing items
        for item in self.templates_tree.get_children():
            self.templates_tree.delete(item)
        
        try:
            config = self.config_manager.get_module_config(self.current_module)
            templates = config.get('templates', [])
            
            for template in templates:
                # Check if template file exists
                template_path = os.path.join('templates', f"{template['name']}.png")
                exists = "Yes" if os.path.exists(template_path) else "No"
                
                self.templates_tree.insert('', 'end', values=(
                    template.get('name', ''),
                    template.get('threshold', ''),
                    "Yes" if template.get('required', False) else "No",
                    exists
                ))
            
            self.status_var.set(f"Loaded {len(templates)} templates")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load templates: {str(e)}")
    
    def add_coordinate(self):
        """Add a new coordinate"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return
        
        # Simple dialog for coordinate input
        dialog = CoordinateDialog(self.root, "Add Coordinate")
        if dialog.result:
            try:
                name, x, y, delay, description = dialog.result
                self.config_manager.add_click_coordinate(self.current_module, name, x, y, description, delay)
                self.load_coordinates()
                self.status_var.set(f"Added coordinate: {name}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add coordinate: {str(e)}")
    
    def edit_coordinate(self):
        """Edit selected coordinate"""
        selection = self.coords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to edit")
            return
        
        item = self.coords_tree.item(selection[0])
        values = item['values']
        
        # Pre-fill dialog with current values
        dialog = CoordinateDialog(self.root, "Edit Coordinate", values)
        if dialog.result:
            try:
                name, x, y, delay, description = dialog.result
                
                # Update the coordinate
                config = self.config_manager.get_module_config(self.current_module)
                coordinates = config.get('click_coordinates', [])
                
                # Find and update the coordinate
                for coord in coordinates:
                    if coord.get('name') == values[0]:  # Match by original name
                        coord['name'] = name
                        coord['x'] = x
                        coord['y'] = y
                        coord['delay'] = delay
                        coord['description'] = description
                        break
                
                self.config_manager.update_module_config(self.current_module, config)
                self.load_coordinates()
                self.status_var.set(f"Updated coordinate: {name}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to edit coordinate: {str(e)}")
    
    def delete_coordinate(self):
        """Delete selected coordinate"""
        selection = self.coords_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a coordinate to delete")
            return
        
        item = self.coords_tree.item(selection[0])
        coord_name = item['values'][0]
        
        if messagebox.askyesno("Confirm Delete", f"Delete coordinate '{coord_name}'?"):
            try:
                config = self.config_manager.get_module_config(self.current_module)
                coordinates = config.get('click_coordinates', [])
                
                # Remove the coordinate
                coordinates = [c for c in coordinates if c.get('name') != coord_name]
                config['click_coordinates'] = coordinates
                
                self.config_manager.update_module_config(self.current_module, config)
                self.load_coordinates()
                self.status_var.set(f"Deleted coordinate: {coord_name}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete coordinate: {str(e)}")
    
    def add_template(self):
        """Add a new template"""
        if not self.current_module:
            messagebox.showwarning("Warning", "Please select a module first")
            return
        
        name = simpledialog.askstring("Template Name", "Enter template name:")
        if not name:
            return
        
        threshold = simpledialog.askfloat("Threshold", "Enter matching threshold (0.0-1.0):",
                                        initialvalue=0.8, minvalue=0.0, maxvalue=1.0)
        if threshold is None:
            return
        
        required = messagebox.askyesno("Required", "Is this template required?")
        
        try:
            self.config_manager.add_template(self.current_module, name, threshold, required)
            self.load_templates()
            self.status_var.set(f"Added template: {name}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add template: {str(e)}")
    
    def edit_template(self):
        """Edit selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to edit")
            return
        
        item = self.templates_tree.item(selection[0])
        values = item['values']
        
        name = simpledialog.askstring("Template Name", "Enter template name:", initialvalue=values[0])
        if not name:
            return
        
        threshold = simpledialog.askfloat("Threshold", "Enter matching threshold (0.0-1.0):",
                                        initialvalue=float(values[1]), minvalue=0.0, maxvalue=1.0)
        if threshold is None:
            return
        
        required = messagebox.askyesno("Required", "Is this template required?", 
                                     default=messagebox.YES if values[2] == "Yes" else messagebox.NO)
        
        try:
            config = self.config_manager.get_module_config(self.current_module)
            templates = config.get('templates', [])
            
            # Find and update the template
            for template in templates:
                if template.get('name') == values[0]:  # Match by original name
                    template['name'] = name
                    template['threshold'] = threshold
                    template['required'] = required
                    break
            
            self.config_manager.update_module_config(self.current_module, config)
            self.load_templates()
            self.status_var.set(f"Updated template: {name}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to edit template: {str(e)}")
    
    def delete_template(self):
        """Delete selected template"""
        selection = self.templates_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return
        
        item = self.templates_tree.item(selection[0])
        template_name = item['values'][0]
        
        if messagebox.askyesno("Confirm Delete", f"Delete template '{template_name}'?"):
            try:
                config = self.config_manager.get_module_config(self.current_module)
                templates = config.get('templates', [])
                
                # Remove the template
                templates = [t for t in templates if t.get('name') != template_name]
                config['templates'] = templates
                
                self.config_manager.update_module_config(self.current_module, config)
                self.load_templates()
                self.status_var.set(f"Deleted template: {template_name}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete template: {str(e)}")
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()

class CoordinateDialog:
    def __init__(self, parent, title, initial_values=None):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Create form
        frame = ttk.Frame(self.dialog, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Name
        ttk.Label(frame, text="Name:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=initial_values[0] if initial_values else "")
        ttk.Entry(frame, textvariable=self.name_var, width=30).grid(row=0, column=1, pady=5)
        
        # X coordinate
        ttk.Label(frame, text="X:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.x_var = tk.StringVar(value=initial_values[1] if initial_values else "")
        ttk.Entry(frame, textvariable=self.x_var, width=30).grid(row=1, column=1, pady=5)
        
        # Y coordinate
        ttk.Label(frame, text="Y:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.y_var = tk.StringVar(value=initial_values[2] if initial_values else "")
        ttk.Entry(frame, textvariable=self.y_var, width=30).grid(row=2, column=1, pady=5)
        
        # Delay
        ttk.Label(frame, text="Delay:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.delay_var = tk.StringVar(value=initial_values[3] if initial_values else "1.0")
        ttk.Entry(frame, textvariable=self.delay_var, width=30).grid(row=3, column=1, pady=5)
        
        # Description
        ttk.Label(frame, text="Description:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.desc_var = tk.StringVar(value=initial_values[4] if initial_values else "")
        ttk.Entry(frame, textvariable=self.desc_var, width=30).grid(row=4, column=1, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # Focus on name field
        self.dialog.focus_set()
    
    def ok_clicked(self):
        try:
            name = self.name_var.get().strip()
            x = int(self.x_var.get())
            y = int(self.y_var.get())
            delay = float(self.delay_var.get())
            description = self.desc_var.get().strip()
            
            if not name:
                messagebox.showwarning("Warning", "Name is required")
                return
            
            self.result = (name, x, y, delay, description)
            self.dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Invalid numeric values")
    
    def cancel_clicked(self):
        self.dialog.destroy()

if __name__ == "__main__":
    app = SimpleConfigEditor()
    app.run()
