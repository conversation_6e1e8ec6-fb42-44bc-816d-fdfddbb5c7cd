#!/usr/bin/env python3
"""
Universal Template & Coordinate Capture Tool
Captures templates, coordinates, and regions for any automation needs
"""

import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import time
import json
from datetime import datetime

class UniversalCaptureGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Universal Template & Coordinate Capture Tool")
        self.root.geometry("800x700")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.captured_image = None
        self.captured_coordinates = []
        self.captured_regions = []
        self.preview_label = None
        self.capture_mode = "template"  # template, coordinate, region
        
        # Results storage
        self.results = {
            'templates': [],
            'coordinates': [],
            'regions': [],
            'timestamp': None
        }
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI interface"""
        # Title
        title_label = tk.Label(self.root, text="🛠️ Universal Capture Tool", 
                              font=('Arial', 18, 'bold'), 
                              fg='white', bg='#2b2b2b')
        title_label.pack(pady=10)
        
        # Mode selection frame
        mode_frame = tk.Frame(self.root, bg='#2b2b2b')
        mode_frame.pack(pady=10)
        
        tk.Label(mode_frame, text="Capture Mode:", font=('Arial', 12, 'bold'),
                fg='white', bg='#2b2b2b').pack(side=tk.LEFT, padx=10)
        
        self.mode_var = tk.StringVar(value="template")
        modes = [("📸 Template", "template"), ("📍 Coordinate", "coordinate"), ("📐 Region", "region")]
        
        for text, mode in modes:
            tk.Radiobutton(mode_frame, text=text, variable=self.mode_var, value=mode,
                          font=('Arial', 10), fg='white', bg='#2b2b2b',
                          selectcolor='#4CAF50', command=self.update_mode).pack(side=tk.LEFT, padx=5)
        
        # Instructions frame
        self.instructions_frame = tk.Frame(self.root, bg='#2b2b2b')
        self.instructions_frame.pack(pady=10, fill=tk.X, padx=20)
        
        self.instructions_label = tk.Label(self.instructions_frame, 
                                          font=('Arial', 10), 
                                          fg='#cccccc', bg='#2b2b2b',
                                          justify='left', wraplength=750)
        self.instructions_label.pack()
        
        # Buttons frame
        button_frame = tk.Frame(self.root, bg='#2b2b2b')
        button_frame.pack(pady=15)
        
        # Capture button
        self.capture_btn = tk.Button(button_frame, text="📸 Capture", 
                                    command=self.start_capture,
                                    font=('Arial', 12, 'bold'),
                                    bg='#4CAF50', fg='white',
                                    padx=20, pady=10)
        self.capture_btn.pack(side=tk.LEFT, padx=10)
        
        # Clear button
        clear_btn = tk.Button(button_frame, text="🗑️ Clear All", 
                             command=self.clear_all,
                             font=('Arial', 12, 'bold'),
                             bg='#f44336', fg='white',
                             padx=20, pady=10)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # Export button
        export_btn = tk.Button(button_frame, text="💾 Export Results", 
                              command=self.export_results,
                              font=('Arial', 12, 'bold'),
                              bg='#2196F3', fg='white',
                              padx=20, pady=10)
        export_btn.pack(side=tk.LEFT, padx=10)
        
        # Results frame
        results_frame = tk.Frame(self.root, bg='#2b2b2b')
        results_frame.pack(pady=10, fill=tk.BOTH, expand=True, padx=20)
        
        # Results notebook
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Templates tab
        self.templates_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(self.templates_frame, text="📸 Templates")
        
        self.templates_listbox = tk.Listbox(self.templates_frame, bg='#3b3b3b', fg='white',
                                           font=('Consolas', 10), height=8)
        self.templates_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Coordinates tab
        self.coords_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(self.coords_frame, text="📍 Coordinates")
        
        self.coords_listbox = tk.Listbox(self.coords_frame, bg='#3b3b3b', fg='white',
                                        font=('Consolas', 10), height=8)
        self.coords_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Regions tab
        self.regions_frame = tk.Frame(self.notebook, bg='#2b2b2b')
        self.notebook.add(self.regions_frame, text="📐 Regions")
        
        self.regions_listbox = tk.Listbox(self.regions_frame, bg='#3b3b3b', fg='white',
                                         font=('Consolas', 10), height=8)
        self.regions_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Preview frame
        preview_frame = tk.Frame(self.root, bg='#2b2b2b')
        preview_frame.pack(pady=10, fill=tk.X, padx=20)
        
        preview_title = tk.Label(preview_frame, text="Preview:", 
                                font=('Arial', 12, 'bold'), 
                                fg='white', bg='#2b2b2b')
        preview_title.pack()
        
        # Preview label for image
        self.preview_label = tk.Label(preview_frame, bg='#2b2b2b')
        self.preview_label.pack(pady=5)
        
        # Status label
        self.status_label = tk.Label(self.root, text="Ready - Select capture mode and click Capture", 
                                    font=('Arial', 10), 
                                    fg='#4CAF50', bg='#2b2b2b')
        self.status_label.pack(pady=10)
        
        # Update initial mode
        self.update_mode()
        
    def update_mode(self):
        """Update instructions based on selected mode"""
        mode = self.mode_var.get()
        self.capture_mode = mode
        
        instructions = {
            'template': """
📸 TEMPLATE MODE:
1. Click 'Capture' to take a screenshot
2. Drag to select the template area (UI element to detect)
3. Enter a name for the template
4. Template will be saved and added to results
            """,
            'coordinate': """
📍 COORDINATE MODE:
1. Click 'Capture' to take a screenshot
2. Click on the exact point you want to capture
3. Enter a description for the coordinate
4. Coordinate will be added to results
            """,
            'region': """
📐 REGION MODE:
1. Click 'Capture' to take a screenshot
2. Drag to select a rectangular region
3. Enter a description for the region
4. Region coordinates will be added to results
            """
        }
        
        self.instructions_label.config(text=instructions[mode])
        
        # Update button text
        button_texts = {
            'template': '📸 Capture Template',
            'coordinate': '📍 Capture Coordinate', 
            'region': '📐 Capture Region'
        }
        self.capture_btn.config(text=button_texts[mode])
    
    def start_capture(self):
        """Start the capture process"""
        try:
            self.status_label.config(text="Minimizing window in 3 seconds...", fg='orange')
            self.root.update()
            time.sleep(3)
            
            # Minimize window
            self.root.withdraw()
            time.sleep(0.5)
            
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            # Restore window
            self.root.deiconify()
            
            # Open appropriate capture window
            if self.capture_mode == "template":
                self.capture_template(screenshot_cv)
            elif self.capture_mode == "coordinate":
                self.capture_coordinate(screenshot_cv)
            elif self.capture_mode == "region":
                self.capture_region(screenshot_cv)
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture: {str(e)}")
            self.status_label.config(text="Capture failed", fg='red')
    
    def capture_template(self, screenshot):
        """Capture template area"""
        self.open_selection_window(screenshot, "template")
    
    def capture_coordinate(self, screenshot):
        """Capture single coordinate"""
        self.open_selection_window(screenshot, "coordinate")
    
    def capture_region(self, screenshot):
        """Capture region area"""
        self.open_selection_window(screenshot, "region")
    
    def open_selection_window(self, screenshot, mode):
        """Open window for selection"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title(f"Select {mode.title()}")
        selection_window.configure(bg='#2b2b2b')
        
        # Convert screenshot for display
        screenshot_rgb = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        screenshot_pil = Image.fromarray(screenshot_rgb)
        
        # Resize for display if too large
        display_width = 1400
        display_height = 900
        if screenshot_pil.width > display_width or screenshot_pil.height > display_height:
            screenshot_pil.thumbnail((display_width, display_height), Image.Resampling.LANCZOS)
        
        screenshot_tk = ImageTk.PhotoImage(screenshot_pil)
        
        # Create canvas for selection
        canvas = tk.Canvas(selection_window, 
                          width=screenshot_pil.width, 
                          height=screenshot_pil.height,
                          bg='black')
        canvas.pack(padx=10, pady=10)
        
        # Display screenshot
        canvas.create_image(0, 0, anchor=tk.NW, image=screenshot_tk)
        
        # Selection variables
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        
        # Scale factors
        scale_x = screenshot.shape[1] / screenshot_pil.width
        scale_y = screenshot.shape[0] / screenshot_pil.height
        
        def on_mouse_press(event):
            self.selection_start = (event.x, event.y)
            if mode == "coordinate":
                # For coordinate mode, just capture the click point
                orig_x = int(event.x * scale_x)
                orig_y = int(event.y * scale_y)
                self.process_coordinate_capture(orig_x, orig_y)
                selection_window.destroy()
            else:
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
        
        def on_mouse_drag(event):
            if self.selection_start and mode != "coordinate":
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
                self.selection_rect = canvas.create_rectangle(
                    self.selection_start[0], self.selection_start[1],
                    event.x, event.y,
                    outline='red', width=2
                )
        
        def on_mouse_release(event):
            if mode == "coordinate":
                return
                
            self.selection_end = (event.x, event.y)
            
            if self.selection_start and self.selection_end:
                # Calculate selection area
                x1, y1 = self.selection_start
                x2, y2 = self.selection_end
                
                # Ensure correct order
                x1, x2 = min(x1, x2), max(x1, x2)
                y1, y2 = min(y1, y2), max(y1, y2)
                
                # Scale back to original screenshot size
                orig_x1 = int(x1 * scale_x)
                orig_y1 = int(y1 * scale_y)
                orig_x2 = int(x2 * scale_x)
                orig_y2 = int(y2 * scale_y)
                
                if mode == "template":
                    # Extract template
                    template = screenshot[orig_y1:orig_y2, orig_x1:orig_x2]
                    if template.size > 0:
                        self.process_template_capture(template, (orig_x1, orig_y1, orig_x2, orig_y2))
                        selection_window.destroy()
                    else:
                        messagebox.showerror("Error", "Invalid selection area")
                        
                elif mode == "region":
                    self.process_region_capture((orig_x1, orig_y1, orig_x2, orig_y2))
                    selection_window.destroy()
        
        # Bind mouse events
        canvas.bind("<Button-1>", on_mouse_press)
        if mode != "coordinate":
            canvas.bind("<B1-Motion>", on_mouse_drag)
            canvas.bind("<ButtonRelease-1>", on_mouse_release)
        
        # Instructions
        instruction_texts = {
            'template': "Drag to select the template area",
            'coordinate': "Click on the exact point to capture",
            'region': "Drag to select the region area"
        }
        
        inst_label = tk.Label(selection_window, 
                             text=instruction_texts[mode], 
                             font=('Arial', 12), 
                             fg='white', bg='#2b2b2b')
        inst_label.pack(pady=5)
        
        # Keep reference to prevent garbage collection
        canvas.image = screenshot_tk
    
    def process_template_capture(self, template, bounds):
        """Process captured template"""
        name = self.get_user_input("Template Name", "Enter a name for this template:")
        if name:
            # Save template
            templates_dir = "templates"
            if not os.path.exists(templates_dir):
                os.makedirs(templates_dir)
            
            filename = f"{name}.png"
            filepath = os.path.join(templates_dir, filename)
            cv2.imwrite(filepath, template)
            
            # Add to results
            template_info = {
                'name': name,
                'filename': filename,
                'bounds': bounds,
                'size': f"{template.shape[1]}x{template.shape[0]}",
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.results['templates'].append(template_info)
            self.update_templates_list()
            self.update_preview(template)
            
            self.status_label.config(text=f"Template '{name}' captured successfully!", fg='green')
    
    def process_coordinate_capture(self, x, y):
        """Process captured coordinate"""
        description = self.get_user_input("Coordinate Description", 
                                         f"Enter description for coordinate ({x}, {y}):")
        if description:
            coord_info = {
                'description': description,
                'x': x,
                'y': y,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.results['coordinates'].append(coord_info)
            self.update_coordinates_list()
            
            self.status_label.config(text=f"Coordinate ({x}, {y}) captured successfully!", fg='green')
    
    def process_region_capture(self, bounds):
        """Process captured region"""
        x1, y1, x2, y2 = bounds
        description = self.get_user_input("Region Description", 
                                         f"Enter description for region ({x1}, {y1}, {x2}, {y2}):")
        if description:
            region_info = {
                'description': description,
                'x1': x1,
                'y1': y1,
                'x2': x2,
                'y2': y2,
                'width': x2 - x1,
                'height': y2 - y1,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.results['regions'].append(region_info)
            self.update_regions_list()
            
            self.status_label.config(text=f"Region captured successfully!", fg='green')
    
    def get_user_input(self, title, prompt):
        """Get user input via dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("400x150")
        dialog.configure(bg='#2b2b2b')
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 200, self.root.winfo_rooty() + 200))
        
        result = [None]
        
        tk.Label(dialog, text=prompt, font=('Arial', 10), 
                fg='white', bg='#2b2b2b', wraplength=350).pack(pady=10)
        
        entry = tk.Entry(dialog, font=('Arial', 12), width=30)
        entry.pack(pady=10)
        entry.focus()
        
        def on_ok():
            result[0] = entry.get().strip()
            dialog.destroy()
        
        def on_cancel():
            dialog.destroy()
        
        button_frame = tk.Frame(dialog, bg='#2b2b2b')
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="OK", command=on_ok, 
                 bg='#4CAF50', fg='white', padx=20).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=on_cancel,
                 bg='#f44336', fg='white', padx=20).pack(side=tk.LEFT, padx=5)
        
        entry.bind('<Return>', lambda e: on_ok())
        entry.bind('<Escape>', lambda e: on_cancel())
        
        dialog.wait_window()
        return result[0]
    
    def update_templates_list(self):
        """Update templates listbox"""
        self.templates_listbox.delete(0, tk.END)
        for i, template in enumerate(self.results['templates']):
            display_text = f"{i+1}. {template['name']} - {template['size']} - {template['timestamp']}"
            self.templates_listbox.insert(tk.END, display_text)
    
    def update_coordinates_list(self):
        """Update coordinates listbox"""
        self.coords_listbox.delete(0, tk.END)
        for i, coord in enumerate(self.results['coordinates']):
            display_text = f"{i+1}. ({coord['x']}, {coord['y']}) - {coord['description']} - {coord['timestamp']}"
            self.coords_listbox.insert(tk.END, display_text)
    
    def update_regions_list(self):
        """Update regions listbox"""
        self.regions_listbox.delete(0, tk.END)
        for i, region in enumerate(self.results['regions']):
            display_text = f"{i+1}. ({region['x1']}, {region['y1']}, {region['x2']}, {region['y2']}) - {region['width']}x{region['height']} - {region['description']} - {region['timestamp']}"
            self.regions_listbox.insert(tk.END, display_text)
    
    def update_preview(self, image):
        """Update preview with captured image"""
        if image is not None:
            # Convert for display
            if len(image.shape) == 3:
                preview_img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                preview_img = image
            preview_pil = Image.fromarray(preview_img)
            
            # Resize for preview if too large
            max_preview_size = 200
            if preview_pil.width > max_preview_size or preview_pil.height > max_preview_size:
                preview_pil.thumbnail((max_preview_size, max_preview_size), Image.Resampling.LANCZOS)
            
            preview_tk = ImageTk.PhotoImage(preview_pil)
            
            self.preview_label.config(image=preview_tk)
            self.preview_label.image = preview_tk  # Keep reference
    
    def clear_all(self):
        """Clear all captured data"""
        if messagebox.askyesno("Confirm Clear", "Are you sure you want to clear all captured data?"):
            self.results = {
                'templates': [],
                'coordinates': [],
                'regions': [],
                'timestamp': None
            }
            
            self.templates_listbox.delete(0, tk.END)
            self.coords_listbox.delete(0, tk.END)
            self.regions_listbox.delete(0, tk.END)
            
            self.preview_label.config(image='')
            self.preview_label.image = None
            
            self.status_label.config(text="All data cleared", fg='orange')
    
    def export_results(self):
        """Export all results to JSON file"""
        if not any([self.results['templates'], self.results['coordinates'], self.results['regions']]):
            messagebox.showwarning("No Data", "No data to export. Capture some templates, coordinates, or regions first.")
            return
        
        # Add timestamp
        self.results['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Ask for filename
        filename = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialfile=f"capture_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)
                
                # Show summary
                summary = f"""Results exported successfully!

File: {os.path.basename(filename)}

Summary:
• Templates: {len(self.results['templates'])}
• Coordinates: {len(self.results['coordinates'])}
• Regions: {len(self.results['regions'])}

You can now share this file to specify exactly what you need for automation!"""
                
                messagebox.showinfo("Export Successful", summary)
                self.status_label.config(text=f"Results exported to {os.path.basename(filename)}", fg='green')
                
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export results: {str(e)}")
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🛠️ Universal Template & Coordinate Capture Tool")
    print("=" * 50)
    print("📸 Capture templates for image detection")
    print("📍 Capture coordinates for click actions")
    print("📐 Capture regions for OCR or scanning")
    print("💾 Export results to share precise requirements")
    print("=" * 50)
    print("Starting GUI...")
    app = UniversalCaptureGUI()
    app.run()
