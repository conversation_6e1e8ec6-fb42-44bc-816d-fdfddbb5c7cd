#!/usr/bin/env python3
"""
OCR Monitoring and Configuration GUI
Provides real-time monitoring and configuration for OCR detection results
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import json
import os
import time
import threading
from datetime import datetime
from typing import Dict, Any, Optional
import cv2
import numpy as np
from PIL import Image, ImageTk

class OCRMonitorGUI:
    """GUI for monitoring and configuring OCR detection results"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("OCR Monitor & Configuration")
        self.root.geometry("1200x800")
        
        # OCR service reference
        self.ocr_service = None
        self.monitoring_active = False
        self.update_thread = None
        
        # GUI state
        self.debug_images = {}
        self.current_stats = {}
        
        self._setup_gui()
        self._load_ocr_service()
        
    def _setup_gui(self):
        """Setup the GUI layout"""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Tab 1: Real-time Monitoring
        self.monitoring_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitoring_frame, text="Real-time Monitoring")
        self._setup_monitoring_tab()
        
        # Tab 2: Configuration
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="Configuration")
        self._setup_config_tab()
        
        # Tab 3: Statistics
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="Statistics")
        self._setup_stats_tab()
        
        # Tab 4: Debug Images
        self.debug_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.debug_frame, text="Debug Images")
        self._setup_debug_tab()
        
    def _setup_monitoring_tab(self):
        """Setup real-time monitoring tab"""
        # Control buttons
        control_frame = ttk.Frame(self.monitoring_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text="Start Monitoring", 
                                   command=self._start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="Stop Monitoring", 
                                  command=self._stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.clear_btn = ttk.Button(control_frame, text="Clear Log", 
                                   command=self._clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        # Status display
        status_frame = ttk.LabelFrame(self.monitoring_frame, text="Current Status")
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_text = tk.StringVar(value="OCR Service: Not Connected")
        ttk.Label(status_frame, textvariable=self.status_text).pack(pady=5)
        
        # Real-time log
        log_frame = ttk.LabelFrame(self.monitoring_frame, text="OCR Detection Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def _setup_config_tab(self):
        """Setup configuration tab"""
        # Configuration options
        config_frame = ttk.LabelFrame(self.config_frame, text="OCR Debug Configuration")
        config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create configuration variables
        self.config_vars = {}
        
        config_options = [
            ('enable_detailed_logging', 'Enable Detailed Logging', True),
            ('save_debug_images', 'Save Debug Images', True),
            ('log_confidence_scores', 'Log Confidence Scores', True),
            ('log_processing_times', 'Log Processing Times', True),
            ('log_preprocessing_steps', 'Log Preprocessing Steps', False),
            ('log_raw_ocr_output', 'Log Raw OCR Output', False),
            ('save_failed_detections', 'Save Failed Detections', True)
        ]
        
        for i, (key, label, default) in enumerate(config_options):
            var = tk.BooleanVar(value=default)
            self.config_vars[key] = var
            
            row = i // 2
            col = i % 2
            
            ttk.Checkbutton(config_frame, text=label, variable=var,
                           command=self._update_config).grid(row=row, column=col, 
                                                            sticky=tk.W, padx=10, pady=2)
        
        # Confidence threshold
        threshold_frame = ttk.Frame(config_frame)
        threshold_frame.grid(row=len(config_options)//2 + 1, column=0, columnspan=2, 
                           sticky=tk.W, padx=10, pady=5)
        
        ttk.Label(threshold_frame, text="Confidence Threshold:").pack(side=tk.LEFT)
        self.confidence_var = tk.DoubleVar(value=0.3)
        confidence_scale = ttk.Scale(threshold_frame, from_=0.1, to=0.9, 
                                   variable=self.confidence_var, orient=tk.HORIZONTAL,
                                   command=self._update_config)
        confidence_scale.pack(side=tk.LEFT, padx=5)
        
        self.confidence_label = ttk.Label(threshold_frame, text="0.30")
        self.confidence_label.pack(side=tk.LEFT, padx=5)
        
        # Max debug images
        max_images_frame = ttk.Frame(config_frame)
        max_images_frame.grid(row=len(config_options)//2 + 2, column=0, columnspan=2, 
                            sticky=tk.W, padx=10, pady=5)
        
        ttk.Label(max_images_frame, text="Max Debug Images:").pack(side=tk.LEFT)
        self.max_images_var = tk.IntVar(value=100)
        max_images_spin = ttk.Spinbox(max_images_frame, from_=10, to=1000, 
                                    textvariable=self.max_images_var, width=10,
                                    command=self._update_config)
        max_images_spin.pack(side=tk.LEFT, padx=5)
        
        # Control buttons
        btn_frame = ttk.Frame(self.config_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(btn_frame, text="Load Config", 
                  command=self._load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Save Config", 
                  command=self._save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Reset to Defaults", 
                  command=self._reset_config).pack(side=tk.LEFT, padx=5)
        
    def _setup_stats_tab(self):
        """Setup statistics tab"""
        # Statistics display
        stats_frame = ttk.LabelFrame(self.stats_frame, text="OCR Performance Statistics")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=25, 
                                                   font=('Consolas', 10))
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        stats_btn_frame = ttk.Frame(self.stats_frame)
        stats_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(stats_btn_frame, text="Refresh Stats", 
                  command=self._refresh_stats).pack(side=tk.LEFT, padx=5)
        ttk.Button(stats_btn_frame, text="Reset Stats", 
                  command=self._reset_stats).pack(side=tk.LEFT, padx=5)
        ttk.Button(stats_btn_frame, text="Export Stats", 
                  command=self._export_stats).pack(side=tk.LEFT, padx=5)
        
    def _setup_debug_tab(self):
        """Setup debug images tab"""
        # Image browser
        browser_frame = ttk.LabelFrame(self.debug_frame, text="Debug Image Browser")
        browser_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Directory selection
        dir_frame = ttk.Frame(browser_frame)
        dir_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(dir_frame, text="Debug Directory:").pack(side=tk.LEFT)
        self.debug_dir_var = tk.StringVar(value="debug/ocr_images")
        ttk.Entry(dir_frame, textvariable=self.debug_dir_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(dir_frame, text="Browse", 
                  command=self._browse_debug_dir).pack(side=tk.LEFT, padx=5)
        ttk.Button(dir_frame, text="Refresh", 
                  command=self._refresh_debug_images).pack(side=tk.LEFT, padx=5)
        
        # Image list and preview
        content_frame = ttk.Frame(browser_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Image list
        list_frame = ttk.Frame(content_frame)
        list_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Label(list_frame, text="Debug Images:").pack()
        self.image_listbox = tk.Listbox(list_frame, width=30, height=20)
        self.image_listbox.pack(fill=tk.Y, expand=True)
        self.image_listbox.bind('<<ListboxSelect>>', self._on_image_select)
        
        # Image preview
        preview_frame = ttk.Frame(content_frame)
        preview_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(preview_frame, text="Image Preview:").pack()
        self.image_label = ttk.Label(preview_frame, text="Select an image to preview")
        self.image_label.pack(expand=True)
        
        # Image info
        self.image_info_text = scrolledtext.ScrolledText(preview_frame, height=8, 
                                                        font=('Consolas', 9))
        self.image_info_text.pack(fill=tk.X, pady=5)

    def _load_ocr_service(self):
        """Load the OCR service"""
        try:
            from ai_ocr_service import get_ai_ocr_service
            self.ocr_service = get_ai_ocr_service()
            if self.ocr_service:
                self.status_text.set("OCR Service: Connected")
                self._log("OCR service connected successfully")
            else:
                self.status_text.set("OCR Service: Failed to connect")
                self._log("Failed to connect to OCR service")
        except Exception as e:
            self.status_text.set(f"OCR Service: Error - {e}")
            self._log(f"Error loading OCR service: {e}")

    def _start_monitoring(self):
        """Start real-time monitoring"""
        if not self.ocr_service:
            messagebox.showerror("Error", "OCR service not available")
            return

        self.monitoring_active = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        # Start monitoring thread
        self.update_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.update_thread.start()

        self._log("Monitoring started")

    def _stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self._log("Monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                if self.ocr_service:
                    # Get current statistics
                    stats = self.ocr_service.get_ocr_statistics()

                    # Update GUI in main thread
                    self.root.after(0, self._update_monitoring_display, stats)

                time.sleep(1.0)  # Update every second
            except Exception as e:
                self.root.after(0, self._log, f"Monitoring error: {e}")
                break

    def _update_monitoring_display(self, stats):
        """Update monitoring display with current stats"""
        if stats != self.current_stats:
            self.current_stats = stats

            # Log significant changes
            if stats.get('total_processed', 0) > 0:
                success_rate = stats.get('success_rate', 0) * 100
                avg_time = stats.get('avg_processing_time', 0)
                avg_conf = stats.get('avg_confidence', 0)

                status_msg = (f"Processed: {stats['total_processed']} | "
                            f"Success: {success_rate:.1f}% | "
                            f"Avg Time: {avg_time:.3f}s | "
                            f"Avg Confidence: {avg_conf:.3f}")

                self._log(f"[STATS] {status_msg}")

    def _clear_log(self):
        """Clear the monitoring log"""
        self.log_text.delete(1.0, tk.END)

    def _log(self, message):
        """Add message to monitoring log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def _update_config(self, *args):
        """Update OCR configuration"""
        if not self.ocr_service:
            return

        try:
            # Build configuration dictionary
            new_config = {}

            for key, var in self.config_vars.items():
                new_config[key] = var.get()

            new_config['confidence_threshold'] = self.confidence_var.get()
            new_config['max_debug_images'] = self.max_images_var.get()

            # Update confidence label
            self.confidence_label.config(text=f"{self.confidence_var.get():.2f}")

            # Apply configuration
            self.ocr_service.update_debug_config(new_config)
            self._log(f"Configuration updated: {list(new_config.keys())}")

        except Exception as e:
            self._log(f"Error updating configuration: {e}")

    def _load_config(self):
        """Load configuration from file"""
        try:
            filename = filedialog.askopenfilename(
                title="Load OCR Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r') as f:
                    config = json.load(f)

                # Update GUI controls
                for key, var in self.config_vars.items():
                    if key in config:
                        var.set(config[key])

                if 'confidence_threshold' in config:
                    self.confidence_var.set(config['confidence_threshold'])

                if 'max_debug_images' in config:
                    self.max_images_var.set(config['max_debug_images'])

                self._update_config()
                self._log(f"Configuration loaded from {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def _save_config(self):
        """Save configuration to file"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save OCR Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                config = {}

                for key, var in self.config_vars.items():
                    config[key] = var.get()

                config['confidence_threshold'] = self.confidence_var.get()
                config['max_debug_images'] = self.max_images_var.get()

                with open(filename, 'w') as f:
                    json.dump(config, f, indent=2)

                self._log(f"Configuration saved to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def _reset_config(self):
        """Reset configuration to defaults"""
        defaults = {
            'enable_detailed_logging': True,
            'save_debug_images': True,
            'log_confidence_scores': True,
            'log_processing_times': True,
            'log_preprocessing_steps': False,
            'log_raw_ocr_output': False,
            'save_failed_detections': True
        }

        for key, var in self.config_vars.items():
            var.set(defaults.get(key, False))

        self.confidence_var.set(0.3)
        self.max_images_var.set(100)

        self._update_config()
        self._log("Configuration reset to defaults")

    def _refresh_stats(self):
        """Refresh statistics display"""
        if not self.ocr_service:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "OCR service not available")
            return

        try:
            stats = self.ocr_service.get_ocr_statistics()

            # Format statistics for display
            stats_display = "=== OCR Performance Statistics ===\n\n"

            # Basic stats
            stats_display += f"Total Processed: {stats.get('total_processed', 0)}\n"
            stats_display += f"Successful Detections: {stats.get('successful_detections', 0)}\n"
            stats_display += f"Success Rate: {stats.get('success_rate', 0):.1%}\n\n"

            # Timing stats
            if stats.get('avg_processing_time', 0) > 0:
                stats_display += "=== Processing Times ===\n"
                stats_display += f"Average: {stats['avg_processing_time']:.3f}s\n"
                stats_display += f"Minimum: {stats.get('min_processing_time', 0):.3f}s\n"
                stats_display += f"Maximum: {stats.get('max_processing_time', 0):.3f}s\n\n"

            # Confidence stats
            if stats.get('avg_confidence', 0) > 0:
                stats_display += "=== Confidence Scores ===\n"
                stats_display += f"Average: {stats['avg_confidence']:.3f}\n"
                stats_display += f"Minimum: {stats.get('min_confidence', 0):.3f}\n"
                stats_display += f"Maximum: {stats.get('max_confidence', 0):.3f}\n\n"

            # Method usage
            if stats.get('method_usage'):
                stats_display += "=== Method Usage ===\n"
                total = stats['total_processed']
                for method, count in stats['method_usage'].items():
                    percentage = (count / total * 100) if total > 0 else 0
                    stats_display += f"{method}: {count} ({percentage:.1f}%)\n"
                stats_display += "\n"

            # Configuration info
            if hasattr(self.ocr_service, 'debug_config'):
                config = self.ocr_service.get_debug_config()
                stats_display += "=== Current Configuration ===\n"
                for key, value in config.items():
                    stats_display += f"{key}: {value}\n"

            # Update display
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, stats_display)

        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"Error retrieving statistics: {e}")

    def _reset_stats(self):
        """Reset OCR statistics"""
        if not self.ocr_service:
            messagebox.showerror("Error", "OCR service not available")
            return

        try:
            self.ocr_service.reset_statistics()
            self._refresh_stats()
            self._log("Statistics reset")
            messagebox.showinfo("Success", "Statistics have been reset")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to reset statistics: {e}")

    def _export_stats(self):
        """Export statistics to file"""
        if not self.ocr_service:
            messagebox.showerror("Error", "OCR service not available")
            return

        try:
            filename = filedialog.asksaveasfilename(
                title="Export OCR Statistics",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                stats = self.ocr_service.get_ocr_statistics()

                if filename.endswith('.json'):
                    with open(filename, 'w') as f:
                        json.dump(stats, f, indent=2)
                else:
                    # Export as text
                    with open(filename, 'w') as f:
                        f.write(self.stats_text.get(1.0, tk.END))

                self._log(f"Statistics exported to {filename}")
                messagebox.showinfo("Success", f"Statistics exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export statistics: {e}")

    def _browse_debug_dir(self):
        """Browse for debug directory"""
        directory = filedialog.askdirectory(
            title="Select Debug Images Directory",
            initialdir=self.debug_dir_var.get()
        )

        if directory:
            self.debug_dir_var.set(directory)
            self._refresh_debug_images()

    def _refresh_debug_images(self):
        """Refresh debug images list"""
        debug_dir = self.debug_dir_var.get()

        self.image_listbox.delete(0, tk.END)

        if not os.path.exists(debug_dir):
            self._log(f"Debug directory not found: {debug_dir}")
            return

        try:
            # Get all image files
            image_files = []
            for ext in ['.png', '.jpg', '.jpeg', '.bmp']:
                pattern = os.path.join(debug_dir, f"*{ext}")
                import glob
                image_files.extend(glob.glob(pattern))

            # Sort by modification time (newest first)
            image_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            # Add to listbox
            for filepath in image_files:
                filename = os.path.basename(filepath)
                self.image_listbox.insert(tk.END, filename)

            self._log(f"Found {len(image_files)} debug images in {debug_dir}")

        except Exception as e:
            self._log(f"Error refreshing debug images: {e}")

    def _on_image_select(self, event):
        """Handle image selection"""
        selection = self.image_listbox.curselection()
        if not selection:
            return

        filename = self.image_listbox.get(selection[0])
        filepath = os.path.join(self.debug_dir_var.get(), filename)

        try:
            # Load and display image
            image = Image.open(filepath)

            # Resize for display if too large
            display_size = (300, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(image)
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep a reference

            # Display image info
            info = f"File: {filename}\n"
            info += f"Path: {filepath}\n"
            info += f"Size: {os.path.getsize(filepath)} bytes\n"
            info += f"Modified: {datetime.fromtimestamp(os.path.getmtime(filepath))}\n"
            info += f"Dimensions: {image.size}\n"

            # Try to extract info from filename
            if '_' in filename:
                parts = filename.replace('.png', '').replace('.jpg', '').split('_')
                if len(parts) >= 2:
                    info += f"Piece ID: {parts[0]}\n"
                    if 'success' in filename:
                        info += f"Detection Result: SUCCESS\n"
                    elif 'failed' in filename:
                        info += f"Detection Result: FAILED\n"

            self.image_info_text.delete(1.0, tk.END)
            self.image_info_text.insert(tk.END, info)

        except Exception as e:
            self.image_label.config(image="", text=f"Error loading image: {e}")
            self.image_info_text.delete(1.0, tk.END)
            self.image_info_text.insert(tk.END, f"Error: {e}")


def main():
    """Main function to run the OCR Monitor GUI"""
    root = tk.Tk()
    app = OCRMonitorGUI(root)

    # Auto-refresh debug images on startup
    root.after(1000, app._refresh_debug_images)

    root.mainloop()


if __name__ == "__main__":
    main()
