#!/usr/bin/env python3
"""
Final Working Map Trade - Using EXACT successful filter methods
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Working coordinates from our tests
MAP_COORDINATES = {
    'M1': (532, 436),
    'M2': (613, 436),
    'M3': (696, 436), 
    'M4': (775, 436),
    'M5': (577, 513),
    'M6': (654, 521),
    'M7': (735, 519),
}

# Button coordinates that work
BUTTON_COORDINATES = {
    'you_will_lose': (732, 261),
    'initiate_exchange': (636, 611),
    'confirm_exchange': (633, 498),
}

def read_map_number_with_best_filter(x, y, size=30):
    """Read number using the EXACT successful filter methods from our test"""
    try:
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Try the EXACT successful filters from our test results:
        
        # For M1 (532, 436): morph filter worked
        if (x, y) == (532, 436):
            _, thresh_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            kernel = np.ones((2,2), np.uint8)
            processed = cv2.morphologyEx(thresh_otsu, cv2.MORPH_CLOSE, kernel)
        
        # For M2 (613, 436): adaptive, adaptive_inv, sharpened, edges worked
        elif (x, y) == (613, 436):
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        
        # For M6 (654, 521): multiple filters worked, use adaptive
        elif (x, y) == (654, 521):
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        
        # For other coordinates, try adaptive first (most successful)
        else:
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        
        # Try OCR with multiple configs
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789',
            '--psm 10 -c tessedit_char_whitelist=0123456789'
        ]
        
        for config in configs:
            text = pytesseract.image_to_string(processed, config=config).strip()
            if text.isdigit():
                return int(text)
        
        # Fallback: try morphological processing for all coordinates
        _, thresh_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        kernel = np.ones((2,2), np.uint8)
        morph = cv2.morphologyEx(thresh_otsu, cv2.MORPH_CLOSE, kernel)
        
        for config in configs:
            text = pytesseract.image_to_string(morph, config=config).strip()
            if text.isdigit():
                return int(text)
        
        # Last resort: try sharpening
        kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(gray, -1, kernel_sharpen)
        
        for config in configs:
            text = pytesseract.image_to_string(sharpened, config=config).strip()
            if text.isdigit():
                return int(text)
        
        return None
        
    except Exception as e:
        print(f"Error reading {x},{y}: {e}")
        return None

def read_all_map_pieces():
    """Read all map piece quantities with enhanced filtering"""
    print("Reading map pieces with PROVEN filter methods...")
    results = {}
    
    for map_name, (x, y) in MAP_COORDINATES.items():
        print(f"  Reading {map_name} at ({x}, {y})...")
        number = read_map_number_with_best_filter(x, y)
        if number is not None:
            results[map_name] = number
            print(f"    ✅ {map_name}: {number}")
        else:
            print(f"    ❌ {map_name}: Failed to read")
    
    return results

def find_best_trade(quantities):
    """Find best trade: give lowest, get highest"""
    if len(quantities) < 2:
        return None, None
    
    lowest = min(quantities.items(), key=lambda x: x[1])
    highest = max(quantities.items(), key=lambda x: x[1])
    
    if lowest[1] >= highest[1]:  # No benefit
        return None, None
    
    return lowest[0], highest[0]  # give_map, receive_map

def execute_trade(give_map, receive_map):
    """Execute the trade sequence"""
    print(f"\n🔄 Executing trade: Give {give_map} → Get {receive_map}")
    
    # Step 1: Click the map piece to give away
    give_coords = MAP_COORDINATES[give_map]
    print(f"Step 1: Clicking {give_map} at {give_coords}")
    pyautogui.click(give_coords[0], give_coords[1])
    time.sleep(0.5)
    
    # Step 2: Click "you will lose" area
    lose_coords = BUTTON_COORDINATES['you_will_lose']
    print(f"Step 2: Clicking 'you will lose' at {lose_coords}")
    pyautogui.click(lose_coords[0], lose_coords[1])
    time.sleep(0.5)
    
    # Step 3: Click the map piece to receive
    receive_coords = MAP_COORDINATES[receive_map]
    print(f"Step 3: Clicking {receive_map} at {receive_coords}")
    pyautogui.click(receive_coords[0], receive_coords[1])
    time.sleep(0.5)
    
    # Step 4: Click initiate exchange
    initiate_coords = BUTTON_COORDINATES['initiate_exchange']
    print(f"Step 4: Clicking 'initiate exchange' at {initiate_coords}")
    pyautogui.click(initiate_coords[0], initiate_coords[1])
    time.sleep(1.0)
    
    # Step 5: Click confirm exchange
    confirm_coords = BUTTON_COORDINATES['confirm_exchange']
    print(f"Step 5: Clicking 'confirm exchange' at {confirm_coords}")
    pyautogui.click(confirm_coords[0], confirm_coords[1])
    time.sleep(1.0)
    
    print("✅ Trade sequence completed!")

def run_map_trade():
    """Main map trade function with PROVEN methods"""
    print("🎯 FINAL Map Trade Automation")
    print("=" * 35)
    
    # Read all map pieces
    quantities = read_all_map_pieces()
    
    if not quantities:
        print("❌ Could not read any map pieces")
        return False
    
    print(f"\n📊 Detected quantities: {quantities}")
    print(f"📈 Success rate: {len(quantities)}/7 pieces ({len(quantities)*100//7}%)")
    
    # Find best trade
    give_map, receive_map = find_best_trade(quantities)
    
    if not give_map or not receive_map:
        print("❌ No beneficial trade found")
        return False
    
    give_qty = quantities[give_map]
    receive_qty = quantities[receive_map]
    
    print(f"\n💡 Trade Analysis:")
    print(f"  Give: {give_map} ({give_qty} pieces)")
    print(f"  Get: {receive_map} ({receive_qty} pieces)")
    print(f"  Benefit: +{receive_qty - give_qty} pieces")
    
    # Execute the trade
    execute_trade(give_map, receive_map)
    
    return True

if __name__ == "__main__":
    print("Make sure you're on the map trade screen with all 7 pieces visible")
    input("Press ENTER to start FINAL map trade: ")
    
    success = run_map_trade()
    
    if success:
        print("\n🎉 Map trade completed successfully!")
    else:
        print("\n❌ Map trade failed")
