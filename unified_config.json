{"config_version": "1.0.0", "last_updated": "2025-10-03T19:49:32.831721", "system_settings": {"scan_interval": 0.1, "priority_check_interval": 1.0, "screenshot_path": "screenshots/", "templates_path": "templates/", "window_width": 800, "window_height": 600, "theme": "dark", "centralized_scanner_enabled": true, "max_concurrent_detections": 3, "template_cache_size": 100, "detection_timeout": 5.0, "debug_mode": false}, "global_coordinates": {"map_pieces": {"M1": {"x": 1092, "y": 532, "description": "Map piece M1 click position"}, "M2": {"x": 1494, "y": 584, "description": "Map piece M2 click position"}, "M3": {"x": 1334, "y": 680, "description": "Map piece M3 click position"}, "M4": {"x": 1020, "y": 834, "description": "Map piece M4 click position"}, "M5": {"x": 1169, "y": 1029, "description": "Map piece M5 click position"}, "M6": {"x": 1410, "y": 985, "description": "Map piece M6 click position"}, "M7": {"x": 1569, "y": 830, "description": "Map piece M7 click position"}}}, "modules": {"dig": {"enabled": true, "priority": -1, "cooldown": 0.0, "manual_trigger": false, "settings": {"rapid_click_speed": 0.1, "rapid_click_duration": 20.0, "timer_threshold": 10, "thank_you_message": "Thank you for the dig!"}, "templates": {"dig_icon": {"threshold": 0.8, "required": false, "description": "Main dig icon trigger", "scanner_priority": 0, "scanner_enabled": true, "scanner_action": "custom_execute"}, "dig_up_dropdown": {"threshold": 0.8, "required": false, "description": "Dig up dropdown trigger", "scanner_priority": 0, "scanner_enabled": true, "scanner_action": "custom_execute"}, "test_flight_treasure": {"threshold": 0.8, "required": false, "description": "Test flight treasure trigger", "scanner_priority": 0, "scanner_enabled": true, "scanner_action": "custom_execute"}, "dig_up_treasure": {"threshold": 0.8, "required": false, "description": "Dig up treasure trigger", "scanner_priority": 0, "scanner_enabled": true, "scanner_action": "custom_execute"}}, "coordinates": {"dig_icon_click": {"x": 1419, "y": 1230, "delay": 1.0, "repeat": 1, "description": "Click dig icon"}, "chat_link": {"x": 1316, "y": 1172, "delay": 1.0, "repeat": 1, "description": "Click link in chat"}, "squad_select": {"x": 1263, "y": 839, "delay": 1.0, "repeat": 1, "description": "Select squad for dig"}, "send_squad": {"x": 1265, "y": 1069, "delay": 2.0, "repeat": 1, "description": "Send squad to dig"}, "loot_icon": {"x": 1262, "y": 843, "delay": 0.001, "repeat": 50, "description": "Loot collection icon (spam click)"}, "close_window": {"x": 900, "y": 100, "delay": 1.0, "repeat": 1, "description": "Close dig window"}}, "scan_regions": {"dig_area": {"x": 300, "y": 200, "width": 600, "height": 400, "description": "Area to scan for dig interface"}, "timer_area": {"x": 700, "y": 300, "width": 200, "height": 100, "description": "Area to scan for timer"}}}, "help_click": {"enabled": true, "priority": 0, "cooldown": 0.0, "manual_trigger": false, "settings": {}, "templates": {"Help_template": {"threshold": 0.7, "required": false, "description": "Main help template", "scanner_priority": 1, "scanner_enabled": true, "scanner_action": "action_1_HelpButton"}, "Help_chat": {"threshold": 0.7, "required": false, "description": "Help chat button", "scanner_priority": 2, "scanner_enabled": true, "scanner_action": "action_2_HelpChat"}, "help_world": {"threshold": 0.8, "required": false, "description": "Help world template", "scanner_priority": 1, "scanner_enabled": true, "scanner_action": "action_1_HelpButton"}}, "coordinates": {"1_HelpButton": {"x": 1164, "y": 521, "delay": 0.01, "repeat": 1, "use_esc_key": false, "description": "Click main help button"}, "2_HelpChat": {"x": 799, "y": 579, "delay": 0.01, "repeat": 1, "use_esc_key": false, "description": "Click help chat button"}}, "scan_regions": {"full_screen": {"x": 0, "y": 0, "width": 800, "height": 600, "description": "Full screen scan area"}, "alliance_area": {"x": 50, "y": 450, "width": 200, "height": 150, "description": "Alliance area scan"}, "help_list": {"x": 150, "y": 100, "width": 500, "height": 400, "description": "Help list area"}}}, "zombie_invasion": {"enabled": true, "priority": 1, "cooldown": 0.0, "manual_trigger": false, "settings": {}, "templates": {"squad_0_4": {"threshold": 0.95, "required": false, "description": "Template for 0/4 or empty squad status", "scanner_priority": 4, "scanner_enabled": false, "scanner_action": "deploy_squad"}, "squad_1_4": {"threshold": 0.95, "required": false, "description": "Template for 1/4 squad status", "scanner_priority": 5, "scanner_enabled": false, "scanner_action": "deploy_squad"}, "squad_2_4": {"threshold": 0.95, "required": false, "description": "Template for 2/4 squad status", "scanner_priority": 6, "scanner_enabled": false, "scanner_action": "deploy_squad"}, "squad_3_4": {"threshold": 0.95, "required": false, "description": "Template for 3/4 squad status", "scanner_priority": 7, "scanner_enabled": false, "scanner_action": "deploy_squad"}, "squad_4_4": {"threshold": 0.95, "required": false, "description": "Template for 4/4 squad status - STOP when detected", "scanner_priority": 8, "scanner_enabled": false, "scanner_action": "deploy_squad"}}, "coordinates": {"events_menu": {"x": 1580, "y": 287, "delay": 1.5, "repeat": 1, "description": "Events button in bottom menu"}, "search_zombie": {"x": 1139, "y": 1311, "delay": 2.0, "repeat": 1, "description": "Search button for zombies"}, "sword_attack": {"x": 1262, "y": 1036, "delay": 1.5, "repeat": 1, "description": "Sword icon to attack"}, "march_troops": {"x": 1261, "y": 1064, "delay": 1.5, "repeat": 1, "description": "March button to send troops"}, "zombie_invasion_tab": {"x": 1616, "y": 161, "delay": 1.5, "repeat": 1, "description": "Zombie Invasion tab in Events menu"}}, "scan_regions": {"events_area": {"x": 1500, "y": 200, "width": 200, "height": 200, "description": "Events area scan"}, "squad_status": {"x": 50, "y": 50, "width": 200, "height": 100, "description": "Squad status area"}, "zombie_search": {"x": 1000, "y": 1200, "width": 400, "height": 200, "description": "Zombie search area"}}}, "map_trade": {"enabled": true, "priority": 2, "cooldown": 30.0, "manual_trigger": false, "settings": {}, "templates": {"truck": {"threshold": 0.8, "required": true, "description": "Truck template to verify map trade location", "scanner_priority": 9, "scanner_enabled": true, "scanner_action": "start_trading"}}, "coordinates": {"1_map_button": {"x": 53, "y": 913, "delay": 1.0, "repeat": 1, "use_esc_key": false, "description": "STEP 1: Click map button to open map trade"}, "2_hidden_treasure": {"x": 1246, "y": 155, "delay": 1.0, "repeat": 1, "use_esc_key": false, "description": "STEP 2: Click hidden treasure"}, "3_trade_area": {"x": 1619, "y": 1363, "delay": 1.0, "repeat": 1, "use_esc_key": false, "description": "STEP 3: Click trade area"}, "14_return_base": {"x": 0, "y": 0, "delay": 1.0, "repeat": 1, "use_esc_key": true, "description": "STEP 14: Return to base (ESC key)"}}, "scan_regions": {"map_trade_area": {"x": 500, "y": 200, "width": 600, "height": 600, "description": "Main map trade interface area"}, "map_pieces_area": {"x": 1000, "y": 750, "width": 600, "height": 300, "description": "Map pieces selection area"}, "truck_detection_area": {"x": 0, "y": 0, "width": 1920, "height": 1080, "description": "Full screen for truck detection"}}, "map_piece_coordinates": {"M1": {"quantity_x": 1068, "quantity_y": 817, "click_x": 1026, "click_y": 765, "description": "Map piece M1 position"}, "M2": {"quantity_x": 1224, "quantity_y": 817, "click_x": 1188, "click_y": 772, "description": "Map piece M2 position"}, "M3": {"quantity_x": 1384, "quantity_y": 820, "click_x": 1342, "click_y": 773, "description": "Map piece M3 position"}, "M4": {"quantity_x": 1533, "quantity_y": 816, "click_x": 1497, "click_y": 773, "description": "Map piece M4 position"}, "M5": {"quantity_x": 1144, "quantity_y": 971, "click_x": 1106, "click_y": 923, "description": "Map piece M5 position"}, "M6": {"quantity_x": 1298, "quantity_y": 970, "click_x": 1265, "click_y": 919, "description": "Map piece M6 position"}, "M7": {"quantity_x": 1457, "quantity_y": 968, "click_x": 1420, "click_y": 924, "description": "Map piece M7 position"}}}, "alliance_donation": {"enabled": true, "priority": 3, "cooldown": 600.0, "manual_trigger": false, "settings": {}, "templates": {"alliance_button": {"threshold": 0.8, "required": true, "description": "Alliance button in bottom menu", "scanner_priority": 12, "scanner_enabled": false, "scanner_action": "start_sequence"}}, "coordinates": {"1_Alliance": {"x": 1217, "y": 523, "delay": 0.7, "repeat": 1, "use_esc_key": false, "description": "Click Alliance main button"}, "2_gift": {"x": 548, "y": 415, "delay": 1.0, "repeat": 1, "use_esc_key": false, "description": "Click gifts"}, "Donate_button": {"x": 708, "y": 558, "delay": 0.1, "repeat": 15, "use_esc_key": false, "description": "Fast donation clicks (15x)"}, "exit": {"x": 796, "y": 153, "delay": 2.0, "repeat": 1, "use_esc_key": true, "description": "Close donation menu with ESC key"}}, "scan_regions": {"alliance_tabs": {"x": 200, "y": 100, "width": 400, "height": 100, "description": "Alliance tabs area"}, "donation_area": {"x": 150, "y": 200, "width": 500, "height": 300, "description": "Donation interface area"}, "confirmation": {"x": 300, "y": 250, "width": 200, "height": 150, "description": "Confirmation dialog area"}}}}, "template_exclusion_rules": [{"name": "help_priority_rule", "description": "If Help_Template is detected, ignore Help_chat", "primary_template": "Help_Template", "excluded_templates": ["Help_chat"], "enabled": true}, {"name": "truck_priority_rule", "description": "Golden trucks have priority over purple trucks", "primary_template": "Golden_Truck", "excluded_templates": ["Purple_Truck"], "enabled": true}]}