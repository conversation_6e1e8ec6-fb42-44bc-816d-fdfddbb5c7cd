#!/usr/bin/env python3
"""
Test script for the unified configuration system
"""

from unified_config_manager import UnifiedConfigManager
import json

def test_unified_config():
    """Test the unified configuration system"""
    print("Testing Unified Configuration System")
    print("=" * 50)
    
    try:
        # Initialize config manager
        config_manager = UnifiedConfigManager()
        
        # Load configuration (will migrate if needed)
        print("Loading configuration...")
        config_data = config_manager.load_config()
        
        # Display basic info
        print(f"Configuration version: {config_data.get('config_version', 'Unknown')}")
        print(f"Last updated: {config_data.get('last_updated', 'Unknown')}")
        print(f"Number of modules: {len(config_data.get('modules', {}))}")
        
        # List modules
        print("\nAvailable modules:")
        for module_name, module_config in config_data.get('modules', {}).items():
            enabled = "✓" if module_config.get('enabled', False) else "✗"
            priority = module_config.get('priority', 0)
            template_count = len(module_config.get('templates', {}))
            coord_count = len(module_config.get('coordinates', {}))
            
            print(f"  {enabled} {module_name} (priority: {priority}, templates: {template_count}, coords: {coord_count})")
        
        # Test system settings
        print("\nSystem settings:")
        system_settings = config_data.get('system_settings', {})
        for key, value in system_settings.items():
            print(f"  {key}: {value}")
        
        # Test global coordinates
        print("\nGlobal coordinates:")
        global_coords = config_data.get('global_coordinates', {})
        for category, coords in global_coords.items():
            print(f"  {category}: {len(coords)} coordinates")
            for coord_name, coord_data in coords.items():
                print(f"    {coord_name}: ({coord_data.get('x', 0)}, {coord_data.get('y', 0)})")
        
        # Test specific module details
        print("\nDetailed module test (dig module):")
        dig_config = config_manager.get_module_config('dig')
        if dig_config:
            print(f"  Enabled: {dig_config.get('enabled', False)}")
            print(f"  Priority: {dig_config.get('priority', 0)}")
            print(f"  Templates: {list(dig_config.get('templates', {}).keys())}")
            print(f"  Coordinates: {list(dig_config.get('coordinates', {}).keys())}")
        else:
            print("  Dig module not found")
        
        # Test template exclusion rules
        print("\nTemplate exclusion rules:")
        exclusion_rules = config_data.get('template_exclusion_rules', [])
        for rule in exclusion_rules:
            print(f"  {rule.get('name', 'Unnamed')}: {rule.get('description', 'No description')}")
        
        print("\n✓ Configuration system test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n✗ Configuration system test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_modification():
    """Test configuration modification"""
    print("\nTesting configuration modification...")
    
    try:
        config_manager = UnifiedConfigManager()
        config_data = config_manager.load_config()
        
        # Test updating system settings
        original_scan_interval = config_data.get('system_settings', {}).get('scan_interval', 0.1)
        print(f"Original scan interval: {original_scan_interval}")
        
        # Update system settings
        new_settings = {'scan_interval': 0.2, 'test_setting': 'test_value'}
        config_manager.update_system_settings(new_settings)
        
        # Verify update
        updated_settings = config_manager.get_system_settings()
        print(f"Updated scan interval: {updated_settings.get('scan_interval')}")
        print(f"Test setting: {updated_settings.get('test_setting')}")
        
        # Test module configuration update
        if 'dig' in config_data.get('modules', {}):
            dig_config = config_manager.get_module_config('dig')
            original_priority = dig_config.get('priority', 0)
            print(f"Original dig priority: {original_priority}")
            
            # Update module config
            dig_config['priority'] = -2
            dig_config['test_field'] = 'test_value'
            config_manager.update_module_config('dig', dig_config)
            
            # Verify update
            updated_dig = config_manager.get_module_config('dig')
            print(f"Updated dig priority: {updated_dig.get('priority')}")
            print(f"Test field: {updated_dig.get('test_field')}")
        
        print("✓ Configuration modification test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Configuration modification test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Unified Configuration System Test Suite")
    print("=" * 60)
    
    # Run tests
    test1_passed = test_unified_config()
    test2_passed = test_config_modification()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"  Basic Configuration Test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"  Configuration Modification Test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Unified configuration system is working correctly.")
    else:
        print("\n❌ SOME TESTS FAILED! Please check the errors above.")
