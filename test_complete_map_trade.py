#!/usr/bin/env python3
"""
Complete Map Trade System Test
"""

import cv2
import numpy as np
import pyautogui
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class CompleteMapTradeTest:
    def __init__(self):
        # Your calibrated coordinates
        self.map_pieces = {
            'M1': {'quantity_coord': (1068, 817), 'click_coord': (1026, 765)},
            'M2': {'quantity_coord': (1224, 817), 'click_coord': (1188, 772)},
            'M3': {'quantity_coord': (1384, 820), 'click_coord': (1342, 773)},
            'M4': {'quantity_coord': (1533, 816), 'click_coord': (1497, 773)},
            'M5': {'quantity_coord': (1144, 971), 'click_coord': (1106, 923)},  # Corrected
            'M6': {'quantity_coord': (1298, 970), 'click_coord': (1265, 919)},  # Corrected
            'M7': {'quantity_coord': (1457, 968), 'click_coord': (1420, 924)},
        }
        
        # UI coordinates
        self.lose_area_coord = (1448, 468)
        self.initiate_coord = (1258, 1145)
        self.confirm_coord = (1258, 933)  # Captured exact coordinate
    
    def detect_screen_state(self):
        """Detect current screen state"""
        try:
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            
            # OCR to find text indicators
            text = pytesseract.image_to_string(gray).lower()
            
            if "cancel exchange" in text:
                return "trade_in_progress"
            elif "initiate exchange" in text:
                return "ready_to_trade"
            elif "confirm" in text and "exchange" in text:
                return "confirmation_dialog"
            else:
                return "unknown"
                
        except Exception as e:
            print(f"Error detecting screen state: {e}")
            return "unknown"
    
    def read_all_quantities(self):
        """Read quantities from all 7 map pieces"""
        quantities = {}
        
        print("Reading all map piece quantities...")
        
        for map_name, coords in self.map_pieces.items():
            qty_x, qty_y = coords['quantity_coord']
            
            try:
                # Capture 35x35 region
                region_x = qty_x - 17
                region_y = qty_y - 17
                
                screenshot = pyautogui.screenshot(region=(region_x, region_y, 35, 35))
                image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                
                # Direct OCR
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(gray, config=config).strip()
                
                if text.isdigit():
                    quantities[map_name] = int(text)
                    print(f"  ✅ {map_name}: {text} pieces")
                else:
                    print(f"  ❌ {map_name}: Failed to read")
                    
            except Exception as e:
                print(f"  ❌ {map_name}: Error - {e}")
        
        return quantities
    
    def find_optimal_trade(self, quantities):
        """Find optimal trade: lowest → highest"""
        if not quantities:
            return None, None
        
        lowest_map = min(quantities.keys(), key=lambda k: quantities[k])
        highest_map = max(quantities.keys(), key=lambda k: quantities[k])
        
        return lowest_map, highest_map
    
    def execute_trade_sequence(self, give_map, receive_map):
        """Execute the complete trade sequence"""
        print(f"\n🚀 Executing trade: {give_map} → {receive_map}")
        
        try:
            # Step 1: Click map piece to give
            give_x, give_y = self.map_pieces[give_map]['click_coord']
            print(f"1. Clicking {give_map} at ({give_x}, {give_y})")
            pyautogui.click(give_x, give_y)
            time.sleep(0.5)
            
            # Step 2: Click "you will lose" area
            lose_x, lose_y = self.lose_area_coord
            print(f"2. Clicking 'you will lose' at ({lose_x}, {lose_y})")
            pyautogui.click(lose_x, lose_y)
            time.sleep(0.5)
            
            # Step 3: Click map piece to receive
            receive_x, receive_y = self.map_pieces[receive_map]['click_coord']
            print(f"3. Clicking {receive_map} at ({receive_x}, {receive_y})")
            pyautogui.click(receive_x, receive_y)
            time.sleep(0.5)
            
            # Step 4: Click initiate exchange
            initiate_x, initiate_y = self.initiate_coord
            print(f"4. Clicking 'Initiate Exchange' at ({initiate_x}, {initiate_y})")
            pyautogui.click(initiate_x, initiate_y)
            time.sleep(1.0)
            
            # Step 5: Click confirmation
            confirm_x, confirm_y = self.confirm_coord
            print(f"5. Clicking 'Confirm' at ({confirm_x}, {confirm_y})")
            pyautogui.click(confirm_x, confirm_y)
            
            print("✅ Trade sequence executed!")
            return True
            
        except Exception as e:
            print(f"❌ Error executing trade: {e}")
            return False
    
    def run_complete_test(self):
        """Run the complete intelligent map trade test"""
        print("Complete Intelligent Map Trade Test")
        print("=" * 50)
        
        # Step 1: Detect screen state
        print("\n1. Detecting screen state...")
        state = self.detect_screen_state()
        print(f"   Screen state: {state}")
        
        if state == "trade_in_progress":
            print("   Trade already in progress - waiting for completion")
            return
        elif state != "ready_to_trade":
            print("   Navigate to initiate exchange screen first")
            return
        
        # Step 2: Read all quantities
        print("\n2. Reading map piece quantities...")
        quantities = self.read_all_quantities()
        
        if not quantities:
            print("   ❌ Failed to read any quantities")
            return
        
        print(f"   Successfully read {len(quantities)}/7 quantities")
        
        # Step 3: Find optimal trade
        print("\n3. Finding optimal trade...")
        give_map, receive_map = self.find_optimal_trade(quantities)
        
        if not give_map or not receive_map:
            print("   ❌ Could not determine optimal trade")
            return
        
        give_qty = quantities[give_map]
        receive_qty = quantities[receive_map]
        
        print(f"   🎯 Optimal trade:")
        print(f"      Give: {give_map} ({give_qty} pieces) ← LOWEST")
        print(f"      Receive: {receive_map} ({receive_qty} pieces) ← HIGHEST")
        
        # Step 4: Execute trade
        execute = input("\n4. Execute the trade? (y/n): ").lower().strip()
        
        if execute == 'y':
            success = self.execute_trade_sequence(give_map, receive_map)
            if success:
                print("\n🎉 Intelligent map trade completed successfully!")
            else:
                print("\n❌ Trade execution failed")
        else:
            print("\n   Trade cancelled by user")

def main():
    """Main function"""
    try:
        trader = CompleteMapTradeTest()
        trader.run_complete_test()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
