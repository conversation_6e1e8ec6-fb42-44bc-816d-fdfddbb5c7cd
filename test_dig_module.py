"""
Test script for the Dig Module
Tests the dig module functionality without running the full automation
"""

import sys
import os
import logging

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.dig import DigModule

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_dig_module():
    """Test the dig module initialization and basic functionality"""
    print("🏗️ Testing Dig Module...")
    
    # Create dig module instance
    dig_module = DigModule()
    
    print(f"✅ Module created: {dig_module.name}")
    print(f"✅ Priority: {dig_module.priority}")
    print(f"✅ Enabled: {dig_module.enabled}")
    print(f"✅ Rapid click speed: {dig_module.rapid_click_speed}")
    print(f"✅ Thank you message: '{dig_module.thank_you_message}'")
    print(f"✅ Current status: {dig_module.get_dig_status()}")
    
    # Test required templates
    required_templates = dig_module.get_required_templates()
    print(f"✅ Required templates ({len(required_templates)}):")
    for template in required_templates:
        print(f"   - {template}")
    
    # Test configuration methods
    print("\n🔧 Testing configuration methods...")
    
    # Test rapid click speed setting
    original_speed = dig_module.rapid_click_speed
    dig_module.set_rapid_click_speed(0.01)
    print(f"✅ Rapid click speed changed: {original_speed} -> {dig_module.rapid_click_speed}")
    
    # Test thank you message setting
    original_message = dig_module.thank_you_message
    dig_module.set_thank_you_message("Test message for dig completion!")
    print(f"✅ Thank you message changed: '{original_message}' -> '{dig_module.thank_you_message}'")
    
    # Test can_execute with empty screen data
    screen_data = {
        'templates_found': {},
        'templates_detected': {}
    }
    
    can_execute = dig_module.custom_can_execute(screen_data)
    print(f"✅ Can execute with empty screen: {can_execute}")
    
    # Test can_execute with dig trigger
    screen_data_with_trigger = {
        'templates_found': {
            'dig_icon': [{'x': 100, 'y': 100, 'w': 50, 'h': 50}]
        },
        'templates_detected': {
            'dig_icon': True
        }
    }
    
    can_execute_with_trigger = dig_module.custom_can_execute(screen_data_with_trigger)
    print(f"✅ Can execute with dig trigger: {can_execute_with_trigger}")
    
    print("\n✅ All tests passed! Dig module is ready for use.")
    print("\n📋 Next steps:")
    print("1. Add the required template images to the templates/ folder")
    print("2. Configure template detection thresholds in the GUI")
    print("3. Test with actual game screenshots")
    print("4. Adjust coordinates and timing as needed")

if __name__ == "__main__":
    setup_logging()
    test_dig_module()
