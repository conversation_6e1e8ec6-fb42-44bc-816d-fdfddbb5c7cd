#!/usr/bin/env python3
"""
Test script to verify MapTradeModule can be imported and instantiated
"""
import sys
import os

def test_map_trade_import():
    """Test importing and instantiating MapTradeModule"""
    print("🧪 Testing MapTradeModule Import...")
    
    try:
        # Add modules to path
        sys.path.append('modules')
        
        # Test import
        print("1. Testing import...")
        from map_trade import MapTradeModule
        print("✅ Import successful")
        
        # Test instantiation
        print("2. Testing instantiation...")
        module = MapTradeModule()
        print("✅ Instantiation successful")
        
        # Test basic functionality
        print("3. Testing basic functionality...")
        
        # Check if coordinate regions are loaded
        if hasattr(module, 'map_piece_regions') and module.map_piece_regions:
            print(f"✅ Map piece regions loaded: {len(module.map_piece_regions)} pieces")
        else:
            print("⚠️ No map piece regions loaded (this is OK if config not set)")
        
        # Check if OCR methods exist
        if hasattr(module, '_extract_number_from_image'):
            print("✅ OCR methods available")
        else:
            print("❌ OCR methods missing")
            return False
        
        # Check if batch processing methods exist
        if hasattr(module, '_read_map_quantities_batch'):
            print("✅ Batch processing methods available")
        else:
            print("❌ Batch processing methods missing")
            return False
        
        print("✅ All tests passed!")
        return True
        
    except IndentationError as e:
        print(f"❌ Indentation Error: {e}")
        return False
    except SyntaxError as e:
        print(f"❌ Syntax Error: {e}")
        return False
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_main_app_import():
    """Test importing the main application"""
    print("\n🧪 Testing Main Application Import...")
    
    try:
        print("1. Testing Lastwar_helper_v1.py import...")
        import Lastwar_helper_v1
        print("✅ Main application imported successfully")
        return True
        
    except IndentationError as e:
        print(f"❌ Indentation Error in main app: {e}")
        return False
    except ImportError as e:
        print(f"❌ Import Error in main app: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing main app: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Map Trade Module Import Test")
    print("=" * 40)
    
    # Test MapTradeModule
    map_trade_success = test_map_trade_import()
    
    # Test main application
    main_app_success = test_main_app_import()
    
    print("\n📊 TEST RESULTS")
    print("=" * 40)
    print(f"MapTradeModule: {'✅ PASS' if map_trade_success else '❌ FAIL'}")
    print(f"Main Application: {'✅ PASS' if main_app_success else '❌ FAIL'}")
    
    if map_trade_success and main_app_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Indentation error fixed")
        print("✅ MapTradeModule loads correctly")
        print("✅ Main application can start")
        print("✅ OCR improvements preserved")
    else:
        print("\n⚠️ Some tests failed - check errors above")
