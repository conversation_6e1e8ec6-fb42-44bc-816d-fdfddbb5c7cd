#!/usr/bin/env python3
"""
Test different capture sizes to see what works best
"""
import cv2
import numpy as np
import pytesseract
import pyautogui

def test_different_sizes():
    """Test different capture sizes at current mouse position"""
    
    # Configure Tesseract
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    print("=== CAPTURE SIZE TESTING ===")
    print("Position your mouse over a number and press ENTER")
    input("Press ENTER when ready: ")
    
    # Get mouse position
    x, y = pyautogui.position()
    print(f"Testing at position: ({x}, {y})")
    
    # Test different sizes
    sizes_to_test = [25, 35, 50, 75, 100, 150]
    
    for size in sizes_to_test:
        print(f"\n--- Testing {size}x{size} pixel capture ---")
        
        try:
            # Capture region
            region_x = x - size // 2
            region_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Save the captured image so you can see what it looks like
            cv2.imwrite(f"capture_{size}x{size}.png", image)
            print(f"  Saved: capture_{size}x{size}.png")
            
            # Test with different scaling and methods
            methods_to_test = [
                ("Original", gray, 1),
                ("2x scaled", cv2.resize(gray, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC), 2),
                ("3x scaled", cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC), 3),
            ]
            
            best_results = []
            
            for method_name, processed_image, scale in methods_to_test:
                # Save processed image
                cv2.imwrite(f"capture_{size}x{size}_{method_name.replace(' ', '_')}.png", processed_image)
                
                # Test with PSM 8 (most reliable)
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(processed_image, config=config).strip()
                
                if text.isdigit():
                    result = f"  ✅ {method_name}: '{text}'"
                    best_results.append(f"{method_name}: {text}")
                elif text:
                    result = f"  ❌ {method_name}: '{text}' (not digit)"
                else:
                    result = f"  ❌ {method_name}: (empty)"
                
                print(result)
            
            if best_results:
                print(f"  🎉 WORKING: {', '.join(best_results)}")
            else:
                print(f"  💀 NO METHODS WORKED")
                
        except Exception as e:
            print(f"  ERROR: {e}")
    
    print(f"\n=== SUMMARY ===")
    print("Check the saved images to see what each size captures:")
    for size in sizes_to_test:
        print(f"  capture_{size}x{size}.png - Shows the {size}x{size} region")
        print(f"  capture_{size}x{size}_Original.png - Grayscale version")
        print(f"  capture_{size}x{size}_2x_scaled.png - 2x scaled version")
        print(f"  capture_{size}x{size}_3x_scaled.png - 3x scaled version")
    
    print(f"\nRecommendation: Use the smallest size that consistently captures the number")

def test_single_size():
    """Test a specific size interactively"""
    print("=== SINGLE SIZE TESTING ===")
    
    # Configure Tesseract
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    while True:
        try:
            size = int(input("Enter capture size (e.g., 50): "))
            break
        except ValueError:
            print("Please enter a valid number")
    
    print(f"Testing {size}x{size} capture")
    print("Position mouse and press ENTER to test")
    
    while True:
        input("Press ENTER to capture: ")
        
        # Get mouse position
        x, y = pyautogui.position()
        print(f"Capturing {size}x{size} at ({x}, {y})")
        
        try:
            # Capture region
            region_x = x - size // 2
            region_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Save images
            cv2.imwrite(f"test_capture_{size}.png", image)
            print(f"  Saved: test_capture_{size}.png")
            
            # Test OCR methods
            methods = [
                ("Original", gray),
                ("2x", cv2.resize(gray, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)),
                ("3x", cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)),
                ("4x", cv2.resize(gray, None, fx=4, fy=4, interpolation=cv2.INTER_CUBIC)),
            ]
            
            for method_name, processed in methods:
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(processed, config=config).strip()
                
                if text.isdigit():
                    print(f"  ✅ {method_name}: {text}")
                elif text:
                    print(f"  ❌ {method_name}: '{text}'")
                else:
                    print(f"  ❌ {method_name}: (empty)")
            
        except Exception as e:
            print(f"  ERROR: {e}")
        
        again = input("Test another position? (y/n): ").lower().strip()
        if again != 'y':
            break

if __name__ == "__main__":
    print("Choose testing mode:")
    print("1. Test multiple sizes at one position")
    print("2. Test single size at multiple positions")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        test_different_sizes()
    elif choice == "2":
        test_single_size()
    else:
        print("Invalid choice")
