"""
Enhanced Help Click Module for Last War automation
Streamlined for centralized scanner - no internal image detection
"""
import time
import pya<PERSON><PERSON><PERSON>
from typing import Dict, Any

from enhanced_base_module import EnhancedBaseModule


class EnhancedHelpClickModule(EnhancedBaseModule):
    """Enhanced help click module - relies on centralized scanner for detection"""

    def __init__(self):
        super().__init__(name="help_click", priority=0, enabled=True)

        # Settings
        self.click_delay = 0.5
        self.max_helps_per_cycle = 20

        # Region names for coordinate validation
        self.main_help_region = "Help_region"
        self.chat_help_coordinate = "Help_chatbutton"
        
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        This module is now controlled by centralized scanner only
        Legacy method - always returns False since centralized scanner handles execution
        """
        self.logger.debug("⏸️ HELP_CLICK: Legacy can_execute called - centralized scanner handles execution")
        return False

    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Legacy execute method - centralized scanner calls action methods directly
        """
        self.logger.debug("⏸️ HELP_CLICK: Legacy execute called - centralized scanner handles execution")
        return False

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Legacy method required by base class - always returns False since centralized scanner handles execution
        """
        self.logger.debug("⏸️ HELP_CLICK: Legacy custom_can_execute called - centralized scanner handles execution")
        return False

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Legacy method required by base class - centralized scanner calls action methods directly
        """
        self.logger.debug("⏸️ HELP_CLICK: Legacy custom_execute called - centralized scanner handles execution")
        return False

    # Legacy methods removed - centralized scanner handles all execution

    def _is_position_in_region(self, x: int, y: int, region_name: str) -> bool:
        """Check if a position is within a specified region"""
        try:
            region = self.config_manager.get_region_by_name(self.name, region_name)
            if not region:
                self.logger.debug(f"Region '{region_name}' not found, allowing click")
                return True  # If no region defined, allow click

            region_x, region_y, region_w, region_h = region

            # Check if position is within region bounds
            in_region = (region_x <= x <= region_x + region_w and
                        region_y <= y <= region_y + region_h)

            self.logger.debug(f"Position ({x}, {y}) in region {region_name} ({region_x}, {region_y}, {region_w}x{region_h}): {in_region}")
            return in_region

        except Exception as e:
            self.logger.error(f"Error checking position in region: {str(e)}")
            return True  # Default to allowing click

    def get_cooldown(self) -> float:
        """Get cooldown time for help clicking"""
        return 0.0  # No cooldown - always ready to help!

    def get_required_templates(self) -> list:
        """Return list of templates required by this module"""
        return ['Help_template', 'Help_chat', 'help_world']

    # ===== CENTRALIZED SCANNER ACTION METHODS =====

    def _handle_centralized_main_help(self, screen_data: Dict[str, Any]) -> int:
        """Handle help buttons for centralized scanner - supports multiple help templates"""
        helps_clicked = 0
        templates_found = screen_data.get('templates_found', {})

        # Debug: Show what templates are available
        self.logger.info(f"DEBUG: Available templates in screen_data: {list(templates_found.keys())}")

        # Check for all possible help templates
        help_templates = ["Help_template", "help_world"]
        self.logger.info(f"DEBUG: Looking for templates: {help_templates}")

        for template_name in help_templates:
            if template_name in templates_found:
                matches = templates_found[template_name]
                self.logger.info(f"[FOUND] {len(matches)} help buttons for template '{template_name}'")

                # Click each help button found
                for i, match in enumerate(matches):
                    if helps_clicked >= self.max_helps_per_cycle:
                        self.logger.info("Reached maximum helps per cycle")
                        break

                    try:
                        x, y, w, h = match
                        click_x = x + w // 2
                        click_y = y + h // 2

                        # Check if this position is within the help region
                        if self._is_position_in_region(click_x, click_y, self.main_help_region):
                            self.logger.info(f"[CLICK] Help button {i+1} at ({click_x}, {click_y}) for template '{template_name}'")
                            pyautogui.click(click_x, click_y)
                            helps_clicked += 1
                            time.sleep(self.click_delay)
                        else:
                            self.logger.debug(f"Help button at ({click_x}, {click_y}) outside help region")

                    except Exception as e:
                        self.logger.error(f"Error clicking help button {i+1}: {str(e)}")
                        continue
            else:
                self.logger.debug(f"Template '{template_name}' not found in screen data")

        if helps_clicked == 0:
            self.logger.info("ℹ️ No help buttons found to click")

        return helps_clicked

    def _handle_centralized_chat_help(self, screen_data: Dict[str, Any]) -> int:
        """Handle chat help buttons for centralized scanner"""
        helps_clicked = 0
        templates_found = screen_data.get('templates_found', {})

        if "Help_chat" in templates_found:
            matches = templates_found["Help_chat"]
            self.logger.info(f"[FOUND] {len(matches)} chat help indicators")

            # If chat help is detected, click the configured chat help coordinate
            if matches:
                try:
                    coordinate = self.config_manager.get_coordinate_by_name(self.name, self.chat_help_coordinate)

                    if coordinate:
                        x, y = coordinate
                        self.logger.info(f"[CLICK] Chat help button at ({x}, {y})")
                        pyautogui.click(x, y)
                        helps_clicked = 1
                        time.sleep(self.click_delay)
                    else:
                        self.logger.warning(f"Chat help coordinate '{self.chat_help_coordinate}' not found in config")

                except Exception as e:
                    self.logger.error(f"Error clicking chat help button: {str(e)}")
        else:
            self.logger.debug("No Help_chat template found in screen data")

        return helps_clicked

    def action_1_HelpButton(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for Help_template/help_world detection - main help buttons"""
        try:
            self.logger.info("[CENTRALIZED] Help template detected - clicking main help buttons")
            helps_clicked = self._handle_centralized_main_help(screen_data)
            success = helps_clicked > 0
            self.logger.info(f"[CENTRALIZED] Main help action completed - {helps_clicked} helps clicked")
            return success
        except Exception as e:
            self.logger.error(f"[ERROR] CENTRALIZED: Error in action_1_HelpButton: {e}")
            return False

    def action_2_HelpChat(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for Help_chat detection - chat help buttons"""
        try:
            self.logger.info("🎯 CENTRALIZED: Help_chat detected - clicking chat help buttons")
            helps_clicked = self._handle_centralized_chat_help(screen_data)
            success = helps_clicked > 0
            self.logger.info(f"✅ CENTRALIZED: Chat help action completed - {helps_clicked} helps clicked")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in action_2_HelpChat: {e}")
            return False
