#!/usr/bin/env python3
"""
Quit Game Template Capture Tool
Captures a template image for detecting when the game is about to quit
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import time

class QuitTemplateCaptureGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Quit Game Template Capture Tool")
        self.root.geometry("600x500")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.captured_image = None
        self.preview_label = None
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI interface"""
        # Title
        title_label = tk.Label(self.root, text="🚪 Quit Game Template Capture", 
                              font=('Arial', 16, 'bold'), 
                              fg='white', bg='#2b2b2b')
        title_label.pack(pady=10)
        
        # Instructions
        instructions = """
Instructions:
1. Navigate to the quit/exit screen in your game
2. Click 'Capture Screen' to take a screenshot
3. Select the quit dialog/button area by dragging
4. Save the template for quit detection
5. The system will press ESC when this template is detected
        """
        
        inst_label = tk.Label(self.root, text=instructions, 
                             font=('Arial', 10), 
                             fg='#cccccc', bg='#2b2b2b',
                             justify='left')
        inst_label.pack(pady=10)
        
        # Buttons frame
        button_frame = tk.Frame(self.root, bg='#2b2b2b')
        button_frame.pack(pady=20)
        
        # Capture button
        capture_btn = tk.Button(button_frame, text="📸 Capture Screen", 
                               command=self.capture_screen,
                               font=('Arial', 12, 'bold'),
                               bg='#4CAF50', fg='white',
                               padx=20, pady=10)
        capture_btn.pack(side=tk.LEFT, padx=10)
        
        # Save button
        save_btn = tk.Button(button_frame, text="💾 Save Template", 
                            command=self.save_template,
                            font=('Arial', 12, 'bold'),
                            bg='#2196F3', fg='white',
                            padx=20, pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # Preview frame
        preview_frame = tk.Frame(self.root, bg='#2b2b2b')
        preview_frame.pack(pady=20, fill=tk.BOTH, expand=True)
        
        preview_title = tk.Label(preview_frame, text="Template Preview:", 
                                font=('Arial', 12, 'bold'), 
                                fg='white', bg='#2b2b2b')
        preview_title.pack()
        
        # Preview label for image
        self.preview_label = tk.Label(preview_frame, bg='#2b2b2b')
        self.preview_label.pack(pady=10)
        
        # Status label
        self.status_label = tk.Label(self.root, text="Ready to capture quit template", 
                                    font=('Arial', 10), 
                                    fg='#4CAF50', bg='#2b2b2b')
        self.status_label.pack(pady=10)
        
    def capture_screen(self):
        """Capture screen and allow user to select quit dialog area"""
        try:
            self.status_label.config(text="Minimizing window in 3 seconds...", fg='orange')
            self.root.update()
            time.sleep(3)
            
            # Minimize window
            self.root.withdraw()
            time.sleep(0.5)
            
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            # Restore window
            self.root.deiconify()
            
            # Open selection window
            self.open_selection_window(screenshot_cv)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screen: {str(e)}")
            self.status_label.config(text="Capture failed", fg='red')
    
    def open_selection_window(self, screenshot):
        """Open window for selecting quit dialog area"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title("Select Quit Dialog Area")
        selection_window.configure(bg='#2b2b2b')
        
        # Convert screenshot for display
        screenshot_rgb = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        screenshot_pil = Image.fromarray(screenshot_rgb)
        
        # Resize for display if too large
        display_width = 1200
        display_height = 800
        if screenshot_pil.width > display_width or screenshot_pil.height > display_height:
            screenshot_pil.thumbnail((display_width, display_height), Image.Resampling.LANCZOS)
        
        screenshot_tk = ImageTk.PhotoImage(screenshot_pil)
        
        # Create canvas for selection
        canvas = tk.Canvas(selection_window, 
                          width=screenshot_pil.width, 
                          height=screenshot_pil.height,
                          bg='black')
        canvas.pack(padx=10, pady=10)
        
        # Display screenshot
        canvas.create_image(0, 0, anchor=tk.NW, image=screenshot_tk)
        
        # Selection variables
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        
        def on_mouse_press(event):
            self.selection_start = (event.x, event.y)
            if self.selection_rect:
                canvas.delete(self.selection_rect)
        
        def on_mouse_drag(event):
            if self.selection_start:
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
                self.selection_rect = canvas.create_rectangle(
                    self.selection_start[0], self.selection_start[1],
                    event.x, event.y,
                    outline='red', width=2
                )
        
        def on_mouse_release(event):
            self.selection_end = (event.x, event.y)
            
            if self.selection_start and self.selection_end:
                # Calculate selection area
                x1, y1 = self.selection_start
                x2, y2 = self.selection_end
                
                # Ensure correct order
                x1, x2 = min(x1, x2), max(x1, x2)
                y1, y2 = min(y1, y2), max(y1, y2)
                
                # Scale back to original screenshot size
                scale_x = screenshot.shape[1] / screenshot_pil.width
                scale_y = screenshot.shape[0] / screenshot_pil.height
                
                orig_x1 = int(x1 * scale_x)
                orig_y1 = int(y1 * scale_y)
                orig_x2 = int(x2 * scale_x)
                orig_y2 = int(y2 * scale_y)
                
                # Extract template
                template = screenshot[orig_y1:orig_y2, orig_x1:orig_x2]
                
                if template.size > 0:
                    self.captured_image = template
                    self.update_preview()
                    selection_window.destroy()
                    self.status_label.config(text="Template captured! Click 'Save Template' to save.", fg='green')
                else:
                    messagebox.showerror("Error", "Invalid selection area")
        
        # Bind mouse events
        canvas.bind("<Button-1>", on_mouse_press)
        canvas.bind("<B1-Motion>", on_mouse_drag)
        canvas.bind("<ButtonRelease-1>", on_mouse_release)
        
        # Instructions
        inst_label = tk.Label(selection_window, 
                             text="Drag to select the quit dialog/button area", 
                             font=('Arial', 12), 
                             fg='white', bg='#2b2b2b')
        inst_label.pack(pady=5)
        
        # Keep reference to prevent garbage collection
        canvas.image = screenshot_tk
    
    def update_preview(self):
        """Update the preview with captured template"""
        if self.captured_image is not None:
            # Convert for display
            preview_img = cv2.cvtColor(self.captured_image, cv2.COLOR_BGR2RGB)
            preview_pil = Image.fromarray(preview_img)
            
            # Resize for preview if too large
            max_preview_size = 300
            if preview_pil.width > max_preview_size or preview_pil.height > max_preview_size:
                preview_pil.thumbnail((max_preview_size, max_preview_size), Image.Resampling.LANCZOS)
            
            preview_tk = ImageTk.PhotoImage(preview_pil)
            
            self.preview_label.config(image=preview_tk)
            self.preview_label.image = preview_tk  # Keep reference
    
    def save_template(self):
        """Save the captured template"""
        if self.captured_image is None:
            messagebox.showerror("Error", "No template captured. Please capture a template first.")
            return
        
        try:
            # Ensure templates directory exists
            templates_dir = "templates"
            if not os.path.exists(templates_dir):
                os.makedirs(templates_dir)
            
            # Default filename
            default_filename = "quit_game_dialog.png"
            filepath = os.path.join(templates_dir, default_filename)
            
            # Ask user for filename
            filepath = filedialog.asksaveasfilename(
                initialdir=templates_dir,
                initialfile=default_filename,
                title="Save Quit Template",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
                defaultextension=".png"
            )
            
            if filepath:
                # Save template
                cv2.imwrite(filepath, self.captured_image)
                
                # Show success message
                messagebox.showinfo("Success", 
                                   f"Template saved successfully!\n\n"
                                   f"File: {os.path.basename(filepath)}\n"
                                   f"Size: {self.captured_image.shape[1]}x{self.captured_image.shape[0]}\n\n"
                                   f"The template will be used to detect quit dialogs and automatically press ESC.")
                
                self.status_label.config(text=f"Template saved: {os.path.basename(filepath)}", fg='green')
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save template: {str(e)}")
            self.status_label.config(text="Save failed", fg='red')
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("Starting Quit Game Template Capture Tool...")
    app = QuitTemplateCaptureGUI()
    app.run()
