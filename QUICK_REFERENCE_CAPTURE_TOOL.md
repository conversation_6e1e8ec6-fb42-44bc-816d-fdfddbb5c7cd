# Universal Capture Tool - Quick Reference

## **🚀 LAUNCH:**
```bash
python universal_capture_tool.py
```

## **📸 TEMPLATE CAPTURE:**
1. Select "📸 Template" mode
2. Click "📸 Capture Template"
3. Drag to select UI element
4. Enter template name
5. Template saved to templates/ folder

## **📍 COORDINATE CAPTURE:**
1. Select "📍 Coordinate" mode  
2. Click "📍 Capture Coordinate"
3. Click exact point
4. Enter description
5. Coordinates saved with description

## **📐 REGION CAPTURE:**
1. Select "📐 Region" mode
2. Click "📐 Capture Region"
3. Drag to select area
4. Enter description
5. Region bounds saved with dimensions

## **💾 EXPORT RESULTS:**
- Click "💾 Export Results"
- Choose filename
- JSON file with all captured data
- Share file for precise specifications

## **🎯 WORKFLOW:**
1. **Capture what you need** (templates/coordinates/regions)
2. **Review in tabs** (organized by type)
3. **Export to JSON** (complete specifications)
4. **Share the file** (precise requirements)

## **📊 RESULTS FORMAT:**
- **Templates:** Name, PNG file, bounds, size, timestamp
- **Coordinates:** X,Y position, description, timestamp  
- **Regions:** Bounds, dimensions, description, timestamp
- **Export:** Complete JSON with all data

**Perfect for specifying exact automation requirements!** 🛠️✨
