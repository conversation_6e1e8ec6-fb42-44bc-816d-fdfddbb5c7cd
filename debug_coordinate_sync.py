#!/usr/bin/env python3
"""
Debug coordinate sync issue between calibration and reading
"""

import cv2
import numpy as np
import pyautogui
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def debug_coordinate_reading():
    """Debug exactly what happens when we read a coordinate"""
    print("Coordinate Reading Debug")
    print("=" * 30)
    
    print("1. Position your mouse over a quantity number you can see clearly")
    input("Press Enter to capture current mouse position...")
    
    # Get exact mouse position
    mouse_x, mouse_y = pyautogui.position()
    print(f"Mouse position: ({mouse_x}, {mouse_y})")
    
    # Test different region sizes and offsets
    sizes = [10, 20, 30, 40]
    
    for size in sizes:
        print(f"\nTesting {size}x{size} region:")
        
        # Calculate region (centered on mouse)
        region_x = mouse_x - size // 2
        region_y = mouse_y - size // 2
        
        print(f"  Region: ({region_x}, {region_y}, {size}, {size})")
        
        try:
            # Capture region
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Save debug image
            cv2.imwrite(f"debug_region_{size}x{size}.png", gray)
            
            # Try OCR
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(gray, config=config).strip()
            
            if text.isdigit():
                print(f"  ✅ OCR Success: {text}")
            else:
                print(f"  ❌ OCR Failed: '{text}'")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    print(f"\nDebug images saved: debug_region_*.png")
    print("Check these images to see what the system is actually capturing")

def test_exact_coordinate():
    """Test reading at exact calibrated coordinate"""
    print("Exact Coordinate Test")
    print("=" * 30)
    
    # PASTE YOUR CALIBRATED COORDINATE HERE
    test_x, test_y = 1065, 821  # Example from your M1 calibration
    
    print(f"Testing coordinate: ({test_x}, {test_y})")
    print("This should be where you calibrated M1 quantity")
    
    # Test different approaches
    approaches = [
        ("Direct 20x20", lambda: capture_region(test_x, test_y, 20)),
        ("Direct 30x30", lambda: capture_region(test_x, test_y, 30)),
        ("Offset -5,-5", lambda: capture_region(test_x-5, test_y-5, 20)),
        ("Offset +5,+5", lambda: capture_region(test_x+5, test_y+5, 20)),
    ]
    
    for name, capture_func in approaches:
        print(f"\n{name}:")
        try:
            image = capture_func()
            
            # Save debug image
            filename = f"debug_{name.replace(' ', '_').replace(',', '_')}.png"
            cv2.imwrite(filename, image)
            
            # Try OCR
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(image, config=config).strip()
            
            if text.isdigit():
                print(f"  ✅ Success: {text}")
            else:
                print(f"  ❌ Failed: '{text}'")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

def capture_region(center_x, center_y, size):
    """Capture region around center point"""
    region_x = center_x - size // 2
    region_y = center_y - size // 2
    
    screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
    image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

def compare_calibration_vs_reading():
    """Compare what calibration captures vs what reading system sees"""
    print("Calibration vs Reading Comparison")
    print("=" * 40)
    
    print("Step 1: Calibrate a coordinate")
    print("Position mouse over a quantity number")
    input("Press Enter to calibrate...")
    
    cal_x, cal_y = pyautogui.position()
    print(f"Calibrated coordinate: ({cal_x}, {cal_y})")
    
    # Capture what calibration sees (immediate)
    cal_image = capture_region(cal_x, cal_y, 20)
    cv2.imwrite("debug_calibration_capture.png", cal_image)
    
    print("\nStep 2: Test reading system")
    print("Make sure you're still on the same screen")
    input("Press Enter to test reading...")
    
    # Capture what reading system sees (using same coordinate)
    read_image = capture_region(cal_x, cal_y, 20)
    cv2.imwrite("debug_reading_capture.png", read_image)
    
    # Test OCR on both
    config = '--psm 8 -c tessedit_char_whitelist=0123456789'
    
    cal_text = pytesseract.image_to_string(cal_image, config=config).strip()
    read_text = pytesseract.image_to_string(read_image, config=config).strip()
    
    print(f"\nResults:")
    print(f"Calibration OCR: '{cal_text}' -> {int(cal_text) if cal_text.isdigit() else 'FAILED'}")
    print(f"Reading OCR:     '{read_text}' -> {int(read_text) if read_text.isdigit() else 'FAILED'}")
    
    if cal_text == read_text:
        print("✅ Calibration and reading match!")
    else:
        print("❌ Calibration and reading don't match!")
        print("Check debug_calibration_capture.png vs debug_reading_capture.png")

def visual_coordinate_test():
    """Visual test - show exactly where we're reading"""
    print("Visual Coordinate Test")
    print("=" * 30)
    
    print("This will show you exactly where the system is reading")
    print("Position mouse over a quantity number")
    input("Press Enter...")
    
    x, y = pyautogui.position()
    
    # Take full screenshot
    full_screenshot = pyautogui.screenshot()
    full_image = cv2.cvtColor(np.array(full_screenshot), cv2.COLOR_RGB2BGR)
    
    # Draw crosshairs at the coordinate
    cv2.line(full_image, (x-20, y), (x+20, y), (0, 255, 0), 2)  # Horizontal line
    cv2.line(full_image, (x, y-20), (x, y+20), (0, 255, 0), 2)  # Vertical line
    
    # Draw the 20x20 capture region
    region_x = x - 10
    region_y = y - 10
    cv2.rectangle(full_image, (region_x, region_y), (region_x+20, region_y+20), (255, 0, 0), 2)
    
    # Save the visual
    cv2.imwrite("debug_visual_coordinate.png", full_image)
    
    print(f"Coordinate: ({x}, {y})")
    print("Visual saved as: debug_visual_coordinate.png")
    print("Green crosshairs = mouse position")
    print("Blue rectangle = 20x20 capture region")

def main():
    """Main menu"""
    while True:
        print("\nCoordinate Sync Debug")
        print("=" * 30)
        print("1. Debug Coordinate Reading")
        print("2. Test Exact Coordinate")
        print("3. Compare Calibration vs Reading")
        print("4. Visual Coordinate Test")
        print("0. Exit")
        
        choice = input("\nChoice (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            debug_coordinate_reading()
        elif choice == '2':
            test_exact_coordinate()
        elif choice == '3':
            compare_calibration_vs_reading()
        elif choice == '4':
            visual_coordinate_test()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
