#!/usr/bin/env python3
"""
Automation Engine - Handles click sequences and form filling
"""

import time
import pyautogui
import logging
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import messagebox
import json
import threading
import keyboard

class AutomationEngine:
    def __init__(self, parent_app):
        self.parent = parent_app
        self.is_recording = False
        self.is_capturing = False  # Whether actively capturing coordinates
        self.is_paused = False
        self.recorded_clicks = []
        self.recording_window = None
        self.current_dialog = None
        
    def execute_scenario(self, appointment, scenario):
        """Execute a complete scenario for an appointment"""
        try:
            logging.info(f"Executing scenario for appointment: {appointment['original']}")
            
            # Get click sequence from scenario
            click_sequence = scenario.get('clicks', [])
            
            if not click_sequence:
                logging.warning("No click sequence found in scenario")
                return False
                
            # Execute each click in sequence
            for i, click_data in enumerate(click_sequence):
                if not self.parent.is_running:
                    return False
                    
                # Wait if paused
                while self.parent.is_paused and self.parent.is_running:
                    time.sleep(0.1)
                    
                if not self.parent.is_running:
                    return False
                    
                self.execute_click(click_data, appointment, i)
                
            logging.info(f"Scenario execution completed for: {appointment['original']}")
            return True
            
        except Exception as e:
            logging.error(f"Error executing scenario: {e}")
            return False
    
    def execute_click(self, click_data, appointment, step_index):
        """Execute a single click with smart form filling"""
        try:
            x = click_data.get('x', 0)
            y = click_data.get('y', 0)
            delay = click_data.get('delay', 1.0)
            click_type = click_data.get('type', 'click')
            description = click_data.get('description', f'Step {step_index + 1}')
            
            logging.info(f"Executing: {description} at ({x}, {y})")
            
            # Execute the action
            if click_type == 'click':
                pyautogui.click(x, y)
            elif click_type == 'double_click':
                pyautogui.doubleClick(x, y)
            elif click_type == 'right_click':
                pyautogui.rightClick(x, y)
            elif click_type == 'type_text':
                # Click first if coordinates are provided
                if x and y:
                    pyautogui.click(x, y)
                    time.sleep(0.5)  # Wait for field to focus
                text = self.get_smart_text(click_data, appointment)
                pyautogui.typewrite(text)
            elif click_type == 'scroll':
                direction = click_data.get('direction', 'down')
                amount = click_data.get('amount', 3)
                scroll_amount = amount if direction == 'down' else -amount
                pyautogui.scroll(scroll_amount)
            elif click_type == 'key_press':
                key = click_data.get('key', 'enter')
                pyautogui.press(key)
                
            # Wait for specified delay
            time.sleep(delay)
            
        except Exception as e:
            logging.error(f"Error executing click: {e}")
            raise
    
    def get_smart_text(self, click_data, appointment):
        """Get smart text based on field type and appointment data"""
        field_type = click_data.get('field_type', 'text')
        
        if field_type == 'date':
            return self.format_date(appointment)
        elif field_type == 'start_time':
            return appointment['time']
        elif field_type == 'end_time':
            return self.calculate_end_time(appointment['time'])
        elif field_type == 'appointment_type':
            return click_data.get('default_text', '')
        else:
            return click_data.get('text', '')
    
    def format_date(self, appointment):
        """Format date for form input"""
        try:
            # Convert to standard date format
            day = appointment['day']
            month = appointment['month']
            year = appointment['year']
            
            # Create datetime object for formatting
            date_str = f"{day} {month} {year}"
            date_obj = datetime.strptime(date_str, "%d %b %Y")
            
            # Return in common web form format (MM/DD/YYYY)
            return date_obj.strftime("%m/%d/%Y")
            
        except Exception as e:
            logging.error(f"Error formatting date: {e}")
            return f"{appointment['day']}/{appointment['month']}/{appointment['year']}"
    
    def calculate_end_time(self, start_time):
        """Calculate end time (start time + 1 hour)"""
        try:
            # Parse start time
            start_dt = datetime.strptime(start_time, "%I:%M %p")
            
            # Add 1 hour
            end_dt = start_dt + timedelta(hours=1)
            
            # Return formatted end time
            return end_dt.strftime("%I:%M %p")
            
        except Exception as e:
            logging.error(f"Error calculating end time: {e}")
            # Fallback: return start time
            return start_time
    
    def start_click_recording(self):
        """Start recording mouse clicks"""
        if self.is_recording:
            messagebox.showwarning("Warning", "Already recording clicks")
            return
            
        self.is_recording = True
        self.recorded_clicks = []
        
        # Create recording window
        self.create_recording_window()
        
        logging.info("Click recording started")
    
    def create_recording_window(self):
        """Create the click recording control window"""
        self.recording_window = tk.Toplevel(self.parent.root)
        self.recording_window.title("Click Recording Control")
        self.recording_window.geometry("550x900")  # DOUBLE THE HEIGHT (LENGTH)
        self.recording_window.attributes('-topmost', True)

        # Instructions
        instructions = tk.Label(self.recording_window,
                              text="CLICK RECORDING SETUP\n\n"
                                   "Instructions:\n"
                                   "1. Click 'Start Capture' when ready to record\n"
                                   "2. Position mouse where you want to click\n"
                                   "3. Press SPACEBAR to capture coordinates\n"
                                   "4. Choose action type and configure\n"
                                   "5. Use Pause to check progress or navigate\n"
                                   "6. Click 'Stop Recording' when done",
                              justify=tk.LEFT, wraplength=500, font=("Arial", 10))
        instructions.pack(pady=10, padx=10)

        # Mouse position display
        self.position_label = tk.Label(self.recording_window,
                                     text="Mouse Position: (0, 0)",
                                     font=("Consolas", 12), fg="blue")
        self.position_label.pack(pady=5)

        # Status label
        self.status_label = tk.Label(self.recording_window,
                                   text="Ready - Click 'Start Capture' to begin",
                                   font=("Arial", 10), fg="green")
        self.status_label.pack(pady=5)

        # Control buttons
        button_frame = tk.Frame(self.recording_window)
        button_frame.pack(pady=10)

        self.start_capture_btn = tk.Button(button_frame, text="Start Capture",
                                         command=self.start_capture, bg="green", fg="white")
        self.start_capture_btn.pack(side=tk.LEFT, padx=5)

        self.pause_btn = tk.Button(button_frame, text="Pause",
                                 command=self.pause_capture, bg="orange", fg="white", state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="Add Scroll",
                 command=self.add_scroll_action, bg="blue", fg="white").pack(side=tk.LEFT, padx=5)

        self.save_btn = tk.Button(button_frame, text="Save Scenario",
                                command=self.save_current_scenario, bg="darkgreen", fg="white",
                                font=("Arial", 10, "bold"))
        self.save_btn.pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="Stop Recording",
                 command=self.stop_recording, bg="red", fg="white").pack(side=tk.LEFT, padx=5)

        # Recorded clicks display
        tk.Label(self.recording_window, text="Recorded Actions:").pack(anchor=tk.W, padx=10, pady=(10, 0))

        self.clicks_listbox = tk.Listbox(self.recording_window, height=10)
        self.clicks_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Edit buttons
        edit_frame = tk.Frame(self.recording_window)
        edit_frame.pack(pady=5)

        tk.Button(edit_frame, text="Test Selected", command=self.test_selected_click, bg="purple", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(edit_frame, text="Edit Selected", command=self.edit_selected_click).pack(side=tk.LEFT, padx=5)
        tk.Button(edit_frame, text="Delete Selected", command=self.delete_selected_click).pack(side=tk.LEFT, padx=5)
        tk.Button(edit_frame, text="Retake Selected", command=self.retake_selected_click, bg="yellow").pack(side=tk.LEFT, padx=5)

        # Save button in edit section too
        tk.Button(edit_frame, text="💾 SAVE SCENARIO", command=self.save_current_scenario,
                 bg="darkgreen", fg="white", font=("Arial", 9, "bold")).pack(side=tk.RIGHT, padx=10)

        # Start mouse position tracker (but not spacebar listener yet)
        self.start_mouse_tracker()

    def save_current_scenario(self):
        """Save the current recorded scenario without stopping recording"""
        if not self.recorded_clicks:
            messagebox.showwarning("Warning", "No actions recorded to save")
            return

        # Ask for scenario name
        scenario_name = self.parent.new_scenario_var.get().strip()
        if not scenario_name:
            scenario_name = messagebox.askstring("Scenario Name", "Enter name for this scenario:")

        if not scenario_name:
            messagebox.showwarning("Warning", "Scenario name required")
            return

        # Save scenario
        self.parent.scenarios[scenario_name] = {
            'name': scenario_name,
            'clicks': self.recorded_clicks.copy(),
            'created': datetime.now().isoformat()
        }

        self.parent.save_configuration()
        self.parent.update_scenario_list()

        messagebox.showinfo("Success", f"Scenario '{scenario_name}' saved with {len(self.recorded_clicks)} actions\n\nRecording continues...")

        logging.info(f"Scenario '{scenario_name}' saved while recording continues.")

    def start_capture(self):
        """Start coordinate capture mode"""
        self.is_capturing = True
        self.is_paused = False

        # Update UI
        self.start_capture_btn.config(text="Capturing...", state=tk.DISABLED, bg="gray")
        self.pause_btn.config(state=tk.NORMAL)
        self.status_label.config(text="CAPTURING - Position mouse and press SPACEBAR", fg="red")

        # Start spacebar listener
        self.start_spacebar_listener()

        logging.info("Coordinate capture started")

    def pause_capture(self):
        """Pause/resume coordinate capture"""
        if self.is_paused:
            # Resume
            self.is_paused = False
            self.pause_btn.config(text="Pause", bg="orange")
            self.status_label.config(text="CAPTURING - Position mouse and press SPACEBAR", fg="red")
            logging.info("Coordinate capture resumed")
        else:
            # Pause
            self.is_paused = True
            self.pause_btn.config(text="Resume", bg="green")
            self.status_label.config(text="PAUSED - Click Resume to continue capturing", fg="orange")
            logging.info("Coordinate capture paused")

    def test_selected_click(self):
        """Test the selected action"""
        selection = self.clicks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to test")
            return

        index = selection[0]
        action = self.recorded_clicks[index]

        try:
            # Pause capture during test
            was_capturing = self.is_capturing
            if was_capturing:
                self.is_capturing = False
                self.status_label.config(text="TESTING ACTION...", fg="purple")

            # Execute the action
            self.execute_click(action, {"original": "TEST", "date": "01/01/2024", "time": "2:30 PM"}, index)

            messagebox.showinfo("Test Complete", f"Action {index + 1} executed successfully!")

            # Resume capture if it was active
            if was_capturing:
                self.is_capturing = True
                self.status_label.config(text="CAPTURING - Position mouse and press SPACEBAR", fg="red")

        except Exception as e:
            messagebox.showerror("Test Failed", f"Action failed: {e}")
            if was_capturing:
                self.is_capturing = True
                self.status_label.config(text="CAPTURING - Position mouse and press SPACEBAR", fg="red")

    def retake_selected_click(self):
        """Retake coordinates for selected action"""
        selection = self.clicks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an action to retake")
            return

        index = selection[0]
        action = self.recorded_clicks[index]

        # Show retake dialog
        dialog = tk.Toplevel(self.recording_window)
        dialog.title("Retake Coordinates")
        dialog.geometry("400x200")
        dialog.attributes('-topmost', True)
        dialog.grab_set()

        tk.Label(dialog, text=f"Retaking coordinates for:\n{action.get('description', 'Unknown action')}",
                font=("Arial", 12)).pack(pady=20)

        tk.Label(dialog, text="Position mouse at new location and click 'Capture'",
                font=("Arial", 10)).pack(pady=10)

        pos_label = tk.Label(dialog, text="Mouse Position: (0, 0)",
                           font=("Consolas", 10), fg="blue")
        pos_label.pack(pady=5)

        # Update position in dialog
        def update_pos():
            if dialog.winfo_exists():
                x, y = pyautogui.position()
                pos_label.config(text=f"Mouse Position: ({x}, {y})")
                dialog.after(100, update_pos)
        update_pos()

        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=20)

        def capture_new_pos():
            x, y = pyautogui.position()
            if 'x' in action and 'y' in action:
                action['x'] = x
                action['y'] = y
            action['description'] = action.get('description', '').replace(f"({action.get('x', 0)},{action.get('y', 0)})", f"({x},{y})")
            self.update_clicks_display()
            dialog.destroy()
            messagebox.showinfo("Success", f"Coordinates updated to ({x}, {y})")

        tk.Button(button_frame, text="Capture", command=capture_new_pos, bg="green", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def start_spacebar_listener(self):
        """Start listening for spacebar presses"""
        def on_spacebar():
            try:
                if (self.is_recording and self.is_capturing and not self.is_paused and
                    hasattr(self, 'recording_window') and self.recording_window.winfo_exists() and
                    self.current_dialog is None):
                    # Get current mouse position
                    x, y = pyautogui.position()
                    self.capture_coordinates(x, y)
            except Exception as e:
                print(f"Error in spacebar handler: {e}")

        # Set up keyboard hook for spacebar with error handling
        try:
            keyboard.on_press_key('space', lambda _: on_spacebar())
        except Exception as e:
            print(f"Error setting up keyboard listener: {e}")
            logging.error(f"Failed to set up keyboard listener: {e}")

    def start_mouse_tracker(self):
        """Start tracking mouse position"""
        def update_position():
            while self.is_recording and hasattr(self, 'recording_window'):
                try:
                    if self.recording_window.winfo_exists():
                        x, y = pyautogui.position()
                        self.recording_window.after(0, lambda: self.position_label.config(
                            text=f"Mouse Position: ({x}, {y})"))
                        time.sleep(0.1)
                    else:
                        break
                except tk.TclError:
                    # Window was destroyed
                    break
                except Exception as e:
                    print(f"Error in mouse tracker: {e}")
                    break

        # Start position tracking in separate thread
        try:
            position_thread = threading.Thread(target=update_position, daemon=True)
            position_thread.start()
        except Exception as e:
            print(f"Error starting mouse tracker: {e}")
            logging.error(f"Failed to start mouse tracker: {e}")

    def capture_coordinates(self, x, y):
        """Capture coordinates and prompt for action type"""
        try:
            # Prevent multiple dialogs
            if self.current_dialog is not None:
                print("Dialog already open, ignoring spacebar")
                return

            # Update status
            if hasattr(self, 'status_label') and self.status_label.winfo_exists():
                self.status_label.config(text=f"Captured: ({x}, {y}) - Configure action", fg="orange")

            # Create action type selection dialog
            self.create_action_type_dialog(x, y)

        except tk.TclError as e:
            print(f"Tkinter error in capture_coordinates: {e}")
        except Exception as e:
            print(f"Error capturing coordinates: {e}")
            logging.error(f"Error capturing coordinates: {e}")

    def create_action_type_dialog(self, x, y):
        """Create dialog to select action type"""
        try:
            dialog = tk.Toplevel()
            dialog.title(f"Configure Action - Position ({x}, {y})")
            dialog.geometry("700x500")
            dialog.attributes('-topmost', True)
            dialog.grab_set()
            dialog.resizable(True, True)

            # Set current dialog reference
            self.current_dialog = dialog

            # Handle dialog close
            def on_dialog_close():
                try:
                    self.current_dialog = None
                    if self.is_capturing and hasattr(self, 'status_label') and self.status_label.winfo_exists():
                        self.status_label.config(text="CAPTURING - Position mouse and press SPACEBAR", fg="red")
                    dialog.destroy()
                except Exception as e:
                    print(f"Error closing dialog: {e}")

            dialog.protocol("WM_DELETE_WINDOW", on_dialog_close)
        except Exception as e:
            print(f"Error creating dialog: {e}")
            self.current_dialog = None
            return

        # Title
        title_label = tk.Label(dialog, text=f"Captured Position: ({x}, {y})",
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=10)

        # Configuration frame at top
        config_frame = tk.LabelFrame(dialog, text="Configuration", font=("Arial", 12))
        config_frame.pack(fill=tk.X, padx=10, pady=10)

        # Text input (for static text)
        tk.Label(config_frame, text="Static Text (if applicable):").pack(anchor=tk.W)
        text_var = tk.StringVar()
        tk.Entry(config_frame, textvariable=text_var, width=50).pack(fill=tk.X, pady=2)

        # Description
        tk.Label(config_frame, text="Description:").pack(anchor=tk.W, pady=(10, 0))
        desc_var = tk.StringVar(value=f"Action at ({x}, {y})")
        tk.Entry(config_frame, textvariable=desc_var, width=50).pack(fill=tk.X, pady=2)

        # Delay
        tk.Label(config_frame, text="Delay after action (seconds):").pack(anchor=tk.W, pady=(10, 0))
        delay_var = tk.StringVar(value="1.0")
        tk.Entry(config_frame, textvariable=delay_var, width=10).pack(anchor=tk.W, pady=2)

        # Action selection with buttons
        tk.Label(dialog, text="Click button to save that action type:", font=("Arial", 12, "bold")).pack(pady=(20, 10))

        # Action type buttons - one button per action type
        actions = [
            ("click", "Click - Regular mouse click"),
            ("double_click", "Double Click - Double mouse click"),
            ("right_click", "Right Click - Right mouse click"),
            ("text", "Text Input - Type static text"),
            ("date", "Date Input - Auto-fill appointment date"),
            ("start_time", "Start Time - Auto-fill appointment start time"),
            ("end_time", "End Time - Auto-fill calculated end time"),
            ("appointment_type", "Appointment Type - Select appointment type")
        ]

        def save_action_type(action_type):
            """Save the specific action type"""
            try:
                delay = float(delay_var.get())
                description = desc_var.get()
                static_text = text_var.get()

                if not description.strip():
                    description = f"{action_type} at ({x}, {y})"

                # Create action data
                if action_type in ['click', 'double_click', 'right_click']:
                    action_data = {
                        'type': action_type,
                        'x': x,
                        'y': y,
                        'delay': delay,
                        'description': description
                    }
                else:
                    # Text input types
                    action_data = {
                        'type': 'type_text',
                        'field_type': action_type,
                        'text': static_text,
                        'x': x,
                        'y': y,
                        'delay': delay,
                        'description': description
                    }

                self.recorded_clicks.append(action_data)
                self.update_clicks_display()

                # Clear dialog reference and update status
                self.current_dialog = None
                if self.is_capturing:
                    self.status_label.config(text="Action saved - Position mouse and press SPACEBAR for next", fg="green")

                dialog.destroy()
                print(f"Action saved: {action_type} at ({x}, {y})")

            except ValueError:
                messagebox.showerror("Error", "Delay must be a valid number")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save action: {e}")

        # Create a button for each action type
        for action_type, description in actions:
            frame = tk.Frame(dialog)
            frame.pack(fill=tk.X, padx=20, pady=2)

            tk.Button(frame, text=f"SAVE {description}",
                     command=lambda at=action_type: save_action_type(at),
                     bg="green", fg="white", font=("Arial", 11, "bold"),
                     width=60, height=1).pack(fill=tk.X)

        # Add separator and scenario save button
        separator = tk.Frame(dialog, height=2, bg="gray")
        separator.pack(fill=tk.X, padx=20, pady=10)

        scenario_frame = tk.Frame(dialog)
        scenario_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Button(scenario_frame, text="💾 SAVE ENTIRE SCENARIO",
                 command=lambda: [dialog.destroy(), self.save_current_scenario()],
                 bg="darkblue", fg="white", font=("Arial", 12, "bold"),
                 width=50, height=2).pack()

    
    def add_scroll_action(self):
        """Add a scroll action"""
        dialog = tk.Toplevel(self.recording_window)
        dialog.title("Add Scroll Action")
        dialog.geometry("350x250")
        dialog.attributes('-topmost', True)
        dialog.grab_set()

        # Scroll direction
        tk.Label(dialog, text="Scroll Direction:", font=("Arial", 10)).pack(pady=10)

        direction_var = tk.StringVar(value="down")
        tk.Radiobutton(dialog, text="Scroll Down", variable=direction_var, value="down").pack(anchor=tk.W, padx=20)
        tk.Radiobutton(dialog, text="Scroll Up", variable=direction_var, value="up").pack(anchor=tk.W, padx=20)

        # Scroll amount
        tk.Label(dialog, text="Scroll Amount:", font=("Arial", 10)).pack(pady=(10, 5))
        amount_var = tk.StringVar(value="3")
        amount_frame = tk.Frame(dialog)
        amount_frame.pack()
        tk.Label(amount_frame, text="Clicks:").pack(side=tk.LEFT)
        tk.Entry(amount_frame, textvariable=amount_var, width=5).pack(side=tk.LEFT, padx=5)

        # Description
        tk.Label(dialog, text="Description:", font=("Arial", 10)).pack(pady=(10, 5))
        desc_var = tk.StringVar(value="Scroll page")
        tk.Entry(dialog, textvariable=desc_var, width=30).pack(pady=5)

        # Delay
        tk.Label(dialog, text="Delay after scroll (seconds):", font=("Arial", 10)).pack(pady=(10, 5))
        delay_var = tk.StringVar(value="1.0")
        tk.Entry(dialog, textvariable=delay_var, width=10).pack(pady=5)

        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=15)

        def add_scroll():
            try:
                direction = direction_var.get()
                amount = int(amount_var.get())
                description = desc_var.get()
                delay = float(delay_var.get())

                scroll_data = {
                    'type': 'scroll',
                    'direction': direction,
                    'amount': amount,
                    'description': description,
                    'delay': delay
                }

                self.recorded_clicks.append(scroll_data)
                self.update_clicks_display()
                dialog.destroy()

                self.status_label.config(text="Scroll action added - Position mouse and press SPACEBAR for next", fg="green")

            except ValueError:
                messagebox.showerror("Error", "Amount must be a number and delay must be a valid number")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add scroll action: {e}")

        tk.Button(button_frame, text="SAVE SCROLL", command=add_scroll, bg="green", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy, bg="red", fg="white").pack(side=tk.LEFT, padx=5)
    

    
    def update_clicks_display(self):
        """Update the clicks display listbox"""
        if hasattr(self, 'clicks_listbox'):
            self.clicks_listbox.delete(0, tk.END)
            for i, action in enumerate(self.recorded_clicks):
                desc = action.get('description', f"Step {i+1}")
                delay = action.get('delay', 1.0)
                action_type = action.get('type', 'unknown')

                # Add type indicator
                if action_type == 'scroll':
                    direction = action.get('direction', 'down')
                    amount = action.get('amount', 3)
                    type_info = f"[SCROLL {direction.upper()} {amount}x]"
                elif action_type == 'type_text':
                    field_type = action.get('field_type', 'text')
                    type_info = f"[TEXT: {field_type.upper()}]"
                elif action_type in ['click', 'double_click', 'right_click']:
                    x = action.get('x', 0)
                    y = action.get('y', 0)
                    type_info = f"[{action_type.upper()} ({x},{y})]"
                else:
                    type_info = f"[{action_type.upper()}]"

                display_text = f"{i+1}. {type_info} {desc} (delay: {delay}s)"
                self.clicks_listbox.insert(tk.END, display_text)
    
    def edit_selected_click(self):
        """Edit the selected click"""
        selection = self.clicks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a click to edit")
            return
            
        index = selection[0]
        click_data = self.recorded_clicks[index]
        
        # Create edit dialog
        self.create_edit_dialog(click_data, index)
    
    def delete_selected_click(self):
        """Delete the selected click"""
        selection = self.clicks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a click to delete")
            return
            
        index = selection[0]
        del self.recorded_clicks[index]
        self.update_clicks_display()
    
    def create_edit_dialog(self, click_data, index):
        """Create dialog to edit click properties"""
        dialog = tk.Toplevel(self.recording_window)
        dialog.title("Edit Click")
        dialog.geometry("400x350")
        dialog.attributes('-topmost', True)
        
        # Description
        tk.Label(dialog, text="Description:").pack(anchor=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar(value=click_data.get('description', ''))
        tk.Entry(dialog, textvariable=desc_var, width=40).pack(padx=10, pady=5)
        
        # Delay
        tk.Label(dialog, text="Delay (seconds):").pack(anchor=tk.W, padx=10, pady=5)
        delay_var = tk.StringVar(value=str(click_data.get('delay', 1.0)))
        tk.Entry(dialog, textvariable=delay_var, width=10).pack(padx=10, pady=5)
        
        # Coordinates (if applicable)
        if 'x' in click_data and 'y' in click_data:
            tk.Label(dialog, text="Coordinates:").pack(anchor=tk.W, padx=10, pady=5)
            coord_frame = tk.Frame(dialog)
            coord_frame.pack(padx=10, pady=5)
            
            tk.Label(coord_frame, text="X:").pack(side=tk.LEFT)
            x_var = tk.StringVar(value=str(click_data['x']))
            tk.Entry(coord_frame, textvariable=x_var, width=10).pack(side=tk.LEFT, padx=5)
            
            tk.Label(coord_frame, text="Y:").pack(side=tk.LEFT, padx=(10, 0))
            y_var = tk.StringVar(value=str(click_data['y']))
            tk.Entry(coord_frame, textvariable=y_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # Text (if applicable)
        if click_data.get('type') == 'type_text':
            tk.Label(dialog, text="Text:").pack(anchor=tk.W, padx=10, pady=5)
            text_var = tk.StringVar(value=click_data.get('text', ''))
            tk.Entry(dialog, textvariable=text_var, width=40).pack(padx=10, pady=5)
        
        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def save_changes():
            try:
                click_data['description'] = desc_var.get()
                click_data['delay'] = float(delay_var.get())
                
                if 'x' in click_data and 'y' in click_data:
                    click_data['x'] = int(x_var.get())
                    click_data['y'] = int(y_var.get())
                
                if click_data.get('type') == 'type_text':
                    click_data['text'] = text_var.get()
                
                self.recorded_clicks[index] = click_data
                self.update_clicks_display()
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("Error", f"Invalid input: {e}")
        
        tk.Button(button_frame, text="Save", command=save_changes, bg="green", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def stop_recording(self):
        """Stop click recording and save scenario"""
        if not self.is_recording:
            return

        # Close any open dialog
        if self.current_dialog:
            self.current_dialog.destroy()
            self.current_dialog = None

        self.is_recording = False
        self.is_capturing = False
        self.is_paused = False

        # Clean up keyboard listener
        try:
            keyboard.unhook_all()
            print("Keyboard hooks cleaned up successfully")
        except Exception as e:
            print(f"Error cleaning up keyboard hooks: {e}")
            logging.warning(f"Failed to clean up keyboard hooks: {e}")

        if not self.recorded_clicks:
            messagebox.showwarning("Warning", "No actions recorded")
            self.recording_window.destroy()
            return

        # Ask for scenario name
        scenario_name = self.parent.new_scenario_var.get().strip()
        if not scenario_name:
            scenario_name = messagebox.askstring("Scenario Name", "Enter name for this scenario:")

        if not scenario_name:
            messagebox.showwarning("Warning", "Scenario name required")
            # Don't resume recording, just close
            self.recording_window.destroy()
            return

        # Save scenario
        self.parent.scenarios[scenario_name] = {
            'name': scenario_name,
            'clicks': self.recorded_clicks.copy(),
            'created': datetime.now().isoformat()
        }

        self.parent.save_configuration()
        self.parent.update_scenario_list()

        messagebox.showinfo("Success", f"Scenario '{scenario_name}' saved with {len(self.recorded_clicks)} actions")

        self.recording_window.destroy()
        self.recorded_clicks = []

        logging.info(f"Click recording completed. Scenario '{scenario_name}' saved.")
