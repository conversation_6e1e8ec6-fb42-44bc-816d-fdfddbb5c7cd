"""
Last War Automation Helper v1.0 - BACKUP VERSION
Main application entry point
"""
import sys
import os
import logging

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from main_controller import MainController
from gui import LastWarGUI

# Import all modules
from modules.daily_tasks import DailyTasksModule
from modules.help_click import HelpClickModule
from modules.enhanced_help_click import EnhancedHelpClickModule
from modules.alliance_donation import AllianceDonationModule
from modules.zombie_invasion import ZombieInvasionModule


def setup_application():
    """Setup the application with all modules"""
    # Create main controller
    controller = MainController()

    # Register all modules
    modules = [
        DailyTasksModule(),
        # HelpClickModule(),  # Using enhanced version instead
        EnhancedHelpClickModule(),  # Your custom help click module
        AllianceDonationModule(),
        ZombieInvasionModule(),  # Zombie invasion event automation
        # Add more modules as they're implemented
    ]

    for module in modules:
        controller.register_module(module)

    return controller


def main():
    """Main application entry point"""
    try:
        print("Starting Last War Automation Helper v1.0...")

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Setup modules and controller
        controller = setup_application()

        # Create and run GUI with the controller
        app = LastWarGUI(controller)

        print("Application started successfully!")
        print("Use the GUI to control automation modules.")

        # Run the application
        app.run()

    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        logging.error(f"Application error: {str(e)}")
    finally:
        print("Application shutting down...")


if __name__ == "__main__":
    main()
