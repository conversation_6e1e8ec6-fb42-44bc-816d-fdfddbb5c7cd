{"scenarios": {"Sample Web Form": {"name": "Sample Web Form", "clicks": [{"x": 500, "y": 200, "type": "click", "delay": 1.0, "description": "Click date field"}, {"type": "type_text", "field_type": "date", "text": "", "description": "Enter appointment date", "delay": 1.0}, {"x": 500, "y": 250, "type": "click", "delay": 1.0, "description": "Click start time field"}, {"type": "type_text", "field_type": "start_time", "text": "", "description": "Enter start time", "delay": 1.0}, {"x": 500, "y": 300, "type": "click", "delay": 1.0, "description": "Click end time field"}, {"type": "type_text", "field_type": "end_time", "text": "", "description": "Enter end time (auto-calculated)", "delay": 1.0}, {"x": 500, "y": 400, "type": "click", "delay": 1.0, "description": "Click submit button"}], "created": "2024-01-01T12:00:00"}}, "appointment_types": {"Neuro": {"distance": 15.0, "scenario": "Sample Web Form"}, "Williams": {"distance": 8.5, "scenario": "Sample Web Form"}, "Dr Smith": {"distance": 12.0, "scenario": ""}, "General Consultation": {"distance": 5.0, "scenario": ""}, "Follow-up": {"distance": 3.0, "scenario": ""}}}