#!/usr/bin/env python3
"""
Map Piece Detection Debug Tool
- Capture screenshots at coordinates
- Test OCR detection
- Adjust coordinates interactively
- Save debug images in organized folder
"""
import os
import pyautogui
import cv2
import numpy as np
import pytesseract
from datetime import datetime

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Create debug folder
DEBUG_FOLDER = "debug_screenshots"
if not os.path.exists(DEBUG_FOLDER):
    os.makedirs(DEBUG_FOLDER)

# Your current coordinates
COORDINATES = {
    'M1': (1079, 833),
    'M2': (1245, 835), 
    'M3': (1412, 835),
    'M4': (1577, 833),
    'M5': (1163, 994),
    'M6': (1329, 994),
    'M7': (1495, 994)
}

def capture_and_analyze(name, x, y, size=25):
    """Capture region and analyze with OCR"""
    print(f"\n=== {name} at ({x}, {y}) ===")
    
    # Capture region
    capture_x = x - size // 2
    capture_y = y - size // 2
    
    try:
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save original image
        timestamp = datetime.now().strftime("%H%M%S")
        filename = f"{DEBUG_FOLDER}/{name}_{x}_{y}_{timestamp}.png"
        cv2.imwrite(filename, image)
        print(f"Saved: {filename}")
        
        # Convert to grayscale for OCR
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Save grayscale version
        gray_filename = f"{DEBUG_FOLDER}/{name}_{x}_{y}_{timestamp}_gray.png"
        cv2.imwrite(gray_filename, gray)
        
        # Try OCR with your exact config
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        if text.isdigit():
            print(f"OCR SUCCESS: {text}")
            return int(text)
        else:
            print(f"OCR FAILED: '{text}' (not digit)")
            
            # Try pixel analysis as fallback
            _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
            dark_pixels = cv2.countNonZero(binary)
            total_pixels = image.shape[0] * image.shape[1]
            density = dark_pixels / total_pixels if total_pixels > 0 else 0
            
            # Save binary version
            binary_filename = f"{DEBUG_FOLDER}/{name}_{x}_{y}_{timestamp}_binary.png"
            cv2.imwrite(binary_filename, binary)
            
            if density < 0.02:
                pixel_result = 1
            elif density < 0.04:
                pixel_result = 2
            elif density < 0.06:
                pixel_result = 3
            elif density < 0.08:
                pixel_result = 4
            elif density < 0.10:
                pixel_result = 5
            elif density < 0.12:
                pixel_result = 6
            else:
                pixel_result = 7
                
            print(f"PIXEL FALLBACK: density={density:.4f} -> {pixel_result}")
            return pixel_result
            
    except Exception as e:
        print(f"ERROR: {e}")
        return None

def test_all_coordinates():
    """Test all current coordinates"""
    print("TESTING ALL CURRENT COORDINATES")
    print("=" * 50)
    
    results = {}
    for name, (x, y) in COORDINATES.items():
        result = capture_and_analyze(name, x, y)
        results[name] = result
    
    print(f"\nRESULTS SUMMARY:")
    for name, result in results.items():
        print(f"{name}: {result}")
    
    return results

def adjust_coordinate():
    """Interactively adjust a coordinate"""
    print("\nADJUST COORDINATE")
    print("Available: M1, M2, M3, M4, M5, M6, M7")
    name = input("Which coordinate to adjust? ").upper()
    
    if name not in COORDINATES:
        print("Invalid name")
        return
    
    current_x, current_y = COORDINATES[name]
    print(f"Current {name}: ({current_x}, {current_y})")
    
    try:
        new_x = int(input(f"New X (current {current_x}): ") or current_x)
        new_y = int(input(f"New Y (current {current_y}): ") or current_y)
        
        COORDINATES[name] = (new_x, new_y)
        print(f"Updated {name} to ({new_x}, {new_y})")
        
        # Test the new coordinate
        result = capture_and_analyze(name, new_x, new_y)
        print(f"Test result: {result}")
        
    except ValueError:
        print("Invalid input")

def test_different_sizes():
    """Test different capture sizes"""
    name = input("Which coordinate to test sizes for? (M1-M7): ").upper()
    if name not in COORDINATES:
        print("Invalid name")
        return
    
    x, y = COORDINATES[name]
    sizes = [15, 20, 25, 30, 35, 40]
    
    print(f"\nTesting different sizes for {name} at ({x}, {y})")
    for size in sizes:
        print(f"\n--- Size {size}x{size} ---")
        result = capture_and_analyze(f"{name}_size{size}", x, y, size)

def main_menu():
    """Main interactive menu"""
    while True:
        print(f"\n" + "=" * 50)
        print("MAP DETECTION DEBUG TOOL")
        print("=" * 50)
        print("1. Test all coordinates")
        print("2. Adjust coordinate")
        print("3. Test different sizes")
        print("4. Show current coordinates")
        print("5. Exit")
        
        choice = input("\nChoice: ").strip()
        
        if choice == "1":
            test_all_coordinates()
        elif choice == "2":
            adjust_coordinate()
        elif choice == "3":
            test_different_sizes()
        elif choice == "4":
            print("\nCURRENT COORDINATES:")
            for name, (x, y) in COORDINATES.items():
                print(f"{name}: ({x}, {y})")
        elif choice == "5":
            break
        else:
            print("Invalid choice")

if __name__ == "__main__":
    print("Map Piece Detection Debug Tool")
    print(f"Debug images will be saved to: {DEBUG_FOLDER}/")
    print("Make sure the game is visible and ready")
    
    main_menu()
