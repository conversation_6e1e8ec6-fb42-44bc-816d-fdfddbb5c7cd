#!/usr/bin/env python3
"""
Quick verification of OCR enhancements
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from ai_ocr_service import AINumberRecognition
        print("✅ AINumberRecognition imported")
        
        import ocr_monitor_gui
        print("✅ OCR Monitor GUI imported")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_ocr_service():
    """Test basic OCR service functionality"""
    print("\n🔍 Testing OCR service...")
    
    try:
        from ai_ocr_service import AINumberRecognition
        
        # Test with debug config
        debug_config = {
            'enable_detailed_logging': True,
            'save_debug_images': False,  # Disable to avoid file operations
            'confidence_threshold': 0.3
        }
        
        ocr_service = AINumberRecognition(debug_config=debug_config)
        print(f"✅ OCR service created with {len(ocr_service.active_backends)} backends")
        
        # Test configuration
        config = ocr_service.get_debug_config()
        print(f"✅ Debug config loaded: {len(config)} settings")
        
        # Test statistics
        stats = ocr_service.get_ocr_statistics()
        print(f"✅ Statistics available: {len(stats)} metrics")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR service error: {e}")
        return False

def test_map_trade_integration():
    """Test map trade integration"""
    print("\n🗺️ Testing map trade integration...")
    
    try:
        # Check if the map trade file has been updated
        with open('modules/map_trade.py', 'r') as f:
            content = f.read()
        
        if 'piece_id=piece_id' in content:
            print("✅ Map trade module updated with piece ID support")
        else:
            print("⚠️ Map trade module may not have piece ID support")
        
        if '_current_piece_id' in content:
            print("✅ Map trade module has piece ID tracking")
        else:
            print("⚠️ Map trade module may not have piece ID tracking")
        
        return True
        
    except Exception as e:
        print(f"❌ Map trade integration error: {e}")
        return False

def test_config_files():
    """Test configuration file creation"""
    print("\n⚙️ Testing configuration files...")
    
    try:
        # Test config file creation
        from ai_ocr_service import AINumberRecognition
        ocr_service = AINumberRecognition()
        
        if os.path.exists('ocr_debug_config.json'):
            print("✅ OCR debug config file created")
        else:
            print("⚠️ OCR debug config file not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Config file error: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 OCR Enhancement Verification")
    print("=" * 40)
    
    tests = [
        ("Import Test", test_imports),
        ("OCR Service Test", test_ocr_service),
        ("Map Trade Integration", test_map_trade_integration),
        ("Configuration Files", test_config_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhancements verified successfully!")
        print("\n💡 Next steps:")
        print("1. Run: python launch_ocr_monitor.py")
        print("2. Enable detailed logging in Configuration tab")
        print("3. Run map trade automation to see enhanced OCR logging")
        print("4. Check Debug Images tab for visual analysis")
    else:
        print("⚠️ Some tests failed. Check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
