#!/usr/bin/env python3
"""
Timer Recognition System for Last War Automation
Specialized for reading game timers with white numbers on various backgrounds
Uses digit templates and advanced color preprocessing
"""
import cv2
import numpy as np
import os
import json
import pickle
import pytesseract
from typing import Optional, Dict, List, Tuple, Union
import hashlib
import logging

class TimerRecognition:
    """Advanced timer recognition system using digit templates and color preprocessing"""
    
    def __init__(self):
        self.digit_templates = {}  # digit -> list of template images
        self.template_file = "digit_templates.pkl"
        self.config_file = "timer_config.json"
        
        # Default configuration
        self.config = {
            'template_matching_threshold': 0.7,
            'ocr_psm_mode': 6,  # Single uniform block of text
            'color_preprocessing': True,
            'white_text_threshold': 200,  # Minimum brightness for white text
            'green_background_detection': True,
            'debug_images': False
        }
        
        self.load_config()
        self.load_digit_templates()
        
        # Setup logging
        self.logger = logging.getLogger('TimerRecognition')
        
    def load_config(self):
        """Load timer recognition configuration"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"Timer config loaded: {len(loaded_config)} settings")
            except Exception as e:
                print(f"Failed to load timer config: {e}")
    
    def save_config(self):
        """Save timer recognition configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            print("Timer config saved")
        except Exception as e:
            print(f"Failed to save timer config: {e}")
    
    def load_digit_templates(self):
        """Load saved digit templates"""
        if os.path.exists(self.template_file):
            try:
                with open(self.template_file, 'rb') as f:
                    self.digit_templates = pickle.load(f)
                print(f"Loaded digit templates: {list(self.digit_templates.keys())}")
            except Exception as e:
                print(f"Failed to load digit templates: {e}")
                self.digit_templates = {}
        else:
            self.digit_templates = {}
    
    def save_digit_templates(self):
        """Save digit templates to file"""
        try:
            with open(self.template_file, 'wb') as f:
                pickle.dump(self.digit_templates, f)
            print(f"Saved digit templates for digits: {list(self.digit_templates.keys())}")
        except Exception as e:
            print(f"Failed to save digit templates: {e}")
    
    def preprocess_for_white_numbers(self, image: np.ndarray) -> np.ndarray:
        """
        Advanced preprocessing to convert image to white numbers on black background
        Handles green timer backgrounds and other color variations
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Method 1: Direct white text extraction
        # Create mask for white/bright pixels (likely text)
        white_mask = cv2.threshold(gray, self.config['white_text_threshold'], 255, cv2.THRESH_BINARY)[1]
        
        # Method 2: Handle green backgrounds specifically
        if self.config['green_background_detection'] and len(image.shape) == 3:
            # Convert to HSV for better color filtering
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Define green color range (for timer backgrounds)
            lower_green = np.array([35, 40, 40])
            upper_green = np.array([85, 255, 255])
            green_mask = cv2.inRange(hsv, lower_green, upper_green)
            
            # Create inverse mask (non-green areas)
            non_green_mask = cv2.bitwise_not(green_mask)
            
            # Extract white text from non-green areas
            text_on_green = cv2.bitwise_and(white_mask, white_mask, mask=non_green_mask)
            
            # Combine with direct white extraction
            combined_mask = cv2.bitwise_or(white_mask, text_on_green)
        else:
            combined_mask = white_mask
        
        # Clean up the mask
        # Remove small noise
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        # Create final image: white text on black background
        result = np.zeros_like(gray)
        result[cleaned == 255] = 255
        
        return result
    
    def add_digit_template(self, image: np.ndarray, digit: str) -> bool:
        """Add a template for a specific digit (0-9)"""
        if digit not in '0123456789':
            print(f"Invalid digit: {digit}. Must be 0-9")
            return False
        
        try:
            # Preprocess the image to white on black
            processed = self.preprocess_for_white_numbers(image)
            
            # Create template hash to avoid duplicates
            template_hash = hashlib.md5(processed.tobytes()).hexdigest()
            
            # Initialize digit templates if not exists
            if digit not in self.digit_templates:
                self.digit_templates[digit] = []
            
            # Check for duplicates
            existing_hashes = [t['hash'] for t in self.digit_templates[digit]]
            if template_hash not in existing_hashes:
                template_data = {
                    'image': processed,
                    'hash': template_hash,
                    'original_shape': image.shape,
                    'digit': digit
                }
                self.digit_templates[digit].append(template_data)
                self.save_digit_templates()
                print(f"Added template for digit '{digit}' (total: {len(self.digit_templates[digit])})")
                return True
            else:
                print(f"Duplicate template for digit '{digit}' ignored")
                return False
                
        except Exception as e:
            print(f"Failed to add digit template: {e}")
            return False
    
    def recognize_with_templates(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Recognize digits using template matching"""
        if not self.digit_templates:
            return None, 0.0
        
        try:
            # Preprocess the input image
            processed_input = self.preprocess_for_white_numbers(image)
            
            # Try to match individual digits and build the complete number
            # This is a simplified approach - for complex multi-digit recognition,
            # you might need to segment the image first
            
            best_matches = []
            
            # For each digit template, find the best match
            for digit, templates in self.digit_templates.items():
                digit_best_score = 0.0
                
                for template_data in templates:
                    template = template_data['image']
                    
                    # Skip if template is larger than input
                    if (template.shape[0] > processed_input.shape[0] or 
                        template.shape[1] > processed_input.shape[1]):
                        continue
                    
                    # Template matching with multiple scales
                    for scale in [0.8, 0.9, 1.0, 1.1, 1.2]:
                        if scale != 1.0:
                            new_width = int(template.shape[1] * scale)
                            new_height = int(template.shape[0] * scale)
                            if new_width > 0 and new_height > 0:
                                scaled_template = cv2.resize(template, (new_width, new_height))
                            else:
                                continue
                        else:
                            scaled_template = template
                        
                        # Skip if scaled template is too large
                        if (scaled_template.shape[0] > processed_input.shape[0] or 
                            scaled_template.shape[1] > processed_input.shape[1]):
                            continue
                        
                        # Perform template matching
                        result = cv2.matchTemplate(processed_input, scaled_template, cv2.TM_CCOEFF_NORMED)
                        _, max_val, _, max_loc = cv2.minMaxLoc(result)
                        
                        if max_val > digit_best_score:
                            digit_best_score = max_val
                
                if digit_best_score > self.config['template_matching_threshold']:
                    best_matches.append((digit, digit_best_score))
            
            # Sort by confidence and return the best match
            if best_matches:
                best_matches.sort(key=lambda x: x[1], reverse=True)
                best_digit, best_confidence = best_matches[0]
                return best_digit, best_confidence
            
            return None, 0.0
            
        except Exception as e:
            print(f"Template recognition failed: {e}")
            return None, 0.0
    
    def recognize_with_ocr_psm6(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """Recognize multi-digit numbers using Tesseract PSM 6 (single uniform block)"""
        try:
            # Preprocess the image
            processed = self.preprocess_for_white_numbers(image)
            
            # Configure Tesseract for single block text recognition
            custom_config = f'--oem 3 --psm {self.config["ocr_psm_mode"]} -c tessedit_char_whitelist=0123456789'
            
            # Perform OCR
            ocr_result = pytesseract.image_to_data(
                processed, 
                config=custom_config, 
                output_type=pytesseract.Output.DICT
            )
            
            # Extract text and confidence
            detected_text = ""
            total_confidence = 0.0
            valid_detections = 0
            
            for i in range(len(ocr_result['text'])):
                text = ocr_result['text'][i].strip()
                conf = int(ocr_result['conf'][i])
                
                if text and conf > 0:
                    detected_text += text
                    total_confidence += conf
                    valid_detections += 1
            
            if detected_text and valid_detections > 0:
                avg_confidence = total_confidence / valid_detections / 100.0  # Convert to 0-1 range
                return detected_text, avg_confidence
            
            return None, 0.0
            
        except Exception as e:
            print(f"OCR PSM6 recognition failed: {e}")
            return None, 0.0
    
    def recognize_timer(self, image: np.ndarray) -> Dict:
        """
        Main timer recognition method combining template matching and OCR
        Returns comprehensive results
        """
        results = {
            'template_result': None,
            'template_confidence': 0.0,
            'ocr_result': None,
            'ocr_confidence': 0.0,
            'final_result': None,
            'final_confidence': 0.0,
            'method_used': None,
            'success': False
        }
        
        try:
            # Method 1: Template matching (for single digits or known patterns)
            template_result, template_conf = self.recognize_with_templates(image)
            results['template_result'] = template_result
            results['template_confidence'] = template_conf
            
            # Method 2: OCR with PSM 6 (for multi-digit blocks)
            ocr_result, ocr_conf = self.recognize_with_ocr_psm6(image)
            results['ocr_result'] = ocr_result
            results['ocr_confidence'] = ocr_conf
            
            # Choose the best result
            if template_conf > ocr_conf and template_conf > 0.7:
                results['final_result'] = template_result
                results['final_confidence'] = template_conf
                results['method_used'] = 'Template Matching'
                results['success'] = True
            elif ocr_result and ocr_conf > 0.5:
                results['final_result'] = ocr_result
                results['final_confidence'] = ocr_conf
                results['method_used'] = 'OCR PSM6'
                results['success'] = True
            elif template_result and template_conf > 0.5:
                results['final_result'] = template_result
                results['final_confidence'] = template_conf
                results['method_used'] = 'Template Matching (Low Conf)'
                results['success'] = True
            
            return results
            
        except Exception as e:
            print(f"Timer recognition failed: {e}")
            results['error'] = str(e)
            return results
    
    def get_stats(self) -> Dict:
        """Get statistics about the timer recognition system"""
        stats = {
            'total_digits': len(self.digit_templates),
            'total_templates': sum(len(templates) for templates in self.digit_templates.values()),
            'available_digits': list(self.digit_templates.keys()),
            'config': self.config.copy()
        }
        
        for digit, templates in self.digit_templates.items():
            stats[f'templates_for_{digit}'] = len(templates)
        
        return stats

# Global instance
timer_recognition = None

def get_timer_recognition():
    """Get singleton instance"""
    global timer_recognition
    if timer_recognition is None:
        timer_recognition = TimerRecognition()
    return timer_recognition

if __name__ == "__main__":
    # Test the system
    recognizer = TimerRecognition()
    print("Timer Recognition System Ready")
    print(f"Stats: {recognizer.get_stats()}")
