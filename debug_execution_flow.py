#!/usr/bin/env python3
"""
Debug script to track execution flow and find why ESC steps are inconsistent
"""

import sys
import os
import time
import pyautogui

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_alliance_execution_with_debug():
    """Simulate the alliance donation execution with detailed debugging"""
    print("🔍 Simulating Alliance Donation Execution Flow")
    print("=" * 60)
    
    # Load the sequence like the module does
    import json
    with open('module_configs.json', 'r') as f:
        config = json.load(f)
    
    alliance_config = config.get('alliance_donation', {})
    click_coordinates = alliance_config.get('click_coordinates', [])
    
    # Sort coordinates like the module does
    def get_step_number(coord):
        name = coord.get('name', '')
        try:
            if '_' in name:
                return int(name.split('_')[0])
            else:
                return 999
        except:
            return 999
    
    sorted_coords = sorted(click_coordinates, key=get_step_number)
    
    # Build sequence like the module does
    sequence = []
    for coord in sorted_coords:
        enabled = coord.get('enabled', True)
        if not enabled:
            print(f"⏭️ Skipping disabled step: {coord.get('name', 'Unknown')}")
            continue
        
        x = coord.get('x', 0)
        y = coord.get('y', 0)
        delay = coord.get('delay', 2.0)
        repeat_count = coord.get('repeat', 1)
        use_esc_key = coord.get('use_esc_key', False)
        name = coord.get('name', 'Unknown')
        description = coord.get('description', '')
        
        # Create description like the module does
        action_type = " [ESC]" if use_esc_key else ""
        if repeat_count > 1:
            full_description = f"{name}: {description} (×{repeat_count}){action_type}".strip(': ')
        else:
            full_description = f"{name}: {description}{action_type}".strip(': ')
        
        sequence.append((x, y, delay, repeat_count, full_description, use_esc_key))
    
    print(f"📋 Built sequence with {len(sequence)} steps:")
    for i, step_data in enumerate(sequence):
        x, y, delay, repeat_count, description, use_esc_key = step_data
        action = "🔑 ESC KEY" if use_esc_key else f"🖱️ CLICK ({x}, {y})"
        print(f"  Step {i+1:2d}: {description[:50]:50s} | {action}")
    
    # Ask user if they want to simulate execution
    print(f"\n⚠️ This will simulate the full execution with ESC key presses!")
    response = input("Do you want to run the simulation? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("⏭️ Skipping execution simulation.")
        return
    
    print(f"\n🎯 Starting execution simulation in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    print("\n🚀 Executing sequence...")
    
    try:
        total_steps = len(sequence)
        esc_steps_executed = 0
        click_steps_executed = 0
        
        for i, step_data in enumerate(sequence):
            x, y, delay, repeat_count, description, use_esc_key = step_data
            
            # Debug logging like the module
            action_type = "ESC KEY" if use_esc_key else f"CLICK ({x}, {y})"
            print(f"\n📍 {description} ({i+1}/{total_steps}) - {action_type}")
            print(f"🔧 DEBUG: Step {i+1} data: use_esc_key={use_esc_key}, repeat_count={repeat_count}")
            
            # Execute the action(s) with repeat
            for repeat_num in range(repeat_count):
                if use_esc_key:
                    if repeat_count > 1:
                        print(f"⌨️ ESC key press {repeat_num+1}/{repeat_count}")
                    else:
                        print(f"⌨️ Pressing ESC key")
                    
                    print(f"🔑 DEBUG: About to press ESC key using pyautogui.press('esc')")
                    
                    # Actually press ESC key
                    pyautogui.press('esc')
                    esc_steps_executed += 1
                    
                    print(f"✅ DEBUG: ESC key press completed successfully")
                else:
                    if repeat_count > 1:
                        print(f"🖱️ Click {repeat_num+1}/{repeat_count} at ({x}, {y})")
                    else:
                        print(f"🖱️ Clicked at ({x}, {y})")
                    
                    # Actually click (but we'll skip this for safety)
                    print(f"🔧 DEBUG: Would click at ({x}, {y}) - SKIPPED for safety")
                    click_steps_executed += 1
                
                # Inter-repeat delay
                if repeat_count > 1 and repeat_num < repeat_count - 1:
                    repeat_delay = max(0.01, delay / 2)
                    action_type_delay = "ESC presses" if use_esc_key else "clicks"
                    print(f"⚡ Inter-{action_type_delay} delay: {repeat_delay}s")
                    time.sleep(repeat_delay)
            
            # Step delay
            if delay > 0:
                print(f"⏳ Waiting {delay} seconds...")
                time.sleep(delay)
        
        print(f"\n✅ Execution simulation completed!")
        print(f"📊 Summary:")
        print(f"  🔑 ESC steps executed: {esc_steps_executed}")
        print(f"  🖱️ Click steps executed: {click_steps_executed}")
        print(f"  📋 Total steps: {total_steps}")
        
        if esc_steps_executed == 3:
            print("✅ All 3 ESC steps executed correctly!")
        else:
            print(f"❌ Expected 3 ESC steps, but executed {esc_steps_executed}")
        
    except Exception as e:
        print(f"\n❌ Error during simulation: {str(e)}")
        import traceback
        traceback.print_exc()

def test_esc_key_reliability():
    """Test ESC key reliability with multiple presses"""
    print("\n" + "=" * 60)
    print("🔑 Testing ESC Key Reliability")
    print("=" * 60)
    
    response = input("Do you want to test ESC key reliability? (y/n): ").lower().strip()
    
    if response != 'y' and response != 'yes':
        print("⏭️ Skipping ESC key reliability test.")
        return
    
    print("\n⚠️ This will press ESC key 5 times with 2 second delays!")
    print("Watch carefully to see if all 5 ESC presses have an effect.")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"⏳ {i}...")
        time.sleep(1)
    
    try:
        for i in range(5):
            print(f"\n🔑 ESC Press {i+1}/5:")
            print(f"   About to press ESC key...")
            pyautogui.press('esc')
            print(f"   ✅ ESC key pressed")
            
            if i < 4:  # Don't wait after the last press
                print(f"   ⏳ Waiting 2 seconds...")
                time.sleep(2)
        
        print(f"\n✅ All 5 ESC key presses completed!")
        print("If you didn't see 5 separate effects, there might be:")
        print("  1. No menu/dialog to close")
        print("  2. ESC key being ignored by the game")
        print("  3. Game window not in focus")
        
    except Exception as e:
        print(f"\n❌ Error during ESC reliability test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Alliance Donation Execution Flow Debug")
    print("=" * 60)
    
    try:
        # Test 1: Simulate full execution
        simulate_alliance_execution_with_debug()
        
        # Test 2: Test ESC key reliability
        test_esc_key_reliability()
        
        print("\n" + "=" * 60)
        print("📊 Debug Complete!")
        print("This should help identify if the issue is:")
        print("  1. Execution flow problem")
        print("  2. ESC key reliability issue")
        print("  3. Timing/delay problem")
        
    except Exception as e:
        print(f"❌ Debug failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
