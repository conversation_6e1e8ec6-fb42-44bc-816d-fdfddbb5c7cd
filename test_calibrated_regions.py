#!/usr/bin/env python3
"""
Test calibrated regions for map piece quantity recognition
"""

import cv2
import numpy as np
import pyautogui
import pytesseract

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def test_calibrated_regions():
    """Test OCR on calibrated regions"""
    print("Testing Calibrated Map Piece Regions")
    print("=" * 50)
    
    # PASTE YOUR CALIBRATED COORDINATES HERE
    # Replace these with the coordinates from your calibration
    map_regions = {
        'M1': (100, 230, 60, 30),  # Replace with your calibrated coordinates
        'M2': (200, 230, 60, 30),
        'M3': (300, 230, 60, 30),
        'M4': (400, 230, 60, 30),
        'M5': (500, 230, 60, 30),
        'M6': (600, 230, 60, 30),
        'M7': (700, 230, 60, 30),
    }
    
    print("Current regions (UPDATE THESE WITH YOUR CALIBRATED COORDINATES):")
    for map_name, coords in map_regions.items():
        print(f"  {map_name}: {coords}")
    
    print("\nMake sure you're on the initiate exchange screen")
    input("Press Enter to test all regions...")
    
    results = {}
    
    for map_name, (x, y, width, height) in map_regions.items():
        print(f"\nTesting {map_name} at ({x}, {y}, {width}, {height}):")
        
        try:
            # Capture region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Use direct OCR (the working method)
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(gray, config=config).strip()
            
            if text.isdigit():
                quantity = int(text)
                results[map_name] = quantity
                print(f"  ✅ {map_name}: {quantity} pieces")
            else:
                print(f"  ❌ {map_name}: OCR failed - got '{text}'")
                
            # Save debug image
            cv2.imwrite(f"debug_{map_name}.png", gray)
                
        except Exception as e:
            print(f"  ❌ {map_name}: Error - {e}")
    
    print(f"\n{'='*50}")
    print("RESULTS SUMMARY:")
    print(f"{'='*50}")
    
    if results:
        print(f"Successfully read {len(results)}/7 map pieces:")
        for map_name, quantity in sorted(results.items()):
            print(f"  {map_name}: {quantity} pieces")
        
        if len(results) >= 2:
            # Find optimal trade
            lowest_map = min(results.keys(), key=lambda k: results[k])
            highest_map = max(results.keys(), key=lambda k: results[k])
            
            print(f"\n🎯 OPTIMAL TRADE:")
            print(f"  Give away: {lowest_map} ({results[lowest_map]} pieces) ← LOWEST")
            print(f"  Receive:   {highest_map} ({results[highest_map]} pieces) ← HIGHEST")
            
            # Show all quantities sorted
            print(f"\n📊 ALL QUANTITIES (sorted):")
            sorted_results = sorted(results.items(), key=lambda x: x[1])
            for i, (map_name, quantity) in enumerate(sorted_results):
                marker = " ← GIVE" if i == 0 else " ← RECEIVE" if i == len(sorted_results)-1 else ""
                print(f"  {map_name}: {quantity:3d} pieces{marker}")
        
        # Generate code for map_trade.py
        print(f"\n{'='*50}")
        print("CODE FOR map_trade.py:")
        print(f"{'='*50}")
        print("Copy this to _define_map_piece_regions method:")
        print()
        print("return {")
        for map_name, coords in map_regions.items():
            print(f"    '{map_name}': {coords},")
        print("}")
        
    else:
        print("❌ No successful OCR results")
        print("Check that:")
        print("1. You're on the initiate exchange screen")
        print("2. All 7 map pieces with quantities are visible")
        print("3. Coordinates are calibrated correctly")
        print("4. Numbers are clearly visible and not obscured")
    
    print(f"\nDebug images saved: debug_M1.png through debug_M7.png")
    return results

def update_coordinates():
    """Helper to update coordinates in the test"""
    print("Coordinate Update Helper")
    print("=" * 30)
    print("Paste your calibrated coordinates here:")
    print("Format: M1: (x, y, width, height)")
    print()
    print("Example:")
    print("M1: (150, 250, 60, 30)")
    print("M2: (250, 250, 60, 30)")
    print("...")
    print()
    print("Then update the map_regions dictionary in this script")

def main():
    """Main menu"""
    while True:
        print("\nCalibrated Regions Test")
        print("=" * 30)
        print("1. Test Current Regions")
        print("2. Update Coordinates Helper")
        print("0. Exit")
        
        choice = input("\nEnter choice (0-2): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            test_calibrated_regions()
        elif choice == '2':
            update_coordinates()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
