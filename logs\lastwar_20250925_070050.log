2025-09-25 07:00:50,072 - LastWar.ScreenScanner - INFO - Loaded 55 templates
2025-09-25 07:00:50,097 - LastWar.ScreenScanner - INFO - Loaded 55 templates
2025-09-25 07:00:50,097 - LastWar.MainController - INFO - MainController initialized
2025-09-25 07:00:50,110 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
2025-09-25 07:00:50,110 - LastWar.daily_tasks - INFO - Loaded 7 daily tasks steps from configuration
2025-09-25 07:00:50,110 - LastWar.daily_tasks - INFO -   Step 1: 1_daily_tasks: Open daily tasks menu at (1576, 991) + 1.5s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 2: 2_claim_1: Claim first reward at (600, 200) + 1.0s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 3: 3_claim_2: Claim second reward at (600, 250) + 1.0s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 4: 4_claim_3: Claim third reward at (600, 300) + 1.0s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 5: 5_claim_4: Claim fourth reward at (600, 350) + 1.0s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 6: 6_claim_5: Claim fifth reward at (600, 400) + 1.0s delay
2025-09-25 07:00:50,111 - LastWar.daily_tasks - INFO -   Step 7: 7_close: Close daily tasks window at (750, 50) + 1.0s delay
2025-09-25 07:00:50,138 - LastWar.ScannerGUI - INFO - Discovered 55 templates
2025-09-25 07:00:50,646 - LastWar.ModuleConfigManager - INFO - Loaded module configuration from module_configs.json
