#!/usr/bin/env python3
"""
Test clean terminal output for map trade
"""

from modules.map_trade import MapTradeModule
import logging

def test_clean_output():
 """Test the cleaned up terminal output"""
 
 # Set up clean logging format
 logging.basicConfig(
 level=logging.INFO, 
 format='%(message)s',
 handlers=[logging.StreamHandler()]
 )
 
 print(" Map Trade System - Clean Output Test")
 print("=" * 45)
 
 # Create module
 module = MapTradeModule()
 
 print("\n Testing scenario detection...")
 
 # Test detection
 screen_state = module._detect_screen_state()
 has_red_thumb = module._has_red_thumb()
 has_new_exchange = module._has_new_exchange_button()
 
 print(f"Screen state: {screen_state}")
 print(f"Red thumb: {has_red_thumb}")
 print(f"New exchange: {has_new_exchange}")
 
 print(f"\n Executing map trade...")
 print("-" * 30)
 
 try:
 result = module.manual_trigger_map_trade()
 print("-" * 30)
 print(f" Completed successfully: {result}")
 except Exception as e:
 print(f" Error: {e}")

if __name__ == "__main__":
 test_clean_output()
