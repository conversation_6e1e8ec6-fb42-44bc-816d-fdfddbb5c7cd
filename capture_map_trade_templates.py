#!/usr/bin/env python3
"""
Capture templates for map trade state detection
"""

import cv2
import numpy as np
import pyautogui
import os

def capture_template(name, description):
    """Capture a template for state detection"""
    print(f"\nCapturing template: {name}")
    print(f"Description: {description}")
    print("Position your mouse over the element and press Enter...")
    
    input("Press Enter to capture...")
    
    # Get mouse position
    x, y = pyautogui.position()
    
    # Capture different sizes to find the best one
    sizes = [(30, 15), (50, 25), (80, 30), (100, 40)]
    
    for i, (width, height) in enumerate(sizes):
        # Calculate region
        region_x = x - width // 2
        region_y = y - height // 2
        
        # Capture screenshot
        screenshot = pyautogui.screenshot(region=(region_x, region_y, width, height))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Save template
        filename = f"templates/{name}_{width}x{height}.png"
        cv2.imwrite(filename, image)
        print(f"  Saved: {filename}")
    
    print(f"✅ Template {name} captured in multiple sizes")

def capture_map_trade_templates():
    """Capture all needed templates for map trade detection"""
    print("Map Trade Template Capture")
    print("=" * 40)
    
    # Ensure templates directory exists
    os.makedirs("templates", exist_ok=True)
    
    templates_to_capture = [
        ("red_thumb", "Red thumbs up icon (trade completed) - CAPTURE THIS FIRST"),
        ("cancel_exchange", "Red 'Cancel Exchange' button (when trade is in progress)"),
        ("initiate_exchange", "'Initiate Exchange' button (ready to trade)"),
        ("confirm_trade", "'Confirm' button in confirmation dialog"),
        ("you_will_lose", "'You will lose' text area"),
        ("trade_complete", "Trade completion message or icon"),
    ]
    
    print("We'll capture templates for different map trade states:")
    for name, desc in templates_to_capture:
        print(f"  - {name}: {desc}")
    
    print("\nMake sure you can navigate between different trade states")
    input("Press Enter to start capturing...")
    
    for name, description in templates_to_capture:
        try:
            capture_template(name, description)
        except KeyboardInterrupt:
            print(f"\nSkipped {name}")
            continue
        except Exception as e:
            print(f"Error capturing {name}: {e}")
            continue
    
    print(f"\n{'='*40}")
    print("Template capture complete!")
    print("Check the templates/ directory for captured images")

def test_template_detection():
    """Test template detection on current screen"""
    print("Template Detection Test")
    print("=" * 30)
    
    # List available templates
    template_dir = "templates"
    if not os.path.exists(template_dir):
        print("No templates directory found")
        return
    
    templates = [f for f in os.listdir(template_dir) if f.endswith('.png')]
    map_trade_templates = [t for t in templates if any(keyword in t.lower() for keyword in 
                          ['cancel', 'initiate', 'confirm', 'thumb', 'lose', 'trade'])]
    
    if not map_trade_templates:
        print("No map trade templates found")
        return
    
    print("Available map trade templates:")
    for template in map_trade_templates:
        print(f"  - {template}")
    
    input("\nPress Enter to test detection on current screen...")
    
    # Take screenshot
    screenshot = pyautogui.screenshot()
    screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    # Test each template
    for template_name in map_trade_templates:
        try:
            template_path = os.path.join(template_dir, template_name)
            template = cv2.imread(template_path)
            
            if template is None:
                continue
            
            # Template matching
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.7:  # Good match threshold
                print(f"✅ {template_name}: Found (confidence: {max_val:.2f}) at {max_loc}")
            elif max_val > 0.5:  # Possible match
                print(f"🟡 {template_name}: Possible match (confidence: {max_val:.2f}) at {max_loc}")
            else:
                print(f"❌ {template_name}: Not found (confidence: {max_val:.2f})")
                
        except Exception as e:
            print(f"Error testing {template_name}: {e}")

def capture_confirmation_coordinate():
    """Capture the confirmation button coordinate"""
    print("Confirmation Button Coordinate Capture")
    print("=" * 40)
    print()
    print("Steps:")
    print("1. Navigate to initiate exchange screen")
    print("2. Set up a trade (select pieces)")
    print("3. Click 'Initiate Exchange' to show confirmation dialog")
    print("4. Position mouse over 'Confirm' button")
    print("5. Press Enter to capture coordinate")
    
    input("\nPress Enter when confirmation dialog is visible and mouse is positioned...")
    
    x, y = pyautogui.position()
    print(f"\nConfirmation button coordinate: ({x}, {y})")
    
    # Update the test script
    print(f"\nUpdate test_simple_logic.py with:")
    print(f"confirm_x, confirm_y = {x}, {y}")
    
    # Update the map trade module
    print(f"\nUpdate modules/map_trade.py line ~742 with:")
    print(f"confirm_x, confirm_y = {x}, {y}  # Your calibrated coordinate")
    
    return x, y

def main():
    """Main menu"""
    while True:
        print("\nMap Trade Template Capture")
        print("=" * 30)
        print("1. Capture All Templates")
        print("2. Test Template Detection")
        print("3. Capture Confirmation Coordinate")
        print("0. Exit")
        
        choice = input("\nChoice (0-3): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            capture_map_trade_templates()
        elif choice == '2':
            test_template_detection()
        elif choice == '3':
            capture_confirmation_coordinate()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
