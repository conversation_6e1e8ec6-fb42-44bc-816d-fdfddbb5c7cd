#!/usr/bin/env python3
"""
Direct TrOCR Test - Test the exact coordinates with TrOCR
"""
import cv2
import numpy as np
import pyautogui
from PIL import Image

# TrOCR imports
try:
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    TROCR_AVAILABLE = True
    print("✅ TrOCR imports successful")
except ImportError as e:
    TROCR_AVAILABLE = False
    print(f"❌ TrOCR imports failed: {e}")

def init_trocr():
    """Initialize TrOCR"""
    if not TROCR_AVAILABLE:
        return None, None
    
    try:
        print("Initializing TrOCR...")
        processor = TrOCRProcessor.from_pretrained('microsoft/trocr-base-printed')
        model = VisionEncoderDecoderModel.from_pretrained('microsoft/trocr-base-printed')
        print("✅ TrOCR initialized successfully")
        return processor, model
    except Exception as e:
        print(f"❌ TrOCR initialization failed: {e}")
        return None, None

def capture_region(center_x, center_y, size=20):
    """Capture 20x20 region around center point"""
    x = center_x - size // 2
    y = center_y - size // 2
    screenshot = pyautogui.screenshot(region=(x, y, size, size))
    return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

def extract_with_trocr(image, processor, model):
    """Extract number using TrOCR"""
    try:
        # Convert BGR to RGB for PIL
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Process with TrOCR
        pixel_values = processor(images=pil_image, return_tensors="pt").pixel_values
        generated_ids = model.generate(pixel_values)
        generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
        
        # Filter to only digits
        filtered_text = ''.join(filter(str.isdigit, generated_text))
        
        if filtered_text.isdigit():
            return int(filtered_text)
        else:
            return None
            
    except Exception as e:
        print(f"TrOCR error: {e}")
        return None

def main():
    print("Direct TrOCR Test with Precise Coordinates")
    print("=" * 50)
    
    # Initialize TrOCR
    processor, model = init_trocr()
    if not processor or not model:
        print("❌ Cannot proceed without TrOCR")
        return
    
    # Your precise coordinates
    coordinates = {
        'M1': (532, 436),
        'M2': (613, 436),
        'M3': (696, 436),
        'M4': (775, 436),
        'M5': (577, 513),
        'M6': (654, 521),
        'M7': (735, 519)
    }
    
    print("\nTesting each map piece with TrOCR + 20x20 capture:")
    print("-" * 50)
    
    results = {}
    
    for map_name, (x, y) in coordinates.items():
        print(f"\n{map_name} at ({x}, {y}):")
        
        # Capture 20x20 region
        image = capture_region(x, y, 20)
        
        # Extract with TrOCR
        result = extract_with_trocr(image, processor, model)
        
        if result is not None:
            print(f"  ✅ TrOCR result: {result}")
            results[map_name] = result
        else:
            print(f"  ❌ TrOCR failed")
            results[map_name] = None
    
    print("\n" + "=" * 50)
    print("FINAL RESULTS:")
    print("=" * 50)
    
    success_count = 0
    for map_name, result in results.items():
        if result is not None:
            print(f"{map_name}: {result} ✅")
            success_count += 1
        else:
            print(f"{map_name}: FAILED ❌")
    
    print(f"\nSuccess Rate: {success_count}/7 ({success_count/7*100:.1f}%)")
    
    if success_count == 7:
        print("🎉 PERFECT! All 7 map pieces read successfully!")
    elif success_count >= 5:
        print("👍 Good! Most map pieces working.")
    else:
        print("⚠️ Need coordinate adjustment for failed pieces.")

if __name__ == "__main__":
    main()
