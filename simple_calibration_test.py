#!/usr/bin/env python3
"""
Simple calibration test - no crashes, just basic coordinate capture
"""

import time
import pyauto<PERSON><PERSON>

def simple_coordinate_capture():
    """Simple coordinate capture without complex dependencies"""
    print("Simple Map Trade Coordinate Capture")
    print("=" * 50)
    
    coordinates = {}
    map_pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
    
    print("For each map piece:")
    print("1. Position your mouse over the CENTER of the map piece")
    print("2. Press Enter to capture the coordinates")
    print()
    
    for map_name in map_pieces:
        print(f"Calibrating {map_name}:")
        print("Position your mouse over the CENTER of the map piece")
        
        try:
            input("Press Enter to capture current mouse position...")
            x, y = pyautogui.position()
            
            # Create region around the center point
            width, height = 80, 100
            region_x = x - width // 2
            region_y = y - height // 2
            
            coordinates[map_name] = (region_x, region_y, width, height)
            print(f"✅ {map_name}: ({region_x}, {region_y}, {width}, {height})")
            print()
            
        except Exception as e:
            print(f"❌ Error capturing {map_name}: {e}")
            continue
    
    print("=" * 50)
    print("CALIBRATION RESULTS:")
    print("=" * 50)
    print("Copy these coordinates to your map_trade.py file:")
    print()
    print("def _define_map_piece_regions(self):")
    print("    return {")
    
    for map_name, coords in coordinates.items():
        print(f"        '{map_name}': {coords},")
    
    print("    }")
    print()
    print("=" * 50)
    
    return coordinates

def test_single_region():
    """Test OCR on a single region"""
    print("Single Region OCR Test")
    print("=" * 30)
    
    print("Position your mouse over a map piece with a visible number")
    input("Press Enter to capture and test OCR...")
    
    try:
        import cv2
        import numpy as np
        import pytesseract
        
        # Configure Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # Get mouse position
        x, y = pyautogui.position()
        
        # Define region around mouse
        width, height = 80, 100
        region_x = x - width // 2
        region_y = y - height // 2
        
        print(f"Testing region: ({region_x}, {region_y}, {width}, {height})")
        
        # Capture region
        screenshot = pyautogui.screenshot(region=(region_x, region_y, width, height))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Try OCR
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        print(f"OCR Result: '{text}'")
        
        if text.isdigit():
            print(f"✅ Successfully detected number: {int(text)}")
        else:
            print("❌ Could not detect a valid number")
            
            # Try with different preprocessing
            mask = cv2.inRange(gray, 0, 50)
            text2 = pytesseract.image_to_string(mask, config=config).strip()
            print(f"OCR with mask: '{text2}'")
            
            if text2.isdigit():
                print(f"✅ Mask method detected: {int(text2)}")
        
    except Exception as e:
        print(f"❌ OCR test failed: {e}")

def main():
    """Main menu"""
    while True:
        print("\nSimple Calibration Test")
        print("=" * 30)
        print("1. Capture Map Piece Coordinates")
        print("2. Test Single Region OCR")
        print("0. Exit")
        
        choice = input("\nEnter choice (0-2): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            simple_coordinate_capture()
        elif choice == '2':
            test_single_region()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
