#!/usr/bin/env python3
"""
Quick Button Calibration Tool for Full Screen
"""
import pyautogui
import time

def calibrate_buttons():
    """Fast button calibration"""
    print("🎯 FULL SCREEN BUTTON CALIBRATION")
    print("=" * 35)
    
    buttons = [
        'you_will_lose',
        'initiate_exchange', 
        'confirm_exchange'
    ]
    
    coords = {}
    
    for button in buttons:
        print(f"\nPosition mouse over '{button}' button")
        input("Press ENTER when ready: ")
        x, y = pyautogui.position()
        coords[button] = (x, y)
        print(f"✅ {button}: ({x}, {y})")
    
    print(f"\n📋 COPY THESE COORDINATES:")
    print("BUTTON_COORDINATES = {")
    for button, (x, y) in coords.items():
        print(f"    '{button}': ({x}, {y}),")
    print("}")
    
    return coords

if __name__ == "__main__":
    print("Navigate to map trade screen and position for button calibration")
    input("Press ENTER to start: ")
    calibrate_buttons()
