#!/usr/bin/env python3
"""
Test script to verify ESC key functionality in alliance donation module
"""

import sys
import os
import time
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.alliance_donation import AllianceDonationModule
from module_config_manager import ModuleConfigManager

def test_esc_key_loading():
    """Test if ESC key configuration is loaded correctly"""
    print("🧪 Testing ESC key configuration loading...")
    
    # Create module config manager
    config_manager = ModuleConfigManager()
    
    # Get alliance donation config
    config = config_manager.get_module_config('alliance_donation')
    click_coordinates = config.get('click_coordinates', [])
    
    print(f"📋 Found {len(click_coordinates)} click coordinates")
    
    # Check for ESC key coordinates
    esc_coordinates = []
    for coord in click_coordinates:
        if coord.get('use_esc_key', False):
            esc_coordinates.append(coord)
            print(f"✅ ESC coordinate found: {coord['name']} - {coord.get('description', 'No description')}")
    
    print(f"🔑 Total ESC key coordinates: {len(esc_coordinates)}")
    return esc_coordinates

def test_sequence_loading():
    """Test if the alliance donation module loads the sequence correctly"""
    print("\n🧪 Testing alliance donation sequence loading...")
    
    # Create alliance donation module
    module = AllianceDonationModule()
    
    # Check the loaded sequence
    sequence = module.donation_click_sequence
    print(f"📋 Loaded sequence has {len(sequence)} steps")
    
    # Check each step for ESC key usage
    esc_steps = []
    for i, step_data in enumerate(sequence):
        print(f"Step {i+1}: {len(step_data)} items - {step_data}")
        
        # Check if this step has ESC key flag
        if len(step_data) >= 6:  # Should have 6 items including use_esc_key
            use_esc_key = step_data[5]  # 6th item should be use_esc_key
            if use_esc_key:
                esc_steps.append((i+1, step_data))
                print(f"  ✅ Step {i+1} uses ESC key!")
        else:
            print(f"  ⚠️ Step {i+1} has only {len(step_data)} items (expected 6)")
    
    print(f"🔑 Steps using ESC key: {len(esc_steps)}")
    return esc_steps

def test_execution_logic():
    """Test the execution logic for ESC key steps"""
    print("\n🧪 Testing execution logic...")
    
    # Create test step data with ESC key enabled
    test_step_esc = (100, 100, 1.0, 1, "Test ESC step", True)
    test_step_click = (200, 200, 1.0, 1, "Test click step", False)
    
    print("Testing step parsing logic:")
    
    # Test ESC step
    if len(test_step_esc) == 6:
        x, y, delay, repeat_count, description, use_esc_key = test_step_esc
        print(f"✅ ESC step parsed: use_esc_key={use_esc_key}, description='{description}'")
    else:
        print(f"❌ ESC step parsing failed: {len(test_step_esc)} items")
    
    # Test click step
    if len(test_step_click) == 6:
        x, y, delay, repeat_count, description, use_esc_key = test_step_click
        print(f"✅ Click step parsed: use_esc_key={use_esc_key}, description='{description}'")
    else:
        print(f"❌ Click step parsing failed: {len(test_step_click)} items")

if __name__ == "__main__":
    print("🚀 ESC Key Functionality Test")
    print("=" * 50)
    
    try:
        # Test 1: Configuration loading
        esc_coords = test_esc_key_loading()
        
        # Test 2: Sequence loading
        esc_steps = test_sequence_loading()
        
        # Test 3: Execution logic
        test_execution_logic()
        
        print("\n" + "=" * 50)
        print("📊 Test Summary:")
        print(f"✅ ESC coordinates in config: {len(esc_coords)}")
        print(f"✅ ESC steps in sequence: {len(esc_steps)}")
        
        if len(esc_coords) > 0 and len(esc_steps) > 0:
            print("🎉 ESC key functionality appears to be working!")
        else:
            print("❌ ESC key functionality has issues!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
