#!/usr/bin/env python3
"""
Test Enhanced OCR Logging and Configuration System
Comprehensive testing of the new OCR monitoring and debugging features
"""

import cv2
import numpy as np
import time
import json
import os
from datetime import datetime

def create_test_images():
    """Create test images with numbers for OCR testing"""
    test_images = {}
    
    # Create images with different numbers and qualities
    test_cases = [
        ("M1_clear", "12", (255, 255, 255), (0, 0, 0)),      # Clear white background
        ("M2_noisy", "31", (200, 200, 200), (50, 50, 50)),   # Noisy background
        ("M3_small", "7", (255, 255, 255), (0, 0, 0)),       # Small number
        ("M4_blurry", "25", (255, 255, 255), (0, 0, 0)),     # Will be blurred
        ("M5_contrast", "8", (100, 100, 100), (200, 200, 200)), # Low contrast
        ("M6_rotated", "14", (255, 255, 255), (0, 0, 0)),    # Will be rotated
        ("M7_damaged", "3", (255, 255, 255), (0, 0, 0))      # Will be damaged
    ]
    
    for name, text, bg_color, text_color in test_cases:
        # Create base image
        img = np.full((35, 35, 3), bg_color, dtype=np.uint8)
        
        # Add text
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        thickness = 2
        
        # Get text size and center it
        (text_width, text_height), _ = cv2.getTextSize(text, font, font_scale, thickness)
        x = (35 - text_width) // 2
        y = (35 + text_height) // 2
        
        cv2.putText(img, text, (x, y), font, font_scale, text_color, thickness)
        
        # Apply specific modifications
        if "blurry" in name:
            img = cv2.GaussianBlur(img, (3, 3), 1.0)
        elif "rotated" in name:
            center = (17, 17)
            rotation_matrix = cv2.getRotationMatrix2D(center, 15, 1.0)
            img = cv2.warpAffine(img, rotation_matrix, (35, 35))
        elif "damaged" in name:
            # Add some noise
            noise = np.random.randint(0, 50, img.shape, dtype=np.uint8)
            img = cv2.add(img, noise)
        
        test_images[name] = img
    
    return test_images

def test_enhanced_ocr_logging():
    """Test the enhanced OCR logging system"""
    print("🧪 Testing Enhanced OCR Logging System")
    print("=" * 50)
    
    try:
        # Import the enhanced OCR service
        from ai_ocr_service import AINumberRecognition
        
        # Create OCR service with debug configuration
        debug_config = {
            'enable_detailed_logging': True,
            'save_debug_images': True,
            'log_confidence_scores': True,
            'log_processing_times': True,
            'log_preprocessing_steps': True,
            'log_raw_ocr_output': True,
            'confidence_threshold': 0.3,
            'save_failed_detections': True,
            'max_debug_images': 50,
            'debug_image_format': 'png'
        }
        
        ocr_service = AINumberRecognition(debug_config=debug_config)
        
        if not ocr_service.active_backends:
            print("❌ No OCR backends available")
            return False
        
        print(f"✅ OCR service initialized with backends: {ocr_service.active_backends}")
        print(f"✅ GPU acceleration: {'enabled' if ocr_service.gpu_available else 'disabled'}")
        
        # Create test images
        print("\n📸 Creating test images...")
        test_images = create_test_images()
        print(f"✅ Created {len(test_images)} test images")
        
        # Test each image with detailed logging
        print("\n🔍 Testing OCR with enhanced logging...")
        results = {}
        
        for piece_id, image in test_images.items():
            print(f"\n--- Testing {piece_id} ---")
            
            start_time = time.time()
            result = ocr_service.recognize_number(image, fast_mode=True, piece_id=piece_id)
            processing_time = time.time() - start_time
            
            results[piece_id] = {
                'result': result,
                'processing_time': processing_time,
                'expected': piece_id.split('_')[0]  # Extract expected from name
            }
            
            if result is not None:
                print(f"✅ {piece_id}: Detected {result} in {processing_time:.3f}s")
            else:
                print(f"❌ {piece_id}: Failed to detect number in {processing_time:.3f}s")
        
        # Display comprehensive statistics
        print("\n📊 OCR Performance Statistics")
        print("=" * 30)
        
        stats = ocr_service.get_ocr_statistics()
        print(f"Total Processed: {stats['total_processed']}")
        print(f"Successful Detections: {stats['successful_detections']}")
        print(f"Success Rate: {stats.get('success_rate', 0):.1%}")
        
        if stats.get('avg_processing_time', 0) > 0:
            print(f"Average Processing Time: {stats['avg_processing_time']:.3f}s")
            print(f"Processing Time Range: {stats.get('min_processing_time', 0):.3f}s - {stats.get('max_processing_time', 0):.3f}s")
        
        if stats.get('avg_confidence', 0) > 0:
            print(f"Average Confidence: {stats['avg_confidence']:.3f}")
            print(f"Confidence Range: {stats.get('min_confidence', 0):.3f} - {stats.get('max_confidence', 0):.3f}")
        
        if stats.get('method_usage'):
            print("\nMethod Usage:")
            for method, count in stats['method_usage'].items():
                percentage = (count / stats['total_processed']) * 100 if stats['total_processed'] > 0 else 0
                print(f"  {method}: {count} ({percentage:.1f}%)")
        
        # Test configuration updates
        print("\n⚙️ Testing Configuration Updates...")
        
        new_config = {
            'confidence_threshold': 0.5,
            'enable_detailed_logging': False
        }
        
        ocr_service.update_debug_config(new_config)
        updated_config = ocr_service.get_debug_config()
        
        if updated_config['confidence_threshold'] == 0.5:
            print("✅ Configuration update successful")
        else:
            print("❌ Configuration update failed")
        
        # Test performance summary logging
        print("\n📋 Performance Summary:")
        ocr_service.log_performance_summary()
        
        # Check debug directories
        print("\n📁 Debug Output Check:")
        debug_dirs = ['debug/ocr_images', 'debug/ocr_preprocessing', 'debug/ocr_successful', 'debug/ocr_failed']
        
        for debug_dir in debug_dirs:
            if os.path.exists(debug_dir):
                file_count = len([f for f in os.listdir(debug_dir) if f.endswith(('.png', '.jpg'))])
                print(f"✅ {debug_dir}: {file_count} debug images")
            else:
                print(f"⚠️ {debug_dir}: Directory not found")
        
        # Test statistics reset
        print("\n🔄 Testing Statistics Reset...")
        ocr_service.reset_statistics()
        reset_stats = ocr_service.get_ocr_statistics()
        
        if reset_stats['total_processed'] == 0:
            print("✅ Statistics reset successful")
        else:
            print("❌ Statistics reset failed")
        
        print("\n🎉 Enhanced OCR Logging Test Complete!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_monitor_gui():
    """Test the OCR Monitor GUI (basic import test)"""
    print("\n🖥️ Testing OCR Monitor GUI")
    print("=" * 30)
    
    try:
        import ocr_monitor_gui
        print("✅ OCR Monitor GUI module imported successfully")
        
        # Test GUI creation (without showing)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        app = ocr_monitor_gui.OCRMonitorGUI(root)
        print("✅ OCR Monitor GUI created successfully")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False

def test_map_trade_integration():
    """Test integration with map trade module"""
    print("\n🗺️ Testing Map Trade Integration")
    print("=" * 35)
    
    try:
        # Test that map trade module can use enhanced OCR
        import sys
        import os
        sys.path.append('modules')
        
        from map_trade import MapTradeModule
        
        # Create a mock GUI object
        class MockGUI:
            def __init__(self):
                self.config = {
                    'map_trade': {
                        'enabled': True,
                        'delay': 1.0
                    }
                }
        
        mock_gui = MockGUI()
        map_trade = MapTradeModule(mock_gui)
        
        print("✅ Map trade module imported successfully")
        print("✅ Enhanced OCR integration ready")
        
        return True
        
    except Exception as e:
        print(f"❌ Map trade integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Enhanced OCR Logging & Configuration Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Enhanced OCR Logging", test_enhanced_ocr_logging()))
    test_results.append(("OCR Monitor GUI", test_ocr_monitor_gui()))
    test_results.append(("Map Trade Integration", test_map_trade_integration()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Enhanced OCR system is ready for use.")
    else:
        print("⚠️ Some tests failed. Please check the output above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
