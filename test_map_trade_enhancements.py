#!/usr/bin/env python3
"""
Comprehensive Test Suite for Map Trade Enhancements
Tests both coordinate persistence fixes and OCR scale tolerance improvements
"""
import sys
import os
import time
import json
import cv2
import numpy as np
from typing import Dict, Any, Optional, List

# Add modules directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_coordinate_persistence_fix():
    """Test that coordinate updates now persist correctly"""
    print("🧪 TESTING COORDINATE PERSISTENCE FIX")
    print("=" * 50)
    
    try:
        from unified_config_manager import UnifiedConfigManager
        
        # Load current config
        config_manager = UnifiedConfigManager()
        config_data = config_manager.load_config()
        
        # Create test coordinates
        test_coordinates = {
            'M1': {'quantity_x': 1100, 'quantity_y': 850, 'click_x': 1050, 'click_y': 790},
            'M2': {'quantity_x': 1260, 'quantity_y': 850, 'click_x': 1220, 'click_y': 790},
            'M3': {'quantity_x': 1420, 'quantity_y': 850, 'click_x': 1380, 'click_y': 790}
        }
        
        print("📝 Setting test coordinates in unified config...")
        
        # Ensure structure exists
        if 'modules' not in config_data:
            config_data['modules'] = {}
        if 'map_trade' not in config_data['modules']:
            config_data['modules']['map_trade'] = {}
        
        # Set test coordinates
        config_data['modules']['map_trade']['map_piece_coordinates'] = test_coordinates
        
        # Save config
        config_manager.config_data = config_data
        config_manager.save_config()
        
        print("✅ Test coordinates saved to unified config")
        
        # Now test if module loads these coordinates
        print("🔄 Testing module coordinate loading...")
        
        from map_trade import MapTradeModule
        module = MapTradeModule()
        
        # Check if coordinates were loaded correctly
        success_count = 0
        total_tests = len(test_coordinates)
        
        for piece, expected_coords in test_coordinates.items():
            if hasattr(module, 'map_piece_regions') and piece in module.map_piece_regions:
                loaded_coords = module.map_piece_regions[piece]
                expected_click = (expected_coords['click_x'], expected_coords['click_y'])
                actual_click = loaded_coords.get('click_pos', (0, 0))
                
                if actual_click == expected_click:
                    print(f"✅ {piece}: Coordinates loaded correctly {actual_click}")
                    success_count += 1
                else:
                    print(f"❌ {piece}: Expected {expected_click}, got {actual_click}")
            else:
                print(f"❌ {piece}: Not found in loaded regions")
        
        if success_count == total_tests:
            print(f"🎉 COORDINATE PERSISTENCE FIX: SUCCESS ({success_count}/{total_tests})")
            return True
        else:
            print(f"⚠️ COORDINATE PERSISTENCE FIX: PARTIAL ({success_count}/{total_tests})")
            return False
            
    except Exception as e:
        print(f"❌ Error testing coordinate persistence: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_scale_tolerance():
    """Test OCR scale tolerance improvements"""
    print("\n🔍 TESTING OCR SCALE TOLERANCE IMPROVEMENTS")
    print("=" * 50)
    
    try:
        from map_trade import MapTradeModule
        
        module = MapTradeModule()
        
        # Test multi-scale OCR with synthetic images
        test_results = []
        
        # Create test images with numbers at different scales
        test_cases = [
            {'number': '5', 'scale': 0.8, 'description': 'Small scale (0.8x)'},
            {'number': '12', 'scale': 1.0, 'description': 'Normal scale (1.0x)'},
            {'number': '7', 'scale': 1.2, 'description': 'Large scale (1.2x)'},
            {'number': '23', 'scale': 1.5, 'description': 'Very large scale (1.5x)'}
        ]
        
        for test_case in test_cases:
            print(f"🧪 Testing {test_case['description']}: '{test_case['number']}'")
            
            # Create synthetic test image
            test_image = create_synthetic_number_image(test_case['number'], test_case['scale'])
            
            if test_image is not None:
                # Test enhanced OCR
                result = module._extract_number_from_image(test_image)
                expected = int(test_case['number'])
                
                if result == expected:
                    print(f"✅ OCR Success: {result} (expected {expected})")
                    test_results.append(True)
                else:
                    print(f"❌ OCR Failed: {result} (expected {expected})")
                    test_results.append(False)
                    
                    # Save debug image
                    debug_filename = f"ocr_debug_{test_case['number']}_scale_{test_case['scale']}.png"
                    cv2.imwrite(debug_filename, test_image)
                    print(f"💾 Debug image saved: {debug_filename}")
            else:
                print(f"❌ Failed to create test image")
                test_results.append(False)
        
        success_count = sum(test_results)
        total_tests = len(test_results)
        
        if success_count >= total_tests * 0.75:  # 75% success rate acceptable
            print(f"🎉 OCR SCALE TOLERANCE: SUCCESS ({success_count}/{total_tests})")
            return True
        else:
            print(f"⚠️ OCR SCALE TOLERANCE: NEEDS IMPROVEMENT ({success_count}/{total_tests})")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR scale tolerance: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_synthetic_number_image(number_text: str, scale: float) -> Optional[np.ndarray]:
    """Create a synthetic image with a number for testing OCR"""
    try:
        # Base image size
        base_size = 30
        scaled_size = int(base_size * scale)
        
        # Create white background
        image = np.ones((scaled_size, scaled_size * len(number_text), 3), dtype=np.uint8) * 255
        
        # Add black text
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = scale * 0.8
        thickness = max(1, int(scale * 2))
        
        # Calculate text size and position
        (text_width, text_height), baseline = cv2.getTextSize(number_text, font, font_scale, thickness)
        
        x = (image.shape[1] - text_width) // 2
        y = (image.shape[0] + text_height) // 2
        
        cv2.putText(image, number_text, (x, y), font, font_scale, (0, 0, 0), thickness)
        
        return image
        
    except Exception as e:
        print(f"Error creating synthetic image: {e}")
        return None

def test_coordinate_loading_priority():
    """Test that configuration coordinates have priority over hardcoded ones"""
    print("\n🎯 TESTING COORDINATE LOADING PRIORITY")
    print("=" * 50)
    
    try:
        from map_trade import MapTradeModule
        from unified_config_manager import UnifiedConfigManager
        
        # Set unique test coordinates that are different from hardcoded ones
        config_manager = UnifiedConfigManager()
        config_data = config_manager.load_config()
        
        unique_coords = {
            'M1': {'quantity_x': 9999, 'quantity_y': 9999, 'click_x': 8888, 'click_y': 8888}
        }
        
        # Ensure structure exists
        if 'modules' not in config_data:
            config_data['modules'] = {}
        if 'map_trade' not in config_data['modules']:
            config_data['modules']['map_trade'] = {}
        
        config_data['modules']['map_trade']['map_piece_coordinates'] = unique_coords
        config_manager.config_data = config_data
        config_manager.save_config()
        
        print("📝 Set unique test coordinates (8888, 8888)")
        
        # Load module and check coordinates
        module = MapTradeModule()
        
        if hasattr(module, 'map_piece_regions') and 'M1' in module.map_piece_regions:
            loaded_click = module.map_piece_regions['M1'].get('click_pos', (0, 0))
            
            if loaded_click == (8888, 8888):
                print("✅ Configuration coordinates have priority over hardcoded ones")
                return True
            else:
                print(f"❌ Hardcoded coordinates still being used: {loaded_click}")
                return False
        else:
            print("❌ Could not test coordinate priority - M1 not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing coordinate priority: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run all enhancement tests"""
    print("🚀 MAP TRADE ENHANCEMENTS - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    test_results = {
        'coordinate_persistence': test_coordinate_persistence_fix(),
        'ocr_scale_tolerance': test_ocr_scale_tolerance(),
        'coordinate_priority': test_coordinate_loading_priority()
    }
    
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    for test_name, passed in test_results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        description = {
            'coordinate_persistence': 'Coordinate updates now persist correctly',
            'ocr_scale_tolerance': 'OCR works with different image scales',
            'coordinate_priority': 'Config coordinates override hardcoded ones'
        }
        
        print(f"   {description[test_name]}: {status}")
    
    passed_count = sum(test_results.values())
    total_count = len(test_results)
    
    print(f"\n🎯 OVERALL RESULT: {passed_count}/{total_count} enhancements working")
    
    if passed_count == total_count:
        print("🎉 ALL ENHANCEMENTS SUCCESSFUL!")
        print("   ✅ Coordinate persistence issue resolved")
        print("   ✅ OCR scale tolerance improved")
        print("   ✅ Configuration system working correctly")
    elif passed_count > 0:
        print("⚠️ PARTIAL SUCCESS - Some enhancements working")
        print("   Review failed tests and apply additional fixes")
    else:
        print("❌ ENHANCEMENTS NEED MORE WORK")
        print("   All tests failed - check implementation")
    
    return test_results

def main():
    """Main test execution"""
    results = run_comprehensive_test()
    
    # Exit with appropriate code
    passed_count = sum(results.values())
    total_count = len(results)
    
    if passed_count == total_count:
        sys.exit(0)  # All tests passed
    else:
        sys.exit(1)  # Some tests failed

if __name__ == "__main__":
    main()
