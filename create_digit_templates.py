#!/usr/bin/env python3
"""
Create digit templates for template matching number recognition
"""

import os
import cv2
import numpy as np
import pyautogui

def create_templates_directory():
    """Create templates directory structure"""
    os.makedirs("templates/digits", exist_ok=True)
    print("Created templates/digits directory")

def capture_digit_template(digit: int):
    """Capture a template for a specific digit"""
    print(f"\nCapturing template for digit: {digit}")
    print("1. Navigate to a screen where you can see the digit clearly")
    print("2. Position your mouse over the digit")
    print("3. Press Enter to capture")
    
    input("Press Enter when ready...")
    
    try:
        # Get mouse position
        x, y = pyautogui.position()
        
        # Capture small region around mouse
        region_size = 30  # Adjust based on digit size
        region_x = x - region_size // 2
        region_y = y - region_size // 2
        
        # Capture screenshot
        screenshot = pyautogui.screenshot(region=(region_x, region_y, region_size, region_size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold to get clean digit
        _, binary = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
        
        # Save template
        template_path = f"templates/digits/{digit}.png"
        cv2.imwrite(template_path, binary)
        
        print(f"✅ Template saved: {template_path}")
        print(f"   Region: ({region_x}, {region_y}, {region_size}, {region_size})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error capturing digit {digit}: {e}")
        return False

def test_template_matching():
    """Test template matching with captured templates"""
    print("\nTesting Template Matching")
    print("=" * 30)
    
    # Load all templates
    templates = {}
    for digit in range(10):
        template_path = f"templates/digits/{digit}.png"
        if os.path.exists(template_path):
            template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
            if template is not None:
                templates[digit] = template
                print(f"✅ Loaded template for digit {digit}")
    
    if not templates:
        print("❌ No templates found. Create templates first.")
        return
    
    print(f"\nLoaded {len(templates)} digit templates")
    print("Position your mouse over a digit to test recognition")
    input("Press Enter to test...")
    
    try:
        # Capture test region
        x, y = pyautogui.position()
        region_size = 30
        region_x = x - region_size // 2
        region_y = y - region_size // 2
        
        screenshot = pyautogui.screenshot(region=(region_x, region_y, region_size, region_size))
        test_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        test_gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        
        # Test against all templates
        best_match = None
        best_score = 0
        
        print("\nTemplate matching results:")
        for digit, template in templates.items():
            # Resize template to match test image
            template_resized = cv2.resize(template, (test_gray.shape[1], test_gray.shape[0]))
            
            # Template matching
            result = cv2.matchTemplate(test_gray, template_resized, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, _ = cv2.minMaxLoc(result)
            
            print(f"  Digit {digit}: {max_val:.3f}")
            
            if max_val > best_score:
                best_score = max_val
                best_match = digit
        
        print(f"\n🎯 Best match: Digit {best_match} (confidence: {best_score:.3f})")
        
        if best_score > 0.6:
            print("✅ Good match! Template matching should work well.")
        else:
            print("⚠️ Low confidence. May need better templates or different approach.")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def create_all_templates():
    """Interactive tool to create all digit templates"""
    print("Digit Template Creation Tool")
    print("=" * 40)
    print("This will help you create templates for digits 0-9")
    print("You need to find examples of each digit in your game UI")
    print()
    
    create_templates_directory()
    
    # Create templates for digits 1-7 (most relevant for map pieces)
    important_digits = [1, 2, 3, 4, 5, 6, 7]
    
    for digit in important_digits:
        print(f"\n{'='*20}")
        print(f"Creating template for digit: {digit}")
        print(f"{'='*20}")
        
        success = capture_digit_template(digit)
        if not success:
            retry = input(f"Retry digit {digit}? (y/n): ").lower().strip()
            if retry == 'y':
                capture_digit_template(digit)
    
    print("\n" + "="*40)
    print("Template creation complete!")
    print("Templates saved in: templates/digits/")
    print("You can now use template matching for number recognition.")

def main():
    """Main menu"""
    while True:
        print("\nDigit Template Creation Tool")
        print("=" * 40)
        print("1. Create All Templates (1-7)")
        print("2. Create Single Template")
        print("3. Test Template Matching")
        print("4. List Existing Templates")
        print("0. Exit")
        
        choice = input("\nEnter choice (0-4): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            create_all_templates()
        elif choice == '2':
            try:
                digit = int(input("Enter digit (0-9): "))
                if 0 <= digit <= 9:
                    create_templates_directory()
                    capture_digit_template(digit)
                else:
                    print("Invalid digit. Must be 0-9.")
            except ValueError:
                print("Invalid input. Enter a number.")
        elif choice == '3':
            test_template_matching()
        elif choice == '4':
            print("\nExisting templates:")
            for digit in range(10):
                template_path = f"templates/digits/{digit}.png"
                if os.path.exists(template_path):
                    print(f"  ✅ {digit}.png")
                else:
                    print(f"  ❌ {digit}.png (missing)")
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
