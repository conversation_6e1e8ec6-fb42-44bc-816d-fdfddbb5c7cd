#!/usr/bin/env python3
"""
Test multi-digit number recognition for map piece quantities
"""

import cv2
import numpy as np
import pyautogui
import pytesseract

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def test_single_number_ocr():
    """Test OCR on a single number region"""
    print("Single Number OCR Test")
    print("=" * 30)
    print("Position your mouse over a map piece quantity number")
    print("This will capture a region and try multiple OCR methods")
    
    input("Press Enter when ready...")
    
    try:
        # Get mouse position
        x, y = pyautogui.position()
        
        # Capture region around mouse (adjust size as needed)
        width, height = 60, 30  # Wider for multi-digit numbers
        region_x = x - width // 2
        region_y = y - height // 2
        
        print(f"Capturing region: ({region_x}, {region_y}, {width}, {height})")
        
        # Capture screenshot
        screenshot = pyautogui.screenshot(region=(region_x, region_y, width, height))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        print("\nTrying multiple OCR methods:")
        
        # Method 1: Direct OCR
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text1 = pytesseract.image_to_string(gray, config=config).strip()
        print(f"Method 1 (Direct): '{text1}' -> {int(text1) if text1.isdigit() else 'FAILED'}")
        
        # Method 2: Binary threshold
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        text2 = pytesseract.image_to_string(binary, config=config).strip()
        print(f"Method 2 (Binary): '{text2}' -> {int(text2) if text2.isdigit() else 'FAILED'}")
        
        # Method 3: Inverted binary
        _, binary_inv = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        text3 = pytesseract.image_to_string(binary_inv, config=config).strip()
        print(f"Method 3 (Inverted): '{text3}' -> {int(text3) if text3.isdigit() else 'FAILED'}")
        
        # Method 4: Adaptive threshold
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        text4 = pytesseract.image_to_string(adaptive, config=config).strip()
        print(f"Method 4 (Adaptive): '{text4}' -> {int(text4) if text4.isdigit() else 'FAILED'}")
        
        # Method 5: Different PSM modes
        for psm in [6, 7, 8, 13]:
            config_psm = f'--psm {psm} -c tessedit_char_whitelist=0123456789'
            text_psm = pytesseract.image_to_string(gray, config=config_psm).strip()
            print(f"Method PSM{psm}: '{text_psm}' -> {int(text_psm) if text_psm.isdigit() else 'FAILED'}")
        
        # Find successful results
        successful_results = []
        for method, text in [("Direct", text1), ("Binary", text2), ("Inverted", text3), ("Adaptive", text4)]:
            if text.isdigit():
                successful_results.append((method, int(text)))
        
        if successful_results:
            print(f"\n✅ Successful methods: {successful_results}")
            # Check if all successful methods agree
            values = [result[1] for result in successful_results]
            if len(set(values)) == 1:
                print(f"🎯 All methods agree: {values[0]}")
            else:
                print(f"⚠️ Methods disagree: {values}")
        else:
            print("\n❌ All OCR methods failed")
        
        # Save images for debugging
        cv2.imwrite("debug_original.png", gray)
        cv2.imwrite("debug_binary.png", binary)
        cv2.imwrite("debug_binary_inv.png", binary_inv)
        cv2.imwrite("debug_adaptive.png", adaptive)
        print("\nDebug images saved: debug_*.png")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_all_map_pieces():
    """Test OCR on all 7 map piece regions"""
    print("All Map Pieces OCR Test")
    print("=" * 30)
    print("This will test OCR on all 7 map piece quantity regions")
    print("Make sure you're on the initiate exchange screen")
    
    # Default regions (need calibration)
    map_regions = {
        'M1': (100, 200, 60, 30),
        'M2': (200, 200, 60, 30),
        'M3': (300, 200, 60, 30),
        'M4': (400, 200, 60, 30),
        'M5': (500, 200, 60, 30),
        'M6': (600, 200, 60, 30),
        'M7': (700, 200, 60, 30),
    }
    
    input("Press Enter to test all regions...")
    
    results = {}
    
    for map_name, (x, y, width, height) in map_regions.items():
        print(f"\nTesting {map_name} at ({x}, {y}, {width}, {height}):")
        
        try:
            # Capture region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Try multiple OCR methods
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            
            # Method 1: Direct
            text1 = pytesseract.image_to_string(gray, config=config).strip()
            
            # Method 2: Binary threshold
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
            text2 = pytesseract.image_to_string(binary, config=config).strip()
            
            # Find best result
            if text1.isdigit():
                results[map_name] = int(text1)
                print(f"  ✅ {map_name}: {text1}")
            elif text2.isdigit():
                results[map_name] = int(text2)
                print(f"  ✅ {map_name}: {text2} (binary)")
            else:
                print(f"  ❌ {map_name}: Failed ('{text1}', '{text2}')")
                
        except Exception as e:
            print(f"  ❌ {map_name}: Error - {e}")
    
    print(f"\n{'='*30}")
    print("RESULTS SUMMARY:")
    print(f"{'='*30}")
    
    if results:
        for map_name, quantity in results.items():
            print(f"{map_name}: {quantity}")
        
        # Find optimal trade
        if len(results) >= 2:
            lowest_map = min(results.keys(), key=lambda k: results[k])
            highest_map = max(results.keys(), key=lambda k: results[k])
            
            print(f"\n🎯 OPTIMAL TRADE:")
            print(f"Give: {lowest_map} ({results[lowest_map]} pieces)")
            print(f"Receive: {highest_map} ({results[highest_map]} pieces)")
    else:
        print("❌ No successful OCR results")

def calibrate_number_regions():
    """Calibrate regions for number recognition"""
    print("Number Region Calibration")
    print("=" * 30)
    print("This will help you calibrate regions for map piece quantities")
    
    regions = {}
    
    for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
        print(f"\nCalibrating {map_name}:")
        print("Position your mouse over the QUANTITY NUMBER (not the map piece)")
        input("Press Enter to capture...")
        
        x, y = pyautogui.position()
        width, height = 60, 30  # Good size for multi-digit numbers
        region_x = x - width // 2
        region_y = y - height // 2
        
        regions[map_name] = (region_x, region_y, width, height)
        print(f"  {map_name}: ({region_x}, {region_y}, {width}, {height})")
    
    print(f"\n{'='*40}")
    print("CALIBRATED REGIONS:")
    print(f"{'='*40}")
    print("Copy these to your map_trade.py file:")
    print()
    print("map_regions = {")
    for map_name, coords in regions.items():
        print(f"    '{map_name}': {coords},")
    print("}")
    
    return regions

def main():
    """Main menu"""
    while True:
        print("\nNumber Recognition Test")
        print("=" * 30)
        print("1. Test Single Number OCR")
        print("2. Test All Map Pieces")
        print("3. Calibrate Number Regions")
        print("0. Exit")
        
        choice = input("\nEnter choice (0-3): ").strip()
        
        if choice == '0':
            print("Exiting...")
            break
        elif choice == '1':
            test_single_number_ocr()
        elif choice == '2':
            test_all_map_pieces()
        elif choice == '3':
            calibrate_number_regions()
        else:
            print("Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
