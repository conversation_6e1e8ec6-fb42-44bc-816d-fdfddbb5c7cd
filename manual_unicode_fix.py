#!/usr/bin/env python3
"""
Manual Unicode fix - only replace the most problematic characters
"""

import os

def manual_fix_file(file_path):
    """Manually fix only the most problematic Unicode characters"""
    
    try:
        # Read file as binary to avoid encoding issues
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # Convert to string
        content_str = content.decode('utf-8', errors='ignore')
        
        # Only replace the most problematic Unicode characters
        # These are the ones that cause the most encoding errors
        replacements = [
            ('🗺️', ''),
            ('🔍', ''),
            ('🔄', ''),
            ('✅', ''),
            ('❌', ''),
            ('⚠️', ''),
            ('💰', ''),
            ('🎯', ''),
            ('🚀', ''),
            ('⏸️', ''),
        ]
        
        changes_made = 0
        for old_char, new_char in replacements:
            if old_char in content_str:
                content_str = content_str.replace(old_char, new_char)
                changes_made += 1
        
        # Write back
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content_str)
            print(f"Fixed {changes_made} Unicode issues in {file_path}")
        else:
            print(f"No Unicode issues in {file_path}")
            
        return changes_made
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0

def main():
    """Fix Unicode in key files only"""
    
    print("Manual Unicode Fix")
    print("=" * 20)
    
    # Test if system works first
    try:
        import modules.map_trade
        print("✓ Map trade module loads successfully")
    except Exception as e:
        print(f"✗ Map trade module error: {e}")
    
    try:
        import modules.enhanced_help_click  
        print("✓ Enhanced help click module loads successfully")
    except Exception as e:
        print(f"✗ Enhanced help click module error: {e}")
    
    try:
        import modules.zombie_invasion
        print("✓ Zombie invasion module loads successfully") 
    except Exception as e:
        print(f"✗ Zombie invasion module error: {e}")

if __name__ == "__main__":
    main()
