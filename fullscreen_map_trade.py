#!/usr/bin/env python3
"""
Full Screen Map Trade - Using OLD coordinates with PROVEN simple method
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# OLD WORKING COORDINATES (full screen) - from backup
MAP_COORDINATES = {
    'M1': (1068, 817),
    'M2': (1224, 817),
    'M3': (1384, 820),
    'M4': (1533, 816),
    'M5': (1144, 971),
    'M6': (1300, 971),
    'M7': (1456, 971),
}

# Button coordinates for full screen (you'll need to recalibrate these)
BUTTON_COORDINATES = {
    'you_will_lose': (1200, 400),  # Placeholder - needs recalibration
    'initiate_exchange': (1000, 900),  # Placeholder - needs recalibration
    'confirm_exchange': (1000, 700),  # Placeholder - needs recalibration
}

def read_map_number_simple(x, y, size=25):
    """Read number using PROVEN simple method"""
    try:
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # SIMPLE GRAYSCALE - PROVEN TO WORK
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Try multiple OCR configs
        configs = [
            '--psm 8 -c tessedit_char_whitelist=0123456789',
            '--psm 7 -c tessedit_char_whitelist=0123456789',
            '--psm 10 -c tessedit_char_whitelist=0123456789'
        ]
        
        for config in configs:
            text = pytesseract.image_to_string(gray, config=config).strip()
            if text.isdigit():
                return int(text)
        
        # Fallback: try adaptive threshold (worked for M7)
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        
        for config in configs:
            text = pytesseract.image_to_string(adaptive, config=config).strip()
            if text.isdigit():
                return int(text)
        
        return None
        
    except Exception:
        return None

def read_all_map_pieces():
    """Read all map piece quantities with full screen coordinates"""
    print("Reading map pieces with FULL SCREEN coordinates...")
    results = {}
    
    for map_name, (x, y) in MAP_COORDINATES.items():
        print(f"  Reading {map_name} at ({x}, {y})...")
        number = read_map_number_simple(x, y)
        if number is not None:
            results[map_name] = number
            print(f"    ✅ {map_name}: {number}")
        else:
            print(f"    ❌ {map_name}: Failed to read")
    
    return results

def find_best_trade(quantities):
    """Find best trade: give lowest, get highest"""
    if len(quantities) < 2:
        return None, None
    
    lowest = min(quantities.items(), key=lambda x: x[1])
    highest = max(quantities.items(), key=lambda x: x[1])
    
    if lowest[1] >= highest[1]:  # No benefit
        return None, None
    
    return lowest[0], highest[0]  # give_map, receive_map

def calibrate_buttons():
    """Interactive button calibration for full screen"""
    print("\n🎯 BUTTON CALIBRATION for Full Screen")
    print("=" * 40)
    print("We need to calibrate the button coordinates for full screen mode.")
    print("Position your mouse over each button and press ENTER")
    
    buttons = ['you_will_lose', 'initiate_exchange', 'confirm_exchange']
    calibrated = {}
    
    for button in buttons:
        print(f"\nPosition mouse over '{button}' button and press ENTER")
        input("Press ENTER when ready: ")
        x, y = pyautogui.position()
        calibrated[button] = (x, y)
        print(f"  ✅ {button}: ({x}, {y})")
    
    print(f"\n📋 CALIBRATED COORDINATES:")
    print("BUTTON_COORDINATES = {")
    for button, coords in calibrated.items():
        print(f"    '{button}': {coords},")
    print("}")
    
    return calibrated

def execute_trade_fullscreen(give_map, receive_map, button_coords):
    """Execute trade with full screen coordinates"""
    print(f"\n🔄 Executing FULL SCREEN trade: Give {give_map} → Get {receive_map}")
    
    # Get map coordinates for clicking
    old_coords = {
        'M1': (1026, 765), 'M2': (1188, 772), 'M3': (1342, 773), 'M4': (1497, 773),
        'M5': (1106, 923), 'M6': (1262, 923), 'M7': (1418, 923)
    }
    
    # Step 1: Click the map piece to give away
    give_coords = old_coords[give_map]
    print(f"Step 1: Clicking {give_map} at {give_coords}")
    pyautogui.click(give_coords[0], give_coords[1])
    time.sleep(0.5)
    
    # Step 2: Click "you will lose" area
    lose_coords = button_coords['you_will_lose']
    print(f"Step 2: Clicking 'you will lose' at {lose_coords}")
    pyautogui.click(lose_coords[0], lose_coords[1])
    time.sleep(0.5)
    
    # Step 3: Click the map piece to receive
    receive_coords = old_coords[receive_map]
    print(f"Step 3: Clicking {receive_map} at {receive_coords}")
    pyautogui.click(receive_coords[0], receive_coords[1])
    time.sleep(0.5)
    
    # Step 4: Click initiate exchange
    initiate_coords = button_coords['initiate_exchange']
    print(f"Step 4: Clicking 'initiate exchange' at {initiate_coords}")
    pyautogui.click(initiate_coords[0], initiate_coords[1])
    time.sleep(1.0)
    
    # Step 5: Click confirm exchange
    confirm_coords = button_coords['confirm_exchange']
    print(f"Step 5: Clicking 'confirm exchange' at {confirm_coords}")
    pyautogui.click(confirm_coords[0], confirm_coords[1])
    time.sleep(1.0)
    
    print("✅ Full screen trade sequence completed!")

def run_fullscreen_map_trade():
    """Main full screen map trade function"""
    print("🎯 FULL SCREEN Map Trade Automation")
    print("=" * 40)
    print("⚠️  Make sure the game is in FULL SCREEN mode!")
    print()
    
    # Read all map pieces
    quantities = read_all_map_pieces()
    
    if not quantities:
        print("❌ Could not read any map pieces")
        return False
    
    print(f"\n📊 Detected quantities: {quantities}")
    print(f"📈 Success rate: {len(quantities)}/7 pieces ({len(quantities)*100//7}%)")
    
    # Find best trade
    give_map, receive_map = find_best_trade(quantities)
    
    if not give_map or not receive_map:
        print("❌ No beneficial trade found")
        return False
    
    give_qty = quantities[give_map]
    receive_qty = quantities[receive_map]
    
    print(f"\n💡 Trade Analysis:")
    print(f"  Give: {give_map} ({give_qty} pieces)")
    print(f"  Get: {receive_map} ({receive_qty} pieces)")
    print(f"  Benefit: +{receive_qty - give_qty} pieces")
    
    # Calibrate buttons if needed
    print(f"\nDo you want to:")
    print("1. Use placeholder button coordinates (may not work)")
    print("2. Calibrate button coordinates now")
    
    choice = input("Enter choice (1 or 2): ")
    
    if choice == "2":
        button_coords = calibrate_buttons()
    else:
        button_coords = BUTTON_COORDINATES
    
    # Execute the trade
    execute_trade_fullscreen(give_map, receive_map, button_coords)
    
    return True

if __name__ == "__main__":
    print("Make sure you're on the map trade screen in FULL SCREEN mode")
    input("Press ENTER to start FULL SCREEN map trade: ")
    
    success = run_fullscreen_map_trade()
    
    if success:
        print("\n🎉 Full screen map trade completed successfully!")
    else:
        print("\n❌ Full screen map trade failed")
