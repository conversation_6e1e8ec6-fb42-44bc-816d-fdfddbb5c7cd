"""
Daily Tasks Module for Last War automation
Handles MANUAL execution of daily tasks with button trigger
"""
import time
import pyautogui
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import logging

from enhanced_base_module import EnhancedBaseModule


class DailyTasksModule(EnhancedBaseModule):
    """Module for handling MANUAL daily tasks automation with button trigger"""

    def __init__(self):
        super().__init__(name="daily_tasks", priority=1, enabled=True)

        # Manual execution settings
        self.is_manual_trigger = True  # This module requires manual trigger
        self.is_executing_daily = False  # Flag to track if daily tasks are running
        self.pause_other_modules = False  # Flag to pause other modules during execution

        # Task-specific settings
        self.task_timeout = 30    # 30 seconds timeout per task
        self.step_delay = 2.0     # 2 seconds between clicks
        
        # Base template for smart return-to-base detection
        self.base_template = "Base"  # Template name for Base icon (from config)
        self.base_click_delay = 3.0  # Delay after clicking Base (longer for navigation)

        # Default step delay between clicks
        self.default_step_delay = 2.0

        # Load daily tasks click sequence from configuration
        self.daily_click_sequence = self._load_click_sequence_from_config()

        # Templates for daily tasks detection
        self.daily_task_templates = [
            "base_icon",  # Base icon for return-to-base
            "daily_tasks_button",
            "daily_tasks_icon",
            "tasks_menu",
            "claim_reward",
            "complete_task"
        ]

        # Logger for this module
        self.logger = logging.getLogger("LastWar.daily_tasks")
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        MANUAL TRIGGER ONLY - Never executes automatically
        Daily tasks must be triggered manually via button
        """
        return False  # Never execute automatically

    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to can_execute"""
        return self.can_execute(screen_data)

    def manual_trigger_daily_tasks(self) -> bool:
        """
        MANUAL TRIGGER: Execute daily tasks sequence when button is pressed
        This pauses all other modules during execution
        """
        if self.is_executing_daily:
            self.logger.warning("Daily tasks already running - ignoring trigger")
            return False

        self.logger.info("🎯 MANUAL TRIGGER: Starting daily tasks sequence")
        self.is_executing_daily = True
        self.pause_other_modules = True

        try:
            # Execute the daily tasks sequence
            success = self._execute_daily_sequence()
            return success
        finally:
            # Always resume other modules when done
            self.is_executing_daily = False
            self.pause_other_modules = False
            self.logger.info("🎯 Daily tasks complete - resuming other modules")
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        NEVER CALLED - Daily tasks use manual trigger only
        """
        return False

    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to execute"""
        return self.execute(screen_data)

    def _execute_daily_sequence(self) -> bool:
        """
        Execute the configured daily tasks click sequence
        This is the main execution method called by manual trigger
        """
        try:
            self.logger.info("🚀 Starting daily tasks click sequence")

            # Disable PyAutoGUI safety pause for precise timing
            original_pause = pyautogui.PAUSE
            pyautogui.PAUSE = 0  # Remove default delay

            # Pause all other modules
            self._pause_other_modules()

            # STEP 0: Smart Base Detection and Return
            if not self._smart_return_to_base():
                self.logger.error("❌ Failed to return to base - aborting daily tasks")
                return False

            # Execute each click in the numbered sequence
            total_steps = len(self.daily_click_sequence)
            for i, step_data in enumerate(self.daily_click_sequence):
                # Handle both old format (4 items) and new format (5 items)
                if len(step_data) == 4:
                    x, y, delay, description = step_data
                    repeat_count = 1
                else:
                    x, y, delay, repeat_count, description = step_data

                self.logger.info(f"📍 {description} ({i+1}/{total_steps})")

                # Perform the click(s) with repeat
                for repeat_num in range(repeat_count):
                    if repeat_count > 1:
                        self.logger.info(f"🖱️ Click {repeat_num+1}/{repeat_count} at ({x}, {y})")
                    else:
                        self.logger.info(f"🖱️ Clicked at ({x}, {y})")

                    pyautogui.click(x, y)

                    # Use precise delay between repeated clicks (use the same delay as the step)
                    if repeat_count > 1 and repeat_num < repeat_count - 1:
                        repeat_delay = max(0.01, delay / 2)  # Half the step delay, minimum 0.01s
                        self.logger.info(f"⚡ Inter-click delay: {repeat_delay}s")
                        time.sleep(repeat_delay)

                # Wait for the specified delay after all repeats
                if delay > 0:
                    self.logger.info(f"⏳ Waiting {delay} seconds...")
                    time.sleep(delay)

            self.logger.info("✅ Daily tasks sequence completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error in daily tasks sequence: {str(e)}")
            return False
        finally:
            # Restore PyAutoGUI original pause
            pyautogui.PAUSE = original_pause

            # Always resume other modules
            self._resume_other_modules()
    
    def get_cooldown(self) -> float:
        """No cooldown for manual trigger module"""
        return 0.0

    def _pause_other_modules(self):
        """Pause all other modules during daily tasks execution"""
        try:
            # This would be implemented by the main controller
            # For now, just log the intent
            self.logger.info("🛑 PAUSING all other modules for daily tasks")
            # TODO: Implement actual module pausing via main controller
        except Exception as e:
            self.logger.error(f"Error pausing modules: {str(e)}")

    def _resume_other_modules(self):
        """Resume all other modules after daily tasks completion"""
        try:
            self.logger.info("▶️ RESUMING all other modules")
            # TODO: Implement actual module resuming via main controller
        except Exception as e:
            self.logger.error(f"Error resuming modules: {str(e)}")

    def configure_click_sequence(self, sequence: List[Tuple[int, int, float, str]]):
        """
        Configure the daily tasks click sequence

        Args:
            sequence: List of (x, y, delay_after, description) tuples
        """
        self.daily_click_sequence = sequence
        self.logger.info(f"📝 Configured daily tasks sequence with {len(sequence)} steps")

        # Log the sequence for verification
        for i, (x, y, delay, desc) in enumerate(sequence):
            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

    def get_click_sequence(self) -> List[Tuple[int, int, float, str]]:
        """Get the current click sequence configuration"""
        return self.daily_click_sequence.copy()

    def is_daily_tasks_running(self) -> bool:
        """Check if daily tasks are currently executing"""
        return self.is_executing_daily

    def _smart_return_to_base(self) -> bool:
        """
        Smart base detection: Click Base icon if visible, skip if already at base
        Returns True if ready to proceed, False if failed
        """
        try:
            self.logger.info("🏠 STEP 0: Checking if we need to return to base...")

            # Use simple template matching for base detection
            base_detected = self._detect_base_template()

            if base_detected:
                # Base icon found - we need to click it to return to base
                base_x, base_y = base_detected

                self.logger.info(f"🏠 Base icon detected - clicking to return to base")
                self.logger.info(f"🖱️ Clicking Base at ({base_x}, {base_y})")

                # Click the Base icon
                pyautogui.click(base_x, base_y)

                # Wait longer for base navigation
                self.logger.info(f"⏳ Waiting {self.base_click_delay} seconds for base navigation...")
                time.sleep(self.base_click_delay)

                self.logger.info("✅ Returned to base successfully")
            else:
                # Base icon not found - we're already at base
                self.logger.info("✅ Already at base (Base icon not visible) - proceeding with daily tasks")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error in smart base detection: {str(e)}")
            return False

    def _detect_base_template(self):
        """
        Detect base template using simple template matching
        Returns (x, y) coordinates if found, None if not found
        """
        try:
            import os

            # Check if base template file exists
            template_path = f"templates/{self.base_template}.png"
            if not os.path.exists(template_path):
                self.logger.warning(f"⚠️ Base template not found: {template_path}")
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Load template
            template = cv2.imread(template_path)
            if template is None:
                self.logger.warning(f"⚠️ Could not load base template: {template_path}")
                return None

            # Perform template matching
            result = cv2.matchTemplate(screenshot_bgr, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # Check if match is good enough (threshold)
            threshold = 0.8
            if max_val >= threshold:
                # Calculate center of template
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2

                self.logger.debug(f"📍 Base template found at ({center_x}, {center_y}) with confidence {max_val:.2f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"🔍 Base template not found (best match: {max_val:.2f})")
                return None

        except Exception as e:
            self.logger.error(f"Error in base template detection: {str(e)}")
            return None

    def configure_base_template(self, template_name: str, click_delay: float = 3.0):
        """
        Configure the base template and click delay

        Args:
            template_name: Name of the base template file (without .png)
            click_delay: Delay after clicking base icon (seconds)
        """
        self.base_template = template_name
        self.base_click_delay = click_delay
        self.logger.info(f"🏠 Base template configured: {template_name} with {click_delay}s delay")

    def reload_configuration(self):
        """
        Reload the daily tasks configuration from file (live reload)
        This allows updating delays without restarting the application
        """
        try:
            self.logger.info("🔄 Reloading daily tasks configuration...")

            # Reload the click sequence from config
            old_count = len(self.daily_click_sequence)
            self.daily_click_sequence = self._load_click_sequence_from_config()
            new_count = len(self.daily_click_sequence)

            self.logger.info(f"✅ Configuration reloaded: {old_count} → {new_count} steps")

            # Log the updated sequence
            for i, (x, y, delay, desc) in enumerate(self.daily_click_sequence):
                self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to reload configuration: {str(e)}")
            return False

    def _load_click_sequence_from_config(self):
        """
        Load the daily tasks click sequence from module configuration
        Returns list of (x, y, delay, description) tuples
        """
        try:
            # Get click coordinates from module config
            if hasattr(self, 'module_config') and self.module_config:
                click_coords = self.module_config.get('click_coordinates', [])

                if click_coords:
                    sequence = []

                    # Sort by numerical order (1_, 2_, 3_, etc.)
                    def get_step_number(coord):
                        name = coord.get('name', '')
                        # Extract number from name (e.g., "1_Store" -> 1)
                        try:
                            if '_' in name:
                                return int(name.split('_')[0])
                            else:
                                return 999  # Put non-numbered items at end
                        except:
                            return 999

                    sorted_coords = sorted(click_coords, key=get_step_number)

                    for coord in sorted_coords:
                        # Check if this step is enabled
                        enabled = coord.get('enabled', True)
                        if not enabled:
                            self.logger.info(f"⏭️ Skipping disabled step: {coord.get('name', 'Unknown')}")
                            continue

                        x = coord.get('x', 0)
                        y = coord.get('y', 0)
                        name = coord.get('name', 'Unknown')
                        description = coord.get('description', '')

                        # Get custom delay for this step (fallback to default)
                        custom_delay = coord.get('delay', self.default_step_delay)

                        # Get repeat count for this step (fallback to 1)
                        repeat_count = coord.get('repeat', 1)
                        repeat_count = max(1, int(repeat_count))  # Ensure at least 1

                        # Create description with name, description, and repeat info
                        if repeat_count > 1:
                            full_description = f"{name}: {description} (×{repeat_count})".strip(': ')
                        else:
                            full_description = f"{name}: {description}".strip(': ')

                        # Add to sequence with custom delay and repeat count
                        sequence.append((x, y, custom_delay, repeat_count, full_description))

                    self.logger.info(f"Loaded {len(sequence)} daily tasks steps from configuration")

                    # Log each step for verification with custom delays and repeat counts
                    for i, step_data in enumerate(sequence):
                        if len(step_data) == 4:
                            x, y, delay, desc = step_data
                            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")
                        else:
                            x, y, delay, repeat_count, desc = step_data
                            self.logger.info(f"  Step {i+1}: {desc} at ({x}, {y}) + {delay}s delay")

                    return sequence
                else:
                    self.logger.warning("⚠️ No click coordinates found in configuration")
            else:
                self.logger.warning("⚠️ No module configuration available")

        except Exception as e:
            self.logger.error(f"❌ Error loading click sequence from config: {str(e)}")

        # Return default sequence if config loading fails
        self.logger.info("📋 Using default placeholder sequence")
        return [
            (100, 100, 2.0, "Default: Click daily tasks button"),
            (200, 200, 2.0, "Default: Click first task"),
            (300, 300, 2.0, "Default: Click claim button"),
            (400, 400, 2.0, "Default: Click close button"),
        ]
    
    def _navigate_to_daily_tasks(self, screen_data: Dict[str, Any]) -> bool:
        """Navigate to the daily tasks interface"""
        templates_found = screen_data.get('templates_found', {})
        
        # Try to find and click daily tasks button/icon
        for template in ["daily_tasks_button", "daily_tasks_icon", "tasks_menu"]:
            if template in templates_found:
                matches = templates_found[template]
                if matches:
                    # Click on the first match
                    x, y, w, h = matches[0]
                    click_x = x + w // 2
                    click_y = y + h // 2
                    
                    self.logger.debug(f"Clicking daily tasks at ({click_x}, {click_y})")
                    pyautogui.click(click_x, click_y)
                    time.sleep(2)  # Wait for interface to load
                    return True
        
        # If no template found, try text-based navigation
        text_regions = screen_data.get('text_regions', {})
        for region_name, text in text_regions.items():
            if "daily" in text.lower() and ("task" in text.lower() or "mission" in text.lower()):
                # This is a simplified approach - in real implementation,
                # you'd need to determine the clickable area from OCR results
                self.logger.debug(f"Found daily tasks text in {region_name}, attempting click")
                # For now, just return True to simulate navigation
                return True
        
        return False
    
    def _process_daily_tasks(self, screen_data: Dict[str, Any]) -> int:
        """Process and complete available daily tasks"""
        tasks_completed = 0
        max_attempts = 10  # Prevent infinite loops
        
        for attempt in range(max_attempts):
            # Take a fresh screenshot to see current state
            time.sleep(1)
            
            # Look for claimable rewards
            if self._claim_available_rewards():
                tasks_completed += 1
                continue
            
            # Look for completable tasks
            if self._complete_available_tasks():
                tasks_completed += 1
                continue
            
            # If no more tasks found, break
            break
        
        return tasks_completed
    
    def _claim_available_rewards(self) -> bool:
        """Claim any available rewards"""
        try:
            # Look for claim buttons or reward indicators
            # This would use template matching or OCR to find claimable items
            
            # Simulate finding and clicking a claim button
            # In real implementation, you'd scan for "Claim" buttons
            
            # For now, return False to indicate no rewards found
            return False
            
        except Exception as e:
            self.logger.error(f"Error claiming rewards: {str(e)}")
            return False
    
    def _complete_available_tasks(self) -> bool:
        """Complete any available tasks"""
        try:
            # Look for tasks that can be completed with a single click
            # This would involve scanning for "Complete" or "Go" buttons
            
            # Simulate task completion
            # In real implementation, you'd:
            # 1. Find incomplete tasks
            # 2. Check if they can be auto-completed
            # 3. Click the appropriate buttons
            
            # For now, return False to indicate no tasks completed
            return False
            
        except Exception as e:
            self.logger.error(f"Error completing tasks: {str(e)}")
            return False
    
    def _close_daily_tasks(self):
        """Close the daily tasks interface"""
        try:
            # Look for close button or back button
            # Common locations for close buttons
            close_positions = [
                (50, 50),    # Top-left back button
                (750, 50),   # Top-right close button
                (400, 550),  # Bottom center close button
            ]
            
            for x, y in close_positions:
                # In real implementation, you'd verify the button exists
                # before clicking
                pass
            
            # Press ESC as fallback
            pyautogui.press('esc')
            time.sleep(1)
            
        except Exception as e:
            self.logger.error(f"Error closing daily tasks: {str(e)}")
    
    def get_task_progress(self) -> Dict[str, Any]:
        """Get current task progress information"""
        return {
            'module': self.name,
            'last_execution': self.last_execution,
            'execution_count': self.execution_count,
            'cooldown_remaining': max(0, self.get_cooldown() - (time.time() - (self.last_execution or 0))),
            'status': 'active' if self.enabled else 'disabled'
        }

    # Centralized Scanner Action Methods
    def complete_tasks(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for Daily_Tasks_Icon detection - complete daily tasks"""
        try:
            self.logger.info("🎯 CENTRALIZED: Daily_Tasks_Icon detected - starting task completion")
            success = self.manual_trigger_daily_tasks()
            self.logger.info(f"✅ CENTRALIZED: Daily tasks completion sequence completed - success: {success}")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in complete_tasks action: {e}")
            return False
