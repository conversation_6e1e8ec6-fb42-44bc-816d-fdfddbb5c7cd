# 🏗️ Dig Module Implementation

## Overview
The Dig Module has been completely rewritten from scratch to follow your exact specifications. It implements a 9-step sequence with the highest priority in the automation system.

## Key Features

### ✅ Highest Priority
- Priority: -1 (highest possible)
- Pauses main scanner during execution
- Resumes main scanner when complete

### ✅ Template-Based Triggers
The module activates when ANY of these templates are detected:
- `dig_icon`
- `dig_up_dropdown` 
- `test_flight_treasure`
- `dig_up_treasure`

### ✅ 9-Step Sequence Implementation

**Step 1: Detection** - Main scanner detects dig templates
**Step 2: Open Chat** - Click (1267, 1353) → Click (1176, 170)
**Step 3: Click Treasure** - Scan for dig_up_treasure/test_flight_treasure, click or use chat_click backup
**Step 4: Check Chat Closed** - Wait for alliance_chat_is_on to disappear, then click send_to_dig templates
**Step 5: March to Dig** - Click march_to_dig at (1277, 1087), then click select_tile
**Step 6: Timer Wait** - Detect timer, rapid click when under 10 seconds (20 sec duration)
**Step 7: Exit Game** - Press ESC until quit_game_dialog appears, then ESC once more
**Step 8: Send Message** - Open chat, send configurable thank you message
**Step 9: Complete** - Return to main scanner

### ✅ Configurable Settings
- **Rapid Click Speed**: Adjustable from 0.001 to 1.0 seconds (default: 0.005)
- **Thank You Message**: Customizable message sent after completion
- **GUI Configuration**: Special dialog accessible via ⚙️ button

## File Changes Made

### 1. modules/dig.py - Complete Rewrite
- New step-based architecture
- State tracking for dig progress
- Template detection for all required elements
- Configurable rapid clicking
- Automatic message sending

### 2. template_scanner_config.json - Added Dig Templates
```json
{
  "template_name": "dig_icon",
  "priority": 0,
  "module": "dig",
  "action": "custom_execute",
  "enabled": true
}
```
Added entries for all 4 trigger templates with priority 0.

### 3. module_configs.json - Updated Dig Configuration
- Added all required templates
- Added rapid_click_speed and thank_you_message settings
- Enabled the module by default

### 4. gui.py - Added Special Configuration Dialog
- Custom configuration dialog for dig module
- Rapid click speed spinbox control
- Thank you message text input
- Real-time status display

## Required Templates

You need to create these template images in the templates/ folder:

### Trigger Templates (Step 1)
- `dig_icon.png`
- `dig_up_dropdown.png`
- `test_flight_treasure.png`
- `dig_up_treasure.png`

### Process Templates
- `alliance_chat_is_on.png`
- `send_to_dig_1.png`
- `send_to_dig_2.png`
- `send_to_dig_3.png`
- `send_to_dig_flight.png`
- `send_to_dig_flight_2.png`
- `march_to_dig.png`
- `select_tile.png`
- `quit_game_dialog.png`
- `chat_click.png` (backup)

## Testing

Run the test script to verify the module:
```bash
python test_dig_module.py
```

## Configuration

1. **Via GUI**: Click the ⚙️ button next to the Dig module
2. **Rapid Click Speed**: Set how fast to click when timer < 10 seconds
3. **Thank You Message**: Customize the message sent after dig completion

## Integration

The module is fully integrated with:
- ✅ Main controller (registered with highest priority)
- ✅ Centralized template scanner (4 trigger templates added)
- ✅ GUI (special configuration dialog)
- ✅ Module configuration system

## Next Steps

1. **Create Template Images**: Capture screenshots of all required UI elements
2. **Test Detection**: Use the diagnostic tools to verify template detection
3. **Adjust Coordinates**: Fine-tune click coordinates if needed
4. **Test Full Sequence**: Run with actual dig opportunities
5. **Optimize Timing**: Adjust delays between steps as needed

## Status Monitoring

The module provides detailed status information:
- "Ready" - Waiting for dig triggers
- "Dig in progress - Opening Chat" - Currently executing step 2
- "Dig in progress - Waiting for Timer" - Currently executing step 6
- etc.

The main scanner is paused during dig execution and automatically resumed when complete.
