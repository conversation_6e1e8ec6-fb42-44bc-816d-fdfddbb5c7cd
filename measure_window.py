"""
Quick window size measurement tool
Press Space bar to capture mouse coordinates for window sizing
"""
import tkinter as tk
import pyautogui
import sys

class WindowMeasurer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Window Size Measurer")
        self.root.geometry("400x200")
        self.root.configure(bg='#2d3142')
        
        # Make window always on top
        self.root.attributes('-topmost', True)
        
        self.coordinates = []
        
        # Instructions
        label = tk.Label(self.root, 
                        text="Window Size Measurer\n\n1. Position mouse at TOP-LEFT corner\n2. Press SPACE BAR\n3. Position mouse at BOTTOM-RIGHT corner\n4. Press SPACE BAR again\n\nPress ESC to exit",
                        bg='#2d3142', fg='white', font=('Arial', 12),
                        justify='center')
        label.pack(expand=True)
        
        # Status label
        self.status_label = tk.Label(self.root, 
                                   text="Ready - Position mouse at TOP-LEFT corner and press SPACE",
                                   bg='#2d3142', fg='#8ab4f8', font=('Arial', 10))
        self.status_label.pack(pady=10)
        
        # Bind keys
        self.root.bind('<KeyPress-space>', self.capture_coordinate)
        self.root.bind('<KeyPress-Escape>', self.exit_app)
        
        # Make sure window can receive key events
        self.root.focus_set()
        
        print("Window Measurer started!")
        print("1. Position mouse at TOP-LEFT corner of your desired window")
        print("2. Press SPACE BAR")
        print("3. Position mouse at BOTTOM-RIGHT corner")
        print("4. Press SPACE BAR again")
        print("Press ESC to exit")
        
    def capture_coordinate(self, event):
        x, y = pyautogui.position()
        self.coordinates.append((x, y))
        
        if len(self.coordinates) == 1:
            print(f"TOP-LEFT captured: ({x}, {y})")
            self.status_label.config(text=f"TOP-LEFT: ({x}, {y}) - Now position at BOTTOM-RIGHT and press SPACE")
        elif len(self.coordinates) == 2:
            print(f"BOTTOM-RIGHT captured: ({x}, {y})")
            
            # Calculate window size
            top_left = self.coordinates[0]
            bottom_right = self.coordinates[1]
            
            width = bottom_right[0] - top_left[0]
            height = bottom_right[1] - top_left[1]
            
            print(f"\n=== WINDOW SIZE MEASUREMENT ===")
            print(f"Top-Left: {top_left}")
            print(f"Bottom-Right: {bottom_right}")
            print(f"Width: {width}")
            print(f"Height: {height}")
            print(f"Geometry string: {width}x{height}")
            print("=================================")
            
            self.status_label.config(text=f"DONE! Size: {width}x{height} - Press ESC to exit")
            
            # Reset for another measurement
            self.coordinates = []
    
    def exit_app(self, event):
        print("Exiting...")
        self.root.destroy()
        sys.exit()
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    measurer = WindowMeasurer()
    measurer.run()
