"""
Map Trade Module - Intelligent map trading with image detection and analysis
"""
import time
import logging
import pyautogui
import cv2
import numpy as np
import pytesseract
import os
import warnings
from typing import Dict, Any, List, Tuple, Optional

# Comprehensive warning suppression for libpng and PIL issues
warnings.filterwarnings("ignore", message=".*libpng warning.*")
warnings.filterwarnings("ignore", category=UserWarning, module="PIL")
warnings.filterwarnings("ignore", message=".*sBIT.*")
warnings.filterwarnings("ignore", message=".*invalid.*")
warnings.filterwarnings("ignore", category=RuntimeWarning)

# Also suppress OpenCV warnings
import os
os.environ['OPENCV_LOG_LEVEL'] = 'ERROR'
from enhanced_base_module import EnhancedBaseModule
from unified_config_manager import UnifiedConfigManager

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'


class MapTradeModule(EnhancedBaseModule):
    def __init__(self):
        super().__init__(name="map_trade", priority=2, enabled=True)

        # Timing settings from unified config
        self.execution_interval = 300.0  # 5 minutes in seconds
        self.last_execution_time = 0

        # Execution state
        self.is_executing = False
        self.current_step = 0
        self.total_steps = 10

        # Load map trade specific configuration
        self._load_map_trade_config()

    def _load_map_trade_config(self):
        """Load map trade specific configuration from unified config"""
        try:
            config = self.unified_config_manager.get_module_config("map_trade")
            if not config:
                config = self.config_manager.get_module_config("map_trade")

            if config:
                self.execution_interval = config.get("execution_interval", 300.0)

                # Load coordinates if available
                coordinates = config.get("coordinates", {})
                self.map_trade_coordinates = coordinates

                # Load map piece coordinates from global coordinates
                global_coords = self.unified_config_manager.config_data.get('global_coordinates', {})
                map_pieces = global_coords.get('map_pieces', {})
                if map_pieces:
                    self.map_coordinates = {
                        'M1': map_pieces.get('M1', {'x': 1092, 'y': 532}),
                        'M2': map_pieces.get('M2', {'x': 1258, 'y': 532}),
                        'M3': map_pieces.get('M3', {'x': 1425, 'y': 532}),
                        'M4': map_pieces.get('M4', {'x': 1590, 'y': 532}),
                        'M5': map_pieces.get('M5', {'x': 1175, 'y': 693}),
                        'M6': map_pieces.get('M6', {'x': 1342, 'y': 693}),
                        'M7': map_pieces.get('M7', {'x': 1508, 'y': 693})
                    }
                else:
                    # Use default coordinates
                    self.map_coordinates = {
                        'M1': {'x': 1092, 'y': 532}, 'M2': {'x': 1258, 'y': 532},
                        'M3': {'x': 1425, 'y': 532}, 'M4': {'x': 1590, 'y': 532},
                        'M5': {'x': 1175, 'y': 693}, 'M6': {'x': 1342, 'y': 693},
                        'M7': {'x': 1508, 'y': 693}
                    }

                self.logger.info(f"Loaded map trade config: interval={self.execution_interval}s")
            else:
                raise Exception("No configuration found")

        except Exception as e:
            self.logger.error(f"Error loading map trade config: {str(e)}")
            # Use defaults
            self.execution_interval = 300.0
            self.map_trade_coordinates = {}
            self.map_coordinates = {
                'M1': {'x': 1092, 'y': 532}, 'M2': {'x': 1258, 'y': 532},
                'M3': {'x': 1425, 'y': 532}, 'M4': {'x': 1590, 'y': 532},
                'M5': {'x': 1175, 'y': 693}, 'M6': {'x': 1342, 'y': 693},
                'M7': {'x': 1508, 'y': 693}
            }

    def _apply_module_specific_config(self, config: Dict[str, Any]):
        """Apply map trade specific configuration from unified config"""
        self.execution_interval = config.get("execution_interval", 300.0)

        # Load coordinates if available
        coordinates = config.get("coordinates", {})
        self.map_trade_coordinates = coordinates

        # Load map piece coordinates from global coordinates
        global_coords = self.unified_config_manager.config_data.get('global_coordinates', {})
        map_pieces = global_coords.get('map_pieces', {})
        if map_pieces:
            self.map_coordinates = {}
            for piece in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
                if piece in map_pieces:
                    coord = map_pieces[piece]
                    self.map_coordinates[piece] = {'x': coord.get('x', 0), 'y': coord.get('y', 0)}

        self.logger.info(f"Applied map trade config: interval={self.execution_interval}s")

        # Configure Tesseract path
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Intelligence settings
        self.intelligent_trading = True
        self.map_piece_regions = self._define_map_piece_regions()
        self.trade_analysis_enabled = True

        # Debug settings
        self.debug_mode = True  # Enable detailed logging for troubleshooting
        self.save_debug_screenshots = False  # Set to True to save screenshots for analysis

        # Stored map quantities from big map view (step 2)
        self.stored_map_quantities = {}

        # Template matching for UI elements
        self.ui_templates = {
            'red_thumb': 'red_thumb.png',
            'new_exchange': 'new_exchange_button.png',
            'initiate_exchange': 'initiate_exchange_button.png',
            'you_will_lose': 'you_will_lose_area.png'
        }

        # Persistent trade counters
        self.total_red_thumb_clicks = 0
        self.total_trades_completed = 0

        self.logger.info("Map Trade module initialized - Intelligent trading with image analysis")

    def _check_map_trade_location(self) -> bool:
        """Check if we're in the right location by looking for truck template"""
        try:
            from screen_scanner import ScreenScanner

            scanner = ScreenScanner()
            screenshot = scanner.capture_screen_to_memory()

            # Check for truck template
            if 'truck' in scanner.templates:
                matches = scanner.find_template(screenshot, 'truck', 0.8)
                if matches:
                    self.logger.info("[TRUCK] Truck template detected - in correct map trade location")
                    return True
                else:
                    self.logger.warning("Truck template not detected - not in map trade location")
                    return False
            else:
                self.logger.warning("Truck template not loaded")
                return False

        except Exception as e:
            self.logger.error(f"Error checking map trade location: {str(e)}")
            return False

    def _navigate_to_map_trade(self) -> bool:
        """Navigate to map trade location by pressing ESC and retrying"""
        try:
            import pyautogui

            self.logger.info("[NAV] Attempting to navigate to map trade location...")

            # Press ESC to close any open menus
            self.logger.info("[INPUT] Pressing ESC to close menus")
            pyautogui.press('escape')
            time.sleep(1.0)

            # Wait a moment and check again
            time.sleep(1.0)

            if self._check_map_trade_location():
                self.logger.info("[SUCCESS] Successfully navigated to map trade location")
                return True
            else:
                self.logger.warning("Still not in map trade location after ESC")
                return False

        except Exception as e:
            self.logger.error(f"Error navigating to map trade location: {str(e)}")
            return False

    def get_required_templates(self) -> list:
        """Return list of templates required by this module"""
        return ['map_button', 'trade_button', 'world_button', 'truck']

    def _check_trade_available(self) -> bool:
        """Check if trade_button is available (indicating a trade can be done)"""
        try:
            from screen_scanner import ScreenScanner

            scanner = ScreenScanner()
            screenshot = scanner.capture_screen_to_memory()

            # Check for trade_button template
            if 'trade_button' in scanner.templates:
                matches = scanner.find_template(screenshot, 'trade_button', 0.8)
                if matches:
                    self.logger.info("Trade button detected - trade available")
                    return True
                else:
                    self.logger.debug("Trade button not detected - no trade available")
                    return False
            else:
                self.logger.warning("Trade button template not loaded")
                return False

        except Exception as e:
            self.logger.error(f"Error checking trade availability: {str(e)}")
            return False
    
    def can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """
        Check if map trade should execute (every 5 minutes)
        """
        # Don't execute if already running
        if self.is_executing:
            self.current_activity = f"Executing step {self.current_step}/{self.total_steps}"
            self.waiting_for = "Sequence completion"
            return False
        
        # Check timing - execute every 5 minutes
        current_time = time.time()
        if current_time - self.last_execution_time < self.execution_interval:
            remaining_time = self.execution_interval - (current_time - self.last_execution_time)
            self.current_activity = "Waiting for next trade cycle"
            self.waiting_for = f"Next trade in {remaining_time/60:.1f} minutes"
            return False
        
        # Check if map or world button is visible (ensures we can access map)
        templates_detected = screen_data.get('templates_detected', {})
        map_button_visible = templates_detected.get('map_button', False)
        world_button_visible = templates_detected.get('world_button', False)

        if not (map_button_visible or world_button_visible):
            self.current_activity = "Map/World button not visible"
            self.waiting_for = "Navigate to screen with Map or World button"
            return False
        
        # Ready to execute
        self.current_activity = "Ready for map trade sequence"
        self.waiting_for = "Execution"
        return True
    
    def custom_can_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to can_execute"""
        return self.can_execute(screen_data)
    
    def execute(self, screen_data: Dict[str, Any]) -> bool:
        """Execute the 12-click map trade sequence with location verification"""
        try:
            self.is_executing = True
            self.current_step = 0

            # FIRST: Check if we're in the right location (truck template visible)
            self.logger.info("[MAP] Checking map trade location before starting sequence...")

            if not self._check_map_trade_location():
                self.logger.warning("Not in map trade location - attempting to navigate...")

                # Try to navigate to correct location
                if not self._navigate_to_map_trade():
                    self.logger.error("Failed to navigate to map trade location - aborting")
                    return False

                # Double-check after navigation
                if not self._check_map_trade_location():
                    self.logger.error("Still not in map trade location after navigation - aborting")
                    return False

            self.logger.info("[MAP] Starting Map Trade sequence - 10 clicks")

            # Get click coordinates from configuration
            config = self.config_manager.get_module_config('map_trade')
            click_coordinates = config.get('click_coordinates', [])

            if not click_coordinates:
                self.logger.error("No click coordinates configured for map trade")
                return False

            # Execute initial steps 1-3 (up to 3_trade_option_1)
            for i in range(3):  # Steps 1-3
                if i >= len(click_coordinates):
                    break

                coord = click_coordinates[i]
                if not coord.get('enabled', True):
                    continue

                self.current_step = i + 1
                name = coord.get('name', f'Step_{i+1}')
                x = coord.get('x', 0)
                y = coord.get('y', 0)
                delay = coord.get('delay', 0.5)
                description = coord.get('description', '')

                self.logger.info(f"Map Trade Step {self.current_step}: {name} - {description}")

                try:
                    # Check if this coordinate uses ESC key instead of clicking
                    use_esc_key = coord.get('use_esc_key', False)

                    if use_esc_key:
                        self.logger.debug(f"  Pressing ESC key")
                        pyautogui.press('esc')
                    else:
                        self.logger.debug(f"  Clicked at ({x}, {y})")
                        pyautogui.click(x, y)

                    if delay > 0:
                        time.sleep(delay)

                    # AFTER STEP 2: Read map quantities from big map view
                    if self.current_step == 2:
                        self.logger.info("Step 2 completed - reading map quantities from big map view...")
                        time.sleep(1.0)  # Wait for big map to fully load
                        self._read_big_map_quantities()

                except Exception as e:
                    self.logger.error(f"Failed to execute step {self.current_step}: {str(e)}")
                    continue

            # IMAGE DETECTION AND DECISION after step 3_trade_option_1
            if self.intelligent_trading and self.trade_analysis_enabled:
                self.logger.info("Starting image detection after step 3...")

                # Wait a moment for screen to update
                time.sleep(1.0)

                # Perform post-step-3 analysis
                continue_normal = self._post_step3_analysis()

                if not continue_normal:
                    # Analysis handled everything, we're done
                    self.logger.info("Post-step-3 analysis completed - finishing sequence")
                    self.last_execution_time = time.time()
                    return True

            # Smart trade detection and execution after step 4
            trades_completed = 0
            while True:
                # Check if trade is available
                if self._check_trade_available():
                    self.logger.info(f"Trade #{trades_completed + 1} available - executing trade sequence")

                    # Execute steps 5_trade_option_2 and 6_confirm_2
                    for step_idx in [4, 5]:  # Steps 5 and 6 (0-indexed: 4 and 5)
                        if step_idx >= len(click_coordinates):
                            break

                        coord = click_coordinates[step_idx]
                        if not coord.get('enabled', True):
                            continue

                        self.current_step = step_idx + 1
                        name = coord.get('name', f'Step_{step_idx + 1}')
                        x = coord.get('x', 0)
                        y = coord.get('y', 0)
                        delay = coord.get('delay', 0.5)
                        description = coord.get('description', '')

                        self.logger.info(f"[MAP] Trade Step {step_idx + 1}: {name} - {description}")

                        try:
                            # Check if this coordinate uses ESC key instead of clicking
                            use_esc_key = coord.get('use_esc_key', False)

                            if use_esc_key:
                                self.logger.debug(f"  Pressing ESC key")
                                pyautogui.press('esc')
                            else:
                                pyautogui.click(x, y)

                            if delay > 0:
                                time.sleep(delay)
                        except Exception as e:
                            self.logger.error(f"Failed to execute trade step {step_idx + 1}: {str(e)}")

                    trades_completed += 1
                    self.total_trades_completed += 1
                    self.logger.info(f"[SUCCESS] Trade #{trades_completed} completed - Total trades: {self.total_trades_completed}")
                    time.sleep(1.0)  # Wait before checking for next trade
                else:
                    # No more trades available
                    self.logger.info("No more trades available - proceeding to final step")
                    break

            # Execute final steps 9_trade_option_4 and 10_confirm_4 (skip steps 7 and 8)
            for final_step_idx in [8, 9]:  # Steps 9 and 10 (0-indexed: 8 and 9)
                if final_step_idx < len(click_coordinates):
                    coord = click_coordinates[final_step_idx]
                    if coord.get('enabled', True):
                        self.current_step = final_step_idx + 1
                        name = coord.get('name', f'Step_{final_step_idx + 1}')
                        x = coord.get('x', 0)
                        y = coord.get('y', 0)
                        delay = coord.get('delay', 0.5)
                        description = coord.get('description', '')

                        self.logger.info(f"[MAP] Final Step {final_step_idx + 1}: {name} - {description}")

                        try:
                            # Check if this coordinate uses ESC key instead of clicking
                            use_esc_key = coord.get('use_esc_key', False)

                            if use_esc_key:
                                self.logger.debug(f"  Pressing ESC key")
                                pyautogui.press('esc')
                            else:
                                pyautogui.click(x, y)

                            if delay > 0:
                                time.sleep(delay)
                        except Exception as e:
                            self.logger.error(f"Failed to execute final step {final_step_idx + 1}: {str(e)}")

            self.logger.info(f"[SUCCESS] Smart Map Trade completed - {trades_completed} trades executed")
            
            # Update execution time
            self.last_execution_time = time.time()
            
            self.logger.info(f"Map Trade sequence completed - {self.current_step} steps executed")
            self.current_activity = "Map trade completed"
            self.waiting_for = f"Next cycle in 5 minutes"
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in map trade execution: {str(e)}")
            return False
            
        finally:
            self.is_executing = False
            self.current_step = 0
    
    def custom_execute(self, screen_data: Dict[str, Any]) -> bool:
        """Required abstract method - delegates to execute"""
        return self.execute(screen_data)
    
    def get_cooldown(self) -> float:
        """Get cooldown time - no additional cooldown needed"""
        return 0.0
    
    def manual_trigger_map_trade(self) -> bool:
        """Manual trigger for map trade - follows same flow as automatic execution"""
        try:
            self.logger.info("[MAP] Manual map trade triggered")

            # Reset timing to allow immediate execution
            self.last_execution_time = 0
            self.is_executing = True
            self.current_step = 0

            # Skip location checking for manual trigger (assume user is in right place)
            self.logger.info("[MAP] Starting Map Trade sequence (manual trigger)")

            # Get click coordinates from configuration
            config = self.config_manager.get_module_config('map_trade')
            click_coordinates = config.get('click_coordinates', [])

            if not click_coordinates:
                self.logger.error("No click coordinates configured for map trade")
                return False

            # Execute initial steps 1-3 (same as automatic execution)
            for i in range(3):  # Steps 1-3
                if i >= len(click_coordinates):
                    break

                coord = click_coordinates[i]
                if not coord.get('enabled', True):
                    continue

                self.current_step = i + 1
                name = coord.get('name', f'Step_{i+1}')
                x = coord.get('x', 0)
                y = coord.get('y', 0)
                delay = coord.get('delay', 0.5)
                description = coord.get('description', '')

                self.logger.info(f"Map Trade Step {self.current_step}: {name} - {description}")

                try:
                    # Check if this coordinate uses ESC key instead of clicking
                    use_esc_key = coord.get('use_esc_key', False)

                    if use_esc_key:
                        self.logger.debug(f"  Pressing ESC key")
                        pyautogui.press('esc')
                    else:
                        self.logger.debug(f"  Clicked at ({x}, {y})")
                        pyautogui.click(x, y)

                    if delay > 0:
                        time.sleep(delay)

                    # AFTER STEP 2: Read map quantities from big map view (same as automatic)
                    if self.current_step == 2:
                        self.logger.info("Step 2 completed - reading map quantities from big map view...")
                        time.sleep(1.0)  # Wait for big map to fully load
                        self._read_big_map_quantities()

                except Exception as e:
                    self.logger.error(f"Failed to execute step {self.current_step}: {str(e)}")
                    continue

            # POST-STEP-3 ANALYSIS (same as automatic execution)
            if self.intelligent_trading and self.trade_analysis_enabled:
                self.logger.info("Starting image detection after step 3...")

                # Wait a moment for screen to update
                time.sleep(1.0)

                # Perform post-step-3 analysis
                continue_normal = self._post_step3_analysis()

                if not continue_normal:
                    # Analysis handled everything, we're done
                    self.logger.info("Post-step-3 analysis completed - finishing sequence")
                    self.last_execution_time = time.time()
                    return True

            # If we get here, continue with normal sequence (steps 4+)
            self.logger.info("Continuing with normal sequence from step 4...")
            # This would continue with the rest of the steps, but for now we'll finish
            self.last_execution_time = time.time()
            return True

        except Exception as e:
            self.logger.error(f"Error in manual map trade: {str(e)}")
            return False
        finally:
            self.is_executing = False
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get detailed status information for the status panel"""
        current_time = time.time()
        
        if self.is_executing:
            return {
                'status': 'EXECUTING',
                'activity': f"Step {self.current_step}/{self.total_steps}",
                'progress': f"{self.current_step}/{self.total_steps}",
                'next_execution': 'In progress',
                'red_thumb_clicks': self.total_red_thumb_clicks,
                'trades_completed': self.total_trades_completed
            }
        
        time_since_last = current_time - self.last_execution_time
        time_until_next = max(0, self.execution_interval - time_since_last)
        
        if time_until_next > 0:
            return {
                'status': 'WAITING',
                'activity': 'Waiting for next cycle',
                'progress': f"{time_since_last/60:.1f}/5.0 min",
                'next_execution': f"{time_until_next/60:.1f} minutes",
                'red_thumb_clicks': self.total_red_thumb_clicks,
                'trades_completed': self.total_trades_completed
            }
        else:
            return {
                'status': 'READY',
                'activity': 'Ready to execute',
                'progress': 'Ready',
                'next_execution': 'Now',
                'red_thumb_clicks': self.total_red_thumb_clicks,
                'trades_completed': self.total_trades_completed
            }

    def _define_map_piece_regions(self) -> Dict[str, Dict]:
        """Define coordinates for each map piece - load from configuration with fallback"""
        # Load coordinates from unified configuration
        regions = {}

        # Get coordinates from unified config
        map_trade_config = self.unified_config_manager.config_data.get('modules', {}).get('map_trade', {})
        config_coords = map_trade_config.get('map_piece_coordinates', {})

        # Also check global coordinates as fallback
        global_coords = self.unified_config_manager.config_data.get('global_coordinates', {})
        global_map_pieces = global_coords.get('map_pieces', {})

        # Define fallback hardcoded coordinates (only used if config missing)
        fallback_coords = {
            'M1': {
                'quantity_pos': (1083, 843), 'big_map_pos': (1080, 533), 'click_pos': (1031, 780)
            },
            'M2': {
                'quantity_pos': (1244, 843), 'big_map_pos': (1485, 585), 'click_pos': (1200, 784)
            },
            'M3': {
                'quantity_pos': (1403, 843), 'big_map_pos': (1323, 681), 'click_pos': (1364, 782)
            },
            'M4': {
                'quantity_pos': (1566, 844), 'big_map_pos': (1011, 835), 'click_pos': (1525, 786)
            },
            'M5': {
                'quantity_pos': (1162, 998), 'big_map_pos': (1157, 1030), 'click_pos': (1118, 946)
            },
            'M6': {
                'quantity_pos': (1327, 998), 'big_map_pos': (1412, 988), 'click_pos': (1281, 943)
            },
            'M7': {
                'quantity_pos': (1484, 997), 'big_map_pos': (1570, 832), 'click_pos': (1450, 945)
            }
        }

        for piece in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
            # Priority 1: Module-specific coordinates from unified config
            if piece in config_coords:
                coord = config_coords[piece]
                regions[piece] = {
                    'quantity_pos': (coord.get('quantity_x', 0), coord.get('quantity_y', 0)),
                    'big_map_pos': (coord.get('big_map_x', coord.get('quantity_x', 0)),
                                   coord.get('big_map_y', coord.get('quantity_y', 0))),
                    'click_pos': (coord.get('click_x', 0), coord.get('click_y', 0)),
                    'quantity': None
                }
                self.logger.info(f"[CONFIG] Loaded {piece} coordinates from unified config: "
                               f"click=({coord.get('click_x')}, {coord.get('click_y')})")

            # Priority 2: Global coordinates from unified config
            elif piece in global_map_pieces:
                coord = global_map_pieces[piece]
                # Use global coordinates for click, estimate others
                click_x, click_y = coord.get('x', 0), coord.get('y', 0)
                regions[piece] = {
                    'quantity_pos': (click_x + 50, click_y + 60),  # Estimate quantity position
                    'big_map_pos': (click_x, click_y),             # Use same for big map
                    'click_pos': (click_x, click_y),
                    'quantity': None
                }
                self.logger.info(f"[GLOBAL] Loaded {piece} coordinates from global config: "
                               f"click=({click_x}, {click_y})")

            # Priority 3: Fallback to hardcoded coordinates
            else:
                regions[piece] = fallback_coords[piece].copy()
                regions[piece]['quantity'] = None
                self.logger.warning(f"[FALLBACK] Using hardcoded coordinates for {piece}: "
                                  f"click={fallback_coords[piece]['click_pos']}")

        self.logger.info(f"Map piece regions defined for {len(regions)} pieces")
        return regions

    def _capture_number_region(self, center_x: int, center_y: int, size: int = 20) -> np.ndarray:
        """Capture region around coordinates for number reading with enhanced error handling"""
        try:
            # Create region around the center point
            x = max(0, center_x - size // 2)
            y = max(0, center_y - size // 2)

            # Ensure we don't go beyond screen boundaries
            screen_width, screen_height = pyautogui.size()
            x = min(x, screen_width - size)
            y = min(y, screen_height - size)

            # Ensure minimum size
            actual_width = min(size, screen_width - x)
            actual_height = min(size, screen_height - y)

            if actual_width <= 0 or actual_height <= 0:
                self.logger.error(f"Invalid capture region: ({x}, {y}, {actual_width}, {actual_height})")
                # Return a small black image as fallback
                return np.zeros((20, 20, 3), dtype=np.uint8)

            screenshot = pyautogui.screenshot(region=(x, y, actual_width, actual_height))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            self.logger.debug(f"Captured region: center=({center_x}, {center_y}), "
                            f"region=({x}, {y}, {actual_width}, {actual_height}), "
                            f"image_shape={image.shape}")

            return image

        except Exception as e:
            self.logger.error(f"Error capturing number region: {e}")
            # Return a small black image as fallback
            return np.zeros((20, 20, 3), dtype=np.uint8)

    def _read_big_map_quantities(self) -> Dict[str, int]:
        """Read map piece quantities from the big map view (step 2) - optimized for speed"""
        self.logger.info("Reading map quantities from big map view (fast batch processing)")

        # Use batch processing for better performance
        return self._read_map_quantities_batch(use_big_map=True)

    def _read_map_quantities_batch(self, use_big_map: bool = False) -> Dict[str, int]:
        """Batch process all map piece quantities for maximum speed"""
        start_time = time.time()
        map_quantities = {}

        # Capture all images first (faster than processing one by one)
        captured_images = {}

        for map_name, map_data in self.map_piece_regions.items():
            try:
                if use_big_map:
                    pos_x, pos_y = map_data['big_map_pos']
                    size = 35  # Proven size for big map
                else:
                    pos_x, pos_y = map_data['quantity_pos']
                    size = 20  # Proven size for small map

                # Capture image
                image = self._capture_number_region(pos_x, pos_y, size)

                if image.shape[0] > 0 and image.shape[1] > 0:
                    captured_images[map_name] = image
                    self.logger.debug(f"Captured {map_name} at ({pos_x}, {pos_y})")
                else:
                    self.logger.warning(f"Failed to capture {map_name}")

            except Exception as e:
                self.logger.error(f"Error capturing {map_name}: {e}")
                continue

        # Process all captured images with enhanced debugging
        for map_name, image in captured_images.items():
            try:
                # Set current piece ID for OCR debugging
                self._current_piece_id = map_name
                quantity = self._extract_number_from_image(image)

                if quantity is not None:
                    map_quantities[map_name] = quantity
                    # Store in the map data too
                    self.map_piece_regions[map_name]['quantity'] = quantity
                    self.logger.info(f"[OCR SUCCESS] {map_name}: {quantity} pieces")
                else:
                    self.logger.warning(f"[OCR FAILED] {map_name}: Could not read quantity")

            except Exception as e:
                self.logger.error(f"Error processing {map_name}: {e}")
                continue
            finally:
                # Clear piece ID
                self._current_piece_id = None

        elapsed_time = time.time() - start_time
        self.logger.info(f"Batch processed {len(map_quantities)}/{len(self.map_piece_regions)} "
                        f"map pieces in {elapsed_time:.2f}s")

        # Store for later use
        self.stored_map_quantities = map_quantities.copy()
        return map_quantities

    def _load_number_templates(self) -> Dict[int, np.ndarray]:
        """Load complete number templates (1-7) for template matching"""
        templates = {}

        # Load templates for complete numbers as they appear in game
        for number in range(1, 8):  # 1-7 only
            template_path = f"templates/numbers/{number}.png"
            try:
                if os.path.exists(template_path):
                    template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                    if template is not None:
                        templates[number] = template
                        self.logger.debug(f"Loaded template for number {number}")
            except Exception as e:
                self.logger.debug(f"Could not load template for number {number}: {e}")

        return templates

    def _extract_number_from_image(self, image: np.ndarray) -> Optional[int]:
        """Extract number using OCR with intelligent fallback"""
        try:
            # Determine OCR strategy based on availability
            tesseract_available = getattr(self, '_tesseract_available', None)
            ai_available = getattr(self, '_ai_ocr_available', None)

            # Strategy 1: Try traditional OCR first (if available)
            if tesseract_available is not False:  # None or True
                result = self._extract_number_traditional_ocr(image)
                if result is not None:
                    return result

            # Strategy 2: Try AI OCR (primary method if Tesseract unavailable)
            if ai_available is not False:  # None or True
                result = self._extract_number_ai_ocr(image)
                if result is not None:
                    # Log differently based on whether traditional was tried
                    if tesseract_available is False:
                        self.logger.debug(f"AI OCR success (primary method): {result}")
                    else:
                        self.logger.info(f"AI OCR succeeded where traditional failed: {result}")
                    return result

            # If both methods fail or are unavailable
            available_methods = []
            if tesseract_available:
                available_methods.append("Tesseract")
            if ai_available:
                available_methods.append("AI")

            if not available_methods:
                self.logger.error("No OCR methods available! Install Tesseract or AI OCR libraries.")
            else:
                self.logger.warning(f"OCR failed with {', '.join(available_methods)} - "
                                  f"image shape: {image.shape}, brightness: {np.mean(image):.1f}")

            return None

        except Exception as e:
            self.logger.error(f"Error extracting number from image: {e}")
            return None

    def _extract_number_traditional_ocr(self, image: np.ndarray) -> Optional[int]:
        """Fast traditional OCR method (primary) - with Tesseract availability check"""
        try:
            # Check if Tesseract is available (only check once)
            if not hasattr(self, '_tesseract_available'):
                try:
                    import pytesseract
                    # Test Tesseract availability
                    pytesseract.get_tesseract_version()
                    self._tesseract_available = True
                    self.logger.info("Tesseract OCR available for traditional processing")
                except Exception as e:
                    self._tesseract_available = False
                    self.logger.info(f"Tesseract OCR not available: {e}")
                    return None

            # If Tesseract is not available, return None immediately
            if not self._tesseract_available:
                return None

            import pytesseract

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply adaptive threshold (proven to work well)
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

            # Try multiple OCR configs (same as working version)
            configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789',
                '--psm 7 -c tessedit_char_whitelist=0123456789',
                '--psm 10 -c tessedit_char_whitelist=0123456789'
            ]

            # First attempt with adaptive threshold (simplified validation like working version)
            for config in configs:
                text = pytesseract.image_to_string(processed, config=config).strip()
                if text.isdigit():  # Simple validation like working version
                    number = int(text)
                    self.logger.debug(f"Traditional OCR success: {number}")
                    return number

            # Fallback: try morphological processing
            kernel = np.ones((2, 2), np.uint8)
            morph = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)

            for config in configs:
                text = pytesseract.image_to_string(morph, config=config).strip()
                if text.isdigit():  # Simple validation like working version
                    number = int(text)
                    self.logger.debug(f"Traditional OCR success (morph): {number}")
                    return number

            # Additional fallback: try with different threshold
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            for config in configs:
                text = pytesseract.image_to_string(binary, config=config).strip()
                if text.isdigit():
                    number = int(text)
                    self.logger.debug(f"Traditional OCR success (OTSU): {number}")
                    return number

            return None

        except Exception as e:
            self.logger.debug(f"Traditional OCR error: {e}")
            return None

    def _extract_number_ai_ocr(self, image: np.ndarray) -> Optional[int]:
        """AI-based OCR method (fallback) - optional enhancement"""
        try:
            # Check if AI OCR was already attempted and failed
            if not hasattr(self, '_ai_ocr_available'):
                # Try to import AI OCR service (only check once)
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(__file__)))

                    from ai_ocr_service import recognize_number_ai
                    self._ai_ocr_available = True
                    self._ai_ocr_function = recognize_number_ai
                    self.logger.info("AI OCR service available as fallback")

                except ImportError:
                    self._ai_ocr_available = False
                    self.logger.info("AI OCR not available (install: pip install easyocr paddlepaddle paddleocr)")
                    return None
                except Exception as e:
                    self._ai_ocr_available = False
                    self.logger.debug(f"AI OCR initialization failed: {e}")
                    return None

            # If AI OCR is not available, return None immediately
            if not self._ai_ocr_available:
                return None

            # Use AI OCR with fast mode enabled and piece ID for debugging
            piece_id = getattr(self, '_current_piece_id', 'unknown')
            result = self._ai_ocr_function(image, fast_mode=True, piece_id=piece_id)

            if result is not None:
                self.logger.debug(f"AI OCR success: {result}")
                return result

            return None

        except Exception as e:
            self.logger.debug(f"AI OCR error: {e}")
            return None



    def _analyze_number_by_pixels(self, image: np.ndarray) -> Optional[int]:
        """Fallback method: analyze number by pixel patterns"""
        try:
            # Create binary image
            _, binary = cv2.threshold(image, 100, 255, cv2.THRESH_BINARY_INV)

            # Count dark pixels
            dark_pixels = cv2.countNonZero(binary)
            total_pixels = image.shape[0] * image.shape[1]
            density = dark_pixels / total_pixels if total_pixels > 0 else 0

            # Heuristic mapping (needs calibration for your UI)
            if density < 0.02:
                return 1
            elif density < 0.04:
                return 2
            elif density < 0.06:
                return 3
            elif density < 0.08:
                return 4
            elif density < 0.10:
                return 5
            elif density < 0.12:
                return 6
            else:
                return 7

        except Exception as e:
            self.logger.warning(f"Error in pixel analysis: {e}")
            return None

    def _analyze_map_pieces(self) -> Dict[str, int]:
        """Use stored quantities from big map view, fallback to fast batch reading"""

        # First, try to use stored quantities from big map view
        if self.stored_map_quantities:
            self.logger.info("Using stored map quantities from big map view")
            self.logger.debug(f"Stored quantities: {self.stored_map_quantities}")
            return self.stored_map_quantities.copy()

        # Fallback: Fast batch read from small map coordinates
        self.logger.info("No stored quantities - using fast batch reading from small map...")
        return self._read_map_quantities_batch(use_big_map=False)

    def _find_optimal_trade(self, map_quantities: Dict[str, int]) -> Tuple[Optional[str], Optional[str]]:
        """Simple: Find lowest quantity (give) and highest quantity (receive)"""
        if not map_quantities:
            return None, None

        # Find lowest quantity map (give this away)
        lowest_map = min(map_quantities.keys(), key=lambda k: map_quantities[k])
        lowest_qty = map_quantities[lowest_map]

        # Find highest quantity map (receive this)
        highest_map = max(map_quantities.keys(), key=lambda k: map_quantities[k])
        highest_qty = map_quantities[highest_map]

        self.logger.info(f"Trade decision: Give {lowest_map}({lowest_qty}) -> Receive {highest_map}({highest_qty})")

        return lowest_map, highest_map

    def _click_map_piece(self, map_name: str) -> bool:
        """Simple: Click on a map piece using its click coordinate"""
        if map_name not in self.map_piece_regions:
            self.logger.error(f"Unknown map piece: {map_name}")
            return False

        # Get click position
        click_x, click_y = self.map_piece_regions[map_name]['click_pos']

        try:
            pyautogui.click(click_x, click_y)
            self.logger.info(f"Clicked {map_name} at ({click_x}, {click_y})")
            return True
        except Exception as e:
            self.logger.error(f"Error clicking {map_name}: {e}")
            return False

    def _detect_screen_state(self) -> str:
        """Enhanced template-based screen state detection with robust error handling"""
        try:
            if self.debug_mode:
                self.logger.info("[DEBUG] Starting screen state detection...")

            # Suppress warnings during screenshot and processing
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")

                # Take screenshot for analysis
                screenshot = pyautogui.screenshot()
                screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

                if self.debug_mode:
                    self.logger.info(f"[DEBUG] Screenshot taken: {screenshot_cv.shape}")

                    # Optionally save debug screenshot
                    if self.save_debug_screenshots:
                        debug_path = f"debug_screenshot_{int(time.time())}.png"
                        cv2.imwrite(debug_path, screenshot_cv)
                        self.logger.info(f"[DEBUG] Screenshot saved: {debug_path}")

            # Template detection using larger templates (100x40 size)
            # Check in priority order - cancel_exchange has highest priority
            template_checks = [
                ("cancel_exchange_100x40.png", "trade_in_progress"),
                ("red_thumb_100x40.png", "red_thumb"),
                ("initiate_exchange_100x40.png", "initiate_exchange"),
                ("confirm_trade_100x40.png", "confirmation_dialog"),
            ]

            best_match = None
            best_confidence = 0.0
            best_state = "normal"

            for template_name, state in template_checks:
                try:
                    if self.debug_mode:
                        self.logger.info(f"[DEBUG] Checking template: {template_name} for state: {state}")

                    template_path = os.path.join("templates", template_name)

                    if os.path.exists(template_path):
                        # Suppress warnings during template loading and processing
                        with warnings.catch_warnings():
                            warnings.simplefilter("ignore")
                            template = cv2.imread(template_path)

                        if template is not None:
                            if self.debug_mode:
                                self.logger.info(f"[DEBUG] Template loaded: {template.shape}")

                            # Template matching with error handling
                            with warnings.catch_warnings():
                                warnings.simplefilter("ignore")
                                result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                            if self.debug_mode:
                                self.logger.info(f"[DEBUG] Template match result: confidence={max_val:.3f}, location=({max_loc[0]}, {max_loc[1]})")

                            # Prioritize cancel_exchange - if found with good confidence, return immediately
                            if state == "trade_in_progress" and max_val > 0.7:
                                self.logger.info(f"Detected: {state} (template: {template_name}, confidence: {max_val:.2f}) - HIGH PRIORITY")
                                return state

                            # For other templates, track the best match
                            if max_val > 0.7 and max_val > best_confidence:
                                best_match = template_name
                                best_confidence = max_val
                                best_state = state
                                if self.debug_mode:
                                    self.logger.info(f"[DEBUG] New best match: {state} with confidence {max_val:.3f}")
                            elif max_val > 0.5 and max_val > best_confidence and best_confidence < 0.7:  # Medium confidence
                                best_match = template_name
                                best_confidence = max_val
                                best_state = state
                                self.logger.debug(f"Possible: {state} (template: {template_name}, confidence: {max_val:.2f})")
                        else:
                            if self.debug_mode:
                                self.logger.warning(f"[DEBUG] Could not load template: {template_name}")
                    else:
                        if self.debug_mode:
                            self.logger.warning(f"[DEBUG] Template not found: {template_name}")

                except Exception as e:
                    self.logger.warning(f"Error processing template {template_name}: {str(e)}")
                    if self.debug_mode:
                        self.logger.error(f"[DEBUG] Template processing exception: {e}")
                    continue

            # Return best match if found
            if best_match and best_confidence > 0.5:
                self.logger.info(f"Detected: {best_state} (template: {best_match}, confidence: {best_confidence:.2f})")
                return best_state

            # Fallback to OCR detection with error handling
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
                    text = pytesseract.image_to_string(gray).lower()

                if "cancel exchange" in text:
                    self.logger.info("Detected: Trade in progress (OCR fallback)")
                    return "trade_in_progress"
                elif "initiate exchange" in text:
                    self.logger.info("Detected: Ready for new trade (OCR fallback)")
                    return "initiate_exchange"
                else:
                    self.logger.info("Detected: Normal state")
                    return "normal"

            except Exception as ocr_error:
                self.logger.warning(f"OCR fallback failed: {str(ocr_error)}")
                self.logger.info("Detected: Normal state (OCR failed)")
                return "normal"

        except Exception as e:
            self.logger.error(f"Error detecting screen state: {e}")
            return "normal"

    def _has_red_thumb(self) -> bool:
        """Check if red thumb is present on screen (not Cancel Exchange button)"""
        try:
            # IMPORTANT: Don't detect red thumb if Cancel Exchange is visible
            screen_state = self._detect_screen_state()
            if screen_state == "trade_in_progress":
                self.logger.debug("Cancel Exchange visible - skipping red thumb detection to avoid false positive")
                return False

            # Check for red thumb template first (most reliable)
            template_path = os.path.join("templates", "red_thumb_100x40.png")
            if os.path.exists(template_path):
                screenshot = pyautogui.screenshot()
                screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                template = cv2.imread(template_path)

                if template is not None:
                    result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                    if max_val > 0.9:  # Raised confidence to reduce false positives
                        self.logger.debug(f"Red thumb template detected (confidence: {max_val:.2f})")
                        return True

            # Try other template sizes
            for template_name in ["red_thumb_80x30.png", "red_thumb_50x25.png", "red_thumb_30x15.png"]:
                template_path = os.path.join("templates", template_name)
                if os.path.exists(template_path):
                    screenshot = pyautogui.screenshot()
                    screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                    template = cv2.imread(template_path)

                    if template is not None:
                        result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > 0.9:  # Raised confidence to reduce false positives
                            self.logger.debug(f"Red thumb template {template_name} detected (confidence: {max_val:.2f})")
                            return True

            # Skip color detection as it causes false positives with Cancel Exchange button
            self.logger.debug("No red thumb template detected")
            return False

        except Exception as e:
            self.logger.error(f"Error checking for red thumb: {e}")
            return False

    def _post_step3_analysis(self) -> bool:
        """Analysis and actions after step 3 - handle red thumb, new exchange, etc."""
        try:
            self.logger.info("Performing post-step-3 image detection...")

            # Detect current screen state
            screen_state = self._detect_screen_state()
            self.logger.info(f"Screen state after step 3: {screen_state}")

            # SIMPLE LOGIC: Check in priority order

            # 1. Cancel exchange visible? -> Skip to allies trading (step 4)
            if screen_state == "trade_in_progress":
                self.logger.info("[CANCEL] Cancel Exchange visible - trade already in progress, skipping to allies trading")
                success = self._continue_trade_detection_sequence()
                return False  # We handled everything

            # 2. Check red thumb and new exchange at the same time
            has_red_thumb = self._has_red_thumb()
            has_new_exchange = self._has_new_exchange_button()

            if has_red_thumb:
                # Red thumb visible -> Click red thumb -> Click new exchange
                self.logger.info("[RED_THUMB] Red thumb visible - clicking red thumb then new exchange")

                if self._handle_red_thumb():
                    self.logger.info("[SUCCESS] Red thumb clicked - now clicking new exchange")
                    # Click new exchange directly at calibrated coordinate
                    new_exchange_x, new_exchange_y = 1255, 1142
                    pyautogui.click(new_exchange_x, new_exchange_y)
                    self.logger.info(f"[SUCCESS] Clicked new exchange at ({new_exchange_x}, {new_exchange_y})")

                    # Wait and execute intelligent trade
                    time.sleep(2.0)
                    success = self._execute_intelligent_exchange()
                    return False  # We handled everything
                else:
                    self.logger.warning("Failed to click red thumb - trying fallback approach")
                    # Fallback: try clicking at common red thumb locations
                    fallback_locations = [
                        (1200, 600),  # Common red thumb location 1
                        (1300, 650),  # Common red thumb location 2
                        (1250, 625),  # Common red thumb location 3
                    ]

                    for x, y in fallback_locations:
                        self.logger.info(f"Trying fallback red thumb click at ({x}, {y})")
                        pyautogui.click(x, y)
                        time.sleep(1.0)

                        # Check if new exchange button appeared
                        if self._has_new_exchange_button():
                            self.logger.info("Fallback red thumb click successful - new exchange appeared")
                            new_exchange_x, new_exchange_y = 1255, 1142
                            pyautogui.click(new_exchange_x, new_exchange_y)
                            self.logger.info(f"Clicked new exchange at ({new_exchange_x}, {new_exchange_y})")
                            time.sleep(2.0)
                            success = self._execute_intelligent_exchange()
                            return False  # We handled everything

                    self.logger.warning("All red thumb attempts failed - continuing with normal sequence")
                    return True

            elif has_new_exchange:
                # New exchange visible (no red thumb) -> Click new exchange directly
                self.logger.info("[NEW_EXCHANGE] New exchange visible (no red thumb) - clicking directly")

                # Click new exchange button directly at calibrated coordinate
                new_exchange_x, new_exchange_y = 1255, 1142
                pyautogui.click(new_exchange_x, new_exchange_y)
                self.logger.info(f"[SUCCESS] Clicked new exchange directly at ({new_exchange_x}, {new_exchange_y})")

                # Wait and execute intelligent trade
                time.sleep(2.0)
                success = self._execute_intelligent_exchange()
                return False  # We handled everything

            # 3. Already on initiate exchange screen? -> Do intelligent trade
            elif screen_state == "initiate_exchange":
                self.logger.info("[INITIATE] Already on initiate exchange screen - executing intelligent trade")
                success = self._execute_intelligent_exchange()
                return False  # We handled everything

            else:
                # 4. Normal state -> Continue with normal sequence
                self.logger.info("[NORMAL] Normal state - continuing with regular sequence from step 4")
                return True  # Continue with normal sequence

        except Exception as e:
            self.logger.error(f"Error in post-step-3 analysis: {e}")
            return True  # Continue with normal sequence on error

    def _click_new_exchange_button(self) -> bool:
        """Click the 'new exchange' button with enhanced detection"""
        try:
            self.logger.info("[SCAN] Searching for new exchange button...")

            # Wait a bit more for screen to fully update after red thumb click
            time.sleep(1.5)

            # Try multiple template variations
            template_names = [
                "initiate_exchange_100x40.png",
                "initiate_exchange_80x30.png",
                "initiate_exchange_50x25.png",
                "initiate_exchange_30x15.png"
            ]

            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            for template_name in template_names:
                template_path = os.path.join("templates", template_name)
                if os.path.exists(template_path):
                    template = cv2.imread(template_path)

                    if template is not None:
                        result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        self.logger.debug(f"Template {template_name}: confidence {max_val:.2f}")

                        if max_val > 0.6:  # Lower threshold for better detection
                            # Click on the center of the detected template
                            template_h, template_w = template.shape[:2]
                            center_x = max_loc[0] + template_w // 2
                            center_y = max_loc[1] + template_h // 2

                            pyautogui.click(center_x, center_y)
                            self.logger.info(f"[SUCCESS] Clicked new exchange button via template {template_name} at ({center_x}, {center_y})")
                            return True

            # OCR detection with more variations
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            text = pytesseract.image_to_string(gray).lower()

            self.logger.debug(f"OCR detected text: {text[:200]}...")  # Show first 200 chars

            # Look for various text patterns
            exchange_patterns = [
                "new exchange", "initiate exchange", "start exchange",
                "begin exchange", "exchange", "initiate", "new trade"
            ]

            for pattern in exchange_patterns:
                if pattern in text:
                    self.logger.info(f"[SUCCESS] Found '{pattern}' in OCR text - clicking default location")
                    # Use your exact calibrated coordinate (after red thumb click)
                    default_x, default_y = 1255, 1142  # After red thumb click coordinate
                    pyautogui.click(default_x, default_y)
                    self.logger.info(f"Clicked new exchange button at default location ({default_x}, {default_y})")
                    return True

            # Last resort: try the default coordinate anyway
            self.logger.warning("Could not detect new exchange button - trying default coordinate")
            default_x, default_y = 1255, 1142  # After red thumb click coordinate
            pyautogui.click(default_x, default_y)
            self.logger.info(f"Clicked default new exchange location ({default_x}, {default_y})")

            # Check if click was successful by detecting screen state change
            time.sleep(1.0)
            new_state = self._detect_screen_state()
            if new_state == "initiate_exchange":
                self.logger.info("[SUCCESS] Default click successful - now on initiate exchange screen")
                return True
            else:
                self.logger.warning(f"Default click may have failed - screen state: {new_state}")
                return False

        except Exception as e:
            self.logger.error(f"Error clicking new exchange button: {e}")
            return False

    def _has_new_exchange_button(self) -> bool:
        """Check if new exchange button is available (red thumb already acknowledged)"""
        try:
            self.logger.debug("[SCAN] Checking for new exchange button availability...")

            # Try template detection first
            template_names = [
                "initiate_exchange_100x40.png",
                "initiate_exchange_80x30.png",
                "initiate_exchange_50x25.png"
            ]

            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            for template_name in template_names:
                template_path = os.path.join("templates", template_name)
                if os.path.exists(template_path):
                    template = cv2.imread(template_path)

                    if template is not None:
                        result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > 0.6:  # Good confidence
                            self.logger.debug(f"New exchange button detected via template {template_name} (confidence: {max_val:.2f})")
                            return True

            # OCR fallback
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            text = pytesseract.image_to_string(gray).lower()

            # Look for exchange-related text
            exchange_patterns = [
                "new exchange", "initiate exchange", "start exchange",
                "begin exchange", "exchange"
            ]

            for pattern in exchange_patterns:
                if pattern in text:
                    self.logger.debug(f"New exchange button detected via OCR: '{pattern}'")
                    return True

            self.logger.debug("No new exchange button detected")
            return False

        except Exception as e:
            self.logger.error(f"Error checking for new exchange button: {e}")
            return False


    def _handle_red_thumb(self) -> bool:
        """Handle red thumb scenario: click thumb, then new exchange"""
        try:
            self.logger.info("DEBUG: Starting red thumb handling...")

            # First try template-based detection for more precise clicking
            template_path = os.path.join("templates", "red_thumb_100x40.png")
            if os.path.exists(template_path):
                # Suppress warnings during screenshot and processing
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    screenshot = pyautogui.screenshot()
                    screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                    template = cv2.imread(template_path)

                if template is not None:
                    try:
                        with warnings.catch_warnings():
                            warnings.simplefilter("ignore")
                            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        if max_val > 0.8:  # Lower threshold for clicking
                            # Calculate center of template match
                            template_h, template_w = template.shape[:2]
                            center_x = max_loc[0] + template_w // 2
                            center_y = max_loc[1] + template_h // 2

                            self.logger.info(f"DEBUG: Template match found at ({center_x}, {center_y}) with confidence {max_val:.2f}")

                            # Click on red thumb
                            pyautogui.click(center_x, center_y)
                            self.total_red_thumb_clicks += 1
                            self.logger.info(f"Clicked red thumb at ({center_x}, {center_y}) - Total red thumb clicks: {self.total_red_thumb_clicks}")
                            time.sleep(1.5)  # Wait longer for UI to update

                            # Now click on "New Exchange" button using calibrated coordinates
                            new_exchange_x, new_exchange_y = 1255, 1142
                            pyautogui.click(new_exchange_x, new_exchange_y)
                            self.logger.info(f"Clicked new exchange at ({new_exchange_x}, {new_exchange_y})")
                            time.sleep(1.0)

                            return True
                    except Exception as template_error:
                        self.logger.warning(f"Template processing failed: {str(template_error)}")

            # Fallback to color detection if template fails
            self.logger.info("DEBUG: Template detection failed, trying color detection...")

            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    screenshot = pyautogui.screenshot()
                    screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                    hsv = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2HSV)

                # Red color detection
                lower_red1 = np.array([0, 50, 50])
                upper_red1 = np.array([10, 255, 255])
                lower_red2 = np.array([170, 50, 50])
                upper_red2 = np.array([180, 255, 255])

                mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
                mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
                red_mask = mask1 + mask2

                # Find contours of red areas
                contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                self.logger.info(f"DEBUG: Found {len(contours)} red contours")

                if contours:
                    # Find largest red area (likely the thumb)
                    largest_contour = max(contours, key=cv2.contourArea)
                    area = cv2.contourArea(largest_contour)
                    self.logger.info(f"DEBUG: Largest contour area: {area}")

                    # Get center of the largest red area
                    M = cv2.moments(largest_contour)
                    if M["m00"] != 0:
                        center_x = int(M["m10"] / M["m00"])
                        center_y = int(M["m01"] / M["m00"])
                        self.logger.info(f"DEBUG: Color detection found red area at ({center_x}, {center_y})")

                        # Click on red thumb
                        pyautogui.click(center_x, center_y)
                        self.total_red_thumb_clicks += 1
                        self.logger.info(f"Clicked red thumb at ({center_x}, {center_y}) - Total red thumb clicks: {self.total_red_thumb_clicks}")
                        time.sleep(1.5)

                        # Now click on "New Exchange" button using calibrated coordinates
                        new_exchange_x, new_exchange_y = 1255, 1142
                        pyautogui.click(new_exchange_x, new_exchange_y)
                        self.logger.info(f"Clicked new exchange at ({new_exchange_x}, {new_exchange_y})")
                        time.sleep(1.0)

                        return True
                    else:
                        self.logger.warning("DEBUG: Contour moments calculation failed")
                else:
                    self.logger.warning("DEBUG: No red contours found")

            except Exception as color_error:
                self.logger.error(f"Color detection failed: {str(color_error)}")
                return False

            self.logger.warning("DEBUG: Red thumb handling failed - no valid detection method worked")
            return False

        except Exception as e:
            self.logger.error(f"Error handling red thumb: {e}")
            return False

    def _execute_intelligent_exchange(self) -> bool:
        """Execute intelligent map piece exchange"""
        try:
            self.logger.info("Starting intelligent map piece exchange...")

            # Analyze all map pieces
            map_numbers = self._analyze_map_pieces()

            if not map_numbers:
                self.logger.error("Could not analyze map pieces - falling back to normal process")
                return False

            # Find optimal trade
            give_map, receive_map = self._find_optimal_trade(map_numbers)

            if not give_map or not receive_map:
                self.logger.error("Could not determine optimal trade")
                return False

            # Step 1: Click on the map piece with lowest number (what we'll give)
            self.logger.info(f"Step 1: Selecting {give_map} to give away")
            if not self._click_map_piece(give_map):
                return False
            time.sleep(0.5)

            # Step 2: Switch to "you will lose" area
            lose_area_x, lose_area_y = 1448, 468  # Your calibrated coordinate
            pyautogui.click(lose_area_x, lose_area_y)
            self.logger.info(f"Switched to 'you will lose' area at ({lose_area_x}, {lose_area_y})")
            time.sleep(0.5)

            # Step 3: Click on the map piece with highest number (what we'll receive)
            self.logger.info(f"Step 3: Selecting {receive_map} to receive")
            if not self._click_map_piece(receive_map):
                return False
            time.sleep(0.5)

            # Step 4: Click "Initiate Exchange"
            initiate_x, initiate_y = 1258, 1145  # Your calibrated coordinate
            pyautogui.click(initiate_x, initiate_y)
            self.logger.info(f"Clicked initiate exchange at ({initiate_x}, {initiate_y})")
            time.sleep(1.0)

            # Step 5: Click confirmation button
            confirm_x, confirm_y = 1258, 933  # Captured exact coordinate
            pyautogui.click(confirm_x, confirm_y)
            self.logger.info(f"Clicked confirmation at ({confirm_x}, {confirm_y})")
            time.sleep(1.0)  # Wait for confirmation to process

            self.logger.info("[SUCCESS] Intelligent exchange initiated - continuing with trade detection sequence")

            # Now continue with the original trade detection logic (after step 4_confirm_1)
            return self._continue_trade_detection_sequence()

        except Exception as e:
            self.logger.error(f"Error in intelligent exchange: {e}")
            return False

    def _continue_trade_detection_sequence(self) -> bool:
        """Continue with trade detection sequence from step 4"""
        try:
            self.logger.info("Starting trade detection sequence from step 4...")

            # Get click coordinates for the remaining steps
            from module_config_manager import ModuleConfigManager
            config_manager = ModuleConfigManager()
            config = config_manager.get_module_config("map_trade")

            if not config or 'click_coordinates' not in config:
                self.logger.error("Map trade configuration not found")
                return False

            click_coordinates = config['click_coordinates']

            # STEP 4: Click 4_confirm_1 first (this is step 4, index 3)
            if len(click_coordinates) > 3:
                coord = click_coordinates[3]  # Step 4 (0-indexed: 3)
                if coord.get('enabled', True):
                    self.current_step = 4
                    self.current_activity = f"Executing {coord.get('name', 'step_4')}"

                    x, y = coord['x'], coord['y']
                    delay = coord.get('delay', 1.0)

                    self.logger.info(f"Step 4: Clicking {coord.get('name', '4_confirm_1')} at ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(delay)

            # Now start trade detection loop with safety counter
            trades_completed = 0
            max_trades = 10  # Safety limit to prevent infinite loops

            while trades_completed < max_trades:
                # Check if trade is available using image detection
                if self._check_trade_available():
                    self.logger.info(f"Trade #{trades_completed + 1} available - executing trade sequence")

                    # Execute steps 5_trade_option_2 and 6_confirm_2
                    for step_idx in [4, 5]:  # Steps 5 and 6 (0-indexed: 4 and 5)
                        if step_idx >= len(click_coordinates):
                            break

                        coord = click_coordinates[step_idx]
                        if not coord.get('enabled', True):
                            continue

                        self.current_step = step_idx + 1
                        self.current_activity = f"Executing {coord.get('name', f'step_{step_idx + 1}')}"

                        x, y = coord['x'], coord['y']
                        delay = coord.get('delay', 1.0)

                        self.logger.info(f"Step {step_idx + 1}: Clicking {coord.get('name', 'unknown')} at ({x}, {y})")
                        pyautogui.click(x, y)
                        time.sleep(delay)

                    trades_completed += 1
                    self.total_trades_completed += 1
                    self.logger.info(f"[SUCCESS] Trade #{trades_completed} completed - Total trades: {self.total_trades_completed}")
                    time.sleep(1.0)  # Wait before checking for next trade
                else:
                    # No more trades available
                    self.logger.info("No more trades available - proceeding to final steps")
                    break

            if trades_completed >= max_trades:
                self.logger.warning(f"Reached maximum trade limit ({max_trades}) - stopping to prevent infinite loop")

            # Execute final steps 9_trade_option_4 and 10_confirm_4 (skip steps 7 and 8)
            for final_step_idx in [8, 9]:  # Steps 9 and 10 (0-indexed: 8 and 9)
                if final_step_idx < len(click_coordinates):
                    coord = click_coordinates[final_step_idx]
                    if coord.get('enabled', True):
                        self.current_step = final_step_idx + 1
                        self.current_activity = f"Executing {coord.get('name', f'step_{final_step_idx + 1}')}"

                        x, y = coord['x'], coord['y']
                        delay = coord.get('delay', 1.0)

                        self.logger.info(f"Final Step {final_step_idx + 1}: Clicking {coord.get('name', 'unknown')} at ({x}, {y})")
                        pyautogui.click(x, y)
                        time.sleep(delay)

            self.logger.info("[SUCCESS] Complete map trade sequence finished!")
            return True

        except Exception as e:
            self.logger.error(f"Error in trade detection sequence: {e}")
            return False

    def _intelligent_trade_analysis(self) -> bool:
        """Enhanced intelligent trading logic with trade-in-progress detection"""
        try:
            self.logger.info("Starting intelligent trade analysis...")

            # Detect current screen state
            screen_state = self._detect_screen_state()

            if screen_state == "trade_in_progress":
                # Trade already in progress - but first check for red thumb
                self.logger.info("Trade in progress detected - checking for red thumb first")

                # Check if there's a red thumb to handle
                if self._has_red_thumb():
                    self.logger.info("Red thumb detected - handling acknowledgment first")
                    if self._handle_red_thumb():
                        self.logger.info("Red thumb handled - waiting for new exchange screen")
                        time.sleep(2.0)  # Wait for screen to update
                        # After handling red thumb, we should be on initiate exchange screen
                        success = self._execute_intelligent_exchange()
                    else:
                        self.logger.warning("Failed to handle red thumb - continuing with trade sequence")
                        success = self._continue_trade_detection_sequence()
                else:
                    # No red thumb, continue with existing trade sequence from step 4
                    self.logger.info("No red thumb - continuing with existing trade sequence from step 4")
                    success = self._continue_trade_detection_sequence()

                return False  # We handled it, don't continue with normal process

            elif screen_state == "initiate_exchange":
                # Ready for new trade - analyze and execute intelligent exchange
                self.logger.info("Ready for new trade - analyzing map pieces for optimal exchange")
                success = self._execute_intelligent_exchange()
                return False  # We handled it, don't continue with normal process

            elif screen_state == "red_elements":
                # Red elements detected - could be thumb or cancel button
                # Try to handle red thumb first
                if not self._handle_red_thumb():
                    self.logger.warning("Red elements detected but couldn't handle - continuing")

                # Wait and try again
                time.sleep(1.0)
                success = self._execute_intelligent_exchange()
                return False  # We handled it, don't continue with normal process

            else:
                # Normal state - continue with regular trade process
                self.logger.info("Normal state detected - continuing with regular trade process")
                return True  # Continue with normal steps

        except Exception as e:
            self.logger.error(f"Error in intelligent trade analysis: {e}")
            return True  # Fall back to normal process



    def calibrate_map_regions(self):
        """Simple calibration tool for map piece regions"""
        print("Map Trade Calibration Tool")
        print("=" * 40)
        print("This tool will help you calibrate the map piece regions M1-M7")
        print("Make sure you're on the initiate exchange screen with all 7 map pieces visible")
        print()

        input("Press Enter when ready to start calibration...")

        new_regions = {}

        for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
            print(f"\nCalibrating {map_name}:")
            print("Position your mouse over the CENTER of the map piece")
            input("Press Enter to capture current mouse position...")

            try:
                x, y = pyautogui.position()
                # Define a region around the clicked point
                width, height = 80, 100  # Default size
                region_x = x - width // 2
                region_y = y - height // 2

                new_regions[map_name] = (region_x, region_y, width, height)
                print(f"  {map_name} region set to: ({region_x}, {region_y}, {width}, {height})")

            except Exception as e:
                print(f"  Error capturing {map_name}: {e}")
                continue

        print("\nCalibration complete!")
        print("New regions:")
        for map_name, region in new_regions.items():
            print(f"  '{map_name}': {region},")

        print("\nCopy these values to the _define_map_piece_regions method in the code.")

        return new_regions
