#!/usr/bin/env python3
"""
Test scenario detection for map trade
"""

from modules.map_trade import MapTradeModule
import logging

def test_scenario_detection():
 """Test which scenario will be triggered"""
 print("Map Trade Scenario Detection Test")
 print("=" * 40)
 
 # Set up logging
 logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
 
 # Create module
 module = MapTradeModule()
 
 print("1. Testing screen state detection...")
 try:
 screen_state = module._detect_screen_state()
 print(f" Screen state: {screen_state}")
 except Exception as e:
 print(f" Error: {e}")
 screen_state = "unknown"
 
 print("\n2. Testing red thumb detection...")
 try:
 has_red_thumb = module._has_red_thumb()
 print(f" Has red thumb: {has_red_thumb}")
 except Exception as e:
 print(f" Error: {e}")
 has_red_thumb = False
 
 print("\n3. Testing new exchange button detection...")
 try:
 has_new_exchange = module._has_new_exchange_button()
 print(f" Has new exchange: {has_new_exchange}")
 except Exception as e:
 print(f" Error: {e}")
 has_new_exchange = False
 
 print(f"\n{'='*50}")
 print("SCENARIO ANALYSIS:")
 print(f"{'='*50}")
 
 if screen_state == "trade_in_progress":
 print(" SCENARIO 1: Cancel Exchange visible")
 print(" Action: Go to Step 4 sequence")
 scenario = 1
 
 elif screen_state == "initiate_exchange":
 print(" SCENARIO 2: Already on initiate exchange screen")
 print(" Action: Execute intelligent trade directly")
 scenario = 2
 
 elif has_red_thumb:
 print(" SCENARIO 3: Red thumb visible")
 print(" Action: Click red thumb → Click new exchange → Intelligent trade")
 scenario = 3
 
 elif has_new_exchange:
 print(" SCENARIO 4: New exchange button visible (no red thumb)")
 print(" Action: Click new exchange directly at (1255, 1142) → Intelligent trade")
 scenario = 4
 
 else:
 print(" SCENARIO 5: Normal state")
 print(" Action: Continue with normal sequence from step 4")
 scenario = 5
 
 print(f"\nDetected Scenario: {scenario}")
 
 # Ask if user wants to test this scenario
 test_it = input(f"\nTest this scenario? (y/n): ").lower().strip()
 
 if test_it == 'y':
 print(f"\n Testing Scenario {scenario}...")
 try:
 result = module.manual_trigger_map_trade()
 print(f"Result: {result}")
 except Exception as e:
 print(f"Error: {e}")
 import traceback
 traceback.print_exc()
 else:
 print("Test cancelled")

if __name__ == "__main__":
 try:
 test_scenario_detection()
 except KeyboardInterrupt:
 print("\nInterrupted")
 except Exception as e:
 print(f"Error: {e}")
 import traceback
 traceback.print_exc()
