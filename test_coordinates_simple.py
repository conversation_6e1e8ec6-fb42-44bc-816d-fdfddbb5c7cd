#!/usr/bin/env python3
"""
Simple test to verify the coordinates are correct
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from map_trade import MapTradeModule

def test_coordinates():
    """Test that coordinates are correct"""
    print("Testing Map Trade Coordinates")
    print("=" * 50)
    
    try:
        # Create module
        print("Creating MapTradeModule...")
        map_trade = MapTradeModule()
        print("✅ MapTradeModule created successfully")
        
        # Check map piece regions
        print("\n1. Map Piece Coordinates:")
        for map_name, region in map_trade.map_piece_regions.items():
            quantity_pos = region['quantity_pos']
            click_pos = region['click_pos']
            print(f"   {map_name}: quantity at {quantity_pos}, click at {click_pos}")
        
        # Check if coordinates are TrOCR optimized (should be around 500-700 range)
        m1_quantity = map_trade.map_piece_regions['M1']['quantity_pos']
        if m1_quantity[0] > 1000:  # Old coordinates
            print("❌ Using OLD coordinates (should be TrOCR optimized ~500-700 range)")
            print(f"   M1 quantity position: {m1_quantity} (should be around (532, 436))")
        else:
            print("✅ Using TrOCR optimized coordinates")
        
        # Test the intelligent exchange method exists
        if hasattr(map_trade, '_execute_intelligent_exchange'):
            print("\n2. ✅ _execute_intelligent_exchange method exists")
            
            # Create a mock test to see what coordinates it would use
            print("\n3. Testing coordinate values in intelligent exchange:")
            
            # We can't easily test the method without running it, but we can check
            # if the map piece regions have the right coordinates
            print("   Map piece click coordinates:")
            for map_name in ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']:
                if map_name in map_trade.map_piece_regions:
                    click_pos = map_trade.map_piece_regions[map_name]['click_pos']
                    print(f"     {map_name}: {click_pos}")
            
            print(f"\n   Expected 'you will lose' coordinates: (732, 261)")
            print(f"   Expected 'initiate exchange' coordinates: (636, 611)")
            print(f"   Expected 'confirm exchange' coordinates: (633, 498)")
            
        else:
            print("❌ _execute_intelligent_exchange method missing")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_coordinates()
