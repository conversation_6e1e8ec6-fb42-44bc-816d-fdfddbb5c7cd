#!/usr/bin/env python3
"""
Complete OCR System Test - Verify all OCR improvements are working
Tests Tesseract, EasyOCR, PaddleOCR, and the map trade integration
"""
import sys
import os
import cv2
import numpy as np
import time

def test_tesseract_installation():
    """Test if Tesseract is properly installed"""
    print("🔍 Testing Tesseract Installation")
    print("-" * 40)
    
    try:
        import pytesseract
        
        # Test basic Tesseract functionality
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        # Create simple test image
        image = np.ones((50, 100, 3), dtype=np.uint8) * 255
        cv2.putText(image, "42", (30, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
        
        # Test OCR
        text = pytesseract.image_to_string(image, config='--psm 8 -c tessedit_char_whitelist=0123456789').strip()
        
        if text == "42":
            print(f"✅ Tesseract OCR working: '{text}'")
            return True
        else:
            print(f"⚠️ Tesseract OCR issue: got '{text}', expected '42'")
            return False
            
    except Exception as e:
        print(f"❌ Tesseract test failed: {e}")
        return False

def test_ai_ocr_libraries():
    """Test AI OCR libraries"""
    print("\n🤖 Testing AI OCR Libraries")
    print("-" * 40)
    
    results = {'easyocr': False, 'paddleocr': False}
    
    # Test EasyOCR
    try:
        import easyocr
        print("✅ EasyOCR library imported")
        
        # Quick test
        reader = easyocr.Reader(['en'], gpu=False)
        
        # Create test image
        image = np.ones((50, 100), dtype=np.uint8) * 255
        cv2.putText(image, "7", (40, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, 0, 2)
        
        results_ocr = reader.readtext(image, allowlist='0123456789')
        if results_ocr and results_ocr[0][1] == "7":
            print("✅ EasyOCR working correctly")
            results['easyocr'] = True
        else:
            print(f"⚠️ EasyOCR result: {results_ocr}")
            
    except Exception as e:
        print(f"❌ EasyOCR error: {e}")
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR library imported")
        
        # Quick test with fallback initialization
        try:
            ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        except:
            ocr = PaddleOCR(use_angle_cls=True, lang='en')
        
        # Create test image
        image = np.ones((50, 100), dtype=np.uint8) * 255
        cv2.putText(image, "9", (40, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.5, 0, 2)
        
        results_ocr = ocr.ocr(image, cls=True)
        if results_ocr and results_ocr[0] and results_ocr[0][0][1][0] == "9":
            print("✅ PaddleOCR working correctly")
            results['paddleocr'] = True
        else:
            print(f"⚠️ PaddleOCR result: {results_ocr}")
            
    except Exception as e:
        print(f"❌ PaddleOCR error: {e}")
    
    return results

def test_map_trade_ocr_integration():
    """Test map trade OCR integration"""
    print("\n🗺️ Testing Map Trade OCR Integration")
    print("-" * 40)
    
    try:
        # Add modules to path
        sys.path.append('modules')
        
        # Import and initialize module
        from map_trade import MapTradeModule
        module = MapTradeModule()
        print("✅ MapTradeModule initialized")
        
        # Test with various map piece numbers
        test_numbers = [1, 5, 12, 25, 99]
        results = {'total': 0, 'passed': 0, 'failed': 0}
        
        print("\n🧪 Testing OCR with map piece numbers:")
        
        for number in test_numbers:
            # Create realistic map piece image
            image = create_map_piece_image(number)
            
            try:
                start_time = time.time()
                result = module._extract_number_from_image(image)
                elapsed_time = time.time() - start_time
                
                results['total'] += 1
                
                if result == number:
                    results['passed'] += 1
                    print(f"   {number:2d}: ✅ SUCCESS ({elapsed_time:.2f}s)")
                else:
                    results['failed'] += 1
                    print(f"   {number:2d}: ❌ FAILED (got {result}, {elapsed_time:.2f}s)")
                    
            except Exception as e:
                results['total'] += 1
                results['failed'] += 1
                print(f"   {number:2d}: ❌ ERROR - {e}")
        
        # Calculate accuracy
        accuracy = (results['passed'] / results['total']) * 100 if results['total'] > 0 else 0
        
        print(f"\n📊 Map Trade OCR Results:")
        print(f"   Accuracy: {accuracy:.1f}% ({results['passed']}/{results['total']})")
        
        # Check OCR method availability
        tesseract_available = getattr(module, '_tesseract_available', 'Unknown')
        ai_available = getattr(module, '_ai_ocr_available', 'Unknown')
        
        print(f"   Tesseract: {'✅ Available' if tesseract_available else '❌ Not Available'}")
        print(f"   AI OCR: {'✅ Available' if ai_available else '❌ Not Available'}")
        
        return accuracy >= 80, results
        
    except Exception as e:
        print(f"❌ Map trade OCR test failed: {e}")
        return False, None

def create_map_piece_image(number: int) -> np.ndarray:
    """Create a realistic map piece number image"""
    # Create a 35x35 image with slight background variation (like game UI)
    image = np.ones((35, 35, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Add some noise to simulate game UI
    noise = np.random.randint(-10, 10, (35, 35, 3))
    image = np.clip(image.astype(int) + noise, 0, 255).astype(np.uint8)
    
    # Add black text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.7 if number >= 10 else 0.9
    color = (0, 0, 0)  # Black
    thickness = 2
    
    # Get text size and center it
    text = str(number)
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = (image.shape[1] - text_size[0]) // 2
    text_y = (image.shape[0] + text_size[1]) // 2
    
    # Draw the number
    cv2.putText(image, text, (text_x, text_y), font, font_scale, color, thickness)
    
    return image

def main():
    print("🚀 Complete OCR System Test")
    print("Testing all OCR improvements for map trade automation")
    print("=" * 60)
    
    # Test Tesseract
    tesseract_ok = test_tesseract_installation()
    
    # Test AI OCR libraries
    ai_results = test_ai_ocr_libraries()
    
    # Test map trade integration
    map_trade_ok, map_results = test_map_trade_ocr_integration()
    
    # Final summary
    print("\n📋 FINAL SUMMARY")
    print("=" * 60)
    
    print(f"Tesseract OCR: {'✅ Working' if tesseract_ok else '❌ Issues'}")
    print(f"EasyOCR: {'✅ Working' if ai_results['easyocr'] else '❌ Issues'}")
    print(f"PaddleOCR: {'✅ Working' if ai_results['paddleocr'] else '❌ Issues'}")
    print(f"Map Trade Integration: {'✅ Working' if map_trade_ok else '❌ Issues'}")
    
    # Overall assessment
    working_methods = sum([tesseract_ok, ai_results['easyocr'], ai_results['paddleocr']])
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    if working_methods >= 2 and map_trade_ok:
        print("✅ OCR SYSTEM FULLY FUNCTIONAL!")
        print("✅ Map trade automation should work reliably")
        print("✅ Multiple OCR methods available for redundancy")
    elif working_methods >= 1 and map_trade_ok:
        print("✅ OCR SYSTEM WORKING!")
        print("✅ Map trade automation should work")
        print("⚠️ Consider installing additional OCR methods for better reliability")
    elif working_methods >= 1:
        print("⚠️ OCR LIBRARIES WORKING BUT INTEGRATION ISSUES")
        print("🔧 Check map trade module integration")
    else:
        print("❌ OCR SYSTEM NEEDS ATTENTION")
        print("🔧 Install OCR libraries and check configurations")
    
    print(f"\n📊 STATISTICS:")
    if map_results:
        print(f"   Map piece recognition accuracy: {(map_results['passed']/map_results['total'])*100:.1f}%")
    print(f"   Working OCR methods: {working_methods}/3")
    
    print(f"\n🛠️ NEXT STEPS:")
    if not tesseract_ok:
        print("• Install Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki")
    if not ai_results['easyocr']:
        print("• Install EasyOCR: pip install easyocr")
    if not ai_results['paddleocr']:
        print("• Install PaddleOCR: pip install paddlepaddle paddleocr")
    if working_methods > 0 and map_trade_ok:
        print("• ✅ Ready to use map trade automation!")
    
    return working_methods >= 1 and map_trade_ok

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 OCR SYSTEM READY FOR MAP TRADING!")
    else:
        print("\n⚠️ OCR system needs configuration before use")
