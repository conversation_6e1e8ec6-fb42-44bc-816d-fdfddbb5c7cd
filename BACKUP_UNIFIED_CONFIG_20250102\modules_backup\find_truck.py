#!/usr/bin/env python3
"""
Find Truck Module - Fast truck finder for plundering
Finds trucks with 2+ golden puzzle pieces under power threshold
Manual trigger only - Lightning fast scanning
"""

import time
import logging
import pyautogui
import cv2
import numpy as np
import os
import json
import pytesseract
from base_module import BaseModule
from module_config_manager import ModuleConfigManager
import re
from typing import Optional, Dict, Any

class FindTruckModule(BaseModule):
    def __init__(self, screen_scanner):
        super().__init__("find_truck", screen_scanner)
        self.priority = -1  # Manual trigger only
        self.enabled = False  # DISABLED - will work on it later
        self.logger = logging.getLogger(__name__)
        self.screen_scanner = screen_scanner  # Store reference to screen scanner

        # Configuration
        self.config_manager = ModuleConfigManager()
        
        # Power threshold (persistent setting) - load from config or use default
        self.max_power_threshold = self._load_power_threshold()
        
        # Load coordinates from configuration
        self.plunder_area_coord = self._get_coordinate_from_config("plunder_area", (930, 796))
        self.loot_plunder_region = self._get_region_from_config("loot_plunder", (927, 921, 1598, 1258))
        self.power_region = self._get_region_from_config("power_region", (1064, 1036, 1208, 1080))
        self.reset_coord = self._get_coordinate_from_config("reset_button", (1598, 87))

        # Safety features
        self.stop_requested = False
        self.safety_check_interval = 1.0  # Check every second
        
        # Template names (templates are loaded by screen_scanner)
        self.template_names = {
            'golden_truck': 'Golden',  # 37x73 template (without .png extension)
            'purple_truck': 'Purple',  # 35x71 template
            'golden_puzzle': 'Golden_puzzle'  # 73x70 template
        }
        
        # Truck tracking
        self.truck_positions = []  # Will store [(x, y, type), ...]
        self.current_truck_index = 0
        
        # Performance settings
        self.scan_timeout = 1.0  # 1 second per truck max
        self.template_confidence = 0.8

        # Configure Tesseract path (same as map_trade module)
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        self.logger.info("Find Truck module initialized - Fast truck finder for plundering")

    def _get_coordinate_from_config(self, coord_name: str, default_coord: tuple) -> tuple:
        """Get coordinate from config or use default"""
        try:
            coordinate = self.config_manager.get_coordinate_by_name(self.name, coord_name)
            if coordinate:
                return coordinate
            else:
                self.logger.warning(f"Coordinate '{coord_name}' not found in config, using default: {default_coord}")
                return default_coord
        except Exception as e:
            self.logger.warning(f"Error loading coordinate '{coord_name}': {e}, using default: {default_coord}")
            return default_coord

    def _get_region_from_config(self, region_name: str, default_region: tuple) -> tuple:
        """Get region from config or use default"""
        try:
            # Try to get region from scan_regions in config
            config = self.config_manager.get_module_config(self.name)
            if config and 'scan_regions' in config:
                for region in config['scan_regions']:
                    if region.get('name') == region_name:
                        return (region['x'], region['y'], region['x'] + region['width'], region['y'] + region['height'])

            self.logger.warning(f"Region '{region_name}' not found in config, using default: {default_region}")
            return default_region
        except Exception as e:
            self.logger.warning(f"Error loading region '{region_name}': {e}, using default: {default_region}")
            return default_region

    def can_run(self, screen_data):
        """This module only runs when manually triggered"""
        return False  # Manual trigger only

    def can_execute(self, templates_detected):
        """This module only runs when manually triggered"""
        return False  # Manual trigger only

    def get_cooldown(self):
        """No cooldown for manual trigger module"""
        return 0
    
    def execute(self, screen_data=None):
        """Execute truck finding sequence"""
        try:
            self.logger.info("FIND TRUCK: Starting fast truck finder")
            self.logger.info(f"Power threshold: {self.max_power_threshold}M")
            
            # Step 1: Click plunder area
            if not self._click_plunder_area():
                return False
            
            # Main search loop - run until suitable truck found
            attempt = 0
            self.stop_requested = False  # Reset stop flag

            while True:
                attempt += 1
                self.logger.info(f"Search attempt {attempt}")

                # Safety check - mouse movement and stop button
                if not self._check_safety_conditions():
                    self.logger.info("Search stopped by safety conditions")
                    return False

                # Step 2: Scan and identify trucks
                if not self._scan_and_identify_trucks():
                    self.logger.warning("Failed to identify trucks, resetting...")
                    self._reset_and_retry()
                    continue

                # Step 3-4: Check each truck for golden puzzles and power
                truck_found = self._check_trucks_for_criteria()

                if truck_found:
                    self.logger.info("SUCCESS: Found suitable truck!")
                    return True

                # No suitable truck found, reset and try again
                self.logger.info("No suitable trucks found, resetting...")
                self._reset_and_retry()
            
        except Exception as e:
            self.logger.error(f"Error in find_truck execution: {str(e)}")
            return False
    
    def _click_plunder_area(self):
        """Step 1: Click on plunder area"""
        try:
            self.logger.info("Step 1: Clicking plunder area")
            pyautogui.click(self.plunder_area_coord[0], self.plunder_area_coord[1])
            self.logger.info("Waiting 3 seconds for UI to fully load...")
            time.sleep(3.0)  # Wait 3 seconds for UI to fully load
            return True
        except Exception as e:
            self.logger.error(f"Failed to click plunder area: {str(e)}")
            return False
    
    def _scan_and_identify_trucks(self):
        """Step 2: Scan for golden and purple trucks, assign coordinates"""
        try:
            self.logger.info("Step 2: Scanning for trucks (lightning fast)")
            start_time = time.time()

            # Take screenshot for truck detection
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Find golden trucks with more lenient template matching
            golden_positions = self._find_template_positions(screenshot_cv, 'golden_truck')
            purple_positions = self._find_template_positions(screenshot_cv, 'purple_truck')

            # Combine and sort by position (left to right, top to bottom)
            all_trucks = []
            for pos in golden_positions:
                all_trucks.append((pos[0], pos[1], 'golden'))
            for pos in purple_positions:
                all_trucks.append((pos[0], pos[1], 'purple'))

            # Sort trucks by position (top to bottom, left to right)
            all_trucks.sort(key=lambda truck: (truck[1], truck[0]))

            # Assign numbers 1-4 to first 4 trucks
            self.truck_positions = all_trucks[:4]

            scan_time = time.time() - start_time
            self.logger.info(f"Truck scan completed in {scan_time:.2f}s")
            self.logger.info(f"Found trucks: {len(self.truck_positions)}")

            for i, (x, y, truck_type) in enumerate(self.truck_positions, 1):
                self.logger.info(f"  {i}. {truck_type.title()} truck at ({x}, {y})")

            return len(self.truck_positions) > 0
            
        except Exception as e:
            self.logger.error(f"Error scanning trucks: {str(e)}")
            return False
    
    def _find_template_positions(self, screenshot, template_name):
        """Find all positions of a template in screenshot with more lenient settings"""
        try:
            template_key = self.template_names[template_name]

            # Check if template is loaded
            if template_key not in self.screen_scanner.templates:
                self.logger.error(f"Template {template_key} not loaded in screen_scanner")
                return []

            # Use screen scanner to find all matches with strict threshold
            # Higher threshold to avoid false positives like grass
            matches = self.screen_scanner.find_template(screenshot, template_key, threshold=0.85)

            # Filter matches by color (keep the color filtering)
            color_filtered_positions = []
            for match in matches:
                x, y, w, h = match
                center_x = x + w // 2
                center_y = y + h // 2

                # Extract the region around the match for color analysis
                region = screenshot[y:y+h, x:x+w]

                # Check if this match has the correct color
                if self._is_correct_color(region, template_name):
                    color_filtered_positions.append((center_x, center_y))

            self.logger.info(f"Found {len(matches)} template matches, {len(color_filtered_positions)} with correct color for {template_name}")
            return color_filtered_positions

        except Exception as e:
            self.logger.error(f"Error finding template {template_name}: {str(e)}")
            return []

    def _determine_truck_type_at_position(self, screenshot, x, y):
        """Determine if truck at position is golden or purple"""
        try:
            # Extract small region around the truck position
            region_size = 50  # 50x50 pixel region around truck center
            x1 = max(0, x - region_size // 2)
            y1 = max(0, y - region_size // 2)
            x2 = min(screenshot.shape[1], x + region_size // 2)
            y2 = min(screenshot.shape[0], y + region_size // 2)

            region = screenshot[y1:y2, x1:x2]
            hsv_region = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)

            # Check for golden color
            golden_lower = np.array([8, 40, 60])
            golden_upper = np.array([45, 255, 255])
            golden_mask = cv2.inRange(hsv_region, golden_lower, golden_upper)
            golden_pixels = cv2.countNonZero(golden_mask)

            # Check for purple color
            purple_lower = np.array([100, 40, 30])
            purple_upper = np.array([170, 255, 255])
            purple_mask = cv2.inRange(hsv_region, purple_lower, purple_upper)
            purple_pixels = cv2.countNonZero(purple_mask)

            # Return the color with more pixels
            if golden_pixels > purple_pixels:
                return 'golden'
            else:
                return 'purple'

        except Exception as e:
            self.logger.error(f"Error determining truck type: {str(e)}")
            return 'unknown'

    def _is_correct_color(self, region, template_name):
        """Check if the region has the correct color for the template type"""
        try:
            if region.size == 0:
                return False

            # Crop region by 10 pixels top and bottom to avoid text overlays
            h = region.shape[0]
            if h > 20:  # Only crop if region is tall enough
                crop_pixels = 10
                region = region[crop_pixels:h-crop_pixels, :]

            # Convert BGR to HSV for better color detection
            hsv_region = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)

            if template_name == 'golden_truck':
                # Golden/yellow color range in HSV - strict ranges to avoid grass
                # Hue: 18-32 (narrow yellow/orange range)
                # Saturation: 150-255 (highly saturated only)
                # Value: 150-255 (bright only)
                lower_golden = np.array([18, 150, 150])
                upper_golden = np.array([32, 255, 255])
                mask = cv2.inRange(hsv_region, lower_golden, upper_golden)

            elif template_name == 'purple_truck':
                # Purple color range in HSV - strict ranges to avoid grass
                # Hue: 125-145 (narrow purple range)
                # Saturation: 150-255 (highly saturated only)
                # Value: 80-255 (reasonably bright)
                lower_purple = np.array([125, 150, 80])
                upper_purple = np.array([145, 255, 255])
                mask = cv2.inRange(hsv_region, lower_purple, upper_purple)

            else:
                # For golden_puzzle, we don't need color filtering
                return True

            # Calculate percentage of pixels that match the color
            color_pixels = cv2.countNonZero(mask)
            total_pixels = region.shape[0] * region.shape[1]
            color_percentage = (color_pixels / total_pixels) * 100

            # Require at least 20% of pixels to match the target color (strict)
            threshold_percentage = 20.0
            is_correct = color_percentage >= threshold_percentage

            self.logger.debug(f"Color analysis for {template_name}: {color_percentage:.1f}% match (threshold: {threshold_percentage}%)")
            return is_correct

        except Exception as e:
            self.logger.error(f"Error in color detection for {template_name}: {str(e)}")
            return True  # Default to accepting if color detection fails

    def _analyze_truck_colors(self, screenshot):
        """Debug method to analyze colors in truck regions"""
        try:
            # This method can be called to help tune color ranges
            # Find all truck-like patterns first
            for template_name in ['golden_truck', 'purple_truck']:
                template_key = self.template_names[template_name]
                if template_key in self.screen_scanner.templates:
                    matches = self.screen_scanner.find_template(screenshot, template_key, threshold=0.7)
                    for match in matches:
                        x, y, w, h = match
                        region = screenshot[y:y+h, x:x+w]

                        # Analyze dominant colors
                        hsv_region = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)

                        # Calculate average HSV values
                        avg_hue = np.mean(hsv_region[:, :, 0])
                        avg_sat = np.mean(hsv_region[:, :, 1])
                        avg_val = np.mean(hsv_region[:, :, 2])

                        self.logger.info(f"Truck at ({x}, {y}): HSV({avg_hue:.1f}, {avg_sat:.1f}, {avg_val:.1f})")

        except Exception as e:
            self.logger.error(f"Error analyzing truck colors: {str(e)}")
    
    def _check_trucks_for_criteria(self):
        """Step 3-4: Check each truck for golden puzzles and power threshold"""
        try:
            for i, (x, y, truck_type) in enumerate(self.truck_positions, 1):
                self.logger.info(f"Checking truck {i}: {truck_type} at ({x}, {y})")

                # Click on truck
                pyautogui.click(x, y)
                time.sleep(0.5)  # Quick wait for loot window

                # Check for golden puzzle pieces
                puzzle_count = self._count_golden_puzzles()
                self.logger.info(f"Truck {i}: Found {puzzle_count} golden puzzle pieces")

                if puzzle_count < 2:
                    self.logger.info(f"Truck {i}: Not enough puzzles ({puzzle_count}/2)")
                    continue

                # Check power level
                self.logger.info(f"Truck {i}: Checking power threshold...")
                power_ok = self._check_power_threshold()

                if not power_ok:
                    self.logger.info(f"Truck {i}: Power too high - skipping")
                    continue

                self.logger.info(f"Truck {i}: Power OK - within threshold")

                # Found suitable truck!
                self.logger.info(f"Truck {i}: PERFECT! {puzzle_count} puzzles + power OK")
                self._click_on_truck(x, y)  # Final click to select
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking trucks: {str(e)}")
            return False
    
    def _count_golden_puzzles(self):
        """Count golden puzzle pieces in loot plunder region"""
        try:
            # Take screenshot of loot region
            x1, y1, x2, y2 = self.loot_plunder_region
            region_screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            region_np = np.array(region_screenshot)
            region_cv = cv2.cvtColor(region_np, cv2.COLOR_RGB2BGR)

            # Use screen scanner to find golden puzzle templates
            template_key = self.template_names['golden_puzzle']

            # Check if template is loaded
            if template_key not in self.screen_scanner.templates:
                self.logger.error(f"Template {template_key} not loaded in screen_scanner")
                return 0

            matches = self.screen_scanner.find_template(region_cv, template_key, threshold=0.8)

            puzzle_count = len(matches)
            self.logger.info(f"Found {puzzle_count} golden puzzle pieces in loot region")
            return puzzle_count

        except Exception as e:
            self.logger.error(f"Error counting puzzles: {str(e)}")
            return 0
    
    def _check_power_threshold(self):
        """Check if power is under threshold"""
        try:
            self.logger.info(f"DEBUG: Starting power check, threshold is {self.max_power_threshold}M")
            self.logger.info(f"DEBUG: Power region coordinates: {self.power_region}")

            # Take screenshot of power region
            x1, y1, x2, y2 = self.power_region
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            self.logger.info(f"DEBUG: Captured power region screenshot: {x2-x1}x{y2-y1} pixels")

            # OCR to read power value (simplified)
            # This would need actual OCR implementation
            power_text = self._ocr_power_region(screenshot)
            self.logger.info(f"DEBUG: Raw OCR result: '{power_text}'")

            power_value = self._parse_power_value(power_text)
            self.logger.info(f"DEBUG: Parsed power value: {power_value}M")

            self.logger.info(f"Power detected: {power_value}M (threshold: {self.max_power_threshold}M)")

            if power_value <= self.max_power_threshold:
                self.logger.info(f"Power check PASSED: {power_value}M <= {self.max_power_threshold}M")
                return True
            else:
                self.logger.info(f"Power check FAILED: {power_value}M > {self.max_power_threshold}M")
                return False

        except Exception as e:
            self.logger.error(f"Error checking power: {str(e)}")
            self.logger.error(f"DEBUG: Exception details: {type(e).__name__}: {str(e)}")
            return False
    
    def _capture_power_region(self, center_x: int, center_y: int, width: int = 144, height: int = 44) -> np.ndarray:
        """Capture power region for OCR (same method as map_trade)"""
        x = center_x - width // 2
        y = center_y - height // 2
        screenshot = pyautogui.screenshot(region=(x, y, width, height))
        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    def _extract_power_from_image(self, image: np.ndarray) -> Optional[str]:
        """Extract power text using direct OCR (same method as map_trade)"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Use direct OCR with configuration for numbers and letters (for M, K, B)
            config = '--psm 8 -c tessedit_char_whitelist=0123456789MKB.,'
            text = pytesseract.image_to_string(gray, config=config).strip()

            self.logger.info(f"Direct OCR result: '{text}'")
            return text if text else None

        except Exception as e:
            self.logger.warning(f"Error extracting power from image: {e}")
            return None

    def _ocr_power_region(self, screenshot=None):
        """OCR the power region to extract text"""
        try:
            # Calculate center of power region
            x1, y1, x2, y2 = self.power_region
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            width = x2 - x1
            height = y2 - y1

            # Capture power region using map_trade method
            power_image = self._capture_power_region(center_x, center_y, width, height)

            # Extract power text using map_trade OCR method
            power_text = self._extract_power_from_image(power_image)

            if power_text:
                self.logger.info(f"OCR power text: '{power_text}'")
                return power_text.strip()
            else:
                self.logger.warning("OCR returned no power text")
                return ""

        except Exception as e:
            self.logger.error(f"Error reading power text: {str(e)}")
            return ""
    
    def _parse_power_value(self, power_text):
        """Parse power text to numeric value"""
        try:
            # Check if OCR returned empty or invalid text
            if not power_text or power_text.strip() == '':
                self.logger.warning("OCR returned empty power text - assuming high power to fail check")
                return 999.0  # Return high value to fail threshold check

            # Clean the text and extract number and multiplier
            clean_text = power_text.replace(',', '').replace(' ', '').upper()
            self.logger.info(f"Parsing power text: '{power_text}' -> '{clean_text}'")

            # Extract number and multiplier with more flexible regex
            match = re.search(r'(\d+\.?\d*)([KMB]?)', clean_text)
            if match:
                number = float(match.group(1))
                multiplier = match.group(2)

                self.logger.info(f"Extracted: number={number}, multiplier='{multiplier}'")

                if multiplier == 'K':
                    result = number / 1000  # Convert to millions
                elif multiplier == 'M':
                    result = number
                elif multiplier == 'B':
                    result = number * 1000  # Convert to millions
                else:
                    # If no multiplier, assume it's already in millions or a large number
                    if number > 1000:
                        result = number / 1000000  # Convert large number to millions
                    else:
                        result = number  # Assume it's already in millions

                self.logger.info(f"Parsed power value: {result}M")
                return result

            self.logger.warning(f"Could not parse power text: '{power_text}' - no valid number found")
            return 999.0  # Return high value to fail threshold check

        except Exception as e:
            self.logger.error(f"Error parsing power value: {str(e)}")
            return 999.0  # Return high value to fail threshold check
    
    def _click_on_truck(self, x, y):
        """Final click on selected truck"""
        try:
            self.logger.info(f"Final selection: Clicking truck at ({x}, {y})")
            pyautogui.click(x, y)
            time.sleep(0.5)
        except Exception as e:
            self.logger.error(f"Error clicking truck: {str(e)}")
    
    def _reset_and_retry(self):
        """Reset the search and try again"""
        try:
            self.logger.info("Resetting search...")
            pyautogui.click(self.reset_coord[0], self.reset_coord[1])
            self.logger.info("Waiting 2 seconds for reset to complete...")
            time.sleep(2.0)  # Wait longer for reset to complete

            # Clear truck positions for new scan
            self.truck_positions = []
            self.current_truck_index = 0

        except Exception as e:
            self.logger.error(f"Error resetting: {str(e)}")

    def _check_safety_conditions(self):
        """Check if search should be stopped (mouse movement or stop button)"""
        try:
            # Check if stop was requested via GUI
            if self.stop_requested:
                self.logger.info("Stop requested via GUI - halting search")
                return False

            # Check mouse position for safety pause
            current_mouse_pos = pyautogui.position()
            screen_width, screen_height = pyautogui.size()

            # If mouse is at screen edges (safety zone), pause for 5 seconds
            edge_threshold = 10
            if (current_mouse_pos.x <= edge_threshold or
                current_mouse_pos.x >= screen_width - edge_threshold or
                current_mouse_pos.y <= edge_threshold or
                current_mouse_pos.y >= screen_height - edge_threshold):

                self.logger.info("Mouse at screen edge - pausing for 5 seconds (move mouse to center to continue)")
                time.sleep(5.0)

                # Check again after pause - if still at edge, continue pausing
                new_mouse_pos = pyautogui.position()
                if (new_mouse_pos.x <= edge_threshold or
                    new_mouse_pos.x >= screen_width - edge_threshold or
                    new_mouse_pos.y <= edge_threshold or
                    new_mouse_pos.y >= screen_height - edge_threshold):

                    self.logger.info("Mouse still at screen edge - continuing pause...")
                    return self._check_safety_conditions()  # Recursive check

            return True  # Safe to continue

        except Exception as e:
            self.logger.error(f"Error in safety check: {str(e)}")
            return True  # Default to continue on error

    def stop_search(self):
        """Stop the current search operation"""
        self.stop_requested = True
        self.logger.info("Search stop requested")

    def _load_power_threshold(self):
        """Load power threshold from config file"""
        try:
            import json
            config_file = "find_truck_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    threshold = config.get('max_power_threshold', 1.8)
                    self.logger.info(f"Loaded power threshold: {threshold}M")
                    return threshold
        except Exception as e:
            self.logger.error(f"Error loading power threshold: {str(e)}")

        # Default value
        return 1.8

    def _save_power_threshold(self):
        """Save power threshold to config file"""
        try:
            import json
            config_file = "find_truck_config.json"
            config = {'max_power_threshold': self.max_power_threshold}
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            self.logger.info(f"Saved power threshold: {self.max_power_threshold}M")
        except Exception as e:
            self.logger.error(f"Error saving power threshold: {str(e)}")

    def set_power_threshold(self, threshold):
        """Set the maximum power threshold"""
        try:
            self.max_power_threshold = float(threshold)
            self._save_power_threshold()  # Save to config file
            self.logger.info(f"Power threshold updated to: {self.max_power_threshold}M")
        except ValueError:
            self.logger.error(f"Invalid power threshold: {threshold}")
    
    def get_power_threshold(self):
        """Get current power threshold"""
        return self.max_power_threshold
    
    def get_status_info(self):
        """Get module status information"""
        return {
            'status': 'MANUAL',
            'activity': 'Manual trigger only',
            'progress': 'Ready',
            'next_execution': 'Manual',
            'power_threshold': f"{self.max_power_threshold}M"
        }

    # Centralized Scanner Action Methods
    def attack_truck(self, screen_data: Dict[str, Any]) -> bool:
        """Action method for Purple_Truck/Golden_Truck detection - attack truck"""
        try:
            self.logger.info("🎯 CENTRALIZED: Truck detected - starting attack sequence")
            success = self.execute(screen_data)
            self.logger.info(f"✅ CENTRALIZED: Truck attack sequence completed - success: {success}")
            return success
        except Exception as e:
            self.logger.error(f"❌ CENTRALIZED: Error in attack_truck action: {e}")
            return False
