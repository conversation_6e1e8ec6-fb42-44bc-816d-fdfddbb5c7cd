#!/usr/bin/env python3
"""
Test script to verify the action configuration dialog works properly
"""

import tkinter as tk
from tkinter import messagebox

def create_test_dialog():
    """Create a test version of the action configuration dialog"""
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    dialog = tk.Toplevel(root)
    dialog.title("Test Action Dialog - Position (100, 200)")
    dialog.geometry("500x500")
    dialog.attributes('-topmost', True)
    dialog.grab_set()
    dialog.resizable(False, False)
    
    # Title
    title_label = tk.Label(dialog, text="Captured Position: (100, 200)",
                          font=("Arial", 12, "bold"))
    title_label.pack(pady=10)
    
    # Action type selection
    tk.Label(dialog, text="Select Action Type:", font=("Arial", 10)).pack(pady=(10, 5))
    
    action_var = tk.StringVar(value="click")
    
    # Action type radio buttons
    actions = [
        ("click", "Click - Regular mouse click"),
        ("double_click", "Double Click - Double mouse click"),
        ("right_click", "Right Click - Right mouse click"),
        ("text", "Text Input - Type static text"),
        ("date", "Date Input - Auto-fill appointment date"),
        ("start_time", "Start Time - Auto-fill appointment start time"),
        ("end_time", "End Time - Auto-fill calculated end time"),
        ("appointment_type", "Appointment Type - Select appointment type")
    ]
    
    for value, text in actions:
        tk.Radiobutton(dialog, text=text, variable=action_var, value=value,
                      anchor=tk.W, wraplength=350).pack(anchor=tk.W, padx=20, pady=2)
    
    # Additional configuration frame
    config_frame = tk.LabelFrame(dialog, text="Configuration", padding=10)
    config_frame.pack(fill=tk.X, padx=10, pady=10)
    
    # Text input (for static text)
    tk.Label(config_frame, text="Static Text (if applicable):").pack(anchor=tk.W)
    text_var = tk.StringVar()
    tk.Entry(config_frame, textvariable=text_var, width=40).pack(fill=tk.X, pady=2)
    
    # Description
    tk.Label(config_frame, text="Description:").pack(anchor=tk.W, pady=(10, 0))
    desc_var = tk.StringVar(value="Test action at (100, 200)")
    tk.Entry(config_frame, textvariable=desc_var, width=40).pack(fill=tk.X, pady=2)
    
    # Delay
    tk.Label(config_frame, text="Delay after action (seconds):").pack(anchor=tk.W, pady=(10, 0))
    delay_var = tk.StringVar(value="1.0")
    tk.Entry(config_frame, textvariable=delay_var, width=10).pack(anchor=tk.W, pady=2)
    
    # Instructions
    instruction_frame = tk.Frame(dialog, bg="lightyellow", relief=tk.SUNKEN, bd=2)
    instruction_frame.pack(fill=tk.X, padx=10, pady=10)
    
    instruction_text = tk.Label(instruction_frame, 
                              text="1. Select action type above\n2. Configure settings\n3. Click 'SAVE ACTION' to add to sequence",
                              bg="lightyellow", font=("Arial", 10), justify=tk.LEFT)
    instruction_text.pack(pady=5)
    
    # Buttons - Make them more prominent
    button_frame = tk.Frame(dialog, bg="lightgray", relief=tk.RAISED, bd=2)
    button_frame.pack(fill=tk.X, pady=20, padx=10)
    
    # Add a separator line
    separator = tk.Frame(dialog, height=2, bg="gray")
    separator.pack(fill=tk.X, padx=10, pady=(0, 10))
    
    def save_action():
        action_type = action_var.get()
        description = desc_var.get()
        delay = delay_var.get()
        static_text = text_var.get()
        
        messagebox.showinfo("Action Saved", 
                           f"Action Type: {action_type}\n"
                           f"Description: {description}\n"
                           f"Delay: {delay}s\n"
                           f"Text: {static_text}")
        dialog.destroy()
        root.quit()
    
    def cancel_action():
        messagebox.showinfo("Cancelled", "Action cancelled")
        dialog.destroy()
        root.quit()
    
    # Create larger, more prominent buttons
    save_btn = tk.Button(button_frame, text="✓ SAVE ACTION", command=save_action,
                       bg="green", fg="white", font=("Arial", 12, "bold"),
                       width=15, height=2, relief=tk.RAISED, bd=3)
    save_btn.pack(side=tk.LEFT, padx=20, pady=10)
    
    cancel_btn = tk.Button(button_frame, text="✗ CANCEL", command=cancel_action,
                         bg="red", fg="white", font=("Arial", 12, "bold"),
                         width=15, height=2, relief=tk.RAISED, bd=3)
    cancel_btn.pack(side=tk.RIGHT, padx=20, pady=10)
    
    # Center the dialog on screen
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    print("Testing action configuration dialog...")
    create_test_dialog()
    print("Test completed!")
