"""
Debug Template Setup Tool
Creates templates needed for UI recovery debug feature
"""
import tkinter as tk
from tkinter import messagebox
import pyautogui
import os
from PIL import Image, ImageTk
import time

class DebugTemplateSetup:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Debug Template Setup")
        self.root.geometry("600x500")
        
        # Templates needed for UI detection and navigation
        self.ui_templates = [
            {
                'name': 'events_button',
                'description': 'Events button (usually bottom right of screen)',
                'instructions': 'Position mouse over the Events button and press SPACE'
            },
            {
                'name': 'base_button',
                'description': 'Base button (usually bottom left of screen)',
                'instructions': 'Position mouse over the Base button and press SPACE'
            },
            {
                'name': 'world_button',
                'description': 'World button (for world map view)',
                'instructions': 'Position mouse over the World button and press SPACE'
            }
        ]
        
        self.current_template = 0
        self.capturing = False
        self.setup_ui()
        
    def setup_ui(self):
        # Title
        title_label = tk.Label(self.root, text="🎯 UI Template Setup",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Instructions
        instructions = tk.Text(self.root, height=8, width=70, wrap=tk.WORD)
        instructions.pack(pady=10, padx=20)
        
        instructions.insert(tk.END, """UI Template Setup Instructions:

1. These templates help the system navigate and detect UI states
2. We'll capture: Events, Base, and World buttons
3. These are essential for automation and UI recovery
4. Click 'Start Capture' to begin
5. Position your game window so UI elements are visible

6. Two capture methods available:

   METHOD 1 - Quick Capture (recommended for buttons):
   • Position mouse over the UI element center
   • Press SPACE to capture 50x50 area around mouse

   METHOD 2 - Drag Selection (for larger/custom areas):
   • Position mouse at top-left corner of desired area
   • Press CTRL+SPACE to set first corner
   • Move mouse to bottom-right corner
   • Press CTRL+SPACE again to capture selection

Templates will be saved to the 'templates' folder.""")
        
        instructions.config(state=tk.DISABLED)
        
        # Current template info
        self.info_frame = tk.Frame(self.root)
        self.info_frame.pack(pady=20)
        
        self.template_label = tk.Label(self.info_frame, text="", 
                                      font=("Arial", 12, "bold"))
        self.template_label.pack()
        
        self.desc_label = tk.Label(self.info_frame, text="", 
                                  font=("Arial", 10))
        self.desc_label.pack()
        
        # Buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        self.capture_button = tk.Button(button_frame, text="🎯 Start Capture", 
                                       command=self.start_capture,
                                       font=("Arial", 12, "bold"),
                                       bg="#4CAF50", fg="white",
                                       padx=20, pady=10)
        self.capture_button.pack(side=tk.LEFT, padx=10)
        
        self.skip_button = tk.Button(button_frame, text="⏭️ Skip This", 
                                    command=self.skip_template,
                                    font=("Arial", 12),
                                    bg="#FF9800", fg="white",
                                    padx=20, pady=10)
        self.skip_button.pack(side=tk.LEFT, padx=10)
        
        self.done_button = tk.Button(button_frame, text="✅ Done", 
                                    command=self.finish_setup,
                                    font=("Arial", 12, "bold"),
                                    bg="#2196F3", fg="white",
                                    padx=20, pady=10)
        self.done_button.pack(side=tk.LEFT, padx=10)
        
        # Status
        self.status_label = tk.Label(self.root, text="Ready to capture templates", 
                                    font=("Arial", 10))
        self.status_label.pack(pady=10)
        
        self.update_template_info()
        
    def update_template_info(self):
        if self.current_template < len(self.ui_templates):
            template = self.ui_templates[self.current_template]
            self.template_label.config(text=f"Template {self.current_template + 1}/{len(self.ui_templates)}: {template['name']}")
            self.desc_label.config(text=template['description'])
            self.status_label.config(text=f"Instructions: {template['instructions']}")
        else:
            self.template_label.config(text="All templates captured!")
            self.desc_label.config(text="UI templates setup complete")
            self.status_label.config(text="Templates ready for automation and UI recovery")
            self.capture_button.config(state=tk.DISABLED)
            self.skip_button.config(state=tk.DISABLED)
    
    def start_capture(self):
        if self.current_template >= len(self.ui_templates):
            return

        template = self.ui_templates[self.current_template]

        # Start capture mode
        self.capturing = True
        self.capture_button.config(text="🎯 Capturing...", state=tk.DISABLED)
        self.skip_button.config(state=tk.DISABLED)

        # Hide window for capture
        self.root.withdraw()

        try:
            # Wait a moment for window to hide
            time.sleep(0.5)

            # Show simple instruction
            import tkinter.messagebox as msgbox
            msgbox.showinfo("Ready to Capture",
                f"Capturing: {template['name']}\n\n"
                f"{template['description']}\n\n"
                f"1. Position your mouse over the {template['name']}\n"
                f"2. Press SPACE to capture\n"
                f"3. Don't click anything - just press SPACE!")

            # Wait for spacebar press
            self.status_label.config(text=f"Position mouse over {template['name']} and press SPACE...")
            self.wait_for_spacebar(template)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture template: {str(e)}")
            self.capturing = False

        finally:
            # Show window again
            self.root.deiconify()
            self.capture_button.config(text="🎯 Start Capture", state=tk.NORMAL)
            self.skip_button.config(state=tk.NORMAL)
            self.update_template_info()

    def wait_for_spacebar(self, template):
        """Wait for spacebar press and capture template"""
        import keyboard

        # Show instruction window
        instruction_window = tk.Toplevel()
        instruction_window.title("Capture Mode")
        instruction_window.geometry("400x200")
        instruction_window.attributes('-topmost', True)

        tk.Label(instruction_window,
                text=f"🎯 Capturing: {template['name']}",
                font=("Arial", 14, "bold")).pack(pady=10)

        tk.Label(instruction_window,
                text=template['description'],
                font=("Arial", 10)).pack(pady=5)

        # Capture method selection
        method_frame = tk.Frame(instruction_window)
        method_frame.pack(pady=10)

        tk.Label(method_frame,
                text="Choose capture method:",
                font=("Arial", 11, "bold")).pack()

        tk.Label(method_frame,
                text="SPACE = Quick capture (50x50 around mouse)",
                font=("Arial", 10),
                fg="blue").pack()

        tk.Label(method_frame,
                text="CTRL+SPACE = Drag selection (click top-left, then bottom-right)",
                font=("Arial", 10),
                fg="green").pack()

        status_var = tk.StringVar(value="Waiting for SPACE...")
        status_label = tk.Label(instruction_window,
                               textvariable=status_var,
                               font=("Arial", 10))
        status_label.pack(pady=5)

        # Drag selection state
        drag_state = {'first_click': None, 'selecting': False}

        def on_space():
            """Quick capture: 50x50 around mouse"""
            try:
                # Get mouse position
                mouse_x, mouse_y = pyautogui.position()

                # Take screenshot
                screenshot = pyautogui.screenshot()

                # Capture 50x50 area around mouse
                size = 50
                left = max(0, mouse_x - size//2)
                top = max(0, mouse_y - size//2)
                right = min(screenshot.width, mouse_x + size//2)
                bottom = min(screenshot.height, mouse_y + size//2)

                template_image = screenshot.crop((left, top, right, bottom))

                # Save template
                templates_dir = "templates"
                if not os.path.exists(templates_dir):
                    os.makedirs(templates_dir)

                template_path = os.path.join(templates_dir, f"{template['name']}.png")
                template_image.save(template_path)

                status_var.set(f"✅ Quick captured 50x50 at ({mouse_x}, {mouse_y})")
                self.current_template += 1

                # Close instruction window after short delay
                instruction_window.after(1500, instruction_window.destroy)

            except Exception as e:
                status_var.set(f"❌ Error: {str(e)}")
                instruction_window.after(3000, instruction_window.destroy)

        def on_ctrl_space():
            """Drag selection: Click top-left, then bottom-right"""
            try:
                if not drag_state['selecting']:
                    # First click - record top-left
                    drag_state['first_click'] = pyautogui.position()
                    drag_state['selecting'] = True
                    status_var.set(f"📍 Top-left set at {drag_state['first_click']}. Now click bottom-right and press CTRL+SPACE again")
                else:
                    # Second click - record bottom-right and capture
                    second_click = pyautogui.position()

                    # Calculate selection area
                    left = min(drag_state['first_click'][0], second_click[0])
                    top = min(drag_state['first_click'][1], second_click[1])
                    right = max(drag_state['first_click'][0], second_click[0])
                    bottom = max(drag_state['first_click'][1], second_click[1])

                    # Take screenshot and crop selection
                    screenshot = pyautogui.screenshot()
                    template_image = screenshot.crop((left, top, right, bottom))

                    # Save template
                    templates_dir = "templates"
                    if not os.path.exists(templates_dir):
                        os.makedirs(templates_dir)

                    template_path = os.path.join(templates_dir, f"{template['name']}.png")
                    template_image.save(template_path)

                    width = right - left
                    height = bottom - top
                    status_var.set(f"✅ Drag captured {width}x{height} from {drag_state['first_click']} to {second_click}")
                    self.current_template += 1

                    # Close instruction window after short delay
                    instruction_window.after(1500, instruction_window.destroy)

            except Exception as e:
                status_var.set(f"❌ Error: {str(e)}")
                instruction_window.after(3000, instruction_window.destroy)

        # Set up keyboard listeners
        def on_key_event(e):
            if e.name == 'space':
                if keyboard.is_pressed('ctrl'):
                    on_ctrl_space()
                else:
                    on_space()

        keyboard.on_press(on_key_event)

        # Wait for window to close
        instruction_window.wait_window()

        # Remove keyboard listener
        keyboard.unhook_all()
    
    def skip_template(self):
        if self.current_template < len(self.ui_templates):
            template = self.ui_templates[self.current_template]
            self.status_label.config(text=f"⏭️ Skipped: {template['name']}")
            self.current_template += 1
            self.update_template_info()
    
    def finish_setup(self):
        messagebox.showinfo("Setup Complete",
            "UI template setup finished!\n\n"
            "Templates captured:\n"
            "• events_button - For main screen detection\n"
            "• base_button - For base view detection\n"
            "• world_button - For world map detection\n\n"
            "These templates enable UI recovery, navigation, and automation features.")
        self.root.destroy()
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = DebugTemplateSetup()
    app.run()
