#!/usr/bin/env python3
"""
Map piece click coordinate calibration tool
"""
import pyautogui
import time

def calibrate_map_clicks():
    """Help calibrate map piece click coordinates"""
    print("Map Piece Click Coordinate Calibration")
    print("=" * 50)
    print("This tool will help you find the correct click coordinates for map pieces")
    print()
    print("Instructions:")
    print("1. Make sure you're on the map trade screen with all 7 map pieces visible")
    print("2. Position your mouse over the CENTER of each map piece")
    print("3. Press SPACE to capture the coordinates")
    print("4. Press ESC to finish")
    print()
    
    map_pieces = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7']
    coordinates = {}
    
    for map_piece in map_pieces:
        print(f"\nCalibrating {map_piece}:")
        print(f"Position your mouse over the CENTER of {map_piece} and press SPACE")
        
        while True:
            # Check for key press
            try:
                # Simple way to wait for space key
                input(f"Press ENTER when mouse is positioned over {map_piece}: ")
                
                # Get current mouse position
                x, y = pyautogui.position()
                coordinates[map_piece] = (x, y)
                
                print(f"✅ {map_piece} click coordinate captured: ({x}, {y})")
                break
                
            except KeyboardInterrupt:
                print("\nCalibration cancelled")
                return None
    
    print("\n" + "=" * 50)
    print("CALIBRATION RESULTS:")
    print("Copy these coordinates to your map_trade.py file:")
    print()
    
    for map_piece in map_pieces:
        if map_piece in coordinates:
            x, y = coordinates[map_piece]
            print(f"            '{map_piece}': {{")
            print(f"                'quantity_pos': (532, 436),  # Keep TrOCR coordinates")
            print(f"                'click_pos': ({x}, {y}),     # Your calibrated click coordinates")
            print(f"                'quantity': None")
            print(f"            }},")
    
    print("\n" + "=" * 50)
    print("Next steps:")
    print("1. Update the _define_map_piece_regions method in modules/map_trade.py")
    print("2. Replace the 'click_pos' values with the coordinates above")
    print("3. Keep the 'quantity_pos' values as they are (TrOCR optimized)")
    print("4. Test the map trade automation again")
    
    return coordinates

if __name__ == "__main__":
    calibrate_map_clicks()
