#!/usr/bin/env python3
"""
Simple Map Trade Test - Clean and Working
"""
import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# Configure Tesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# TrOCR coordinates that we know work
COORDINATES = {
    'M1': (532, 436),
    'M2': (613, 436),
    'M3': (696, 436),
    'M4': (775, 436),
    'M5': (577, 513),
    'M6': (654, 521),
    'M7': (735, 519),
}

def capture_and_read_number(x, y, size=25):
    """Capture region and read number - simple and clean"""
    try:
        # Capture region
        capture_x = x - size // 2
        capture_y = y - size // 2
        screenshot = pyautogui.screenshot(region=(capture_x, capture_y, size, size))
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # OCR with simple config
        config = '--psm 8 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(gray, config=config).strip()
        
        if text.isdigit():
            return int(text)
        return None
        
    except Exception as e:
        print(f"Error reading at ({x}, {y}): {e}")
        return None

def test_map_reading():
    """Test reading all map pieces"""
    print("Simple Map Reading Test")
    print("=" * 40)
    
    results = {}
    
    for map_name, (x, y) in COORDINATES.items():
        print(f"Reading {map_name} at ({x}, {y})...")
        number = capture_and_read_number(x, y)
        
        if number is not None:
            results[map_name] = number
            print(f"  ✅ {map_name}: {number}")
        else:
            print(f"  ❌ {map_name}: Could not read")
    
    print(f"\nResults: {results}")
    
    if results:
        # Find trade
        lowest = min(results.items(), key=lambda x: x[1])
        highest = max(results.items(), key=lambda x: x[1])
        
        print(f"\nTrade Decision:")
        print(f"  Give: {lowest[0]} ({lowest[1]} pieces)")
        print(f"  Get: {highest[0]} ({highest[1]} pieces)")
        
        # Test coordinates
        print(f"\nButton coordinates to use:")
        print(f"  'You will lose' button: (732, 261)")
        print(f"  'Initiate exchange': (636, 611)")
        print(f"  'Confirm exchange': (633, 498)")
        
        return True
    else:
        print("\n❌ No numbers detected")
        print("Make sure you're on the map trade screen")
        return False

if __name__ == "__main__":
    print("Position yourself on the map trade screen and press ENTER")
    input("Press ENTER when ready: ")
    
    test_map_reading()
