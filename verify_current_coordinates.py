#!/usr/bin/env python3
"""
Quick verification of current map coordinates
Shows what we're actually capturing at each position
"""
import cv2
import numpy as np
import pytesseract
import pyautogui

def verify_coordinates():
    """Verify current coordinates and show what we're capturing"""
    
    # Configure Tesseract
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    # Current coordinates from the module
    current_coords = {
        'M1': (531, 435),
        'M2': (612, 437), 
        'M3': (695, 436),
        'M4': (774, 436),
        'M5': (572, 514),
        'M6': (653, 514),
        'M7': (733, 513)
    }
    
    print("=== VERIFYING CURRENT COORDINATES ===")
    print("Testing OCR at each current coordinate position")
    print()
    
    success_count = 0
    
    for map_name, (x, y) in current_coords.items():
        print(f"{map_name} at ({x}, {y}):")
        
        try:
            # Capture 35x35 region
            size = 35
            region_x = x - size // 2
            region_y = y - size // 2
            screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
            image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Save debug image
            cv2.imwrite(f"verify_{map_name}.png", image)
            
            # Apply 3x scaling (proven method)
            scaled = cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
            
            # Use proven PSM 8 configuration
            config = '--psm 8 -c tessedit_char_whitelist=0123456789'
            text = pytesseract.image_to_string(scaled, config=config).strip()
            
            if text.isdigit():
                print(f"  ✅ SUCCESS: {text}")
                success_count += 1
            elif text:
                print(f"  ❌ FAILED: '{text}' (not a digit)")
            else:
                print(f"  ❌ FAILED: (empty result)")
                
            print(f"  Debug image: verify_{map_name}.png")
            
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
        
        print()
    
    print(f"SUCCESS RATE: {success_count}/7 ({success_count/7*100:.1f}%)")
    
    if success_count == 7:
        print("🎉 ALL COORDINATES WORKING!")
    else:
        failed_pieces = []
        for map_name, (x, y) in current_coords.items():
            # Quick retest to identify failed pieces
            try:
                size = 35
                region_x = x - size // 2
                region_y = y - size // 2
                screenshot = pyautogui.screenshot(region=(region_x, region_y, size, size))
                image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                scaled = cv2.resize(gray, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
                config = '--psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(scaled, config=config).strip()
                
                if not text.isdigit():
                    failed_pieces.append(map_name)
            except:
                failed_pieces.append(map_name)
        
        print(f"❌ FAILED PIECES: {', '.join(failed_pieces)}")
        print("Run calibrate_all_map_coordinates.py to fix these coordinates")

if __name__ == "__main__":
    verify_coordinates()
